# See http://help.github.com/ignore-files/ for more about ignoring files.

# direnv managed environment variables
.envrc

# compiled output
dist
tmp
/out-tsc

# dependencies
node_modules

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

.cursor/

# misc
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings

# drawio
*.bkp
*.dtmp

# dart
coverage/
coverage.lcov

# flutter
.flutter-*

# Credential Configurations
*creds*
*credentials*
*amplify_config*

# System Files
.DS_Store
.envrc
.versions
Thumbs.db

.nx/cache

.cursor/rules/nx-rules.mdc
.github/instructions/nx.instructions.md
