# Novassist - AI-Powered Healthcare Automation

![Novassist Logo](https://img.shields.io/badge/Novassist-Healthcare%20AI-blue?style=for-the-badge)

A comprehensive healthcare startup platform focused on AI agentic workflow automation of administrative tasks. The platform creates comprehensive patient profiles, reduces clinician burden, and improves accuracy in healthcare operations.

This repository consists of Novassit.AI's digital experience application clients, websites and common modules.

## 📚 Documentation

This repository includes a comprehensive application framework for building enterprise Flutter applications. For detailed documentation, see:

### 🏗️ Framework Documentation
- **[Framework Overview](docs/README.md)** - High-level introduction to the NovAssist Application Framework
- **[Technical Architecture](docs/architecture.md)** - Detailed architecture diagrams and component relationships
- **[Non-Functional Capabilities](docs/non-functional-capabilities.md)** - Detailed documentation of framework components
- **[Integration Examples](docs/test-application-flow.md)** - Real-world examples of framework integration
- **[Implementation Guide](docs/integration-guide.md)** - Step-by-step guide for building applications with the framework
- **[Documentation Summary](docs/summary.md)** - Complete overview of all documentation

### 🧪 Test Application
The test application at `apps/tests/mycs_feature_test` demonstrates how all framework components work together to create a complete, functional application. See the [Integration Examples](docs/test-application-flow.md) documentation for detailed analysis.

## 🏛️ Repository Architecture

This repository consists of applications and shared modules across multiple technology stacks. It uses Nx tooling to manage the dependencies between these components.

<a alt="Nx logo" href="https://nx.dev" target="_blank" rel="noreferrer"><img src="https://raw.githubusercontent.com/nrwl/nx/master/images/nx-logo.png" width="45"></a>

### 🎯 Framework Components

The NovAssist Application Framework provides:

#### Functional Capabilities (Features)
- **User Identity Management** - Authentication, user profiles, session management
- **Organization Management** - Multi-tenant support, organization switching  
- **Billing and Payments** - Subscription management, payment processing

#### Non-Functional Capabilities (Components)
- **Application Feature Management** - Plugin architecture, feature registry
- **Application Navigation** - Routing, layouts, authentication guards
- **Application State Management** - BLoC pattern, persistent storage
- **Data Access Service Scaffolding** - HTTP clients, authentication interceptors
- **UI Widgets** - Forms, dialogs, navigation components
- **Common Utilities** - Async operations, FFI, caching
- **Logging and Error Handling** - Structured logging, error recovery

### 🚀 Quick Start

To get started with the framework:

1. **Explore the Documentation**: Start with the [Framework Overview](docs/README.md)
2. **Review Architecture**: Understand the [Technical Architecture](docs/architecture.md)
3. **Study Examples**: Examine the [Integration Examples](docs/test-application-flow.md)
4. **Build Your App**: Follow the [Implementation Guide](docs/integration-guide.md)

## 🛠️ Development

### Running tasks

To execute tasks with Nx use the following syntax:

```
nx <target> <project> <...options>
```

You can also run multiple targets:

```
nx run-many -t <target1> <target2>
```

..or add `-p` to filter specific projects

```
nx run-many -t <target1> <target2> -p <proj1> <proj2>
```

Targets can be defined in the `package.json` or `projects.json`. Learn more [in the docs](https://nx.dev/features/run-tasks).

### Want better Editor Integration?

Have a look at the [Nx Console extensions](https://nx.dev/nx-console). It provides autocomplete support, a UI for exploring and running tasks & generators, and more! Available for VSCode, IntelliJ and comes with a LSP for Vim users.

### Ready to deploy?

Just run `nx build demoapp` to build the application. The build artifacts will be stored in the `dist/` directory, ready to be deployed.

### Set up CI!

Nx comes with local caching already built-in (check your `nx.json`). On CI you might want to go a step further.

- [Set up remote caching](https://nx.dev/features/share-your-cache)
- [Set up task distribution across multiple machines](https://nx.dev/nx-cloud/features/distribute-task-execution)
- [Learn more how to setup CI](https://nx.dev/recipes/ci)

## 📦 Project Generation

The following NX commands were run to generate the projects within this repo.

### Applications

**Client Application**

```
name='<NAME OF APPLICATION>'
description='<DESCRIPTION OF WHAT THE LIBRARY CLASSESS AND FUNCTIONS DO>'

npx nx generate \
  @nxrocks/nx-flutter:project \
  --project-name=${name} \
  --directory=apps/clients/${name} \
  --org=ai.novassist.${name} \
  --description="${description}" \
  --template=app \
  --useFvm=false
```

**Integration Test Application**

```
name='<NAME OF APPLICATION>'
description='<DESCRIPTION OF WHAT THE APPLICATION DOES>'

npx nx generate \
  @nxrocks/nx-flutter:project \
  --project-name=${name} \
  --directory=apps/tests/${name} \
  --org=ai.novassist.test.${name} \
  --description="${description}" \
  --template=app \
  --useFvm=false
```

### Shared Feature Plugins

```
name='<NAME OF FEATURE PACKAGE>'
description='<DESCRIPTION OF WHAT FEATURE THE PACKAGE IMPLEMENTS>'

npx nx generate \
  @nxrocks/nx-flutter:project \
  --project-name=${name}_feature \
  --directory=libs/shared/feature/flutter/${name} \
  --org=ai.novassist.shared.feature.flutter.${name} \
  --description="${description}" \
  --template=package \
  --useFvm=false
```

### Shared Service Libraries

```
name='<NAME OF SERVICE PLUGIN>'
description='<DESCRIPTION OF WHAT THE SERVICE IMPLEMENTS>'

npx nx generate \
  @nxrocks/nx-flutter:project \
  --project-name=${name}_service \
  --directory=libs/shared/service/flutter/${name} \
  --org=ai.novassist.shared.service.flutter.${name} \
  --description="${description}" \
  --template=package \
  --useFvm=false
```

### Component Libraries

```
name='<NAME OF COMPONENT PLUGIN>'
description='<DESCRIPTION OF WHAT COMPONENTS THE PLUGIN IMPLEMENTS>'

npx nx generate \
  @nxrocks/nx-flutter:project \
  --project-name=${name}_component \
  --directory=libs/component/flutter/${name} \
  --org=ai.novassist.component.flutter.${name} \
  --description="${description}" \
  --template=plugin \
  --useFvm=false
```

### Common Utility Libraries

```
name='<NAME OF LIBRARY>'
description='<DESCRIPTION OF WHAT THE LIBRARY CLASSESS AND FUNCTIONS DO>'

npx nx generate \
  @nxrocks/nx-flutter:project \
  --project-name=${name} \
  --directory=libs/commons/dart/${name} \
  --org=ai.novassist.commons.dart.${name} \
  --description="${description}" \
  --template=package \
  --useFvm=false
```

## 🔍 Explore the Project Graph

Run `nx graph` to show the graph of the workspace.
It will show tasks that you can run with Nx.

- [Learn more about Exploring the Project Graph](https://nx.dev/core-features/explore-graph)

## 📖 Learn More

If you happen to use Nx plugins, you can leverage code generators that might come with it.

Run `nx list` to get a list of available plugins and whether they have generators. Then run `nx list <plugin-name>` to see what generators are available.

Learn more about [Nx generators on the docs](https://nx.dev/features/generate-code).
