{"name": "novassist-monorepo", "version": "1.0.0", "private": true, "description": "Novassist - AI-Powered Healthcare Automation Platform", "workspaces": ["apps/websites/*"], "scripts": {"dev": "npm run dev --workspace=apps/websites/novassist.ai", "build": "npm run build --workspace=apps/websites/novassist.ai", "start": "npm run start --workspace=apps/websites/novassist.ai", "lint": "npm run lint --workspace=apps/websites/novassist.ai", "lint:fix": "npm run lint:fix --workspace=apps/websites/novassist.ai", "type-check": "npm run type-check --workspace=apps/websites/novassist.ai", "clean": "npm run clean --workspace=apps/websites/novassist.ai", "install:all": "npm install", "dev:website": "npm run dev --workspace=apps/websites/novassist.ai", "build:website": "npm run build --workspace=apps/websites/novassist.ai"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=22.0.0", "npm": ">=10.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/novassist.git"}, "keywords": ["healthcare", "ai", "automation", "workflow", "patient-profiles", "clinical-efficiency", "monorepo"], "author": "Novassist Team", "license": "PROPRIETARY"}