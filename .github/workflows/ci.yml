name: CI

on:
  workflow_dispatch:
  # push:
  #   branches:
  #     - main
  # pull_request:

permissions:
  actions: read
  contents: read

jobs:
  main:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      # Connect your workspace on nx.app and uncomment this to enable task distribution.
      # The "--stop-agents-after" is optional, but allows idle agents to shut down once the "build" targets have been requested
      # - run: npx nx-cloud start-ci-run --distribute-on="5 linux-medium-js" --stop-agents-after="build"

      # Cache node_modules
      - uses: actions/setup-node@v3
        with:
          node-version: 20
          cache: 'npm'
      - run: npm ci
      - uses: nrwl/nx-set-shas@v4

      - run: npx nx-cloud record -- nx format:check
      - run: npx nx affected -t lint test build
