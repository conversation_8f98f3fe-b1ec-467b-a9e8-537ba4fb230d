import 'package:test/test.dart';
import 'package:utilities_ab/cache/cache.dart';
import 'package:utilities_ab/cache/cache_entry.dart';
import 'package:utilities_ab/cache/eviction_algorithm.dart';
import 'package:utilities_ab/cache/in_memory_storage.dart';

void main() {
  group('Cache Tests', () {
    late Cache<String, String> lruCache;
    late Cache<String, String> lfuCache;
    late InMemoryStorage<String, CacheEntry<String, String>> storage;

    setUp(() {
      storage = InMemoryStorage<String, CacheEntry<String, String>>();

      // Mock loader function that simulates loading from backing store
      Future<String> mockLoader(String key) async {
        await Future.delayed(
            Duration(milliseconds: 10)); // Simulate network delay
        return 'Loaded: $key';
      }

      lruCache = Cache<String, String>(
        storage: storage,
        algorithm: EvictionAlgorithm.lru,
        maxSize: 3,
        loader: mockLoader,
      );

      lfuCache = Cache<String, String>(
        storage: InMemoryStorage<String, CacheEntry<String, String>>(),
        algorithm: EvictionAlgorithm.lfu,
        maxSize: 3,
        loader: mockLoader,
      );
    });

    group('LRU Cache Tests', () {
      test('should load and cache values', () async {
        final value = await lruCache['key1'];
        expect(value, equals('Loaded: key1'));

        final cachedValue = await lruCache['key1'];
        expect(cachedValue, equals('Loaded: key1'));
      });

      test('should evict least recently used item when cache is full',
          () async {
        // Fill the cache
        await lruCache['key1'];
        await lruCache['key2'];
        await lruCache['key3'];

        // Access key1 to make it recently used
        await lruCache['key1'];

        // Add a new key, should evict key2 (least recently used)
        await lruCache['key4'];

        // key2 should be evicted
        expect(await lruCache.containsKey('key2'), isFalse);
        expect(await lruCache.containsKey('key1'), isTrue);
        expect(await lruCache.containsKey('key3'), isTrue);
        expect(await lruCache.containsKey('key4'), isTrue);
      });

      test('should support direct assignment', () async {
        await lruCache.put('direct', 'Direct Value');

        final value = await lruCache['direct'];
        expect(value, equals('Direct Value'));
      });

      test('should support assignment operator for immediate storage',
          () async {
        lruCache['immediate'] = 'Immediate Value';

        // Wait a bit for async storage to complete
        await Future.delayed(Duration(milliseconds: 10));

        final value = await lruCache['immediate'];
        expect(value, equals('Immediate Value'));
      });

      test('should remove items', () async {
        await lruCache['key1'];
        expect(await lruCache.containsKey('key1'), isTrue);

        final removed = await lruCache.remove('key1');
        expect(removed, isTrue);
        expect(await lruCache.containsKey('key1'), isFalse);
      });

      test('should clear all items', () async {
        await lruCache['key1'];
        await lruCache['key2'];

        expect(await lruCache.size, equals(2));

        await lruCache.clear();
        expect(await lruCache.size, equals(0));
      });
    });

    group('LFU Cache Tests', () {
      test('should evict least frequently used item when cache is full',
          () async {
        // Fill the cache
        await lfuCache['key1'];
        await lfuCache['key2'];
        await lfuCache['key3'];

        // Access key1 multiple times to increase its frequency
        await lfuCache['key1'];
        await lfuCache['key1'];
        await lfuCache['key1'];

        // Access key2 once
        await lfuCache['key2'];

        // Add a new key, should evict key3 (least frequently used)
        await lfuCache['key4'];

        // key3 should be evicted
        expect(await lfuCache.containsKey('key3'), isFalse);
        expect(await lfuCache.containsKey('key1'), isTrue);
        expect(await lfuCache.containsKey('key2'), isTrue);
        expect(await lfuCache.containsKey('key4'), isTrue);
      });
    });

    group('Storage Interface Tests', () {
      test('InMemoryStorage should work correctly', () async {
        final storage = InMemoryStorage<String, String>();

        // Test put and get
        final putResult = await storage.put('key1', 'value1');
        expect(putResult, isTrue);

        final value = await storage.get('key1');
        expect(value, equals('value1'));

        // Test containsKey
        expect(await storage.containsKey('key1'), isTrue);
        expect(await storage.containsKey('key2'), isFalse);

        // Test remove
        final removeResult = await storage.remove('key1');
        expect(removeResult, isTrue);
        expect(await storage.containsKey('key1'), isFalse);

        // Test size
        expect(await storage.size, equals(0));

        // Test clear
        await storage.put('key1', 'value1');
        await storage.put('key2', 'value2');
        expect(await storage.size, equals(2));

        await storage.clear();
        expect(await storage.size, equals(0));

        // Test keys and values
        await storage.put('key1', 'value1');
        await storage.put('key2', 'value2');

        final keys = await storage.keys;
        expect(keys, containsAll(['key1', 'key2']));

        final values = await storage.values;
        expect(values, containsAll(['value1', 'value2']));
      });
    });

    group('Cache Entry Tests', () {
      test('should update access metadata correctly', () {
        final entry = CacheEntry<String, String>(
          key: 'test',
          value: 'value',
          frequency: 1,
        );

        final originalFrequency = entry.frequency;
        final originalAccess = entry.lastAccessed;

        // Wait a bit to ensure time difference
        Future.delayed(Duration(milliseconds: 10), () {
          entry.updateAccess();

          expect(entry.frequency, equals(originalFrequency + 1));
          expect(entry.lastAccessed.isAfter(originalAccess), isTrue);
        });
      });
    });
  });
}
