{"name": "utilities", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/commons/dart/utilities/src", "projectType": "library", "targets": {"analyze": {"executor": "nx:run-commands", "options": {"command": "flutter analyze", "cwd": "libs/commons/dart/utilities"}}, "clean": {"executor": "nx:run-commands", "options": {"command": "flutter clean", "cwd": "libs/commons/dart/utilities"}}, "format": {"executor": "nx:run-commands", "options": {"command": "dart format libs/commons/dart/utilities/*", "cwd": "libs/commons/dart/utilities"}}, "test": {"executor": "nx:run-commands", "options": {"command": "flutter test", "cwd": "libs/commons/dart/utilities"}}, "doctor": {"executor": "nx:run-commands", "options": {"command": "flutter doctor", "cwd": "libs/commons/dart/utilities"}}, "build-aar": {"executor": "nx:run-commands", "options": {"command": "flutter build aar", "cwd": "libs/commons/dart/utilities"}, "outputs": ["{workspaceRoot}/libs/commons/dart/utilities/build"]}, "build-apk": {"executor": "nx:run-commands", "options": {"command": "flutter build apk", "cwd": "libs/commons/dart/utilities"}, "outputs": ["{workspaceRoot}/libs/commons/dart/utilities/build"]}, "build-appbundle": {"executor": "nx:run-commands", "options": {"command": "flutter build appbundle", "cwd": "libs/commons/dart/utilities"}, "outputs": ["{workspaceRoot}/libs/commons/dart/utilities/build"]}, "build-bundle": {"executor": "nx:run-commands", "options": {"command": "flutter build bundle", "cwd": "libs/commons/dart/utilities"}, "outputs": ["{workspaceRoot}/libs/commons/dart/utilities/build"]}, "build-ios": {"executor": "nx:run-commands", "options": {"command": "flutter build ios", "cwd": "libs/commons/dart/utilities"}, "outputs": ["{workspaceRoot}/libs/commons/dart/utilities/build"]}, "build-ios-framework": {"executor": "nx:run-commands", "options": {"command": "flutter build ios-framework", "cwd": "libs/commons/dart/utilities"}, "outputs": ["{workspaceRoot}/libs/commons/dart/utilities/build"]}, "build-ipa": {"executor": "nx:run-commands", "options": {"command": "flutter build ipa", "cwd": "libs/commons/dart/utilities"}, "outputs": ["{workspaceRoot}/libs/commons/dart/utilities/build"]}}, "tags": []}