import 'storage_interface.dart';

/// In-memory implementation of the cache storage interface.
///
/// This implementation stores all data in memory using a Map.
/// It's fast but data is lost when the application terminates.
class InMemoryStorage<K, V> implements CacheStorage<K, V> {
  final Map<K, V> _storage = {};

  @override
  Future<bool> put(K key, V value) async {
    try {
      _storage[key] = value;
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<V?> get(K key) async {
    return _storage[key];
  }

  @override
  Future<bool> remove(K key) async {
    if (_storage.containsKey(key)) {
      _storage.remove(key);
      return true;
    }
    return false;
  }

  @override
  Future<bool> contains<PERSON><PERSON>(K key) async {
    return _storage.contains<PERSON><PERSON>(key);
  }

  @override
  Future<void> clear() async {
    _storage.clear();
  }

  @override
  Future<int> get size async {
    return _storage.length;
  }

  @override
  Future<List<K>> get keys async {
    return _storage.keys.toList();
  }

  @override
  Future<List<V>> get values async {
    return _storage.values.toList();
  }
}
