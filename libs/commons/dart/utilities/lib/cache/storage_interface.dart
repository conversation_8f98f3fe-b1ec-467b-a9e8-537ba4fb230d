/// Interface for cache storage implementations.
///
/// This interface defines the contract that all cache storage implementations
/// must follow, allowing for different storage backends (in-memory, file-based, etc.).
abstract class CacheStorage<K, V> {
  /// Stores a value with the given key.
  ///
  /// Returns true if the operation was successful, false otherwise.
  Future<bool> put(K key, V value);

  /// Retrieves a value by its key.
  ///
  /// Returns the value if found, null otherwise.
  Future<V?> get(K key);

  /// Removes a value by its key.
  ///
  /// Returns true if the value was removed, false if it didn't exist.
  Future<bool> remove(K key);

  /// Checks if a key exists in the storage.
  Future<bool> containsKey(K key);

  /// Clears all stored values.
  Future<void> clear();

  /// Returns the number of items currently stored.
  Future<int> get size;

  /// Returns all keys currently stored.
  Future<List<K>> get keys;

  /// Returns all values currently stored.
  Future<List<V>> get values;
}
