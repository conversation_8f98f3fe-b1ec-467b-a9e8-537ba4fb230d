import 'cache_entry.dart';
import 'eviction_algorithm.dart';
import 'storage_interface.dart';

/// A generic cache implementation supporting LFU and LRU eviction algorithms.
///
/// This cache automatically loads missing values from a backing store using
/// a provided callback function. It supports both Least Frequently Used (LFU)
/// and Least Recently Used (LRU) eviction policies.
class Cache<K, V> {
  final CacheStorage<K, CacheEntry<K, V>> _storage;
  final EvictionAlgorithm _algorithm;
  final int _maxSize;
  final Future<V> Function(K key) _loader;

  /// Creates a new cache instance.
  ///
  /// [storage] - The storage implementation to use for caching
  /// [algorithm] - The eviction algorithm to use (LFU or LRU)
  /// [maxSize] - Maximum number of items to store in the cache
  /// [loader] - Callback function to load values from backing store
  Cache({
    required CacheStorage<K, CacheEntry<K, V>> storage,
    required EvictionAlgorithm algorithm,
    required int maxSize,
    required Future<V> Function(K key) loader,
  })  : _storage = storage,
        _algorithm = algorithm,
        _maxSize = maxSize,
        _loader = loader;

  /// Retrieves a value from the cache by key.
  ///
  /// If the value is not in the cache, it will be loaded from the backing store
  /// using the provided loader function.
  Future<V> operator [](K key) async {
    // Check if the key exists in cache
    final entry = await _storage.get(key);

    if (entry != null) {
      // Update access metadata
      entry.updateAccess();
      await _storage.put(key, entry);
      return entry.value;
    }

    // Load from backing store
    final value = await _loader(key);

    // Create new cache entry
    final newEntry = CacheEntry<K, V>(
      key: key,
      value: value,
    );

    // Check if we need to evict an item
    final currentSize = await _storage.size;
    if (currentSize >= _maxSize) {
      await _evictItem();
    }

    // Store the new entry
    await _storage.put(key, newEntry);

    return value;
  }

  /// Stores a value in the cache with the given key.
  void operator []=(K key, V value) {
    final entry = CacheEntry<K, V>(
      key: key,
      value: value,
    );

    // Store immediately without eviction check for direct assignment
    _storage.put(key, entry);
  }

  /// Stores a value in the cache with the given key (async version with eviction).
  Future<void> put(K key, V value) async {
    final entry = CacheEntry<K, V>(
      key: key,
      value: value,
    );

    // Check if we need to evict an item
    final currentSize = await _storage.size;
    if (currentSize >= _maxSize && !await _storage.containsKey(key)) {
      await _evictItem();
    }

    await _storage.put(key, entry);
  }

  /// Removes a value from the cache.
  Future<bool> remove(K key) async {
    return await _storage.remove(key);
  }

  /// Checks if a key exists in the cache.
  Future<bool> containsKey(K key) async {
    return await _storage.containsKey(key);
  }

  /// Clears all items from the cache.
  Future<void> clear() async {
    await _storage.clear();
  }

  /// Returns the current number of items in the cache.
  Future<int> get size async {
    return await _storage.size;
  }

  /// Returns all keys currently in the cache.
  Future<List<K>> get keys async {
    return await _storage.keys;
  }

  /// Returns all values currently in the cache.
  Future<List<V>> get values async {
    final entries = await _storage.values;
    return entries.map((entry) => entry.value).toList();
  }

  /// Evicts an item based on the selected algorithm.
  Future<void> _evictItem() async {
    final entries = await _storage.values;
    if (entries.isEmpty) return;

    K? keyToEvict;

    switch (_algorithm) {
      case EvictionAlgorithm.lfu:
        keyToEvict = _findLFUKey(entries);
        break;
      case EvictionAlgorithm.lru:
        keyToEvict = _findLRUKey(entries);
        break;
    }

    if (keyToEvict != null) {
      await _storage.remove(keyToEvict);
    }
  }

  /// Finds the least frequently used key.
  K? _findLFUKey(List<CacheEntry<K, V>> entries) {
    if (entries.isEmpty) return null;

    CacheEntry<K, V>? lfuEntry;

    // TODO: Remove this once we have a better way to handle this
    // This is a workaround to avoid the issue with the int.maxFinite value
    // on the web platform
    //
    // const bool kIsWeb = bool.fromEnvironment('dart.library.js_util');
    // int minFrequency = kIsWeb ? 0x7FFFFFFFFFFFFFFF : 0x20000000000000; // 2^53
    int minFrequency = 0x20000000000000; // 2^53

    for (final entry in entries) {
      if (entry.frequency < minFrequency) {
        minFrequency = entry.frequency;
        lfuEntry = entry;
      }
    }

    return lfuEntry?.key;
  }

  /// Finds the least recently used key.
  K? _findLRUKey(List<CacheEntry<K, V>> entries) {
    if (entries.isEmpty) return null;

    CacheEntry<K, V>? lruEntry;
    DateTime? oldestAccess;

    for (final entry in entries) {
      if (oldestAccess == null || entry.lastAccessed.isBefore(oldestAccess)) {
        oldestAccess = entry.lastAccessed;
        lruEntry = entry;
      }
    }

    return lruEntry?.key;
  }
}
