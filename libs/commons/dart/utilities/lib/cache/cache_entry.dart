/// Represents an entry in the cache with metadata for eviction algorithms.
class CacheEntry<K, V> {
  final K key;
  final V value;
  int frequency; // For LFU
  DateTime lastAccessed; // For LRU

  CacheEntry({
    required this.key,
    required this.value,
    this.frequency = 1,
    DateTime? lastAccessed,
  }) : lastAccessed = lastAccessed ?? DateTime.now();

  /// Updates the access metadata for this entry.
  void updateAccess() {
    frequency++;
    lastAccessed = DateTime.now();
  }

  @override
  String toString() {
    return 'CacheEntry(key: $key, frequency: $frequency, lastAccessed: $lastAccessed)';
  }
}
