{"name": "ffi_helper", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/commons/dart/ffi_helper/src", "projectType": "library", "targets": {"analyze": {"executor": "nx:run-commands", "options": {"command": "flutter analyze", "cwd": "libs/commons/dart/ffi_helper"}}, "clean": {"executor": "nx:run-commands", "options": {"command": "flutter clean", "cwd": "libs/commons/dart/ffi_helper"}}, "format": {"executor": "nx:run-commands", "options": {"command": "dart format libs/commons/dart/ffi_helper/*", "cwd": "libs/commons/dart/ffi_helper"}}, "test": {"executor": "nx:run-commands", "options": {"command": "flutter test", "cwd": "libs/commons/dart/ffi_helper"}}, "doctor": {"executor": "nx:run-commands", "options": {"command": "flutter doctor", "cwd": "libs/commons/dart/ffi_helper"}}, "build-aar": {"executor": "nx:run-commands", "options": {"command": "flutter build aar", "cwd": "libs/commons/dart/ffi_helper"}, "outputs": ["{workspaceRoot}/libs/commons/dart/ffi_helper/build"]}, "build-apk": {"executor": "nx:run-commands", "options": {"command": "flutter build apk", "cwd": "libs/commons/dart/ffi_helper"}, "outputs": ["{workspaceRoot}/libs/commons/dart/ffi_helper/build"]}, "build-appbundle": {"executor": "nx:run-commands", "options": {"command": "flutter build appbundle", "cwd": "libs/commons/dart/ffi_helper"}, "outputs": ["{workspaceRoot}/libs/commons/dart/ffi_helper/build"]}, "build-bundle": {"executor": "nx:run-commands", "options": {"command": "flutter build bundle", "cwd": "libs/commons/dart/ffi_helper"}, "outputs": ["{workspaceRoot}/libs/commons/dart/ffi_helper/build"]}, "build-ios": {"executor": "nx:run-commands", "options": {"command": "flutter build ios", "cwd": "libs/commons/dart/ffi_helper"}, "outputs": ["{workspaceRoot}/libs/commons/dart/ffi_helper/build"]}, "build-ios-framework": {"executor": "nx:run-commands", "options": {"command": "flutter build ios-framework", "cwd": "libs/commons/dart/ffi_helper"}, "outputs": ["{workspaceRoot}/libs/commons/dart/ffi_helper/build"]}, "build-ipa": {"executor": "nx:run-commands", "options": {"command": "flutter build ipa", "cwd": "libs/commons/dart/ffi_helper"}, "outputs": ["{workspaceRoot}/libs/commons/dart/ffi_helper/build"]}}, "tags": []}