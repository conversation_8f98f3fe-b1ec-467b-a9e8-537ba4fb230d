
![Pub](https://img.shields.io/pub/v/bottom_navy_bar)

Abstract classes that handle boiler plate for Dart stubs and skeletons that wrap foreign interfaces implementations.

## Features

* Stubs for foreign function object instances and method calls used to invoke instance methods / functions.
* Skeletons for Dart implementations of foreign interfaces that can be used to call from the foreign environment to dart.
* Support for async invocations

## Getting started

Add the dependency in `pubspec.yaml`:

```yaml
dependencies:
  ...
  ffi_helper_ab: ^0.0.1
```
