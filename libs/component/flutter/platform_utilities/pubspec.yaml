name: platform_utilities_component
description: "MyCS platform common utility classes and functions"
version: 0.1.0
homepage: https://github.com/novassist-ai/novassist/libs/component/flutter/platform_utilities

environment:
  sdk: '>=3.3.0 <4.0.0'
  flutter: '>=3.3.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  plugin_platform_interface: ^2.0.2

  intl: ^0.20.2
  path_provider: ^2.1.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

flutter:
  generate: true

  plugin:
    platforms:
      android:
        package: ai.novassist.component.flutter.platform_utilities.platform_utilities_component
        pluginClass: PlatformUtilitiesComponentPlugin
      ios:
        pluginClass: PlatformUtilitiesComponentPlugin
      linux:
        pluginClass: PlatformUtilitiesComponentPlugin
      macos:
        pluginClass: PlatformUtilitiesComponentPlugin
      windows:
        pluginClass: PlatformUtilitiesComponentPluginCApi
      web:
        pluginClass: PlatformUtilitiesComponentWeb
        fileName: platform_utilities_component_web.dart
