import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/date_symbol_data_local.dart';

class AppPlatform {
  static String appDataPath = '';

  static var isWeb = kIsWeb;
  static var isMobile = !isWeb && (Platform.isAndroid || Platform.isIOS);
  static var isDesktop = !isWeb && !isMobile;
  static var isDarkMode = false;

  static Future<void> init() async {
    if (isWeb) {
      appDataPath = '';
    } else {
      appDataPath = (await getApplicationDocumentsDirectory()).path;
    }

    // initialize intl package localized date formats
    await initializeDateFormatting();
  }

  static initOnBuild(BuildContext context) {
    var brightness = MediaQuery.of(context).platformBrightness;
    isDarkMode = brightness == Brightness.dark;
  }
}
