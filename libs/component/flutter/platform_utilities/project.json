{"name": "platform_utilities_component", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/component/flutter/platform_utilities/src", "projectType": "library", "targets": {"analyze": {"executor": "nx:run-commands", "options": {"command": "flutter analyze", "cwd": "libs/component/flutter/platform_utilities"}}, "clean": {"executor": "nx:run-commands", "options": {"command": "flutter clean", "cwd": "libs/component/flutter/platform_utilities"}}, "format": {"executor": "nx:run-commands", "options": {"command": "dart format libs/component/flutter/platform_utilities/*", "cwd": "libs/component/flutter/platform_utilities"}}, "test": {"executor": "nx:run-commands", "options": {"command": "flutter test", "cwd": "libs/component/flutter/platform_utilities"}}, "doctor": {"executor": "nx:run-commands", "options": {"command": "flutter doctor", "cwd": "libs/component/flutter/platform_utilities"}}, "build-aar": {"executor": "nx:run-commands", "options": {"command": "flutter build aar", "cwd": "libs/component/flutter/platform_utilities"}, "outputs": ["{workspaceRoot}/libs/component/flutter/platform_utilities/build"]}, "build-apk": {"executor": "nx:run-commands", "options": {"command": "flutter build apk", "cwd": "libs/component/flutter/platform_utilities"}, "outputs": ["{workspaceRoot}/libs/component/flutter/platform_utilities/build"]}, "build-appbundle": {"executor": "nx:run-commands", "options": {"command": "flutter build appbundle", "cwd": "libs/component/flutter/platform_utilities"}, "outputs": ["{workspaceRoot}/libs/component/flutter/platform_utilities/build"]}, "build-bundle": {"executor": "nx:run-commands", "options": {"command": "flutter build bundle", "cwd": "libs/component/flutter/platform_utilities"}, "outputs": ["{workspaceRoot}/libs/component/flutter/platform_utilities/build"]}, "build-ios": {"executor": "nx:run-commands", "options": {"command": "flutter build ios", "cwd": "libs/component/flutter/platform_utilities"}, "outputs": ["{workspaceRoot}/libs/component/flutter/platform_utilities/build"]}, "build-ios-framework": {"executor": "nx:run-commands", "options": {"command": "flutter build ios-framework", "cwd": "libs/component/flutter/platform_utilities"}, "outputs": ["{workspaceRoot}/libs/component/flutter/platform_utilities/build"]}, "build-ipa": {"executor": "nx:run-commands", "options": {"command": "flutter build ipa", "cwd": "libs/component/flutter/platform_utilities"}, "outputs": ["{workspaceRoot}/libs/component/flutter/platform_utilities/build"]}}, "tags": []}