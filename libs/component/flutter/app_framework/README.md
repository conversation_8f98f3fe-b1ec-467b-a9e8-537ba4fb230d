# Application Framework Component

MyCS application framework component implements core foundational classes that provides framework for building:

* application features
* managing state and data services 
* handling of notifications 

## The Feature framework

The following sequence diagram outlines the life-cycle of a feature.

```mermaid
sequenceDiagram
    App Main->>FeatureRegistry: FeatureRegistry.intialize(&configLoaderFn)
    
    rect rgb(112,128,144)

    note right of App Main: feature registration and initialization
    activate App Main
        rect rgb(80,128,144)
        loop for each feature
            create participant Feature
            App Main->>Feature: feature=Feature()
            Note right of Feature: This is an implementation<br/>of the Feature class and it<br/>implements the feature's<br/> scope, state and anchors<br/>for UI hooks.
            App Main->>FeatureRegistry: register(feature)
            activate FeatureRegistry
            FeatureRegistry->>FeatureRegistry: config=load feature configuration
            FeatureRegistry->>Feature: initialize(config, registered features)
            activate Feature
                create participant Service
                Feature->>Service: add Service to Feature.blocs property
                Note right of Service: This is an implementation<br/>of the Service class which<br/>implements BloC<br/>functionality used by the<br/>feature..
                Feature-->>FeatureRegistry: 
            deactivate Feature
            FeatureRegistry->>FeatureRegistry: add feature to list of registered features
            FeatureRegistry-->>App Main: 
            deactivate FeatureRegistry
            end
        end
    deactivate App Main

    note right of App Main: set up localization for build context
    activate App Main
        rect rgb(80,128,144)        
        App Main->>FeatureRegistry: updateLocalizations(context)
        activate FeatureRegistry
        loop for each feature
            FeatureRegistry->>Feature: blocs
            loop for each bloc that is a Service
                FeatureRegistry->>Service: updateLocalizations(context)
            end
        end
        FeatureRegistry-->>App Main: 
        deactivate FeatureRegistry
        end
    deactivate App Main

    end

    rect rgb(112,128,144)
    note right of App Main: feature ux
    activate App Main
        create participant FeatureContainer
        App Main->>FeatureContainer: feature=FeatureContainer()
        Note right of FeatureContainer: This is an implementation<br/>of the FeatureContainer widget<br/> that implements UI hooks for<br/> features. It queries features for<br/> anchors that can then be<br/> rendered into the container.
        rect rgb(80,128,144)        
            activate FeatureContainer
            Note left of FeatureContainer: initState()
                FeatureContainer->>FeatureRegistry: anchors = getAnchors(hookName)
                activate FeatureRegistry
                loop for each feature
                    FeatureRegistry->>Feature: anchors
                    FeatureRegistry->>FeatureRegistry: enumerate each anchor<br/>that matches the hookName                
                end
                FeatureRegistry-->>FeatureContainer: anchors for hookName
                deactivate FeatureRegistry
            deactivate FeatureContainer
        end
        
        rect rgb(80,128,144)
            activate FeatureContainer
            Note left of FeatureContainer: build(context)
            Note left of FeatureContainer: FeatureContainer implementations'<br/>Widget build override should add<br/>the feature anchors to its UI
                FeatureContainer->>FeatureContainer: build(container, anchors)                
            deactivate FeatureContainer
        end
    deactivate App Main
    end
```

## Getting Started

### Install

Add the plugin as a dependency to you Flutter app's pubspec.yaml.

```
dependencies:
  .
  .
  app_framework_component: ^0.0.26
  .
  .
```

#### Business Logic Component

#### Implementing a Feature

#### Integrating Notifications
