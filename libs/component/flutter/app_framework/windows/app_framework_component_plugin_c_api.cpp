#include "include/app_framework_component/app_framework_component_plugin_c_api.h"

#include <flutter/plugin_registrar_windows.h>

#include "app_framework_component_plugin.h"

void AppFrameworkComponentPluginCApiRegisterWithRegistrar(
    FlutterDesktopPluginRegistrarRef registrar) {
  app_framework_component::AppFrameworkComponentPlugin::RegisterWithRegistrar(
      flutter::PluginRegistrarManager::GetInstance()
          ->GetRegistrar<flutter::PluginRegistrarWindows>(registrar));
}
