#ifndef FLUTTER_PLUGIN_APP_FRAMEWORK_COMPONENT_PLUGIN_H_
#define FLUTTER_PLUGIN_APP_FRAMEWORK_COMPONENT_PLUGIN_H_

#include <flutter/method_channel.h>
#include <flutter/plugin_registrar_windows.h>

#include <memory>

namespace app_framework_component {

class AppFrameworkComponentPlugin : public flutter::Plugin {
 public:
  static void RegisterWithRegistrar(flutter::PluginRegistrarWindows *registrar);

  AppFrameworkComponentPlugin();

  virtual ~AppFrameworkComponentPlugin();

  // Disallow copy and assign.
  AppFrameworkComponentPlugin(const AppFrameworkComponentPlugin&) = delete;
  AppFrameworkComponentPlugin& operator=(const AppFrameworkComponentPlugin&) = delete;

  // Called when a method is called on this plugin's channel from Dart.
  void HandleMethodCall(
      const flutter::MethodCall<flutter::EncodableValue> &method_call,
      std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result);
};

}  // namespace app_framework_component

#endif  // FLUTTER_PLUGIN_APP_FRAMEWORK_COMPONENT_PLUGIN_H_
