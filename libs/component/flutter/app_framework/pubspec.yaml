name: app_framework_component
description: "MyCS application feature and business logic componenet framework"
publish_to: 'none'
version: 0.1.0
homepage: https://github.com/novassist-ai/novassist/libs/component/flutter/app_framework

environment:
  sdk: '>=3.4.1 <4.0.0'
  flutter: '>=3.3.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  plugin_platform_interface: ^2.0.2

  web: ^1.1.1
  intl: ^0.20.2
  async: ^2.11.0
  meta: ^1.11.0
  bloc: ^9.0.0
  flutter_bloc: ^9.1.0
  equatable: ^2.0.5
  logging: ^1.2.0
  uuid: ^4.4.0
  hydrated_bloc: ^10.0.0
  flutter_secure_storage: ^9.2.2
  dart_jsonwebtoken: ^3.2.0

  utilities_ab:
    path: ../../../commons/dart/utilities

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

flutter:
  generate: true

  plugin:
    platforms:
      android:
        package: ai.novassist.component.flutter.app_framework.app_framework_component
        pluginClass: AppFrameworkComponentPlugin
      ios:
        pluginClass: AppFrameworkComponentPlugin
      linux:
        pluginClass: AppFrameworkComponentPlugin
      macos:
        pluginClass: AppFrameworkComponentPlugin
      windows:
        pluginClass: AppFrameworkComponentPluginCApi
      web:
        pluginClass: AppFrameworkComponentWeb
        fileName: app_framework_component_web.dart
