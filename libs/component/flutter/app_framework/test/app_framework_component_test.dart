import 'package:flutter_test/flutter_test.dart';
import 'package:app_framework_component/app_framework_component.dart';
import 'package:app_framework_component/app_framework_component_platform_interface.dart';
import 'package:app_framework_component/app_framework_component_method_channel.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

class MockAppFrameworkComponentPlatform
    with MockPlatformInterfaceMixin
    implements AppFrameworkComponentPlatform {

  @override
  Future<String?> getPlatformVersion() => Future.value('42');
}

void main() {
  final AppFrameworkComponentPlatform initialPlatform = AppFrameworkComponentPlatform.instance;

  test('$MethodChannelAppFrameworkComponent is the default instance', () {
    expect(initialPlatform, isInstanceOf<MethodChannelAppFrameworkComponent>());
  });

  test('getPlatformVersion', () async {
    AppFrameworkComponent appFrameworkComponentPlugin = AppFrameworkComponent();
    MockAppFrameworkComponentPlatform fakePlatform = MockAppFrameworkComponentPlatform();
    AppFrameworkComponentPlatform.instance = fakePlatform;

    expect(await appFrameworkComponentPlugin.getPlatformVersion(), '42');
  });
}
