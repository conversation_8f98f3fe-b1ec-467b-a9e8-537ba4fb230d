import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:app_framework_component/app_framework_component_method_channel.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  MethodChannelAppFrameworkComponent platform = MethodChannelAppFrameworkComponent();
  const MethodChannel channel = MethodChannel('app_framework_component');

  setUp(() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      channel,
      (MethodCall methodCall) async {
        return '42';
      },
    );
  });

  tearDown(() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(channel, null);
  });

  test('getPlatformVersion', () async {
    expect(await platform.getPlatformVersion(), '42');
  });
}
