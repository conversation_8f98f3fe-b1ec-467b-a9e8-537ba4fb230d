import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'app_framework_component_method_channel.dart';

abstract class AppFrameworkComponentPlatform extends PlatformInterface {
  /// Constructs a AppFrameworkComponentPlatform.
  AppFrameworkComponentPlatform() : super(token: _token);

  static final Object _token = Object();

  static AppFrameworkComponentPlatform _instance = MethodChannelAppFrameworkComponent();

  /// The default instance of [AppFrameworkComponentPlatform] to use.
  ///
  /// Defaults to [MethodChannelAppFrameworkComponent].
  static AppFrameworkComponentPlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [AppFrameworkComponentPlatform] when
  /// they register themselves.
  static set instance(AppFrameworkComponentPlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  Future<String?> getPlatformVersion() {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }
}
