import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'feature_registry.dart';

final class FeatureContainer<T> extends StatefulWidget {
  /// The [hookName] identifies the UI hook that
  /// this [FeatureContainer] is associated with.
  /// It is used to retrieve all the anchors of
  /// type [T] implemented by registered features.
  final String hookName;

  /// The [initializeAnchors] function is used to
  /// initialize the anchors before they saved
  /// in the [FeatureContainer] state.
  final InititializeAnchors<T>? initializeAnchors;

  /// The [builder] function is used to build the
  /// widget tree that will be displayed in the
  /// UI hook identified by [hookName].
  final FeatureContainerBuilder<T> builder;

  const FeatureContainer({
    super.key,
    required this.hookName,
    this.initializeAnchors,
    required this.builder,
  });

  @override
  @nonVirtual
  State<FeatureContainer<T>> createState() => _FeatureContainer<T>();
}

class _FeatureContainer<T> extends State<FeatureContainer<T>> {
  List<T> anchors = [];

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final anchors = FeatureRegistry.instance().getAnchors<T>(
      widget.hookName,
      context: context,
    );
    if (widget.initializeAnchors != null) {
      this.anchors = [
        ...widget.initializeAnchors!(
          context,
          anchors,
        ),
      ];
    } else {
      this.anchors = [
        ...anchors,
      ];
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.builder(context, anchors);
  }
}

typedef InititializeAnchors<T> = List<T> Function(
  BuildContext context,
  List<T> anchors,
);

typedef FeatureContainerBuilder<T> = Widget Function(
  BuildContext context,
  List<T> anchors,
);
