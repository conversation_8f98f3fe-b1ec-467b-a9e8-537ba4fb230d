import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logging/logging.dart' as logging;

import 'feature.dart';

final class FeatureRegistry {
  static final _logger = logging.Logger('FeatureRegistry');

  final FeatureConfigLoader _loader;
  final Map<String, Feature> _features = {};

  /// Returns the [Feature] with the given [name].
  T? getFeature<T extends Feature>(String name) {
    return _features[name] as T?;
  }

  /// Registers the provided [feature] with the registry.
  Future<void> registerFeature(Feature feature) async {
    late final FeatureConfig? config;
    try {
      if (feature.configDeserializer != null) {
        config = feature.configDeserializer!(
          await _loader(feature.name),
        );
      } else {
        config = null;
      }
    } catch (error, stackTrace) {
      _logger.severe(
        'Failed to load and parse configuration '
        'for feature "${feature.name}".',
        error,
        stackTrace,
      );
    }
    await feature.initialize(config, _features);
    _features[feature.name] = feature;
  }

  /// Returns a list of all the localizations delegates
  /// that are associated with the registered features.
  List<LocalizationsDelegate> getLocalizationsDelegates() {
    final delegates = <LocalizationsDelegate>[];
    for (final feature in _features.values) {
      delegates.addAll(feature.localizationsDelegates);
    }
    return delegates;
  }

  /// Returns a list of feature anchors of type [T] that
  /// are associated with the given [hookName].
  List<T> getAnchors<T>(
    String hookName, {
    BuildContext? context,
  }) {
    final anchors = <T>[];
    for (final feature in _features.values) {
      for (final anchor in feature.anchors(context)) {
        if (anchor.hookName == hookName && anchor is T) {
          anchors.add(anchor as T);
        }
      }
    }
    return anchors;
  }

  /// Sets the current [context] for all the registered
  /// feature services. This needs to be explicitly called
  /// when the context is available by the application.
  /// It is required to ensure that the services can
  /// access the localizations and other context specific
  /// information.
  void setCurrentContext(BuildContext context) {
    for (final feature in _features.values) {
      for (final service in feature.services.services) {
        service.setCurrentContext(context);
      }
    }
  }

  /// Provides all the registerd feature services to the
  /// widget tree.
  Widget scope(
    BuildContext context, {
    required Widget child,
  }) {
    assert(
      _features.isNotEmpty,
      'No features have been registered with the FeatureRegistry',
    );

    return _buildFeatureScope(
      context,
      _features.values.iterator,
      child,
    );
  }

  Widget _buildFeatureScope(
    BuildContext context,
    Iterator<Feature> features,
    Widget child,
  ) {
    if (features.moveNext()) {
      final feature = features.current;

      final providers = feature.services.providers;
      if (providers.isNotEmpty) {
        // Makes sure that the feature services are available
        // to all the children of the feature scope.
        return MultiBlocProvider(
          providers: providers,
          child: feature.scope(
            context,
            key: ObjectKey(feature.name),
            child: _buildFeatureScope(context, features, child),
          ),
        );
      } else {
        return _buildFeatureScope(context, features, child);
      }
    } else {
      return child;
    }
  }

  /// Disposes of all the registered features.
  void dispose() {
    for (var feature in _features.values) {
      feature.dispose();
    }
  }

  // FeatureRegistry singleton creation and access

  /// The singleton instance of the [FeatureRegistry].
  static FeatureRegistry? _instance;

  /// Factory that returns the singleton instance of
  /// the [FeatureRegistry].
  factory FeatureRegistry.instance() {
    assert(
      _instance != null,
      'FeatureRegistry has not been initialized',
    );
    return _instance!;
  }

  /// Initializes the [FeatureRegistry] with the
  /// provided [loader] function which is used to
  /// load the feature configuration.
  static intialize(FeatureConfigLoader loader) {
    _instance ??= FeatureRegistry._(loader);
  }

  /// Checks if a feature is registered
  static isRegistered(String name) {
    return _instance?.getFeature(name) != null;
  }

  FeatureRegistry._(this._loader);
}

/// A function that loads the feature configuration which
/// is a map of feature names to feature configurations.
/// Typical this configuration is loaded from a JSON file.
typedef FeatureConfigLoader = Future<Map<String, dynamic>> Function(
  String featureName,
);
