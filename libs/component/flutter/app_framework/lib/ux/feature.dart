import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../bloc/service.dart';
import '../notifications/app_notifications.dart';
import 'feature_container.dart';

abstract class Feature {
  /// The name that uniquely identifies this feature.
  String get name;

  /// The services that are associated with this feature.
  /// This includes BLoCs and other services that are
  /// required by the feature.
  final FeatureServices services = FeatureServices();

  /// The deserializer for the feature configuration.
  FeatureConfigDeserializer? get configDeserializer => null;

  /// The localizations delegates that are associated with
  /// this feature.
  List<LocalizationsDelegate> get localizationsDelegates => const [];

  /// Callback to initialize the [Feature]. This method
  /// is called when the feature is registered and should
  /// not be invoked directly. It should also be noted
  /// that the [registeredFeatures] map is a map of all
  /// the features that have been registered with the
  /// feature registry up to the point when this feature
  /// is registered.
  Future<void> initialize(
    FeatureConfig? config,
    Map<String, Feature> registeredFeatures,
  ) async {}

  /// The anchors that present the feature's UI for a
  /// specific UI hook. It should be noted some anchors
  /// may be retrieved before a BuildContext is
  /// available, in which case the [context] parameter
  /// will be null. In such cases, the anchor will defer
  /// the creation of the anchor UI via a [Builder] -
  /// refer to the documentation of the specific
  /// [FeatureAnchor] documentation for more
  /// information.
  List<FeatureAnchor> anchors(
    BuildContext? context,
  ) {
    return [];
  }

  /// Returns a widget that once inserted into the tree
  /// will provide the feature to all the children of the
  /// widget. The widget from the [scope] method must be
  /// the parent of any [FeatureContainer] within which
  /// this feature's UI will be surfaced. The same applies
  /// even if the feature service required is not UI related.
  Widget scope(
    BuildContext context, {
    Key? key,
    required Widget child,
  }) {
    return child;
  }

  /// Disposes of the feature. This method is called
  /// when the feature registry is disposed and should
  /// not be invoked directly.
  void dispose() {}
}

/// A function that deserializes the feature configuration
/// from a JSON map to a [FeatureConfig] instance.
typedef FeatureConfigDeserializer = FeatureConfig Function(
  Map<String, dynamic> json,
);

typedef FeatureBloCCreator<T extends BlocBase> = T Function(
  BuildContext context,
);

/// The configuration base for a feature. This should
/// be a class that is JSON serializable.
abstract class FeatureConfig {
  const FeatureConfig();
}

// An application's feature container will iterate
// over all feature hooks and pick the anchors that
// are relevant to that container
abstract class FeatureAnchor {
  String get hookName;

  const FeatureAnchor();
}

/// Container for feature services and BLoCs.
final class FeatureServices {
  final List<BlocProvider> providers = [];
  final List<Service> services = [];

  T get<T extends Service>() {
    final i = services.indexWhere((s) => s is T);
    if (i != -1) {
      return services[i] as T;
    }
    throw Exception('Service not found');
  }

  void add<T extends Service>(FeatureBloCCreator<T> creator) {
    providers.add(
      BlocProvider<T>(
        create: (BuildContext context) {
          final service = creator(context);

          final i = services.indexWhere((s) => s is T);
          if (i != -1) {
            services.removeAt(i);
          }
          services.add(service);
          AppNotifications.hub().register(service);

          return service;
        },
        lazy: false,
      ),
    );
  }
}
