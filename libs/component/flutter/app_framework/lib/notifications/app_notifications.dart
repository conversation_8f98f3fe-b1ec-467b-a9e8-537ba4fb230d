import 'dart:async';
import 'package:logging/logging.dart' as logging;

import 'package:app_framework_component/app_framework.dart' as app;

class AppNotifications {
  final logging.Logger _logger = logging.Logger('AppNotifications');

  static final AppNotifications _instance = AppNotifications._();

  List<app.Service> get services => _services;
  final _services = List<app.Service>.empty(growable: true);

  final Set<int> _queuedMessageHashes = {};

  factory AppNotifications.hub() {
    return _instance;
  }

  AppNotifications._();

  /// Register a service with the hub. If a service of the
  /// same type is already registered, it will be replaced.
  void register<T extends app.Service>(T service) {
    final i = _services.indexWhere((s) => s is T);
    if (i != -1) {
      _services.removeAt(i);
    }
    _services.add(service);
  }

  /// Subscribe to the message streams of all the services
  /// registered with the hub and adds the messages to a
  /// single [messageStream].
  List<StreamSubscription<app.State>> subscribeToMessageStreams(
    StreamController<app.Message> messageStream,
  ) {
    final subscriptions = <StreamSubscription<app.State>>[];
    for (var service in _services) {
      subscriptions.add(
        service.stream.listen(
          (state) {
            _logger.fine('Received state update: $state');

            for (var message in state.messages) {
              service.removeStateMessage(message);
              if (!_queuedMessageHashes.contains(message.hashCode)) {
                _queuedMessageHashes.add(message.hashCode);
                messageStream.add(message);
              }
            }
          },
        ),
      );
    }
    return subscriptions;
  }

  /// Once a message is handled, it should be removed from the
  /// [_queuedMessageHashes] set so that if the same message is
  /// received again, it can be added to the stream. This avoids
  /// notifying on duplicate messages.
  void messageHandled(app.Message message) {
    _queuedMessageHashes.remove(message.hashCode);
  }
}
