import 'dart:async';
import 'package:flutter/material.dart';

import 'package:app_framework_component/app_framework.dart' as app;

/// A scope that provides [StateMessageStream] for the subtree.
class StateMessageStream extends InheritedNotifier<StateMessageChangeNotifier> {
  /// Creates a [StateMessageStream] scope that
  /// handles access to the state message stream.
  const StateMessageStream({
    super.key,
    required super.notifier,
    required super.child,
  });

  /// Gets the most recent [app.Message] from the [StateMessageStream].
  static List<app.Message> of(BuildContext context) {
    return context
        .dependOnInheritedWidgetOfExactType<StateMessageStream>()!
        .notifier!
        .messages;
  }
}

/// A class that notifies a state change .
class StateMessageChangeNotifier extends ChangeNotifier {
  final List<app.Message> _messages = [];
  List<app.Message> get messages {
    final messages = List<app.Message>.from(_messages);
    _messages.clear();
    return messages;
  }

  StreamController<app.Message> get messageStream => _messageStream;
  final _messageStream = StreamController<app.Message>();

  StateMessageChangeNotifier() {
    _messageStream.stream.listen((message) {
      _messages.add(message);
      notifyListeners();
    });
  }

  @override
  void dispose() {
    _messageStream.close();
    super.dispose();
  }
}
