import 'dart:async';
import 'package:flutter/material.dart';

import 'package:app_framework_component/app_framework.dart' as app;

import 'app_notifications.dart';
import 'state_message_stream.dart';

class NotificationScope extends StatefulWidget {
  final Widget child;

  final Duration duration;

  final bool floatSnackBar;
  final double leftOffset;
  final double rightOffset;
  final double bottomOffset;
  final EdgeInsets snackBarMargin;

  const NotificationScope({
    super.key,
    required this.child,
    this.duration = const Duration(seconds: 8),
    this.floatSnackBar = false,
    this.leftOffset = 0.0,
    this.rightOffset = 0.0,
    this.bottomOffset = 0.0,
    this.snackBarMargin = const EdgeInsets.fromLTRB(15.0, 5.0, 15.0, 5.0),
  });

  @override
  State<NotificationScope> createState() => _NotificationScopeState();
}

class _NotificationScopeState extends State<NotificationScope> {
  final _messageChangeNotifier = StateMessageChangeNotifier();
  late final List<StreamSubscription<app.State>> _subscriptions;

  @override
  void initState() {
    _subscriptions = AppNotifications.hub().subscribeToMessageStreams(
      _messageChangeNotifier.messageStream,
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return StateMessageStream(
      notifier: _messageChangeNotifier,
      child: StatefulBuilder(builder: (context, _) {
        final messages = StateMessageStream.of(context);
        if (messages.isNotEmpty) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            for (var message in messages) {
              AppNotifications.hub().messageHandled(message);

              // Cannot show snackbar in build method so
              // we use addPostFrameCallback to show snackbar
              _handleStateMessages(
                context,
                message,
              );
            }
          });
        }
        return widget.child;
      }),
    );
  }

  @override
  void dispose() {
    for (var subscription in _subscriptions) {
      subscription.cancel();
    }
    _messageChangeNotifier.dispose();
    super.dispose();
  }

  void _handleStateMessages(
    BuildContext context,
    app.Message message,
  ) {
    final theme = Theme.of(context);

    final Text text;
    final Color backgroundColor;

    switch (message.type) {
      case app.MessageType.info:
        text = Text(
          message.message,
          style: theme.textTheme.bodyMedium!.copyWith(
            color: theme.colorScheme.onPrimaryContainer,
          ),
        );
        backgroundColor = theme.colorScheme.primaryContainer;
        break;
      case app.MessageType.alert:
        text = Text(
          message.message,
          style: theme.textTheme.bodyMedium!.copyWith(
            color: Colors.white,
          ),
        );
        backgroundColor = Colors.deepOrange;
        break;
      case app.MessageType.warning:
        text = Text(
          message.message,
          style: theme.textTheme.bodyMedium!.copyWith(
            color: Colors.black,
          ),
        );
        backgroundColor = Colors.amber;
        break;
      case app.MessageType.error:
        text = Text(
          message.message,
          style: theme.textTheme.bodyMedium!.copyWith(
            color: theme.colorScheme.onErrorContainer,
          ),
        );
        backgroundColor = theme.colorScheme.errorContainer;
        break;
    }

    final SnackBar snackBar;
    if (widget.floatSnackBar) {
      snackBar = SnackBar(
        content: text,
        backgroundColor: backgroundColor,
        margin: EdgeInsets.fromLTRB(
          widget.snackBarMargin.left + widget.leftOffset,
          widget.snackBarMargin.top + widget.bottomOffset,
          widget.snackBarMargin.right,
          widget.snackBarMargin.bottom + widget.bottomOffset,
        ),
        behavior: SnackBarBehavior.floating,
        duration: widget.duration,
        showCloseIcon: true,
      );
    } else {
      snackBar = SnackBar(
        content: text,
        backgroundColor: backgroundColor,
        duration: widget.duration,
        showCloseIcon: true,
      );
    }

    ScaffoldMessenger.of(context).showSnackBar(
      snackBar,
    );
  }
}
