import 'package:meta/meta.dart';
import 'package:dart_jsonwebtoken/dart_jsonwebtoken.dart';

abstract class AuthSession {
  /// The current logged in user's access token
  String? get accessToken;

  /// The current logged in user's user id
  String? get userId;

  /// Returns if the user is currently logged in
  @nonVirtual
  bool get loggedIn => accessToken != null;

  /// The current logged in user's JWT token
  @nonVirtual
  JWT? get jwtToken {
    if (accessToken == null) {
      return null;
    }

    final jwt = JWT.decode(accessToken!);

    return JWT(
      jwt.payload,
      header: jwt.header,
      audience: _parseAud(jwt.payload['aud']),
      issuer: jwt.payload['iss'],
      subject: jwt.payload['sub'],
      jwtId: jwt.payload['jti'],
    );
  }

  static Audience? _parseAud(dynamic val) {
    if (val is String) {
      return Audience.one(val);
    } else if (val is List<String>) {
      return Audience(val);
    } else {
      return null;
    }
  }
}
