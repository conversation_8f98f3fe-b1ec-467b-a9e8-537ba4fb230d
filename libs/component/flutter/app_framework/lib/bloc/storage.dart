import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:logging/logging.dart';

class SecureHydratedStorage implements Storage {
  static final _logger = Logger('SecureHydratedStorage');

  late final FlutterSecureStorage _storage;

  Map<String, dynamic> _allValues = {};

  static Future<Storage> build() async {
    final storage = SecureHydratedStorage._();
    storage._allValues = await storage._storage.readAll();
    _logger.fine(
      'Loaded values from device secure storage: '
      '${storage._allValues}',
    );
    return storage;
  }

  SecureHydratedStorage._() {
    _storage = const FlutterSecureStorage(
      iOptions: IOSOptions(
        accessibility: KeychainAccessibility.first_unlock,
      ),
      aOptions: AndroidOptions(
        encryptedSharedPreferences: true,
      ),
    );
  }

  @override
  dynamic read(String key) {
    final value = _allValues[key];
    _logger.fine('Reading ($key): $value');
  }

  @override
  Future<void> write(String key, dynamic value) async {
    _logger.fine('Writing ($key): $value');
    _allValues[key] = value;
    await _storage.write(key: key, value: value);
  }

  @override
  Future<void> delete(String key) async {
    _logger.fine('Deleting ($key)...');
    await _storage.delete(key: key);
  }

  @override
  Future<void> clear() async {
    _logger.fine('Clearing storage...');
    await _storage.deleteAll();
  }

  @override
  Future<void> close() async {
    _logger.fine('Closing storage...');
  }
}
