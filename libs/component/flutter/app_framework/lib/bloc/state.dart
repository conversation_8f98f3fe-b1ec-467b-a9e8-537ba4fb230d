import 'package:meta/meta.dart';
import 'package:equatable/equatable.dart';
import 'package:logging/logging.dart';
import 'package:uuid/uuid.dart';

/// Base class for all Bloc states of an application
/// that implements common states including error
/// handling and loading states.
abstract class State<T> extends Equatable {
  static final _logger = Logger('State');

  final Set<Message> messages;
  final Set<LoadingState> loadingStates;

  get hasError => messages.any((message) => message.isError);

  const State({
    required this.loadingStates,
    required this.messages,
  });

  @override
  @nonVirtual
  List<Object?> get props => List.from([
        loadingStates,
        messages,
      ])
        ..addAll(
          additionalProps,
        );

  @nonVirtual
  Message? peekMessage() => messages.isEmpty ? null : messages.first;

  @nonVirtual
  T addMessage(
    Message message, [
    Object? error,
    StackTrace? stackTrace,
  ]) {
    if (message.type == MessageType.error) {
      _logger.severe(
        'Error state alert: ${message.message}',
        error,
        stackTrace,
      );
    }
    return copyWith(
      messages: Set<Message>.from(messages)..add(message),
    );
  }

  @nonVirtual
  T removeMessage(
    Message message,
  ) {
    return copyWith(
      messages: Set<Message>.from(messages)..remove(message),
    );
  }

  @nonVirtual
  T clearMessages() {
    return copyWith(
      messages: {},
    );
  }

  @nonVirtual
  T startLoading(
    LoadingState loadingState, {
    clearMessages = true,
  }) {
    if (clearMessages) {
      return copyWith(
        messages: {},
        loadingStates: Set<LoadingState>.from(
          loadingStates,
        )..add(
            loadingState,
          ),
      );
    } else {
      return copyWith(
        loadingStates: Set<LoadingState>.from(
          loadingStates,
        )..add(
            loadingState,
          ),
      );
    }
  }

  @nonVirtual
  T endLoading(
    LoadingState loadingState,
  ) {
    return copyWith(
      loadingStates: Set<LoadingState>.from(
        loadingStates,
      )..remove(
          loadingState,
        ),
    );
  }

  @nonVirtual
  bool isLoading(List<LoadingState> loadingStates) {
    for (final loadingState in loadingStates) {
      if (this.loadingStates.contains(loadingState)) {
        return true;
      }
    }
    return false;
  }

  /// The concrete class must implement this method to
  /// return a list of additional properties that are
  /// specific to the concrete class.
  List<Object?> get additionalProps;

  /// The concrete class must implement this method to
  /// return a new instance of the state with the given
  /// parameters.
  T copyWith({
    Set<Message>? messages,
    Set<LoadingState>? loadingStates,
  });
}

/// A state that indicates that the application is
/// currently loading data.
class LoadingState extends Equatable {
  final String name;

  const LoadingState(this.name);

  @override
  @nonVirtual
  List<Object?> get props => [name];
}

/// A message that can be displayed to the user
/// to provide information or alert the user of
/// an issue.
class Message extends Equatable {
  final String id;

  final MessageType type;
  final String message;

  bool get isError => type == MessageType.error;

  static Message info(
    String message, [
    String? messageId,
  ]) =>
      Message._(
        id: _uuid.v4(),
        type: MessageType.info,
        message: message,
      );

  static Message alert(
    String message, [
    String? messageId,
  ]) =>
      Message._(
        id: _uuid.v4(),
        type: MessageType.alert,
        message: message,
      );

  static Message warning(
    String message, [
    String? messageId,
  ]) =>
      Message._(
        id: _uuid.v4(),
        type: MessageType.warning,
        message: message,
      );

  static Message error(
    String message, [
    String? messageId,
  ]) =>
      Message._(
        id: _uuid.v4(),
        type: MessageType.error,
        message: message,
      );

  const Message._({
    required this.id,
    required this.type,
    required this.message,
  });

  @override
  List<Object?> get props => [type, message];
}

/// The type of message that can be displayed to
/// the user.
enum MessageType {
  info,
  alert,
  warning,
  error,
}

var _uuid = const Uuid();
