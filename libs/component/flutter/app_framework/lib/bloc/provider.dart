import 'package:equatable/equatable.dart';
import 'package:utilities_ab/utilities.dart';

/// Base interface for a Bloc repository provider
abstract class RepositoryProvider {
  /// Streams events emitted by the provider
  /// to the consumers of the provider
  Stream<ProviderEvent> get providerEventStream;

  /// Initializes the repository provider. This method should
  /// setup persistent connections to the backend services and
  /// initialize any resources used by the backend services.
  Future<void> initialize();

  /// Disposes the repository provider. This method should
  /// close and release any resources used by the backend services.
  Future<void> dispose();
}

/// The [ProviderEvent] class is the base class for all events
/// for this provider. The events are used to communicate
/// between a [RepositoryProvider] and the consumer of the
/// [RepositoryProvider].
abstract class ProviderEvent {}

/// The [ErrorEventType] is a [ProviderEvent] used to communicate
/// errors between a [RepositoryProvider] and the consumer of the
/// [RepositoryProvider].
class ErrorEventType<T extends AppException> extends Equatable
    implements ProviderEvent {
  final T? error;
  const ErrorEventType(this.error);

  @override
  List<Object?> get props => [error];

  @override
  String toString() => '$runtimeType: $error';
}

/// The [NullProvider] class is a [RepositoryProvider] that does
/// not provide any backend services. This provider is used when
/// a feature does not require any backend services to function.
class NullProvider extends RepositoryProvider {
  @override
  Stream<ProviderEvent> get providerEventStream => const Stream.empty();

  @override
  Future<void> initialize() async {}

  @override
  Future<void> dispose() async {}
}
