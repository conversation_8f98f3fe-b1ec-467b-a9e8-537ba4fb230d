import 'dart:async';
import 'package:flutter/material.dart' show BuildContext;
import 'package:bloc/bloc.dart';

import '../ux/feature.dart';
import 'state.dart';
import 'provider.dart';

/// Base class for all services of an application. Each service
/// is responsible for providing backend data services for a
/// particular feature of the application. The services implements
/// state management using the Cubit library.
abstract class Service<StateT extends State,
    ProviderT extends RepositoryProvider> extends Cubit<StateT> {
  /// The repository provider for the service.
  final ProviderT? _provider;
  bool _initialized = false;

  /// Returns a provider that does
  /// not require initialization.
  ProviderT get provider {
    if (_provider != null) {
      return _provider;
    } else {
      throw StateError('The provider for this service is not set.');
    }
  }

  /// Returns and initialized instance
  /// of the provider for this service.
  Future<ProviderT> get intializedProvider async {
    if (_provider != null) {
      if (!_initialized) {
        await _provider.initialize();
        _initialized = true;
      }
      return _provider;
    } else {
      throw StateError('The provider for this service is not set.');
    }
  }

  Service({
    required StateT initialState,
    ProviderT? provider,
  })  : _provider = provider,
        super(initialState);

  /// This method is called by from the top level
  /// of the widget tree to initialize the service's
  /// localizations so it can be applied throughout
  /// the service regardless of whether if the
  /// context is available. This method should return
  /// either itself initialized with the context or
  /// a new instance of the [Service] initialized with
  /// the context. This is a convenience method that
  /// allows the service to be initialized with the
  /// context without having to create a new instance
  /// and is called by the [Feature] class when creating
  /// BLoC providers for the service.
  void setCurrentContext(BuildContext context) {}

  /// This method should be called when you want
  /// to wait for a particular loading state to
  /// start.
  Future<void> ensureLoadingStarted(
    LoadingState loadingState,
  ) async {
    await stream.firstWhere(
      (state) => state.isLoading([loadingState]),
    );
  }

  /// This method should be called when you want
  /// to wait for a particular loading state to
  /// finish.
  Future<void> ensureLoadingFinished(
    LoadingState loadingState,
  ) async {
    await stream.firstWhere(
      (state) => !state.isLoading([loadingState]),
    );
  }

  /// Removes the given [message] from the state
  void addStateMessage(
    Message message, [
    Object? error,
    StackTrace? stackTrace,
  ]) {
    final oldState = state;
    final newState = oldState.addMessage(
      message,
      error,
      stackTrace,
    );
    if (oldState != newState) {
      emit(newState);
    }
  }

  /// Removes the given [message] from the state
  void removeStateMessage(
    Message message,
  ) {
    final oldState = state;
    final newState = state.removeMessage(message);
    if (oldState != newState) {
      emit(newState);
    }
  }
}
