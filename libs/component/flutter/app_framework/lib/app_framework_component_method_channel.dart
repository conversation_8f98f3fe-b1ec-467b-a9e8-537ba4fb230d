import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'app_framework_component_platform_interface.dart';

/// An implementation of [AppFrameworkComponentPlatform] that uses method channels.
class MethodChannelAppFrameworkComponent extends AppFrameworkComponentPlatform {
  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('app_framework_component');

  @override
  Future<String?> getPlatformVersion() async {
    final version = await methodChannel.invokeMethod<String>('getPlatformVersion');
    return version;
  }
}
