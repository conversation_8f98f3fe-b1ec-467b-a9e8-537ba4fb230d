import 'package:flutter/material.dart';

import 'folder_tab.dart';
import 'display_area_size.dart';

class TabbedFolders extends StatefulWidget {
  final List<FolderTab> tabs;

  final double selectedHeight;
  final double unselectedHeight;
  final double indicatorHeight;
  final double topPadding;
  final double verticalTabPadding;

  final TabAxisAlignment alignment;

  final bool showContentBorder;
  final bool disableContentAnimation;

  final Function(TabController)? onInit;
  final Function(int)? onTabChanged;

  const TabbedFolders({
    super.key,
    required this.tabs,
    this.selectedHeight = 34.0,
    this.unselectedHeight = 26.0,
    this.indicatorHeight = 4.0,
    this.topPadding = 0.0,
    this.verticalTabPadding = 2.0,
    this.alignment = TabAxisAlignment.start,
    this.showContentBorder = true,
    this.disableContentAnimation = false,
    this.onInit,
    this.onTabChanged,
  });

  @override
  State<TabbedFolders> createState() => _TabbedFoldersState();
}

class _TabbedFoldersState extends State<TabbedFolders>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  PageController? _pageController;

  @override
  void initState() {
    _tabController = TabController(
      vsync: this,
      length: widget.tabs.length,
    );
    if (widget.onInit != null) {
      widget.onInit!(_tabController);
    }
    if (widget.onTabChanged != null) {
      _tabController.addListener(() {
        widget.onTabChanged!(_tabController.index);
      });
    }

    if (widget.disableContentAnimation) {
      _pageController = PageController();
      _tabController.addListener(() {
        _pageController?.jumpToPage(_tabController.index);
      });
    }

    super.initState();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _pageController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final ThemeData theme = Theme.of(context);

    return LayoutBuilder(builder: (context, constraints) {
      double height = constraints.maxHeight -
          widget.topPadding -
          widget.selectedHeight -
          widget.verticalTabPadding -
          widget.indicatorHeight +
          2;

      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          FolderTabBar(
            alignment: widget.alignment,
            selectedHeight: widget.selectedHeight,
            unselectedHeight: widget.unselectedHeight,
            indicatorHeight: widget.indicatorHeight,
            topPadding: widget.topPadding,
            verticalTabPadding: widget.verticalTabPadding,
            tabs: widget.tabs.map((tab) => tab._getTabItem(context)).toList(),
            controller: _tabController,
          ),
          SizedBox(
            height: height,
            child: widget.showContentBorder
                ? Container(
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surface,
                      border: Border.all(
                        color: theme.colorScheme.primary,
                      ),
                      borderRadius: const BorderRadius.vertical(
                        bottom: Radius.circular(5),
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: const BorderRadius.vertical(
                        bottom: Radius.circular(5),
                      ),
                      child: _buildContentTabs(
                        constraints,
                        height,
                      ),
                    ),
                  )
                : _buildContentTabs(
                    constraints,
                    height,
                  ),
          )
        ],
      );
    });
  }

  Widget _buildContentTabs(
    BoxConstraints constraints,
    double height,
  ) {
    if (widget.disableContentAnimation) {
      return PageView(
        controller: _pageController,
        physics: const NeverScrollableScrollPhysics(),
        children: widget.tabs
            .map(
              (tab) => DisplayAreaSizeProvider(
                minWidth: constraints.minWidth,
                maxWidth: constraints.maxWidth,
                minHeight: height,
                maxHeight: height,
                child: tab.content!,
              ),
            )
            .toList(),
      );
    } else {
      return TabBarView(
          controller: _tabController,
          children: widget.tabs
              .map(
                (tab) => DisplayAreaSizeProvider(
                  minWidth: constraints.minWidth,
                  maxWidth: constraints.maxWidth,
                  minHeight: height,
                  maxHeight: height,
                  child: tab.content!,
                ),
              )
              .toList());
    }
  }
}

class FolderTab {
  final String title;
  final Widget? icon;
  final IconData? iconData;
  final double spaceBetween;

  final Color? color;
  final Color? unselectedColor;
  final Color? labelColor;
  final Color? unselectedLabelColor;
  final TextStyle? labelStyle;
  final TextStyle? unselectedLabelStyle;

  final Widget? content;

  const FolderTab({
    required this.title,
    this.icon,
    this.iconData,
    this.spaceBetween = 8,
    this.color,
    this.unselectedColor,
    this.labelColor,
    this.unselectedLabelColor,
    this.labelStyle,
    this.unselectedLabelStyle,
    required this.content,
  });

  TabItem _getTabItem(BuildContext context) {
    final ThemeData theme = Theme.of(context);

    return TabItem(
      title: Row(children: [
        icon ?? //
            (iconData != null //
                ? Icon(iconData)
                : const SizedBox.shrink()),
        SizedBox(width: spaceBetween),
        Text(title)
      ]),
      color: color ?? theme.colorScheme.primary,
      unselectedColor: unselectedColor ?? //
          theme.colorScheme.primary.withValues(alpha: 0.6),
      labelColor: labelColor ?? //
          theme.colorScheme.onPrimary,
      unselectedLabelColor: unselectedColor ?? //
          theme.colorScheme.inversePrimary,
      labelStyle: labelStyle ?? //
          const TextStyle(
            fontWeight: FontWeight.bold,
          ),
      unselectedLabelStyle: unselectedLabelStyle,
    );
  }
}
