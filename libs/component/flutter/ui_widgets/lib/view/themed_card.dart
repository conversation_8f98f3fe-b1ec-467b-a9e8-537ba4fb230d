import 'package:flutter/material.dart';

class ThemedCard extends StatefulWidget with WidgetsBindingObserver {
  final Widget child;
  final CardTheme lightTheme;
  final CardTheme darkTheme;
  final ThemedCardType type;
  final bool borderOnForeground;
  final bool semanticContainer;

  const ThemedCard({
    super.key,
    required this.child,
    required this.lightTheme,
    required this.darkTheme,
    this.borderOnForeground = true,
    this.semanticContainer = true,
    this.type = ThemedCardType.normal,
  });

  @override
  State<ThemedCard> createState() => _ThemedCardState();
}

class _ThemedCardState extends State<ThemedCard> {
  bool isDarkMode = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    isDarkMode = Theme.of(context).brightness == Brightness.dark;
  }

  @override
  Widget build(BuildContext context) {
    final theme = isDarkMode ? widget.darkTheme : widget.lightTheme;

    switch (widget.type) {
      case ThemedCardType.filled:
        return Card.filled(
          clipBehavior: theme.clipBehavior,
          color: theme.color,
          shadowColor: theme.shadowColor,
          surfaceTintColor: theme.surfaceTintColor,
          elevation: theme.elevation,
          margin: theme.margin,
          shape: theme.shape,
          borderOnForeground: widget.borderOnForeground,
          semanticContainer: widget.semanticContainer,
          child: widget.child,
        );
      case ThemedCardType.outline:
        return Card.outlined(
          clipBehavior: theme.clipBehavior,
          color: theme.color,
          shadowColor: theme.shadowColor,
          surfaceTintColor: theme.surfaceTintColor,
          elevation: theme.elevation,
          margin: theme.margin,
          shape: theme.shape,
          borderOnForeground: widget.borderOnForeground,
          semanticContainer: widget.semanticContainer,
          child: widget.child,
        );
      default:
        return Card(
          clipBehavior: theme.clipBehavior,
          color: theme.color,
          shadowColor: theme.shadowColor,
          surfaceTintColor: theme.surfaceTintColor,
          elevation: theme.elevation,
          margin: theme.margin,
          shape: theme.shape,
          borderOnForeground: widget.borderOnForeground,
          semanticContainer: widget.semanticContainer,
          child: widget.child,
        );
    }
  }
}

enum ThemedCardType {
  normal,
  filled,
  outline,
}
