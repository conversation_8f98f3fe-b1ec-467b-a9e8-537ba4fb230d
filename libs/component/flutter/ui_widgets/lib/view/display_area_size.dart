import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

// Provides the display area size at the current position
// of this widger to the children in the widget tree that
// need to be sized around the parent's size.
class DisplayAreaSizeProvider extends StatelessWidget {
  final Widget child;

  final DisplayAreaSize displayAreaSize;

  DisplayAreaSizeProvider({
    super.key,
    double minWidth = 0.0,
    double maxWidth = double.infinity,
    double minHeight = 0.0,
    double maxHeight = double.infinity,
    required this.child,
  }) : displayAreaSize = DisplayAreaSize._(
          minWidth,
          maxWidth,
          minHeight,
          maxHeight,
        );

  @override
  Widget build(Object context) {
    return ChangeNotifierProvider<DisplayAreaSize>.value(
      value: displayAreaSize,
      child: LayoutBuilder(
        builder: (context, constraints) {
          // Update the display area size with that of the parent
          // and notify listeners to repaint their children
          final notify = displayAreaSize._updateSize(constraints);
          if (notify) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              displayAreaSize._notify();
            });
          }
          return child;
        },
      ),
    );
  }
}

// This class is used to provide the size of the parent widget
// to children that need to be size based on the parent's size
// but do not have access to the parent's size directly as
// widgets in between may have been sized dynamically.
class DisplayAreaSize extends ChangeNotifier {
  final double minWidth;
  final double maxWidth;
  final double minHeight;
  final double maxHeight;

  double width;
  double height;

  DisplayAreaSize._(
    this.minWidth,
    this.maxWidth,
    this.minHeight,
    this.maxHeight,
  )   : width = minWidth,
        height = minHeight;

  bool _updateSize(BoxConstraints constraints) {
    assert(
      constraints.maxWidth != double.infinity,
      'The parent widget must have a finite width. To resolve '
      'this you may need to move the DisplayAreaSizeProvider '
      'to a higher level in the widget tree.',
    );
    assert(
      constraints.maxHeight != double.infinity,
      'The parent widget must have a finite height. To resolve '
      'this you may need to move the DisplayAreaSizeProvider '
      'to a higher level in the widget tree.',
    );

    bool notify = false;
    if (constraints.maxWidth != width) {
      width = min(maxWidth, max(minWidth, constraints.maxWidth));
      notify = true;
    }
    if (constraints.maxHeight != height) {
      height = min(maxHeight, max(minHeight, constraints.maxHeight));
      notify = true;
    }
    return notify;
  }

  void _notify() {
    notifyListeners();
  }
}
