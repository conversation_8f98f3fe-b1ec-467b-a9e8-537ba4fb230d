import 'package:flutter/material.dart';

class GroupBox extends StatelessWidget {
  final String? title;
  final Widget child;

  final EdgeInsets margin;
  final EdgeInsets padding;

  final TextStyle? titleTextStyle;
  final Color? titleTextColor;
  final Color? borderColor;
  final Color? backgroundColor;

  const GroupBox({
    super.key,
    this.title,
    required this.child,
    this.margin = const EdgeInsets.all(8.0),
    this.padding = const EdgeInsets.all(8.0),
    this.titleTextStyle,
    this.titleTextColor,
    this.borderColor,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Stack(
      children: [
        Container(
          color: backgroundColor ?? theme.colorScheme.surface,
          padding: margin,
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(
                color: borderColor ??
                    theme.colorScheme.primaryFixedDim.withValues(alpha: 0.2),
                width: 2,
              ),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Padding(
              padding: padding,
              child: child,
            ),
          ),
        ),
        if (title != null)
          Positioned(
            left: margin.left + 8,
            top: margin.top - 8,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 4),
              color: backgroundColor ?? theme.colorScheme.surface,
              child: Text(
                title!,
                style: titleTextStyle ??
                    TextStyle(
                      color:
                          titleTextColor ?? theme.colorScheme.primaryFixedDim,
                      fontWeight: FontWeight.w500,
                    ),
              ),
            ),
          ),
      ],
    );
  }
}
