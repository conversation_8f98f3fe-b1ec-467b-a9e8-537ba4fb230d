import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

MarkdownStyleSheet createMarkDownStyleSheet(
  BuildContext context, {
  Color? color,
  double? fontSize,
  double? lineHeight,
}) {
  final theme = Theme.of(context);
  return MarkdownStyleSheet.fromTheme(
    theme,
  ).copyWith(
    p: theme.textTheme.bodyMedium?.copyWith(
      fontSize: fontSize,
      height: lineHeight ?? 1.5,
      color: color ?? theme.colorScheme.secondary,
    ),
  );
}
