import 'package:flutter/material.dart';

import '../l10n/l10n.dart';
import 'validator.dart';

class ConfirmPasswordValidator extends Validator {
  @override
  bool get isValid => _isValid;
  bool _isValid = true;

  @override
  String? get shortErrorMessage => _shortErrorMessage;
  String? _shortErrorMessage;

  @override
  String? get longErrorMessage => _longErrorMessage;
  String? _longErrorMessage;

  String value;

  ConfirmPasswordValidator({
    required this.value,
  });

  @override
  String? validate(
    BuildContext context,
    String fieldName,
    String? value,
  ) {
    if (value != null && value.isNotEmpty) {
      _isValid = value == this.value;
    } else {
      _isValid = true;
    }
    if (!_isValid) {
      final l10n = context.l10n;
      _shortErrorMessage = l10n.confirmPasswordShortErrorMessage;
      _longErrorMessage = l10n.confirmPasswordLongErrorMessage;
    } else {
      _shortErrorMessage = null;
      _longErrorMessage = null;
    }
    return _shortErrorMessage;
  }
}
