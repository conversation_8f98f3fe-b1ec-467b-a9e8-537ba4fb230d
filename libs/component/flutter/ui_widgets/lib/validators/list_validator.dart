import 'package:flutter/material.dart';

import '../l10n/l10n.dart';
import 'validator.dart';

class ListValidator extends Validator {
  @override
  bool get isValid => _isValid;
  bool _isValid = true;

  @override
  String? get shortErrorMessage => _shortErrorMessage;
  String? _shortErrorMessage;

  @override
  String? get longErrorMessage => _longErrorMessage;
  String? _longErrorMessage;

  List<String> items;

  ListValidator({
    required this.items,
  });

  @override
  String? validate(
    BuildContext context,
    String fieldName,
    String? value,
  ) {
    _longErrorMessage = null;
    _shortErrorMessage = null;

    if (value != null && value.isNotEmpty) {
      final l10n = context.l10n;

      if (!items.contains(value)) {
        _shortErrorMessage = l10n.invalidMessage;
        _longErrorMessage = l10n.invalidList;
      }
    }

    _isValid = _shortErrorMessage == null;
    return _shortErrorMessage;
  }
}
