import 'package:flutter/material.dart';

import '../l10n/l10n.dart';
import 'validator.dart';

class MinLengthValidator extends Validator {
  @override
  bool get isValid => _isValid;
  bool _isValid = true;

  @override
  String? get shortErrorMessage => _shortErrorMessage;
  String? _shortErrorMessage;

  @override
  String? get longErrorMessage => _longErrorMessage;
  String? _longErrorMessage;

  int length;

  MinLengthValidator({
    required this.length,
  });

  @override
  String? validate(
    BuildContext context,
    String fieldName,
    String? value,
  ) {
    _longErrorMessage = null;
    _shortErrorMessage = null;

    if (value != null && value.isNotEmpty) {
      final l10n = context.l10n;

      _isValid = value.length >= length;
      if (!_isValid) {
        _shortErrorMessage = l10n.tooShortMessage;
        _longErrorMessage = l10n.tooShortErrorMessage(fieldName, length);
      }
    } else {
      _isValid = true;
    }
    return _shortErrorMessage;
  }
}
