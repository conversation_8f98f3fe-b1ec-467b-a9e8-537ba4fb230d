import 'package:flutter/material.dart';

/// Abstract class for input validation
abstract class Validator {
  /// Return the state of the validator
  ///  after the most recent validation.
  bool get isValid;

  /// Return a short error message
  /// if the input is invalid
  String? get shortErrorMessage;

  /// Return a long error message
  /// if the input is invalid
  String? get longErrorMessage;

  /// Function to validate the form input. It takes a string
  /// and returns an [String] error message if the input is
  /// invalid or null if the input is valid.
  String? validate(
    BuildContext context,
    String fieldName,
    String? value,
  );
}
