import 'package:flutter/material.dart';

import 'validator.dart';

class ChainedValidator extends Validator {
  @override
  bool get isValid => _isValid;
  bool _isValid = true;

  @override
  String? get shortErrorMessage => _shortErrorMessage;
  String? _shortErrorMessage;

  @override
  String? get longErrorMessage => _longErrorMessage;
  String? _longErrorMessage;

  final List<Validator> _validators;
  final ValidationCompleted? onValidationCompleted;

  ChainedValidator({
    required List<Validator> validators,
    this.onValidationCompleted,
  }) : _validators = validators;

  @override
  String? validate(
    BuildContext context,
    String fieldName,
    String? value,
  ) {
    _isValid = true;
    _shortErrorMessage = null;
    _longErrorMessage = null;

    for (final validator in _validators) {
      final error = validator.validate(context, fieldName, value);
      if (error != null) {
        _isValid = validator.isValid;
        _shortErrorMessage = validator.shortErrorMessage;
        _longErrorMessage = validator.longErrorMessage;
        break;
      }
    }
    if (onValidationCompleted != null) {
      onValidationCompleted!(
        fieldName: fieldName,
        value: value,
        isValid: _isValid,
        shortErrorMessage: _shortErrorMessage,
        longErrorMessage: _longErrorMessage,
      );
    }

    return _shortErrorMessage;
  }
}

typedef ValidationCompleted = void Function({
  required String fieldName,
  required bool isValid,
  String? value,
  String? shortErrorMessage,
  String? longErrorMessage,
});
