import 'package:flutter/material.dart';

import 'validator.dart';
import '../l10n/l10n.dart';

class EmailAddressValidator extends Validator {
  @override
  bool get isValid => _isValid;
  bool _isValid = true;

  @override
  String? get shortErrorMessage => _shortErrorMessage;
  String? _shortErrorMessage;

  @override
  String? get longErrorMessage => _longErrorMessage;
  String? _longErrorMessage;

  EmailAddressValidator();

  @override
  String? validate(
    BuildContext context,
    String fieldName,
    String? value,
  ) {
    _longErrorMessage = null;
    _shortErrorMessage = null;

    if (value != null && value.isNotEmpty) {
      final l10n = context.l10n;

      final validEmailAddress = RegExp(
          r'''^(([^<>()\[\]\\.,;:\s@']+(\.[^<>()\[\]\\.,;:\s@']+)*)|('.+'))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$''');

      _isValid = validEmailAddress.hasMatch(value);
      if (!_isValid) {
        _shortErrorMessage = l10n.invalidMessage;
        _longErrorMessage = l10n.invalidEmailErrorMessage(value);
      }
    } else {
      _isValid = true;
    }
    return _shortErrorMessage;
  }
}
