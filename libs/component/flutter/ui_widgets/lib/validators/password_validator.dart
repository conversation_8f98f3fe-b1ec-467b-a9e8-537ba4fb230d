import 'package:flutter/material.dart';

import '../l10n/l10n.dart';
import 'validator.dart';

class PasswordValidator extends Validator {
  @override
  bool get isValid => _isValid;
  bool _isValid = false;

  @override
  String? get shortErrorMessage => _shortErrorMessage;
  String? _shortErrorMessage;

  @override
  String? get longErrorMessage => _longErrorMessage;
  String? _longErrorMessage;

  @override
  String? validate(
    BuildContext context,
    String fieldName,
    String? value,
  ) {
    _isValid = true;
    _longErrorMessage = null;
    _shortErrorMessage = null;

    if (value != null && value.isNotEmpty) {
      final l10n = context.l10n;

      if (value.length < 8) {
        _shortErrorMessage = l10n.tooShortMessage;
        _longErrorMessage = l10n.passwordTooShortErrorMessage;
        return _shortErrorMessage;
      }
      final p1 = RegExp(
          r'^(?=.*[-_=+()!@#$%^&*{}<>|";:,./?\(\)\[\]\\])[-_=+()!@#$%^&*{}<>|";:,./?\(\)\[\]\\a-zA-Z0-9]{8,}$');
      _isValid = p1.hasMatch(value);
      if (!_isValid) {
        _shortErrorMessage = l10n.invalidMessage;
        _longErrorMessage = l10n.passwordSpecialCharErrorMessage;
        return _shortErrorMessage;
      }
      final p2 = RegExp(
          r'^(?=.*[a-z])[-_=+()!@#$%^&*{}<>|";:,./?\(\)\[\]\\a-zA-Z0-9]{8,}$');
      _isValid = p2.hasMatch(value);
      if (!_isValid) {
        _shortErrorMessage = l10n.invalidMessage;
        _longErrorMessage = l10n.passwordLowercaseCharErrorMessage;
        return _shortErrorMessage;
      }
      final p3 = RegExp(
          r'^(?=.*[A-Z])[-_=+()!@#$%^&*{}<>|";:,./?\(\)\[\]\\a-zA-Z0-9]{8,}$');
      _isValid = p3.hasMatch(value);
      if (!_isValid) {
        _shortErrorMessage = l10n.invalidMessage;
        _longErrorMessage = l10n.passwordUppercaseCharErrorMessage;
        return _shortErrorMessage;
      }
      final p4 = RegExp(
          r'^(?=.*[0-9])[-_=+()!@#$%^&*{}<>|";:,./?\(\)\[\]\\a-zA-Z0-9]{8,}$');
      _isValid = p4.hasMatch(value);
      if (!_isValid) {
        _shortErrorMessage = l10n.invalidMessage;
        _longErrorMessage = l10n.passwordNumberErrorMessage;
        return _shortErrorMessage;
      }
    }
    return null;
  }
}
