import 'package:flutter/material.dart';

import 'validator.dart';

class CallbackValidator extends Validator {
  @override
  bool get isValid => _isValid;
  bool _isValid = true;

  @override
  String? get shortErrorMessage => _isValid ? null : _shortErrorMessage;
  final String? _shortErrorMessage;

  @override
  String? get longErrorMessage => _isValid ? null : _longErrorMessage;
  final String? _longErrorMessage;

  bool Function(String?) callback;

  CallbackValidator({
    required this.callback,
    required String longErrorMessage,
    required String shortErrorMessage,
  })  : _longErrorMessage = longErrorMessage,
        _shortErrorMessage = shortErrorMessage;

  @override
  String? validate(
    BuildContext context,
    String fieldName,
    String? value,
  ) {
    if (value != null && value.isNotEmpty) {
      _isValid = callback(value);
    } else {
      _isValid = true;
    }
    return _shortErrorMessage;
  }
}
