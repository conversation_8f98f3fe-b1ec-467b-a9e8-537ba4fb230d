import 'package:flutter/material.dart';

import 'validator.dart';

class ChainedValidator extends Validator {
  @override
  bool get isValid => _isValid;
  bool _isValid = true;

  @override
  String? get shortErrorMessage => _shortErrorMessage;
  String? _shortErrorMessage;

  @override
  String? get longErrorMessage => _longErrorMessage;
  String? _longErrorMessage;

  final List<Validator> _validators;

  ChainedValidator(
    this._validators,
  );

  @override
  String? validate(
    BuildContext context,
    String fieldName,
    String? value,
  ) {
    _isValid = true;
    _shortErrorMessage = null;
    _longErrorMessage = null;

    for (final validator in _validators) {
      final error = validator.validate(context, fieldName, value);
      if (error != null) {
        _isValid = validator.isValid;
        _shortErrorMessage = validator.shortErrorMessage;
        _longErrorMessage = validator.longErrorMessage;
        return error;
      }
    }

    return null;
  }
}
