import 'package:flutter/material.dart';

import '../l10n/l10n.dart';
import 'validator.dart';

class RegexValidator extends Validator {
  @override
  bool get isValid => _isValid;
  bool _isValid = true;

  @override
  String? get shortErrorMessage => _shortErrorMessage;
  String? _shortErrorMessage;

  @override
  String? get longErrorMessage => _longErrorMessage;
  String? _longErrorMessage;

  final RegExp regex;
  final List<String> examples;

  RegexValidator({
    required this.regex,
    this.examples = const [],
  });

  @override
  String? validate(
    BuildContext context,
    String fieldName,
    String? value,
  ) {
    _longErrorMessage = null;
    _shortErrorMessage = null;

    if (value != null && value.isNotEmpty) {
      final l10n = context.l10n;

      _isValid = regex.hasMatch(value);
      if (!_isValid) {
        _shortErrorMessage = l10n.invalidMessage;
        if (examples.isEmpty) {
          _longErrorMessage = l10n.invalidPattern;
        } else {
          _longErrorMessage = l10n.invalidPatternWithExamples(
            examples.join(', '),
          );
        }
      }
    } else {
      _isValid = true;
    }
    return _shortErrorMessage;
  }
}
