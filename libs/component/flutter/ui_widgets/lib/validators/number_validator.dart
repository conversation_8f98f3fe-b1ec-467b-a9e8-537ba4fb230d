import 'package:flutter/material.dart';

import '../l10n/l10n.dart';
import 'validator.dart';

class NumberValidator extends Validator {
  @override
  bool get isValid => _isValid;
  bool _isValid = true;

  @override
  String? get shortErrorMessage => _shortErrorMessage;
  String? _shortErrorMessage;

  @override
  String? get longErrorMessage => _longErrorMessage;
  String? _longErrorMessage;

  int? min;
  int? max;

  NumberValidator({
    this.min,
    this.max,
  }) : assert(
          min == null || max == null || min <= max,
          'min must be less than or equal to max',
        );

  @override
  String? validate(
    BuildContext context,
    String fieldName,
    String? value,
  ) {
    _longErrorMessage = null;
    _shortErrorMessage = null;

    if (value != null && value.isNotEmpty) {
      final l10n = context.l10n;

      try {
        final number = int.parse(value);
        if (max == null && min != null) {
          if (number < min!) {
            _shortErrorMessage = l10n.invalidMessage;
            _longErrorMessage = l10n.invalidRangeMin(min!);
          }
        } else if (min == null && max != null) {
          if (number > max!) {
            _shortErrorMessage = l10n.invalidMessage;
            _longErrorMessage = l10n.invalidRangeMax(max!);
          }
        } else if ((min != null &&
            max != null &&
            (number < min! || number > max!))) {
          _shortErrorMessage = l10n.invalidMessage;
          _longErrorMessage = l10n.invalidRange(min!, max!);
        }
      } catch (e) {
        _shortErrorMessage = l10n.invalidMessage;
        _longErrorMessage = l10n.invalidNumber;
      }
    }

    _isValid = _shortErrorMessage == null;
    return _shortErrorMessage;
  }
}
