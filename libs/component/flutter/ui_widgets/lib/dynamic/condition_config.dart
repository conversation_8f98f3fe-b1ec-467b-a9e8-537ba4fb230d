import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:expressions/expressions.dart';

import 'input_value.dart';

part 'condition_config.g.dart';

@JsonSerializable(
  fieldRename: FieldRename.kebab,
  createToJson: false,
)
class Conditions {
  final EnableCondition? enableIf;
  final RequiredCondition? requiredIf;
  final List<LabelCondition>? labelValueIf;

  const Conditions({
    this.enableIf,
    this.requiredIf,
    this.labelValueIf,
  });

  ConditionEvalResult evaluate(
    Map<String, InputValue> values,
  ) {
    bool isEnabled = enableIf?.evaluate(
          values,
        ) ??
        true;

    bool isRequired = requiredIf?.evaluate(
          values,
        ) ??
        false;

    String? labelValue;
    for (final condition in labelValueIf ?? []) {
      final value = condition.evaluate(values);
      if (value != null) {
        labelValue = value;
        break;
      }
    }

    return ConditionEvalResult(
      isEnabled: isEnabled,
      isRequired: isRequired,
      value: labelValue,
    );
  }

  factory Conditions.fromJson(Map<String, dynamic> json) =>
      _$ConditionsFromJson(json);
}

class ConditionEvalResult extends Equatable {
  final bool isEnabled;
  final bool isRequired;
  final String? value;

  const ConditionEvalResult({
    required this.isEnabled,
    required this.isRequired,
    this.value,
  });

  @override
  List<Object?> get props => [
        isEnabled,
        isRequired,
        value,
      ];
}

abstract class Condition<T> {
  String condition;

  @JsonKey(
    includeFromJson: false,
  )
  final Expression _expression;

  T evaluate(Map<String, dynamic> context);

  Condition({
    required this.condition,
  }) : _expression = Expression.parse(condition);
}

@JsonSerializable(
  fieldRename: FieldRename.kebab,
  createToJson: false,
)
class EnableCondition extends Condition<bool> {
  @override
  bool evaluate(Map<String, dynamic> context) {
    const evaluator = ExpressionEvaluator();
    final result = evaluator.eval(
      _expression,
      context,
    );
    assert(result is bool, 'EnableCondition must evaluate to a boolean');
    return result;
  }

  EnableCondition({
    required super.condition,
  });

  factory EnableCondition.fromJson(Map<String, dynamic> json) =>
      _$EnableConditionFromJson(json);
}

@JsonSerializable(
  fieldRename: FieldRename.kebab,
  createToJson: false,
)
class RequiredCondition extends Condition<bool> {
  @override
  bool evaluate(Map<String, dynamic> context) {
    const evaluator = ExpressionEvaluator();
    final result = evaluator.eval(
      _expression,
      context,
    );
    assert(result is bool, 'EnableCondition must evaluate to a boolean');
    return result;
  }

  RequiredCondition({
    required super.condition,
  });

  factory RequiredCondition.fromJson(Map<String, dynamic> json) =>
      _$RequiredConditionFromJson(json);
}

@JsonSerializable(
  fieldRename: FieldRename.kebab,
  createToJson: false,
)
class LabelCondition extends Condition<String?> {
  final String value;

  @override
  String? evaluate(Map<String, dynamic> context) {
    const evaluator = ExpressionEvaluator();
    final result = evaluator.eval(
      _expression,
      context,
    );
    assert(result is bool, 'LabelCondition must evaluate to a boolean');
    return result == true ? value : null;
  }

  LabelCondition({
    required super.condition,
    required this.value,
  });

  factory LabelCondition.fromJson(Map<String, dynamic> json) =>
      _$LabelConditionFromJson(json);
}
