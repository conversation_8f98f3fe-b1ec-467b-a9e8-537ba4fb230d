import 'package:flutter/material.dart';

abstract class InputValue {
  String get id;
  dynamic get value;
  ValueNotifier get controller;

  @override
  int get hashCode => id.hashCode ^ value.hashCode;

  @override
  String toString() {
    return value == null ? '' : value.toString();
  }

  @override
  bool operator ==(Object other) {
    if (other is InputValue) {
      return other.id == id && other.value == value;
    } else {
      return other.toString() == toString();
    }
  }
}
