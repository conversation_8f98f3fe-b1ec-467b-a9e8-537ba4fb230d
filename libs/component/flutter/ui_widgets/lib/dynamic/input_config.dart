import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';

import 'input_value.dart';
import 'condition_config.dart';
import 'validator_config.dart';

import '../form/text_input.dart';
import '../validators/validator.dart';
import '../validators/chained_validator.dart';
import '../validators/list_validator.dart';

part 'input_config.g.dart';

abstract class InputConfig<T> {
  final InputType type;

  final String id;
  final String label;

  final T? defaultValue;

  final bool? required;
  final Conditions? conditions;

  @ValidationConfigConverter()
  final List<ValidatorConfig>? validators;

  @JsonKey(
    includeFromJson: false,
  )
  Validator? _validator;

  @JsonKey(
    includeFromJson: false,
  )
  InputChanged? onChanged;

  @JsonKey(
    includeFromJson: false,
  )
  ConditionEvalResult? conditionEvalResult;

  String get labelText => conditionEvalResult?.value ?? label;
  bool get isEnabled => conditionEvalResult?.isEnabled ?? true;
  bool get isRequired => required ?? conditionEvalResult?.isRequired ?? false;

  Widget buildInputWidget(
    BuildContext context,
    InputChanged onChanged,
    Map<String, InputValue> values,
  );

  InputConfig({
    required this.type,
    required this.id,
    required this.label,
    this.defaultValue,
    this.required,
    this.conditions,
    this.validators,
  });

  void evaluateConditions({
    required Map<String, InputValue> values,
    InputChanged? onChanged,
  }) {
    final result = conditions?.evaluate(values);
    if (result != conditionEvalResult) {
      conditionEvalResult = result;
      if (onChanged != null) {
        Future.microtask(() {
          final inputValue = values[id];
          // reset if input is disabled
          final value = result?.isEnabled ?? true
              ? inputValue?.value
              : defaultValue ?? '';

          final notifier = inputValue?.controller;
          if (notifier is TextEditingController) {
            notifier.value = TextEditingValue(
              text: value ?? '',
            );
          } else {
            notifier?.value = null;
          }

          onChanged(
            id,
            isValid(value),
            value,
          );
        });
      }
    }
  }

  bool isValid(String? value) {
    return (!isRequired || (value != null && value.isNotEmpty)) &&
        (_validator?.isValid ?? true);
  }

  Validator? _getValidator() {
    if (_validator == null && validators != null && validators!.isNotEmpty) {
      _validator = validators!.length == 1
          ? validators!.first.createValidator()
          : ChainedValidator(
              validators!
                  .map(
                    (e) => e.createValidator(),
                  )
                  .toList(),
            );
    }
    return _validator;
  }
}

class InputConfigConverter
    implements JsonConverter<InputConfig, Map<String, dynamic>> {
  const InputConfigConverter();

  @override
  InputConfig fromJson(Map<String, dynamic> json) {
    final type = json['type'];
    switch (type) {
      case 'text':
        return TextInputConfig.fromJson(json);
      case 'editableList':
        return EditableListInputConfig.fromJson(json);
      case 'fixedList':
        return FixedListInputConfig.fromJson(json);
      default:
        throw ArgumentError('Unknown InputType: $type');
    }
  }

  @override
  Map<String, dynamic> toJson(InputConfig validator) {
    // Serialization to JSON is not required.
    throw UnimplementedError();
  }
}

enum InputType {
  text,
  editableList,
  fixedList,
}

@JsonSerializable(
  fieldRename: FieldRename.kebab,
  createToJson: false,
)
class TextInputConfig extends InputConfig<String> {
  final int? maxLength;

  @override
  Widget buildInputWidget(
    BuildContext context,
    InputChanged onChanged,
    Map<String, InputValue> values,
  ) {
    final controller = values[id]?.controller;
    assert(
      controller != null && controller is TextEditingController,
      'Controller for $id is not a TextEditingController',
    );

    evaluateConditions(
      values: values,
      onChanged: onChanged,
    );

    return TextInput(
      labelText: labelText,
      maxLength: maxLength,
      fixedHeight: true,
      isRequired: isRequired,
      enabled: isEnabled,
      controller: controller as TextEditingController,
      validator: _getValidator(),
      onChanged: (value, _) {
        onChanged(
          id,
          isValid(value),
          value,
        );
      },
    );
  }

  TextInputConfig({
    required super.id,
    required super.label,
    super.required,
    super.defaultValue,
    super.conditions,
    super.validators,
    this.maxLength,
  }) : super(type: InputType.text);

  factory TextInputConfig.fromJson(Map<String, dynamic> json) =>
      _$TextInputConfigFromJson(json);
}

@JsonSerializable(
  fieldRename: FieldRename.kebab,
  createToJson: false,
)
class EditableListInputConfig extends InputConfig<String> {
  final int? maxLength;

  final List<String> options;

  final String? optionsSemanticLabel;

  @override
  Widget buildInputWidget(
    BuildContext context,
    InputChanged onChanged,
    Map<String, InputValue> values,
  ) {
    final controller = values[id]?.controller;
    assert(
      controller != null && controller is TextEditingController,
      'Controller for $id is not a TextEditingController',
    );

    evaluateConditions(
      values: values,
      onChanged: onChanged,
    );

    final teController = controller as TextEditingController;

    return TextInput(
      labelText: labelText,
      initialValue: teController.text,
      maxLength: maxLength,
      fixedHeight: true,
      isRequired: isRequired,
      enabled: isEnabled,
      controller: teController,
      validator: _getValidator(),
      optionsSemanticLabel: optionsSemanticLabel,
      options: options
          .map(
            (e) => DropdownListItem(
              id: e,
            ),
          )
          .toList(),
      onChanged: (value, _) {
        onChanged(
          id,
          isValid(value),
          value,
        );
      },
    );
  }

  EditableListInputConfig({
    required super.id,
    required super.label,
    super.required,
    super.defaultValue,
    super.conditions,
    super.validators,
    this.maxLength,
    required this.options,
    this.optionsSemanticLabel,
  }) : super(type: InputType.editableList);

  factory EditableListInputConfig.fromJson(Map<String, dynamic> json) =>
      _$EditableListInputConfigFromJson(json);
}

@JsonSerializable(
  fieldRename: FieldRename.kebab,
  createToJson: false,
)
class FixedListInputConfig extends InputConfig<String> {
  @JsonKey(
    includeFromJson: false,
  )
  late final int? maxLength;

  final List<String> options;
  final String? defaultOption;

  final String? optionsSemanticLabel;

  @override
  Widget buildInputWidget(
    BuildContext context,
    InputChanged onChanged,
    Map<String, InputValue> values,
  ) {
    final controller = values[id]?.controller;
    assert(
      controller != null && controller is TextEditingController,
      'Controller for $id is not a TextEditingController',
    );

    evaluateConditions(
      values: values,
      onChanged: onChanged,
    );

    _validator = _validator ?? ListValidator(items: options);

    final teController = controller as TextEditingController;

    return TextInput(
      labelText: labelText,
      initialValue: teController.text,
      fixedHeight: true,
      isRequired: isRequired,
      enabled: isEnabled,
      controller: teController,
      validator: _validator,
      optionsSemanticLabel: optionsSemanticLabel,
      options: options
          .map(
            (e) => DropdownListItem(
              id: e,
            ),
          )
          .toList(),
      onChanged: (value, _) {
        onChanged(
          id,
          isValid(value),
          value,
        );
      },
    );
  }

  FixedListInputConfig({
    required super.id,
    required super.label,
    super.defaultValue,
    super.required,
    super.conditions,
    required this.options,
    this.defaultOption,
    this.optionsSemanticLabel,
  }) : super(type: InputType.fixedList) {
    bool defaultExists = defaultOption == null;
    maxLength = options.fold<int>(
      0,
      (max, o) {
        defaultExists = defaultExists || o == defaultOption;
        return o.length > max ? o.length : max;
      },
    );
    assert(defaultExists, 'defaultOption not in options');
  }

  factory FixedListInputConfig.fromJson(Map<String, dynamic> json) =>
      _$FixedListInputConfigFromJson(json);
}

typedef InputChanged = void Function(
  String id,
  bool isValid,
  String? value,
);
