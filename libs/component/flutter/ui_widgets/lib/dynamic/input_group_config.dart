import 'package:json_annotation/json_annotation.dart';

import 'input_config.dart';

part 'input_group_config.g.dart';

@JsonSerializable(
  fieldRename: FieldRename.kebab,
  createToJson: false,
)
class InputGroupConfig {
  final String? heading;

  @InputConfigConverter()
  final List<InputConfig> inputs;

  const InputGroupConfig({
    this.heading,
    required this.inputs,
  });

  factory InputGroupConfig.fromJson(Map<String, dynamic> json) =>
      _$InputGroupConfigFromJson(json);
}
