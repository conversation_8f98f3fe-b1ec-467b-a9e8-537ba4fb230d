// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'condition_config.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Conditions _$ConditionsFromJson(Map<String, dynamic> json) => Conditions(
      enableIf: json['enable-if'] == null
          ? null
          : EnableCondition.fromJson(json['enable-if'] as Map<String, dynamic>),
      requiredIf: json['required-if'] == null
          ? null
          : RequiredCondition.fromJson(
              json['required-if'] as Map<String, dynamic>),
      labelValueIf: (json['label-value-if'] as List<dynamic>?)
          ?.map((e) => LabelCondition.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

EnableCondition _$EnableConditionFromJson(Map<String, dynamic> json) =>
    EnableCondition(
      condition: json['condition'] as String,
    );

RequiredCondition _$RequiredConditionFromJson(Map<String, dynamic> json) =>
    RequiredCondition(
      condition: json['condition'] as String,
    );

LabelCondition _$LabelConditionFromJson(Map<String, dynamic> json) =>
    LabelCondition(
      condition: json['condition'] as String,
      value: json['value'] as String,
    );
