// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'input_config.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TextInputConfig _$TextInputConfigFromJson(Map<String, dynamic> json) =>
    TextInputConfig(
      id: json['id'] as String,
      label: json['label'] as String,
      required: json['required'] as bool?,
      defaultValue: json['default-value'] as String?,
      conditions: json['conditions'] == null
          ? null
          : Conditions.fromJson(json['conditions'] as Map<String, dynamic>),
      validators: (json['validators'] as List<dynamic>?)
          ?.map((e) => const ValidationConfigConverter()
              .fromJson(e as Map<String, dynamic>))
          .toList(),
      maxLength: (json['max-length'] as num?)?.toInt(),
    );

EditableListInputConfig _$EditableListInputConfigFromJson(
        Map<String, dynamic> json) =>
    EditableListInputConfig(
      id: json['id'] as String,
      label: json['label'] as String,
      required: json['required'] as bool?,
      defaultValue: json['default-value'] as String?,
      conditions: json['conditions'] == null
          ? null
          : Conditions.fromJson(json['conditions'] as Map<String, dynamic>),
      validators: (json['validators'] as List<dynamic>?)
          ?.map((e) => const ValidationConfigConverter()
              .fromJson(e as Map<String, dynamic>))
          .toList(),
      maxLength: (json['max-length'] as num?)?.toInt(),
      options:
          (json['options'] as List<dynamic>).map((e) => e as String).toList(),
      optionsSemanticLabel: json['options-semantic-label'] as String?,
    );

FixedListInputConfig _$FixedListInputConfigFromJson(
        Map<String, dynamic> json) =>
    FixedListInputConfig(
      id: json['id'] as String,
      label: json['label'] as String,
      defaultValue: json['default-value'] as String?,
      required: json['required'] as bool?,
      conditions: json['conditions'] == null
          ? null
          : Conditions.fromJson(json['conditions'] as Map<String, dynamic>),
      options:
          (json['options'] as List<dynamic>).map((e) => e as String).toList(),
      defaultOption: json['default-option'] as String?,
      optionsSemanticLabel: json['options-semantic-label'] as String?,
    );
