import 'package:flutter/material.dart';

import 'input_value.dart';
import 'input_group_config.dart';
import 'input_config.dart';

/// FormInputGroup is a widget that renders a group of
/// input fields based on the provided configuration.
/// The layout is responsive and will wrap fields to
/// the next row when the width of the fields exceeds
/// the provided minFieldWrapWidth.
///
/// Height of widget can be calculated as follows:
///  - if heading exists: +23 (heading) +16 (spacer)
///  - total inputs / numFieldsPerRow * 83 (field height)
///
class FormInputGroup extends StatefulWidget {
  final InputGroupConfig config;

  final FormDataChanged? onChanged;

  final Map<String, dynamic> initialValues;

  final int numFieldsPerRow;
  final double? minFieldWrapWidth;

  const FormInputGroup({
    super.key,
    required this.config,
    this.onChanged,
    this.initialValues = const {},
    this.numFieldsPerRow = 1,
    this.minFieldWrapWidth,
  })  : assert(
          numFieldsPerRow > 0,
          'numFieldsPerRow must be greater than 0',
        ),
        assert(
          minFieldWrapWidth != null || numFieldsPerRow == 1,
          'minFieldWrapWidth is not required when numFieldsPerRow == 1',
        ),
        assert(
          minFieldWrapWidth == null || numFieldsPerRow > 1,
          'minFieldWrapWidth is required when numFieldsPerRow > 1',
        );

  @override
  State<FormInputGroup> createState() => _FormInputGroupState();
}

class _FormInputGroupState extends State<FormInputGroup> {
  final Map<String, InputControllerState> controllers = {};

  @override
  void initState() {
    super.initState();
    for (var input in widget.config.inputs) {
      final initialValue = widget.initialValues[input.id] ?? input.defaultValue;
      input.evaluateConditions(
        values: controllers,
      );

      if (input is TextInputConfig ||
          input is EditableListInputConfig ||
          input is FixedListInputConfig) {
        controllers[input.id] = InputControllerState._(
          TextEditingController.fromValue(
            TextEditingValue(
              text: initialValue ?? '',
            ),
          ),
          FormDataValue(
            id: input.id,
            isValid: input.isValid(initialValue),
            value: initialValue,
          ),
        );
      } else {
        throw UnimplementedError(
          'Input type ${input.type} is not implemented',
        );
      }
    }
  }

  @override
  void dispose() {
    controllers.forEach((_, value) {
      value._controller.dispose();
    });
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    const spacer = SizedBox(height: 16);

    return LayoutBuilder(
      builder: (context, constraints) {
        double fieldWidth = constraints.maxWidth;
        int actualFieldsPerRow = widget.numFieldsPerRow;
        while (actualFieldsPerRow > 1) {
          fieldWidth = constraints.maxWidth / actualFieldsPerRow;
          if (fieldWidth >= widget.minFieldWrapWidth!) {
            break;
          }
          actualFieldsPerRow--;
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.config.heading != null) ...[
              Padding(
                padding: const EdgeInsets.only(left: 8),
                child: Text(
                  widget.config.heading!,
                  style: theme.textTheme.titleLarge,
                ),
              ),
              spacer,
            ],
            if (actualFieldsPerRow > 1) ...[
              Wrap(
                children: widget.config.inputs.map(
                  (input) {
                    return SizedBox(
                      width: fieldWidth,
                      child: input.buildInputWidget(
                        context,
                        _onChanged,
                        controllers,
                      ),
                    );
                  },
                ).toList(),
              ),
            ] else
              ...widget.config.inputs.map(
                (input) => input.buildInputWidget(
                  context,
                  _onChanged,
                  controllers,
                ),
              ),
          ],
        );
      },
    );
  }

  void _onChanged(
    String id,
    bool isValid,
    String? value,
  ) {
    final changedValue = FormDataValue(
      id: id,
      isValid: isValid,
      value: value,
    );

    final controller = controllers[id]!;
    controllers[id] = controller.setValue(changedValue);

    if (widget.onChanged != null) {
      widget.onChanged!(
        changedValue: changedValue,
        isInputGroupValid: controllers.values.fold(
          true,
          (isValid, e) => isValid && e._value.isValid,
        ),
      );
    }
  }
}

typedef FormDataChanged = void Function({
  required FormDataValue changedValue,
  required bool isInputGroupValid,
});

class InputControllerState extends InputValue {
  @override
  String get id => _value.id;
  @override
  dynamic get value => _value.value;
  @override
  ValueNotifier get controller => _controller;

  final ValueNotifier _controller;
  final FormDataValue _value;

  InputControllerState._(
    ValueNotifier<dynamic> formDataController,
    FormDataValue formDataValue,
  )   : _value = formDataValue,
        _controller = formDataController;

  InputControllerState setValue(
    FormDataValue newValue,
  ) {
    return InputControllerState._(
      _controller,
      newValue,
    );
  }
}

class FormDataValue {
  final String id;
  final bool isValid;
  final String? value;

  const FormDataValue({
    required this.id,
    required this.isValid,
    this.value,
  });

  @override
  String toString() {
    return 'FormDataValue{id: $id, '
        'isValid: $isValid, '
        'value: ${value != null ? '"$value"' : 'null'}}';
  }
}
