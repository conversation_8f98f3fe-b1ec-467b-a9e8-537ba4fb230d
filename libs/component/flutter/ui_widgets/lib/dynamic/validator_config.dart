import 'package:json_annotation/json_annotation.dart';

import '../validators/validator.dart';
import '../validators/regex_validator.dart';
import '../validators/number_validator.dart';
import '../validators/min_length_validator.dart';

part 'validator_config.g.dart';

abstract class ValidatorConfig {
  final ValidatorType type;

  Validator createValidator();

  const ValidatorConfig({
    required this.type,
  });
}

class ValidationConfigConverter
    implements JsonConverter<ValidatorConfig, Map<String, dynamic>> {
  const ValidationConfigConverter();

  @override
  ValidatorConfig fromJson(Map<String, dynamic> json) {
    final type = json['type'];
    switch (type) {
      case 'regex':
        return RegexValidatorConfig.fromJson(json);
      case 'number':
        return NumberValidatorConfig.fromJson(json);
      case 'minLength':
        return MinLengthValidatorConfig.from<PERSON>son(json);
      default:
        throw ArgumentError('Unknown InputType: $type');
    }
  }

  @override
  Map<String, dynamic> to<PERSON><PERSON>(ValidatorConfig validator) {
    // Serialization to JSON is not required.
    throw UnimplementedError();
  }
}

enum ValidatorType {
  regex,
  number,
  minLength,
}

@JsonSerializable(
  fieldRename: FieldRename.kebab,
  createToJson: false,
)
class RegexValidatorConfig extends ValidatorConfig {
  final String pattern;

  final List<String> examples;

  @override
  Validator createValidator() {
    return RegexValidator(
      regex: RegExp(pattern),
      examples: examples,
    );
  }

  const RegexValidatorConfig({
    required this.pattern,
    this.examples = const [],
  }) : super(type: ValidatorType.regex);

  factory RegexValidatorConfig.fromJson(Map<String, dynamic> json) =>
      _$RegexValidatorConfigFromJson(json);
}

@JsonSerializable(
  fieldRename: FieldRename.kebab,
  createToJson: false,
)
class NumberValidatorConfig extends ValidatorConfig {
  final int? min;
  final int? max;

  @override
  Validator createValidator() {
    return NumberValidator(
      min: min,
      max: max,
    );
  }

  const NumberValidatorConfig({
    this.min,
    this.max,
  }) : super(type: ValidatorType.number);

  factory NumberValidatorConfig.fromJson(Map<String, dynamic> json) =>
      _$NumberValidatorConfigFromJson(json);
}

@JsonSerializable(
  fieldRename: FieldRename.kebab,
  createToJson: false,
)
class MinLengthValidatorConfig extends ValidatorConfig {
  final int length;

  @override
  Validator createValidator() {
    return MinLengthValidator(length: length);
  }

  const MinLengthValidatorConfig({
    required this.length,
  }) : super(type: ValidatorType.minLength);

  factory MinLengthValidatorConfig.fromJson(Map<String, dynamic> json) =>
      _$MinLengthValidatorConfigFromJson(json);
}
