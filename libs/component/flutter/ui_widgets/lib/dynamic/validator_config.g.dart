// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'validator_config.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RegexValidatorConfig _$RegexValidatorConfigFromJson(
        Map<String, dynamic> json) =>
    RegexValidatorConfig(
      pattern: json['pattern'] as String,
      examples: (json['examples'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

NumberValidatorConfig _$NumberValidatorConfigFromJson(
        Map<String, dynamic> json) =>
    NumberValidatorConfig(
      min: (json['min'] as num?)?.toInt(),
      max: (json['max'] as num?)?.toInt(),
    );

MinLengthValidatorConfig _$MinLengthValidatorConfigFromJson(
        Map<String, dynamic> json) =>
    MinLengthValidatorConfig(
      length: (json['length'] as num).toInt(),
    );
