import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'ui_widgets_component_platform_interface.dart';

/// An implementation of [UiWidgetsComponentPlatform] that uses method channels.
class MethodChannelUiWidgetsComponent extends UiWidgetsComponentPlatform {
  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('ui_widgets_component');

  @override
  Future<String?> getPlatformVersion() async {
    final version = await methodChannel.invokeMethod<String>('getPlatformVersion');
    return version;
  }
}
