import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'ui_widget_localizations_en.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of UIWidgetLocalizations
/// returned by `UIWidgetLocalizations.of(context)`.
///
/// Applications need to include `UIWidgetLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/ui_widget_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: UIWidgetLocalizations.localizationsDelegates,
///   supportedLocales: UIWidgetLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the UIWidgetLocalizations.supportedLocales
/// property.
abstract class UIWidgetLocalizations {
  UIWidgetLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static UIWidgetLocalizations of(BuildContext context) {
    return Localizations.of<UIWidgetLocalizations>(
      context,
      UIWidgetLocalizations,
    )!;
  }

  static const LocalizationsDelegate<UIWidgetLocalizations> delegate =
      _UIWidgetLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[Locale('en')];

  /// No description provided for @email.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// No description provided for @phone.
  ///
  /// In en, this message translates to:
  /// **'Phone'**
  String get phone;

  /// No description provided for @social.
  ///
  /// In en, this message translates to:
  /// **'Social'**
  String get social;

  /// No description provided for @usernameFieldName.
  ///
  /// In en, this message translates to:
  /// **'Username'**
  String get usernameFieldName;

  /// No description provided for @passwordFieldName.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get passwordFieldName;

  /// No description provided for @newPasswordFieldName.
  ///
  /// In en, this message translates to:
  /// **'New Password'**
  String get newPasswordFieldName;

  /// No description provided for @confirmPasswordFieldName.
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get confirmPasswordFieldName;

  /// No description provided for @emailAddressFieldName.
  ///
  /// In en, this message translates to:
  /// **'Email Address'**
  String get emailAddressFieldName;

  /// No description provided for @mobileNumberFieldName.
  ///
  /// In en, this message translates to:
  /// **'Mobile Phone Number'**
  String get mobileNumberFieldName;

  /// No description provided for @phoneNumberFieldName.
  ///
  /// In en, this message translates to:
  /// **'Phone Number'**
  String get phoneNumberFieldName;

  /// No description provided for @socialMediaHandleFieldName.
  ///
  /// In en, this message translates to:
  /// **'Social Media Handle'**
  String get socialMediaHandleFieldName;

  /// No description provided for @semShowPassword.
  ///
  /// In en, this message translates to:
  /// **'Show password'**
  String get semShowPassword;

  /// No description provided for @semHidePassword.
  ///
  /// In en, this message translates to:
  /// **'Hide password'**
  String get semHidePassword;

  /// No description provided for @typeFieldName.
  ///
  /// In en, this message translates to:
  /// **'Type'**
  String get typeFieldName;

  /// No description provided for @descriptionFieldName.
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get descriptionFieldName;

  /// No description provided for @countryCodeFieldName.
  ///
  /// In en, this message translates to:
  /// **'Country'**
  String get countryCodeFieldName;

  /// No description provided for @sovereignStateCodeFieldName.
  ///
  /// In en, this message translates to:
  /// **'Sovereign State'**
  String get sovereignStateCodeFieldName;

  /// No description provided for @semProviceCodePicker.
  ///
  /// In en, this message translates to:
  /// **'Click to open province code picker list'**
  String get semProviceCodePicker;

  /// No description provided for @semUSStateCodePicker.
  ///
  /// In en, this message translates to:
  /// **'Click to open state code picker list'**
  String get semUSStateCodePicker;

  /// No description provided for @semGBCountryPicker.
  ///
  /// In en, this message translates to:
  /// **'Click to open state code picker list'**
  String get semGBCountryPicker;

  /// No description provided for @semCountryCodePicker.
  ///
  /// In en, this message translates to:
  /// **'Click to open country code picker dialog'**
  String get semCountryCodePicker;

  /// No description provided for @semGBSovereignStateCodePicker.
  ///
  /// In en, this message translates to:
  /// **'Click to open sovereign state code picker dialog'**
  String get semGBSovereignStateCodePicker;

  /// No description provided for @semContactTypePicker.
  ///
  /// In en, this message translates to:
  /// **'Click to open contact type picket list'**
  String get semContactTypePicker;

  /// No description provided for @semCodeInputRequired.
  ///
  /// In en, this message translates to:
  /// **'{length} digit code input, required'**
  String semCodeInputRequired(int length);

  /// No description provided for @semCodeInput.
  ///
  /// In en, this message translates to:
  /// **'{length} digit code input'**
  String semCodeInput(int length);

  /// No description provided for @addrNumberFieldName.
  ///
  /// In en, this message translates to:
  /// **'Number'**
  String get addrNumberFieldName;

  /// No description provided for @addrStreetFieldName.
  ///
  /// In en, this message translates to:
  /// **'Street'**
  String get addrStreetFieldName;

  /// No description provided for @addrOtherFieldName.
  ///
  /// In en, this message translates to:
  /// **'Other'**
  String get addrOtherFieldName;

  /// No description provided for @addrMunicipalityFieldName.
  ///
  /// In en, this message translates to:
  /// **'Municipality'**
  String get addrMunicipalityFieldName;

  /// No description provided for @addrCountyFieldName.
  ///
  /// In en, this message translates to:
  /// **'County'**
  String get addrCountyFieldName;

  /// No description provided for @addrProvinceFieldName.
  ///
  /// In en, this message translates to:
  /// **'Province'**
  String get addrProvinceFieldName;

  /// No description provided for @addrPostalCodeFieldName.
  ///
  /// In en, this message translates to:
  /// **'Postal Code'**
  String get addrPostalCodeFieldName;

  /// No description provided for @addrTownFieldName.
  ///
  /// In en, this message translates to:
  /// **'Town'**
  String get addrTownFieldName;

  /// No description provided for @addrCityFieldName.
  ///
  /// In en, this message translates to:
  /// **'City'**
  String get addrCityFieldName;

  /// No description provided for @addrLocalityFieldName.
  ///
  /// In en, this message translates to:
  /// **'Locality'**
  String get addrLocalityFieldName;

  /// No description provided for @addrStateFieldName.
  ///
  /// In en, this message translates to:
  /// **'State'**
  String get addrStateFieldName;

  /// No description provided for @addrZipCodeFieldName.
  ///
  /// In en, this message translates to:
  /// **'Zip Code'**
  String get addrZipCodeFieldName;

  /// No description provided for @contactValueFieldName.
  ///
  /// In en, this message translates to:
  /// **'Contact'**
  String get contactValueFieldName;

  /// No description provided for @resetPasswordText.
  ///
  /// In en, this message translates to:
  /// **'Reset Password'**
  String get resetPasswordText;

  /// No description provided for @codeNotReceivedText.
  ///
  /// In en, this message translates to:
  /// **'Didn\'t receive the code?'**
  String get codeNotReceivedText;

  /// No description provided for @resendCodeText.
  ///
  /// In en, this message translates to:
  /// **'Resend'**
  String get resendCodeText;

  /// No description provided for @colorPickerPrompt.
  ///
  /// In en, this message translates to:
  /// **'Select a color and customize the shade of the color using the pickers below.'**
  String get colorPickerPrompt;

  /// No description provided for @invalidMessage.
  ///
  /// In en, this message translates to:
  /// **'invalid'**
  String get invalidMessage;

  /// No description provided for @tooShortMessage.
  ///
  /// In en, this message translates to:
  /// **'too short'**
  String get tooShortMessage;

  /// No description provided for @invalidPattern.
  ///
  /// In en, this message translates to:
  /// **'Input does not match the required pattern'**
  String get invalidPattern;

  /// No description provided for @invalidPatternWithExamples.
  ///
  /// In en, this message translates to:
  /// **'Input does not match the required pattern (e.g. {examples})'**
  String invalidPatternWithExamples(String examples);

  /// No description provided for @invalidNumber.
  ///
  /// In en, this message translates to:
  /// **'Input is not a valid number'**
  String get invalidNumber;

  /// No description provided for @invalidRangeMin.
  ///
  /// In en, this message translates to:
  /// **'Input should be greater or equal to {min}.'**
  String invalidRangeMin(int min);

  /// No description provided for @invalidRangeMax.
  ///
  /// In en, this message translates to:
  /// **'Input should be less than or equal to {max}.'**
  String invalidRangeMax(int max);

  /// No description provided for @invalidRange.
  ///
  /// In en, this message translates to:
  /// **'Input is not within the required range ({min} - {max})'**
  String invalidRange(int min, int max);

  /// No description provided for @invalidList.
  ///
  /// In en, this message translates to:
  /// **'Input is not in the list of allowed values'**
  String get invalidList;

  /// No description provided for @passwordTooShortErrorMessage.
  ///
  /// In en, this message translates to:
  /// **'Password must be at least 8 characters'**
  String get passwordTooShortErrorMessage;

  /// No description provided for @passwordSpecialCharErrorMessage.
  ///
  /// In en, this message translates to:
  /// **'Password must contain at least one special character'**
  String get passwordSpecialCharErrorMessage;

  /// No description provided for @passwordLowercaseCharErrorMessage.
  ///
  /// In en, this message translates to:
  /// **'Password must contain at least one lowercase letter'**
  String get passwordLowercaseCharErrorMessage;

  /// No description provided for @passwordUppercaseCharErrorMessage.
  ///
  /// In en, this message translates to:
  /// **'Password must contain at least one uppercase letter'**
  String get passwordUppercaseCharErrorMessage;

  /// No description provided for @passwordNumberErrorMessage.
  ///
  /// In en, this message translates to:
  /// **'Password must contain at least one number'**
  String get passwordNumberErrorMessage;

  /// No description provided for @confirmPasswordLongErrorMessage.
  ///
  /// In en, this message translates to:
  /// **'Passwords do not match'**
  String get confirmPasswordLongErrorMessage;

  /// No description provided for @confirmPasswordShortErrorMessage.
  ///
  /// In en, this message translates to:
  /// **'no match'**
  String get confirmPasswordShortErrorMessage;

  /// No description provided for @tooShortErrorMessage.
  ///
  /// In en, this message translates to:
  /// **'{fieldName} must be at least {length} characters long'**
  String tooShortErrorMessage(String fieldName, int length);

  /// No description provided for @invalidEmailErrorMessage.
  ///
  /// In en, this message translates to:
  /// **'\"{value}\" is not a valid email address.'**
  String invalidEmailErrorMessage(String value);

  /// No description provided for @invalidPhoneNumberErrorMessage.
  ///
  /// In en, this message translates to:
  /// **'Not a valid phone number.'**
  String get invalidPhoneNumberErrorMessage;

  /// No description provided for @invalidPostalCodeErrorMessage.
  ///
  /// In en, this message translates to:
  /// **'Invalid Postal Code'**
  String get invalidPostalCodeErrorMessage;

  /// No description provided for @invalidZipCodeErrorMessage.
  ///
  /// In en, this message translates to:
  /// **'Invalid Zip Code'**
  String get invalidZipCodeErrorMessage;

  /// No description provided for @invalidCountryErrorMessage.
  ///
  /// In en, this message translates to:
  /// **'Not a valid country.'**
  String get invalidCountryErrorMessage;
}

class _UIWidgetLocalizationsDelegate
    extends LocalizationsDelegate<UIWidgetLocalizations> {
  const _UIWidgetLocalizationsDelegate();

  @override
  Future<UIWidgetLocalizations> load(Locale locale) {
    return SynchronousFuture<UIWidgetLocalizations>(
      lookupUIWidgetLocalizations(locale),
    );
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en'].contains(locale.languageCode);

  @override
  bool shouldReload(_UIWidgetLocalizationsDelegate old) => false;
}

UIWidgetLocalizations lookupUIWidgetLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return UIWidgetLocalizationsEn();
  }

  throw FlutterError(
    'UIWidgetLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
