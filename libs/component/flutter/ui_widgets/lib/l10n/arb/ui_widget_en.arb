{"@@locale": "en", "email": "Email", "phone": "Phone", "social": "Social", "usernameFieldName": "Username", "passwordFieldName": "Password", "newPasswordFieldName": "New Password", "confirmPasswordFieldName": "Confirm Password", "emailAddressFieldName": "Email Address", "mobileNumberFieldName": "Mobile Phone Number", "phoneNumberFieldName": "Phone Number", "socialMediaHandleFieldName": "Social Media Handle", "semShowPassword": "Show password", "semHidePassword": "Hide password", "typeFieldName": "Type", "descriptionFieldName": "Description", "countryCodeFieldName": "Country", "sovereignStateCodeFieldName": "Sovereign State", "semProviceCodePicker": "Click to open province code picker list", "semUSStateCodePicker": "Click to open state code picker list", "semGBCountryPicker": "Click to open state code picker list", "semCountryCodePicker": "Click to open country code picker dialog", "semGBSovereignStateCodePicker": "Click to open sovereign state code picker dialog", "semContactTypePicker": "Click to open contact type picket list", "semCodeInputRequired": "{length} digit code input, required", "@semCodeInputRequired": {"placeholders": {"length": {"type": "int"}}}, "semCodeInput": "{length} digit code input", "@semCodeInput": {"placeholders": {"length": {"type": "int"}}}, "addrNumberFieldName": "Number", "addrStreetFieldName": "Street", "addrOtherFieldName": "Other", "addrMunicipalityFieldName": "Municipality", "addrCountyFieldName": "County", "addrProvinceFieldName": "Province", "addrPostalCodeFieldName": "Postal Code", "addrTownFieldName": "Town", "addrCityFieldName": "City", "addrLocalityFieldName": "Locality", "addrStateFieldName": "State", "addrZipCodeFieldName": "Zip Code", "contactValueFieldName": "Contact", "resetPasswordText": "Reset Password", "codeNotReceivedText": "Didn't receive the code?", "resendCodeText": "Resend", "colorPickerPrompt": "Select a color and customize the shade of the color using the pickers below.", "invalidMessage": "invalid", "tooShortMessage": "too short", "invalidPattern": "Input does not match the required pattern", "invalidPatternWithExamples": "Input does not match the required pattern (e.g. {examples})", "@invalidPatternWithExamples": {"placeholders": {"examples": {"type": "String"}}}, "invalidNumber": "Input is not a valid number", "invalidRangeMin": "Input should be greater or equal to {min}.", "@invalidRangeMin": {"placeholders": {"min": {"type": "int"}}}, "invalidRangeMax": "Input should be less than or equal to {max}.", "@invalidRangeMax": {"placeholders": {"max": {"type": "int"}}}, "invalidRange": "Input is not within the required range ({min} - {max})", "@invalidRange": {"placeholders": {"min": {"type": "int"}, "max": {"type": "int"}}}, "invalidList": "Input is not in the list of allowed values", "passwordTooShortErrorMessage": "Password must be at least 8 characters", "passwordSpecialCharErrorMessage": "Password must contain at least one special character", "passwordLowercaseCharErrorMessage": "Password must contain at least one lowercase letter", "passwordUppercaseCharErrorMessage": "Password must contain at least one uppercase letter", "passwordNumberErrorMessage": "Password must contain at least one number", "confirmPasswordLongErrorMessage": "Passwords do not match", "confirmPasswordShortErrorMessage": "no match", "tooShortErrorMessage": "{fieldName} must be at least {length} characters long", "@tooShortErrorMessage": {"placeholders": {"fieldName": {"type": "String"}, "length": {"type": "int"}}}, "invalidEmailErrorMessage": "\"{value}\" is not a valid email address.", "@invalidEmailErrorMessage": {"placeholders": {"value": {"type": "String"}}}, "invalidPhoneNumberErrorMessage": "Not a valid phone number.", "invalidPostalCodeErrorMessage": "Invalid Postal Code", "invalidZipCodeErrorMessage": "Invalid Zip Code", "invalidCountryErrorMessage": "Not a valid country."}