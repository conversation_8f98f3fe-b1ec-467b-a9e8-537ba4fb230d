// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'ui_widget_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class UIWidgetLocalizationsEn extends UIWidgetLocalizations {
  UIWidgetLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get email => 'Email';

  @override
  String get phone => 'Phone';

  @override
  String get social => 'Social';

  @override
  String get usernameFieldName => 'Username';

  @override
  String get passwordFieldName => 'Password';

  @override
  String get newPasswordFieldName => 'New Password';

  @override
  String get confirmPasswordFieldName => 'Confirm Password';

  @override
  String get emailAddressFieldName => 'Email Address';

  @override
  String get mobileNumberFieldName => 'Mobile Phone Number';

  @override
  String get phoneNumberFieldName => 'Phone Number';

  @override
  String get socialMediaHandleFieldName => 'Social Media Handle';

  @override
  String get semShowPassword => 'Show password';

  @override
  String get semHidePassword => 'Hide password';

  @override
  String get typeFieldName => 'Type';

  @override
  String get descriptionFieldName => 'Description';

  @override
  String get countryCodeFieldName => 'Country';

  @override
  String get sovereignStateCodeFieldName => 'Sovereign State';

  @override
  String get semProviceCodePicker => 'Click to open province code picker list';

  @override
  String get semUSStateCodePicker => 'Click to open state code picker list';

  @override
  String get semGBCountryPicker => 'Click to open state code picker list';

  @override
  String get semCountryCodePicker => 'Click to open country code picker dialog';

  @override
  String get semGBSovereignStateCodePicker =>
      'Click to open sovereign state code picker dialog';

  @override
  String get semContactTypePicker => 'Click to open contact type picket list';

  @override
  String semCodeInputRequired(int length) {
    return '$length digit code input, required';
  }

  @override
  String semCodeInput(int length) {
    return '$length digit code input';
  }

  @override
  String get addrNumberFieldName => 'Number';

  @override
  String get addrStreetFieldName => 'Street';

  @override
  String get addrOtherFieldName => 'Other';

  @override
  String get addrMunicipalityFieldName => 'Municipality';

  @override
  String get addrCountyFieldName => 'County';

  @override
  String get addrProvinceFieldName => 'Province';

  @override
  String get addrPostalCodeFieldName => 'Postal Code';

  @override
  String get addrTownFieldName => 'Town';

  @override
  String get addrCityFieldName => 'City';

  @override
  String get addrLocalityFieldName => 'Locality';

  @override
  String get addrStateFieldName => 'State';

  @override
  String get addrZipCodeFieldName => 'Zip Code';

  @override
  String get contactValueFieldName => 'Contact';

  @override
  String get resetPasswordText => 'Reset Password';

  @override
  String get codeNotReceivedText => 'Didn\'t receive the code?';

  @override
  String get resendCodeText => 'Resend';

  @override
  String get colorPickerPrompt =>
      'Select a color and customize the shade of the color using the pickers below.';

  @override
  String get invalidMessage => 'invalid';

  @override
  String get tooShortMessage => 'too short';

  @override
  String get invalidPattern => 'Input does not match the required pattern';

  @override
  String invalidPatternWithExamples(String examples) {
    return 'Input does not match the required pattern (e.g. $examples)';
  }

  @override
  String get invalidNumber => 'Input is not a valid number';

  @override
  String invalidRangeMin(int min) {
    return 'Input should be greater or equal to $min.';
  }

  @override
  String invalidRangeMax(int max) {
    return 'Input should be less than or equal to $max.';
  }

  @override
  String invalidRange(int min, int max) {
    return 'Input is not within the required range ($min - $max)';
  }

  @override
  String get invalidList => 'Input is not in the list of allowed values';

  @override
  String get passwordTooShortErrorMessage =>
      'Password must be at least 8 characters';

  @override
  String get passwordSpecialCharErrorMessage =>
      'Password must contain at least one special character';

  @override
  String get passwordLowercaseCharErrorMessage =>
      'Password must contain at least one lowercase letter';

  @override
  String get passwordUppercaseCharErrorMessage =>
      'Password must contain at least one uppercase letter';

  @override
  String get passwordNumberErrorMessage =>
      'Password must contain at least one number';

  @override
  String get confirmPasswordLongErrorMessage => 'Passwords do not match';

  @override
  String get confirmPasswordShortErrorMessage => 'no match';

  @override
  String tooShortErrorMessage(String fieldName, int length) {
    return '$fieldName must be at least $length characters long';
  }

  @override
  String invalidEmailErrorMessage(String value) {
    return '\"$value\" is not a valid email address.';
  }

  @override
  String get invalidPhoneNumberErrorMessage => 'Not a valid phone number.';

  @override
  String get invalidPostalCodeErrorMessage => 'Invalid Postal Code';

  @override
  String get invalidZipCodeErrorMessage => 'Invalid Zip Code';

  @override
  String get invalidCountryErrorMessage => 'Not a valid country.';
}
