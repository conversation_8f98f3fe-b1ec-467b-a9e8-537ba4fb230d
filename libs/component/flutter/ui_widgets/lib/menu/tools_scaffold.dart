import 'package:flutter/material.dart';

import 'tool.dart';

import 'tools_view_bar.dart';
import 'status_view_bar.dart';

class ToolsScaffold extends StatefulWidget {
  /// A widget that implements a frame with a toolbar
  /// that interacts with a child within the frame.

  final double _toolbarHeight;
  final List<Tool> _tools;

  final double _toolBarSpacing;

  final ToolsAlignment _toolsAlignment;
  final FloatToolsAlignment _floatAlignment;

  final double _statusBarHeight;
  final List<Tool>? _statusItems;

  final double _statusBarSpacing;

  final Widget _body;

  const ToolsScaffold({
    super.key,
    double toolBarHeight = 44.0,
    List<Tool> tools = const [],
    double toolbarSpacing = 8,
    ToolsAlignment toolsAlignment = ToolsAlignment.center,
    FloatToolsAlignment floatAlignment = FloatToolsAlignment.left,
    double statusBarHeight = 24.0,
    List<Tool>? statusItems,
    double statusBarSpacing = 0,
    required Widget body,
  })  : _toolbarHeight = toolBarHeight,
        _statusBarHeight = statusBarHeight,
        _tools = tools,
        _toolBarSpacing = toolbarSpacing,
        _toolsAlignment = toolsAlignment,
        _floatAlignment = floatAlignment,
        _statusItems = statusItems,
        _statusBarSpacing = statusBarSpacing,
        _body = body;

  @override
  State<StatefulWidget> createState() => _ToolsScaffoldState();
}

class _ToolsScaffoldState extends State<ToolsScaffold> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return LayoutBuilder(
      builder: (context, constraints) {
        var toolbarWidth = widget._tools.fold(
          4.0,
          (width, tool) =>
              width +
              (tool.calcSize(context, spacing: widget._toolBarSpacing)?.width ??
                  0.0),
        );
        final isCompactView = constraints.maxWidth < toolbarWidth;
        final showStatusBar = widget._statusItems != null;

        return Scaffold(
          appBar: PreferredSize(
            preferredSize: Size.fromHeight(
              widget._toolbarHeight,
            ),
            child: Container(
              height: widget._toolbarHeight,
              decoration: BoxDecoration(
                border: Border.all(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.2),
                  width: 1.0,
                ),
              ),
              child: AppBar(
                flexibleSpace: ToolsViewBar(
                  tools: widget._tools,
                  spacing: widget._toolBarSpacing / 2,
                  alignment: widget._toolsAlignment,
                  isCompactView: isCompactView,
                  float: false,
                ),
                elevation: 5.00,
                shadowColor: theme.colorScheme.shadow,
                backgroundColor: theme.colorScheme.surface,
              ),
            ),
          ),
          body: showStatusBar
              ? Column(
                  children: [
                    Expanded(
                      child: widget._body,
                    ),
                    StatusViewBar(
                      items: widget._statusItems!,
                      height: widget._statusBarHeight,
                      spacing: widget._statusBarSpacing / 2,
                    ),
                  ],
                )
              : widget._body,
          floatingActionButton: isCompactView
              ? ToolsViewBar(
                  tools: widget._tools,
                  spacing: widget._toolBarSpacing / 2,
                  alignment: widget._toolsAlignment,
                  isCompactView: true,
                  float: true,
                  floatOffset: showStatusBar ? widget._statusBarHeight : null,
                )
              : null,
          floatingActionButtonLocation:
              widget._floatAlignment == FloatToolsAlignment.right
                  ? FloatingActionButtonLocation.endFloat
                  : FloatingActionButtonLocation.startFloat,
        );
      },
    );
  }
}

enum FloatToolsAlignment {
  left,
  right,
}
