import 'package:flutter/material.dart';

class AvatarPopupMenu<T> extends StatefulWidget {
  /// The radius of the [CircleAvatar] widget.
  /// which also determines the size of the
  /// [PopupMenuButton] widget.
  final double avatarRadius;

  /// The duration of the animation when the
  /// popup menu is shown in milliseconds.
  final int animationDuration;

  /// Properties passed along to the [CircleAvatar] widget.
  final Color? avatarBgColor;
  final Color? avatarFgColor;
  final ImageProvider? avatarBgImage;
  final ImageProvider? avatarFgImage;
  final ImageErrorListener? onAvatarBgImageError;
  final ImageErrorListener? onAvatarFgImageError;
  final String? avatarInitials;
  final Widget? avatarWidget;

  final Function(bool)? onExpanded;

  /// Called when the popup menu is shown.
  final VoidCallback? onOpened;

  /// Called when the user dismisses the popup menu without selecting an item.
  ///
  /// If the user selects a value, [onSelected] is called instead.
  final PopupMenuCanceled? onCanceled;

  /// Called when the user selects a value from the popup menu created by this button.
  ///
  /// If the popup menu is dismissed without selecting a value, [onCanceled] is
  /// called instead.
  final PopupMenuItemSelected<T>? onSelected;

  /// Text that describes the action that will occur when the button is pressed.
  ///
  /// This text is displayed when the user long-presses on the button and is
  /// used for accessibility.
  final String tooltip;

  /// Text that describes the action that will occur when the button is pressed
  /// used for accessibility.
  final String? semanticsLabel;

  /// The offset is applied relative to the initial position
  /// set by the [position].
  ///
  /// When not set, the offset defaults to [Offset.zero].
  final Offset offset;

  /// The list of items to show in the popup menu.
  final List<PopupMenuEntry<T>> menuItems;

  const AvatarPopupMenu({
    super.key,
    this.avatarBgColor,
    this.avatarBgImage,
    this.avatarFgImage,
    this.onAvatarBgImageError,
    this.onAvatarFgImageError,
    this.avatarFgColor,
    this.avatarInitials,
    this.avatarWidget,
    this.avatarRadius = 48.0,
    this.animationDuration = 500,
    this.onExpanded,
    this.onOpened,
    this.onCanceled,
    this.onSelected,
    this.offset = Offset.zero,
    this.tooltip = '',
    this.semanticsLabel,
    required this.menuItems,
  });

  @override
  State<AvatarPopupMenu<T>> createState() => _AvatarPopupMenu<T>();
}

class _AvatarPopupMenu<T> extends State<AvatarPopupMenu<T>> {
  bool isMenuOpen = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final borderColor =
        theme.iconTheme.color ?? theme.colorScheme.inverseSurface;

    final avatarInitials = widget.avatarInitials != null
        ? Text(
            widget.avatarInitials!,
            style: theme.textTheme.headlineSmall!.copyWith(
              color: theme.colorScheme.inverseSurface,
            ),
          )
        : null;

    return SizedBox(
      width: widget.avatarRadius,
      height: widget.avatarRadius,
      child: Theme(
        data: Theme.of(context).copyWith(
          popupMenuTheme: PopupMenuThemeData(
            shape: RoundedRectangleBorder(
              side: BorderSide(
                color: borderColor,
                width: 2.0,
              ),
              borderRadius: BorderRadius.circular(10.0),
            ),
          ),
        ),
        child: Semantics(
          label: widget.semanticsLabel,
          blockUserActions: true,
          child: PopupMenuButton<T>(
            popUpAnimationStyle: AnimationStyle(
              curve: Easing.emphasizedDecelerate,
              duration: const Duration(milliseconds: 500),
            ),
            tooltip: widget.tooltip,
            icon: SizedBox(
              width: widget.avatarRadius,
              height: widget.avatarRadius,
              child: Stack(
                clipBehavior: Clip.none,
                children: <Widget>[
                  Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: theme.iconTheme.color ??
                            theme.colorScheme.inverseSurface,
                        width: 1.0,
                      ),
                    ),
                    child: CircleAvatar(
                      backgroundColor: widget.avatarBgColor,
                      backgroundImage: widget.avatarBgImage,
                      foregroundImage: widget.avatarFgImage,
                      onBackgroundImageError: widget.onAvatarBgImageError,
                      onForegroundImageError: widget.onAvatarFgImageError,
                      foregroundColor: widget.avatarFgColor,
                      radius: widget.avatarRadius,
                      child: widget.avatarWidget ?? avatarInitials,
                    ),
                  ),
                  Positioned(
                    top: widget.avatarRadius * 0.48,
                    left: widget.avatarRadius * 0.65,
                    child: AnimatedRotation(
                      turns: isMenuOpen ? -0.5 : 0.0,
                      duration: Duration(
                        milliseconds: (widget.animationDuration * 0.6).toInt(),
                      ),
                      child: Icon(
                        Icons.arrow_drop_down,
                        size: widget.avatarRadius * 0.72,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            padding: const EdgeInsets.all(2),
            position: PopupMenuPosition.under,
            offset: widget.offset,
            onOpened: _onOpened,
            onCanceled: _onCanceled,
            onSelected: _onSelected,
            itemBuilder: (BuildContext context) => widget.menuItems,
          ),
        ),
      ),
    );
  }

  void _onOpened() {
    widget.onOpened?.call();
    setState(() => isMenuOpen = true);
  }

  void _onCanceled() {
    widget.onCanceled?.call();
    setState(() => isMenuOpen = false);
  }

  void _onSelected(T result) {
    widget.onSelected?.call(result);
    setState(() => isMenuOpen = false);
  }
}
