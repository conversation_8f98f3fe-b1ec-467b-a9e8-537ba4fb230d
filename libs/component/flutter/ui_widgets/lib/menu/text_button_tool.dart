import 'package:flutter/material.dart';

import '../utils/text.dart';
import 'tool.dart';
import 'status_view_bar.dart';

class TextButtonTool<T> extends ToolBuilder<T> {
  final List<String>? Function(T? param) options;
  final OnOption? onOption;

  final double? iconSize;
  final Color? iconColor;
  final bool animateDown;

  final double? fontSize;
  final Color? fontColor;

  final TextStyle? textStyle;
  final bool textShadow;

  final bool insetToolContainer;
  final bool addPaddingForOptionsDropdown;
  final bool alignDropdownToText;

  final String? buttonSemanticsLabel;
  final String? optionsSemanticsLabel;

  final VoidCallback? onInit;
  final VoidCallback? onDispose;

  @override
  Alignment? get alignment => _alignment;
  final Alignment _alignment;

  @override
  double? get width => _width;
  final double? _width;

  @override
  double? get height => _height;
  final double? _height;

  factory TextButtonTool.statusbar({
    Key? key,
    ToolVisibility visibility = ToolVisibility.showOnCompact,
    ValueNotifier<T>? toolValue,
    Widget? Function(T? param) icon = _null<Widget>,
    IconData? Function(T? param) iconData = _null<IconData>,
    String? Function(T? param) text = _null<String>,
    bool Function(T? param) enabled = _enabled,
    String? Function(T? param) tooltip = _null<String>,
    Function(T? param)? onPressed,
    List<String>? Function(T? param) options = _null<List<String>>,
    OnOption? onOption,
    TextStyle? textStyle,
    bool textShadow = false,
    bool alignDropdownToText = true,
    double iconSize = StatusViewBar.defaultIconSize,
    Color? iconColor,
    double fontSize = StatusViewBar.defaultFontSize,
    Color? fontColor,
    Alignment alignment = Alignment.center,
    double? width,
    double? height = StatusViewBar.defaultHeight,
    String? buttonSemanticsLabel,
    String? optionsSemanticsLabel,
    VoidCallback? onInit,
    VoidCallback? onDispose,
  }) =>
      TextButtonTool(
        key: key,
        visibility: visibility,
        toolValue: toolValue,
        icon: icon,
        iconData: iconData,
        text: text,
        enabled: enabled,
        tooltip: tooltip,
        onPressed: onPressed,
        options: options,
        onOption: onOption,
        textStyle: textStyle,
        textShadow: textShadow,
        insetToolContainer: false,
        addPaddingForOptionsDropdown: false,
        alignDropdownToText: alignDropdownToText,
        iconSize: iconSize,
        iconColor: iconColor,
        animateDown: false,
        fontSize: fontSize,
        fontColor: fontColor,
        alignment: alignment,
        width: width,
        height: height,
        buttonSemanticsLabel: buttonSemanticsLabel,
        optionsSemanticsLabel: optionsSemanticsLabel,
        onInit: onInit,
        onDispose: onDispose,
      );

  const TextButtonTool({
    super.key,
    super.visibility = ToolVisibility.showOnCompact,
    super.toolValue,
    super.icon,
    super.iconData,
    super.text,
    super.enabled,
    super.tooltip,
    super.onPressed,
    this.options = _null<List<String>>,
    this.onOption,
    this.textStyle,
    this.textShadow = false,
    this.insetToolContainer = true,
    this.addPaddingForOptionsDropdown = true,
    this.alignDropdownToText = true,
    this.iconSize = 24,
    this.iconColor,
    this.animateDown = true,
    this.fontSize,
    this.fontColor,
    Alignment alignment = Alignment.center,
    double? width,
    double? height = 32,
    this.buttonSemanticsLabel,
    this.optionsSemanticsLabel,
    this.onInit,
    this.onDispose,
  })  : _alignment = alignment,
        _width = width,
        _height = height;

  @override
  Widget buildWidget(
    BuildContext context, {
    T? param,
    Axis orientation = Axis.horizontal,
    bool compact = false,
    bool floating = false,
    String? tooltip,
  }) {
    final theme = Theme.of(context);

    final text = super.text(param ?? toolValue?.value) ?? '';
    final iconData = super.iconData(param);
    final enabled = super.enabled(param);
    final options = this.options(param);

    final textStyle = this.textStyle ??
        TextStyle(
          color: fontColor ?? theme.colorScheme.onSurface,
          fontSize: fontSize,
          shadows: textShadow
              ? [
                  Shadow(
                    offset: const Offset(1, 1),
                    color: theme.colorScheme.shadow.withValues(alpha: 0.8),
                    blurRadius: 2,
                  ),
                  Shadow(
                    offset: const Offset(-1, -1),
                    color: Colors.white.withValues(alpha: 0.8),
                    blurRadius: 2,
                  )
                ]
              : null,
        );

    final buttonStyle = TextButton.styleFrom(
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.zero,
      ),
    );

    Widget label = Text(
      text,
      style: textStyle,
    );

    final icon = super.icon(param) ??
        (iconData != null
            ? Icon(
                iconData,
                size: iconSize,
                color: iconColor ??
                    textStyle.color ??
                    fontColor ??
                    theme.colorScheme.onSurface,
              )
            : null);

    final isButton = super.onPressed != null;
    final isButtonEnabled = isButton && enabled;
    final onPressed = isButtonEnabled //
        ? () => super.onPressed!(param)
        : null;

    return Semantics(
      label: buttonSemanticsLabel ?? tooltip,
      blockUserActions: true,
      button: isButton,
      enabled: isButtonEnabled,
      child: (options != null &&
              options.isNotEmpty &&
              onOption != null &&
              (options.length > 1 || options.first != text))
          ? _TextOptionsButton(
              label: label,
              options: options,
              onOption: onOption!,
              textStyle: textStyle,
              buttonStyle: buttonStyle,
              height: _height,
              icon: icon,
              iconSize: iconSize,
              paddingAdded: addPaddingForOptionsDropdown,
              alignDropdownToText: alignDropdownToText,
              animateDown: animateDown,
              optionsSemanticsLabel: optionsSemanticsLabel,
              onPressed: onPressed,
              onInit: onInit,
              onDispose: onDispose,
            )
          : _TextButton(
              label: label,
              buttonStyle: buttonStyle,
              icon: icon,
              onPressed: onPressed,
              height: _height,
              onInit: onInit,
              onDispose: onDispose,
            ),
    );
  }

  @override
  Decoration? getToolContainerDecoration(BuildContext context) {
    if (insetToolContainer) {
      final theme = Theme.of(context);

      final isDarkMode = theme.brightness == Brightness.dark;
      Color backgroundColor = isDarkMode
          ? theme.colorScheme.onSurface.withValues(alpha: 0.2)
          : theme.colorScheme.onSurface.withValues(alpha: 0.02);
      Color boxShadowColor = isDarkMode
          ? theme.colorScheme.shadow
          : theme.colorScheme.shadow.withValues(alpha: 0.4);

      return BoxDecoration(
        color: backgroundColor,
        border: Border.all(
          color: theme.colorScheme.onSurface.withValues(alpha: 0.2),
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(4),
        boxShadow: [
          BoxShadow(
            color: boxShadowColor,
          ),
          BoxShadow(
            color: theme.colorScheme.surface,
            blurRadius: 1.0,
            spreadRadius: -1.0,
            offset: const Offset(0, 1),
          ),
        ],
      );
    } else {
      return null;
    }
  }

  @override
  EdgeInsets? getToolContainerPadding(BuildContext context) {
    if (insetToolContainer &&
        addPaddingForOptionsDropdown &&
        onOption != null) {
      return const EdgeInsets.only(left: 8);
    } else {
      return null;
    }
  }

  @override
  Size? calcSize(
    BuildContext context, {
    double spacing = 0,
    Axis orientation = Axis.horizontal,
    T? param,
  }) {
    final w = _width ?? 0;
    final h = _height ?? 0;

    if (w > 0 && h > 0) {
      if (orientation == Axis.horizontal) {
        return Size(w + spacing, h);
      } else {
        return Size(w, h + spacing);
      }
    } else {
      final theme = Theme.of(context);

      final size = getTextSize(
        text(param ?? toolValue?.value) ?? '',
        style: textStyle ??
            TextStyle(
              color: fontColor ?? theme.colorScheme.onSurface,
              fontSize: fontSize,
            ),
      );
      final extraWidth = 24 /* extra padding for TextButton */ +
          (iconSize ?? 0) +
          (addPaddingForOptionsDropdown && onOption != null ? 24 : 0);

      if (orientation == Axis.horizontal) {
        return Size(size.width + extraWidth + spacing, size.height);
      } else {
        return Size(size.width + extraWidth, size.height + spacing);
      }
    }
  }

  static T? _null<T>(_) => null;
  static bool _enabled(_) => true;
}

class _TextButton extends StatefulWidget {
  final Widget label;

  final ButtonStyle? buttonStyle;

  final double? height;
  final Widget? icon;

  final VoidCallback? onPressed;

  final VoidCallback? onInit;
  final VoidCallback? onDispose;

  const _TextButton({
    required this.label,
    this.buttonStyle,
    this.height,
    this.icon,
    this.onPressed,
    this.onInit,
    this.onDispose,
  });

  @override
  State<_TextButton> createState() => _TextButtonState();
}

class _TextButtonState extends State<_TextButton> {
  @override
  void initState() {
    if (widget.onInit != null) {
      widget.onInit!();
    }
    super.initState();
  }

  @override
  void dispose() {
    if (widget.onDispose != null) {
      widget.onDispose!();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.icon != null) {
      return TextButton.icon(
        onPressed: widget.onPressed,
        icon: widget.icon,
        style: widget.buttonStyle,
        label: widget.label,
      );
    } else {
      return TextButton(
        onPressed: widget.onPressed,
        style: widget.buttonStyle,
        child: widget.label,
      );
    }
  }
}

class _TextOptionsButton extends StatefulWidget {
  final Widget label;
  final List<String> options;
  final OnOption onOption;

  final TextStyle? textStyle;
  final ButtonStyle? buttonStyle;

  final double? height;

  final Widget? icon;
  final double? iconSize;

  final bool paddingAdded;
  final bool alignDropdownToText;
  final bool animateDown;

  final String? optionsSemanticsLabel;

  final VoidCallback? onPressed;

  final VoidCallback? onInit;
  final VoidCallback? onDispose;

  const _TextOptionsButton({
    required this.label,
    required this.options,
    required this.onOption,
    this.textStyle,
    this.buttonStyle,
    this.height,
    this.icon,
    this.iconSize,
    this.paddingAdded = false,
    this.alignDropdownToText = false,
    this.animateDown = true,
    this.optionsSemanticsLabel,
    this.onPressed,
    this.onInit,
    this.onDispose,
  });

  @override
  _TextOptionsButtonState createState() => _TextOptionsButtonState();
}

class _TextOptionsButtonState extends State<_TextOptionsButton> {
  final MenuController _menuController = MenuController();

  bool isHovered = false;
  bool isOpen = false;

  late final double optionsMenuXOffset;

  @override
  void initState() {
    super.initState();

    if (widget.onInit != null) {
      widget.onInit!();
    }

    optionsMenuXOffset = (widget.alignDropdownToText
            ? (widget.paddingAdded ? -4 : -12)
            : (widget.paddingAdded ? 12 : 4)) -
        widget.options
            .map((option) => getTextSize(option, style: widget.textStyle).width)
            .reduce((value, element) => value > element ? value : element);
  }

  @override
  void dispose() {
    if (widget.onDispose != null) {
      widget.onDispose!();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    final label = Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        widget.label,
        const SizedBox(width: 4),
        MouseRegion(
          cursor: SystemMouseCursors.click,
          onEnter: (_) {
            setState(() {
              isHovered = true;
            });
          },
          onExit: (_) {
            setState(() {
              isHovered = false;
            });
          },
          child: GestureDetector(
            onTapDown: (_) {
              if (_menuController.isOpen) {
                _menuController.close();
              } else {
                _menuController.open();
              }
            },
            child: MenuAnchor(
              controller: _menuController,
              alignmentOffset: Offset(
                optionsMenuXOffset,
                (widget.height ?? 0) - (widget.iconSize ?? 0),
              ),
              onOpen: () => setState(() {
                isOpen = true;
              }),
              onClose: () => setState(() {
                isOpen = false;
              }),
              menuChildren: List<MenuItemButton>.generate(
                widget.options.length,
                (index) {
                  final option = widget.options[index];
                  return MenuItemButton(
                    style: TextButton.styleFrom(
                      minimumSize: widget.height != null
                          ? Size(0, widget.height! + 8)
                          : null,
                    ),
                    onPressed: () {
                      widget.onOption(option, index);
                    },
                    child: Text(
                      option,
                      style: widget.textStyle,
                    ),
                  );
                },
              ),
              child: Container(
                decoration: BoxDecoration(
                  color: isHovered
                      ? theme.colorScheme.onSurface.withValues(alpha: 0.1)
                      : null,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: AnimatedRotation(
                  turns: widget.animateDown
                      ? isOpen
                          ? 0.0
                          : 0.5
                      : isOpen
                          ? -0.5
                          : 0.0,
                  duration: const Duration(milliseconds: 200),
                  child: Icon(
                    Icons.arrow_drop_up,
                    size: (widget.iconSize ?? 0) + 4,
                    color: theme.colorScheme.onSurface,
                    semanticLabel: widget.optionsSemanticsLabel,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );

    return widget.icon != null
        ? TextButton.icon(
            onPressed: widget.onPressed,
            icon: widget.icon,
            style: widget.buttonStyle,
            label: label,
          )
        : TextButton(
            onPressed: widget.onPressed,
            style: widget.buttonStyle,
            child: label,
          );
  }
}

typedef OnOption = void Function(String option, int index);
