import 'package:flutter/material.dart';

import 'tool.dart';

class DividerTool extends Tool {
  final double length;
  final double width;

  const DividerTool({
    super.key,
    super.visibility = ToolVisibility.always,
    this.length = 32,
    this.width = 1,
  });

  @override
  Widget build(
    BuildContext context, {
    EdgeInsets margin = const EdgeInsets.all(0),
    Axis orientation = Axis.horizontal,
    bool compact = false,
    bool floating = false,
  }) {
    final theme = Theme.of(context);
    if (orientation == Axis.horizontal) {
      return SizedBox(
        height: length,
        child: VerticalDivider(
          color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
        ),
      );
    } else {
      return SizedBox(
        width: length,
        child: Divider(
          color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
        ),
      );
    }
  }

  @override
  Size? calcSize(
    BuildContext context, {
    double spacing = 0,
    Axis orientation = Axis.horizontal,
    dynamic param,
  }) {
    if (orientation == Axis.horizontal) {
      return Size(16, length);
    } else {
      return Size(length, 16);
    }
  }
}
