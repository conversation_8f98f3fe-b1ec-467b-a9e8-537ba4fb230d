import 'package:flutter/material.dart';

import 'tool.dart';

class StatusViewBar extends StatelessWidget {
  final List<Tool> items;

  final double height;
  final double spacing;

  final ToolsAlignment alignment;

  static const double defaultHeight = 36.0;
  static const double defaultIconSize = 24.0;
  static const double defaultFontSize = 12.0;

  const StatusViewBar({
    super.key,
    required this.items,
    this.height = StatusViewBar.defaultHeight,
    this.spacing = 0,
    this.alignment = ToolsAlignment.spaced,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    final alignment = this.alignment == ToolsAlignment.spaced
        ? MainAxisAlignment.spaceBetween
        : this.alignment == ToolsAlignment.center
            ? MainAxisAlignment.center
            : this.alignment == ToolsAlignment.right
                ? MainAxisAlignment.end
                : MainAxisAlignment.start;

    return Container(
      height: height,
      decoration: BoxDecoration(
        border: Border.all(
          color: theme.colorScheme.onSurface.withValues(alpha: 0.2),
          width: 1.0,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.2),
            blurRadius: 4.0,
            offset: const Offset(0, -2), // changes position of shadow
          ),
        ],
        color: theme.colorScheme.surface,
      ),
      child: Row(
        mainAxisAlignment: alignment,
        children: [
          for (final tool in items)
            tool.build(
              context,
              margin: EdgeInsets.only(
                left: spacing,
                right: spacing,
              ),
            ),
        ],
      ),
    );
  }
}
