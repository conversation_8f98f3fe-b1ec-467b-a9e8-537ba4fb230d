import 'package:flutter/material.dart';
import 'package:equatable/equatable.dart';

import 'tool.dart';
import '../utils/text.dart';

class StatusTextTool<T extends Equatable> extends ToolBuilder<T> {
  final EdgeInsets padding;

  final TextStyle? style;
  final bool embossed;

  @override
  Alignment? get alignment => _alignment;
  final Alignment _alignment;

  @override
  double? get width => _width;
  final double _width;

  @override
  double? get height => _height;
  final double _height;

  const StatusTextTool({
    super.key,
    super.visibility = ToolVisibility.showOnCompact,
    super.toolValue,
    super.text,
    super.tooltip,
    this.style,
    this.embossed = false,
    Alignment alignment = Alignment.center,
    double width = 0,
    double height = 32,
    this.padding = const EdgeInsets.fromLTRB(8, 2, 8, 2),
  })  : _alignment = alignment,
        _width = width,
        _height = height;

  @override
  Widget buildWidget(
    BuildContext context, {
    T? param,
    Axis orientation = Axis.horizontal,
    bool compact = false,
    bool floating = false,
    String? tooltip,
  }) {
    return Text(
      text(param) ?? '',
      style: style ?? Theme.of(context).textTheme.titleMedium,
    );
  }

  @override
  Size? calcSize(
    BuildContext context, {
    double spacing = 0,
    Axis orientation = Axis.horizontal,
    T? param,
  }) {
    if (_width > 0 && _height > 0) {
      if (orientation == Axis.horizontal) {
        return Size(_width + spacing, _height);
      } else {
        return Size(_width, _height + spacing);
      }
    } else {
      final size = getTextSize(
        text(toolValue?.value) ?? '',
        style: style ?? Theme.of(context).textTheme.titleMedium,
      );

      if (orientation == Axis.horizontal) {
        return Size(size.width + spacing, size.height);
      } else {
        return Size(size.width, size.height + spacing);
      }
    }
  }

  @override
  Decoration? getToolContainerDecoration(BuildContext context) {
    final theme = Theme.of(context);

    final isDarkMode = theme.brightness == Brightness.dark;
    Color backgroundColor = isDarkMode
        ? theme.colorScheme.onSurface.withValues(alpha: 0.2)
        : theme.colorScheme.onSurface.withValues(alpha: 0.02);
    Color boxShadowColor = isDarkMode
        ? theme.colorScheme.shadow
        : theme.colorScheme.shadow.withValues(alpha: 0.4);

    return BoxDecoration(
      color: backgroundColor,
      border: Border.all(
        color: theme.colorScheme.onSurface.withValues(alpha: 0.2),
        width: 1.0,
      ),
      borderRadius: BorderRadius.circular(4),
      boxShadow: [
        BoxShadow(
          color: boxShadowColor,
        ),
        BoxShadow(
          color: theme.colorScheme.surface,
          blurRadius: 1.0,
          spreadRadius: -1.0,
          offset: const Offset(0, 1),
        ),
      ],
    );
  }

  @override
  EdgeInsets? getToolContainerPadding(BuildContext context) => padding;
}
