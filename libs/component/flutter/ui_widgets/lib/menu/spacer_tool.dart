import 'package:flutter/material.dart';

import 'tool.dart';

class SpacerTool extends Tool {
  const SpacerTool({
    super.key,
    super.visibility = ToolVisibility.always,
  });

  @override
  Widget build(
    BuildContext context, {
    EdgeInsets margin = const EdgeInsets.all(0),
    Axis orientation = Axis.horizontal,
    bool compact = false,
    bool floating = false,
  }) {
    return const Spacer();
  }

  @override
  Size? calcSize(
    BuildContext context, {
    double spacing = 0,
    Axis orientation = Axis.horizontal,
    dynamic param,
  }) =>
      Size.zero;
}
