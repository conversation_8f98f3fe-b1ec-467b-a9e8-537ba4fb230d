import 'package:flutter/material.dart';
import 'package:equatable/equatable.dart';

import 'tool.dart';

class ButtonTool<T extends Equatable> extends ToolBuilder<T> {
  final Size size;

  final Color? iconColor;
  final Color? backgroundColor;
  final Color? pressedIconColor;
  final Color? pressedBackgroundColor;
  final ButtonStyle? style;

  final String? semanticLabel;

  final bool Function(T? param)? isPressed;

  static ButtonStyle styleFrom() => ElevatedButton.styleFrom(
        padding: const EdgeInsets.all(0),
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(5)),
        ),
      );

  const ButtonTool({
    super.key,
    super.visibility = ToolVisibility.floatOnCompact,
    super.toolValue,
    super.icon,
    super.iconData,
    super.tooltip,
    super.enabled,
    super.onPressed,
    this.size = const Size(40, 40),
    this.iconColor,
    this.backgroundColor,
    this.pressedIconColor,
    this.pressedBackgroundColor,
    this.style,
    this.semanticLabel,
    this.isPressed,
  });

  @override
  Widget buildWidget(
    BuildContext context, {
    T? param,
    Axis orientation = Axis.horizontal,
    bool compact = false,
    bool floating = false,
    String? tooltip,
  }) {
    final theme = Theme.of(context);
    final icon = super.icon(param);
    final iconData = super.iconData(param);
    final enabled = super.enabled(param);

    final isButton = super.onPressed != null;
    final isButtonEnabled = isButton && enabled;
    final onPressed = isButtonEnabled //
        ? () => super.onPressed!(param)
        : null;

    final isPressed = this.isPressed != null //
        ? this.isPressed!(param)
        : false;

    return Semantics(
      label: semanticLabel ?? tooltip,
      excludeSemantics: true,
      blockUserActions: true,
      button: isButton,
      enabled: isButtonEnabled,
      child: ElevatedButton(
        key: super.key,
        onPressed: onPressed,
        style: style == null
            ? ElevatedButton.styleFrom(
                padding: const EdgeInsets.all(0),
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.all(Radius.circular(5)),
                ),
                iconColor: isPressed
                    ? this.pressedIconColor ??
                        theme.buttonTheme.colorScheme?.onPrimary
                            .withValues(alpha: 0.5)
                    : this.iconColor,
                backgroundColor: isPressed
                    ? this.pressedBackgroundColor ??
                        theme.buttonTheme.colorScheme?.primary
                            .withValues(alpha: 0.5)
                    : this.backgroundColor,
                maximumSize: size,
                minimumSize: size,
              )
            : style!.copyWith(
                maximumSize: WidgetStateProperty.all(size),
                minimumSize: WidgetStateProperty.all(size),
              ),
        child: icon ?? Icon(iconData),
      ),
    );
  }

  @override
  Size? calcSize(
    BuildContext context, {
    double spacing = 0,
    Axis orientation = Axis.horizontal,
    T? param,
  }) =>
      orientation == Axis.horizontal
          ? Size(
              size.width,
              size.height,
            )
          : Size(
              size.width,
              size.height,
            );
}
