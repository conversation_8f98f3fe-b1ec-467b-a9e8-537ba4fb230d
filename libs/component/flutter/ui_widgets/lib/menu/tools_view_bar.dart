import 'package:flutter/material.dart';

import 'tool.dart';

class ToolsViewBar extends StatelessWidget {
  final List<Tool> tools;
  final double spacing;

  // Left and right margins of toolbar
  final double horizontalMargins;

  final bool isCompactView;

  final ToolsAlignment alignment;
  final bool float;
  final double? floatOffset;

  const ToolsViewBar({
    super.key,
    required this.tools,
    this.spacing = 4,
    this.horizontalMargins = 1,
    this.isCompactView = false,
    this.alignment = ToolsAlignment.center,
    this.float = false,
    this.floatOffset,
  });

  @override
  Widget build(BuildContext context) {
    if (float) {
      assert(isCompactView, 'Float can be set only for a compact view');

      return Wrap(
        direction: Axis.vertical,
        runSpacing: spacing,
        children: [
          ...tools
              .where(
                (tool) =>
                    tool.visibility == ToolVisibility.always ||
                    tool.visibility == ToolVisibility.floatOnCompact,
              )
              .map(
                (tool) => tool.build(
                  context,
                  margin: EdgeInsets.only(
                    top: spacing,
                    bottom: spacing,
                  ),
                  orientation: Axis.vertical,
                  compact: isCompactView,
                  floating: true,
                ),
              ),
          if (floatOffset != null) SizedBox(height: floatOffset),
        ],
      );
    } else {
      final toolsToShow = isCompactView
          ? tools.where(
              (tool) =>
                  tool.visibility == ToolVisibility.always ||
                  tool.visibility == ToolVisibility.showOnCompact,
            )
          : tools;

      final alignment = this.alignment == ToolsAlignment.spaced
          ? MainAxisAlignment.spaceBetween
          : this.alignment == ToolsAlignment.center
              ? MainAxisAlignment.center
              : this.alignment == ToolsAlignment.right
                  ? MainAxisAlignment.end
                  : MainAxisAlignment.start;

      return Padding(
        padding: EdgeInsets.symmetric(
          horizontal: horizontalMargins,
        ),
        child: Row(
          mainAxisAlignment: alignment,
          children: [
            for (final tool in toolsToShow)
              tool.build(
                context,
                margin: EdgeInsets.only(
                  left: spacing,
                  right: spacing,
                ),
                compact: isCompactView,
                floating: false,
              ),
          ],
        ),
      );
    }
  }
}
