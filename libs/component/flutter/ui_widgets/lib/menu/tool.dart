import 'package:flutter/material.dart';
import 'package:equatable/equatable.dart';

abstract class Tool<T> {
  final Key? key;

  final ToolVisibility visibility;

  const Tool({
    this.key,
    this.visibility = ToolVisibility.always,
  });

  Widget build(
    BuildContext context, {
    EdgeInsets margin = const EdgeInsets.all(0),
    Axis orientation = Axis.horizontal,
    bool compact = false,
    bool floating = false,
  });

  Size? calcSize(
    BuildContext context, {
    double spacing = 0,
    Axis orientation = Axis.horizontal,
    T? param,
  }) =>
      orientation == Axis.horizontal ? Size(spacing, 0) : Size(0, spacing);
}

abstract class ToolBuilder<T> extends Tool<T> {
  final ValueNotifier<T>? toolValue;

  final Widget? Function(T? param) icon;

  final IconData? Function(T? param) iconData;
  final String? Function(T? param) text;

  final bool Function(T? param) enabled;

  final bool Function(T? param) hidden;

  final String? Function(T? param) tooltip;

  final void Function(T? param)? onPressed;

  Alignment? get alignment => null;

  double? get width => null;
  double? get height => null;

  const ToolBuilder({
    super.key,
    super.visibility,
    this.toolValue,
    this.icon = _null<Widget>,
    this.iconData = _null<IconData>,
    this.text = _null<String>,
    this.enabled = _true,
    this.hidden = _false,
    this.tooltip = _null<String>,
    this.onPressed,
  });

  @override
  Widget build(
    BuildContext context, {
    EdgeInsets margin = const EdgeInsets.all(0),
    Axis orientation = Axis.horizontal,
    bool compact = false,
    bool floating = false,
  }) {
    if (toolValue == null) {
      return _buildContainer(
        context,
        null,
        margin: margin,
        orientation: orientation,
        compact: compact,
        floating: floating,
      );
    } else {
      return ValueListenableBuilder<T>(
        valueListenable: toolValue!,
        builder: (context, value, _) {
          return _buildContainer(
            context,
            value,
            margin: margin,
            orientation: orientation,
            compact: compact,
            floating: floating,
          );
        },
      );
    }
  }

  Widget _buildContainer(
    BuildContext context,
    T? param, {
    EdgeInsets? margin,
    Axis orientation = Axis.horizontal,
    bool compact = false,
    bool floating = false,
  }) {
    if (this.hidden(param)) {
      return const SizedBox.shrink();
    }
    final toolTip = tooltip(param);

    final tool = Container(
      width: width,
      height: height,
      alignment: alignment,
      margin: margin,
      padding: getToolContainerPadding(
        context,
      ),
      decoration: getToolContainerDecoration(
        context,
      ),
      child: buildWidget(
        context,
        param: param,
        orientation: orientation,
        compact: compact,
        floating: floating,
        tooltip: toolTip,
      ),
    );

    return toolTip != null
        ? Tooltip(
            message: toolTip,
            waitDuration: const Duration(seconds: 1),
            child: tool,
          )
        : tool;
  }

  Widget buildWidget(
    BuildContext context, {
    T? param,
    Axis orientation = Axis.horizontal,
    bool compact = false,
    bool floating = false,
    String? tooltip,
  });

  Decoration? getToolContainerDecoration(BuildContext context) => null;
  EdgeInsets? getToolContainerPadding(BuildContext context) => null;

  static T? _null<T>(_) => null;
  static bool _true(_) => true;
  static bool _false(_) => false;
}

class ToolGroup<T extends Equatable> extends ToolBuilder<T> {
  final List<ToolBuilder<T>> tools;

  const ToolGroup({
    super.key,
    super.toolValue,
    super.visibility = ToolVisibility.always,
    required this.tools,
  });

  @override
  Widget _buildContainer(
    BuildContext context,
    T? param, {
    EdgeInsets? margin,
    Axis orientation = Axis.horizontal,
    bool compact = false,
    bool floating = false,
  }) {
    return Wrap(
      direction: orientation,
      children: tools
          .where(
            (tool) =>
                tool.visibility == ToolVisibility.always ||
                (!compact &&
                    !floating &&
                    tool.visibility != ToolVisibility.onlyFloating) ||
                (compact &&
                    floating &&
                    tool.visibility == ToolVisibility.floatOnCompact) ||
                (compact &&
                    !floating &&
                    tool.visibility == ToolVisibility.showOnCompact) ||
                (!compact &&
                    floating &&
                    tool.visibility == ToolVisibility.onlyFloating),
          )
          .map(
            (tool) => tool._buildContainer(
              context,
              param,
              margin: margin,
              orientation: orientation,
              compact: compact,
            ),
          )
          .toList(),
    );
  }

  @override
  Widget buildWidget(
    BuildContext context, {
    T? param,
    Axis orientation = Axis.horizontal,
    bool compact = false,
    bool floating = false,
    String? tooltip,
  }) {
    throw UnimplementedError();
  }

  @override
  Size? calcSize(
    BuildContext context, {
    double spacing = 0,
    Axis orientation = Axis.horizontal,
    T? param,
  }) =>
      tools.fold(
        Size.zero,
        (size, tool) {
          final toolSize = tool.calcSize(
            context,
            spacing: spacing,
            orientation: orientation,
            param: toolValue?.value,
          );
          if (orientation == Axis.horizontal) {
            return Size(size!.width + toolSize!.width, size.height);
          } else {
            return Size(size!.width, size.height + toolSize!.height);
          }
        },
      );
}

enum ToolVisibility {
  always,
  showOnCompact,
  floatOnCompact,
  onlyFloating,
}

enum ToolsAlignment {
  spaced,
  left,
  right,
  center,
}
