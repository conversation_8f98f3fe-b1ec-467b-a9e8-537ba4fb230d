import 'package:flutter/material.dart';

/// A custom AppBar that allows for the display of tools
/// in the AppBar on the left or right as actions as well
/// as support for a search bar. The AppBar is responsive
/// and the tools and/or search bar will be rendered as a
/// popup menu if the AppBar size cannot accomodate the
/// tools and/or search bar widgets.
class ToolsAppBar extends AppBar {
  ToolsAppBar({
    super.key,
    required BuildContext context,

    /// Customizable AppBar attributes
    String? title,
    double leftIndent = 0.0,
    ToolBar leftToolBar = const ToolBar(),
    double rightIndent = 0.0,
    ToolBar rightToolBar = const ToolBar(),

    /// AppBar attributes
    super.leading,
    super.automaticallyImplyLeading,
    super.actions,
    super.bottom,
    super.elevation,
    super.scrolledUnderElevation,
    super.notificationPredicate,
    super.shadowColor,
    super.surfaceTintColor,
    super.shape,
    super.backgroundColor,
    super.foregroundColor,
    super.iconTheme,
    super.actionsIconTheme,
    super.primary,
    super.centerTitle,
    super.excludeHeaderSemantics,
    super.titleSpacing,
    super.toolbarOpacity,
    super.bottomOpacity,
    super.toolbarHeight,
    super.leadingWidth,
    super.toolbarTextStyle,
    super.titleTextStyle,
    super.systemOverlayStyle,
    super.forceMaterialTransparency,
    super.clipBehavior,

    /// AppBar widgets that are
    /// overridden by the tools

    // Widget? title,
    // Widget? flexibleSpace,
  }) : super(
          title: _titleWidget(
            context,
            title,
            titleTextStyle,
            toolbarTextStyle,
            foregroundColor,
          ),
          // create toolbars for the left and right
          // within the flexible space widget
          flexibleSpace: _flexibleSpaceWidget(
            leftIndent,
            leftToolBar,
            rightIndent,
            rightToolBar,
          ),
        );
}

Widget? _titleWidget(
  BuildContext context,
  String? title,
  TextStyle? titleTextStyle,
  TextStyle? toolbarTextStyle,
  Color? foregroundColor,
) {
  final theme = Theme.of(context);

  // Size? titleTextSize;
  late final Widget? titleWidget;
  if (title != null) {
    final style = titleTextStyle ??
        toolbarTextStyle ??
        AppBarTheme.of(context).titleTextStyle ??
        AppBarTheme.of(context).toolbarTextStyle ??
        theme.textTheme.titleLarge;

    titleWidget = Text(
      title,
      style: style?.copyWith(
        color: foregroundColor ?? theme.colorScheme.onSurface,
        fontWeight: FontWeight.bold,
      ),
    );
  }
  return titleWidget;
}

Widget? _flexibleSpaceWidget(
  double leftIndent,
  ToolBar leftToolBar,
  double rightIndent,
  ToolBar rightToolBar,
) {
  late final Widget? flexibleSpaceWidget;
  if (leftToolBar.isNotEmpty || rightToolBar.isNotEmpty) {
    flexibleSpaceWidget = LayoutBuilder(
      builder: (context, constraints) {
        List<Widget> toolBarWidgets = [];
        if (leftToolBar.isNotEmpty) {
          if (leftIndent > 0) {
            toolBarWidgets.add(
              SizedBox(width: leftIndent),
            );
          }
          for (final tool in leftToolBar.tools) {
            toolBarWidgets.add(
              tool.buildToolBarWidget(context),
            );
          }
        }
        toolBarWidgets.add(
          const Spacer(),
        );
        if (rightToolBar.isNotEmpty) {
          for (final tool in rightToolBar.tools) {
            toolBarWidgets.add(
              tool.buildToolBarWidget(context),
            );
            if (rightIndent > 0) {
              toolBarWidgets.add(
                SizedBox(width: rightIndent),
              );
            }
          }
        }

        return Padding(
          padding: const EdgeInsets.fromLTRB(8.0, 8.0, 8.0, 8.0),
          child: Row(
            children: toolBarWidgets,
          ),
        );
      },
    );
  } else {
    flexibleSpaceWidget = null;
  }
  return flexibleSpaceWidget;
}

class ToolBar {
  final List<ToolWidget> tools;

  bool get isEmpty => tools.isEmpty;
  bool get isNotEmpty => tools.isNotEmpty;

  double get width {
    double totalWidth = 0.0;
    for (final tool in tools) {
      totalWidth += tool.width;
    }
    return totalWidth;
  }

  const ToolBar({
    this.tools = const [],
  });
}

abstract class ToolWidget {
  /// The width of the widget
  /// used to calculate the width
  /// of the toolbar to determine
  /// if the widget overflows and
  /// should be rendered as a popup
  /// menu item
  double get width;

  /// Build widget for the toolbar
  Widget buildToolBarWidget(BuildContext context);

  /// Build widget for the popup menu when
  /// the toolbar widget is not visible
  Widget buildPopupMenuItem(BuildContext context);

  const ToolWidget();
}

class ToolIconButton extends ToolWidget {
  final String title;
  final String? subTitle;
  final String? tooltip;
  final String? semanticLabel;

  final Widget? icon;
  final IconData? iconData;

  final ActionCallback? onPressed;
  final bool enabled;

  const ToolIconButton({
    required this.title,
    this.subTitle,
    this.tooltip,
    this.semanticLabel,
    this.icon,
    this.iconData,
    this.onPressed,
    this.enabled = true,
  }) : assert(
          icon != null || iconData != null,
          'icon or iconData is required',
        );

  @override
  double get width => 40.0;

  @override
  Widget buildToolBarWidget(BuildContext context) {
    final iconButton = IconButton(
      icon: icon ?? Icon(iconData),
      onPressed:
          enabled && onPressed != null ? () => onPressed!(context) : null,
    );

    return Semantics(
      label: semanticLabel ?? tooltip,
      excludeSemantics: true,
      blockUserActions: true,
      child: tooltip != null
          ? Tooltip(
              message: tooltip,
              waitDuration: const Duration(seconds: 1),
              child: iconButton,
            )
          : iconButton,
    );
  }

  @override
  Widget buildPopupMenuItem(BuildContext context) {
    // TODO: implement buildPopupMenuItem
    throw UnimplementedError();
  }
}

enum ToolType {
  iconButton,
  searchBar,
}

typedef ActionCallback = void Function(BuildContext context);
