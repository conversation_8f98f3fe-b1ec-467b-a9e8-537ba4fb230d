import 'package:flutter/material.dart';

class AvatarButton extends StatefulWidget {
  /// Properties passed along to the [CircleAvatar] widget.
  final Color? backgroundColor;
  final Color? foregroundColor;
  final ImageProvider? backgroundImage;
  final ImageProvider? foregroundImage;
  final ImageErrorListener? onBackgroundImageError;
  final ImageErrorListener? onForegroundImageError;
  final double? radius;
  final Function(bool)? onExpanded;
  final Widget? child;

  const AvatarButton({
    super.key,
    this.backgroundColor,
    this.backgroundImage,
    this.foregroundImage,
    this.onBackgroundImageError,
    this.onForegroundImageError,
    this.foregroundColor,
    this.radius,
    this.onExpanded,
    this.child,
  });

  @override
  State<AvatarButton> createState() => _AvatarButtonState();
}

class _AvatarButtonState extends State<AvatarButton> {
  bool expanded = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          SizedBox(
            width: widget.radius,
            height: widget.radius,
            child: Stack(
              fit: StackFit.expand,
              children: <Widget>[
                Padding(
                  padding: const EdgeInsets.all(11.0),
                  child: Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: theme.iconTheme.color ??
                            theme.colorScheme.inverseSurface,
                        width: 1.0,
                      ),
                    ),
                    child: CircleAvatar(
                      backgroundColor: widget.backgroundColor,
                      backgroundImage: widget.backgroundImage,
                      foregroundImage: widget.foregroundImage,
                      onBackgroundImageError: widget.onBackgroundImageError,
                      onForegroundImageError: widget.onForegroundImageError,
                      foregroundColor: widget.foregroundColor,
                      radius: widget.radius,
                      child: widget.child,
                    ),
                  ),
                ),
                Align(
                  alignment: Alignment.bottomRight,
                  child: AnimatedRotation(
                    turns: expanded ? -0.5 : 0.0,
                    duration: const Duration(milliseconds: 200),
                    child: const Icon(
                      Icons.arrow_drop_down,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(11.0),
                  child: IconButton(
                    onPressed: _onSelected,
                    icon: const Icon(null),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _onSelected() {
    final expanded = !this.expanded;
    widget.onExpanded?.call(expanded);

    setState(
      () {
        this.expanded = expanded;
      },
    );
  }
}
