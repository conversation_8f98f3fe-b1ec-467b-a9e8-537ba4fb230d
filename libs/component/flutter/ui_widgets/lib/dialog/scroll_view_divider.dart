import 'package:flutter/material.dart';

class ScrollViewDivider extends StatelessWidget {
  final ScrollViewDividerPosition position;

  /// Currently only vertical is supported.
  final ScrollViewDirection type = ScrollViewDirection.vertical;

  final ScrollMetrics? metrics;

  // Divider properties
  final double? height;
  final double? thickness;
  final double? indent;
  final double? endIndent;
  final Color? color;

  const ScrollViewDivider({
    super.key,
    required this.position,
    this.metrics,
    this.height,
    this.thickness,
    this.indent,
    this.endIndent,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    if (metrics != null) {
      if (metrics!.extentBefore > 0.0 &&
          position == ScrollViewDividerPosition.start) {
        return Row(
          children: [
            Expanded(child: _buildDivider()),
            Icon(
              Icons.keyboard_arrow_up,
              color: color,
            ),
            Expanded(child: _buildDivider()),
          ],
        );
      }
      if (metrics!.extentAfter > 0.0 &&
          position == ScrollViewDividerPosition.end) {
        return Row(
          children: [
            Expanded(child: _buildDivider()),
            Icon(
              Icons.keyboard_arrow_down,
              color: color,
            ),
            Expanded(child: _buildDivider()),
          ],
        );
      }
    }
    return SizedBox(
      height: 24,
      child: _buildDivider(),
    );
  }

  Widget _buildDivider() {
    return Divider(
      height: height,
      thickness: thickness,
      indent: indent,
      endIndent: endIndent,
      color: color?.withValues(alpha: 0.5),
    );
  }
}

enum ScrollViewDirection {
  horizontal,
  vertical,
}

enum ScrollViewDividerPosition {
  start,
  end,
}
