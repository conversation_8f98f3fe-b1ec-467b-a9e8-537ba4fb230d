import 'dart:io';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:logging/logging.dart' as logging;

import '../view/display_area_size.dart';
import 'scroll_view_divider.dart';

class DialogForm extends StatefulWidget {
  final Key? formKey;

  /// The total of the form display area.
  final double formViewHeight;

  /// The height of the non-scrollable area for
  /// the given [formViewHeight]. This would be the
  /// height of the title and actions as well as
  /// any padding or margins.
  final double nonScrollableHeight;

  /// Wrap the form with a [DisplayAreaSizeProvider].
  ///
  /// The dialog form requires the [DisplayAreaSize]
  /// to calculate the height of the scroll view
  /// port. This value should be provided from a
  /// parent widget that has access to the size of
  /// the display area. If the form is dynamically
  /// sized then the provider needs to set at a
  /// higher level in the widget tree and this
  /// value should be set to false.
  final bool wrapWithDisplayAreaSizeProvider;

  /// If the body of the form is not scrollable
  /// then it is wrapped in a [SingleChildScrollView].
  /// This is required to ensure the form height
  /// is constrained to the [formViewHeight] and
  /// the scroll view port is correctly calculated
  /// when container size changes dynamically.
  /// Set this value to true if the body wraps
  /// a scrollable widget otherwise you will get
  /// a render error.
  final bool isBodyScrollable;

  final String title;

  final Widget body;
  final Widget actions;

  final VoidCallback? onCloseDialog;

  const DialogForm({
    super.key,
    this.formKey,
    this.wrapWithDisplayAreaSizeProvider = true,
    this.isBodyScrollable = false,
    this.formViewHeight = 0.0,
    this.nonScrollableHeight = 122.0,
    required this.title,
    required this.body,
    required this.actions,
    this.onCloseDialog,
  });

  @override
  State<StatefulWidget> createState() => _DialogFormState();
}

class _DialogFormState extends State<DialogForm> {
  ScrollMetrics? scrollMetrics;

  late final double _formInputHeight;

  @override
  void initState() {
    super.initState();

    /// Calculate the height of the input fields
    /// that should scroll if the overall form
    /// display area < [formViewHeight].
    _formInputHeight = max(
      widget.formViewHeight -
          widget.nonScrollableHeight -
          (widget.onCloseDialog == null ? 0.0 : 10.0),
      0,
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.wrapWithDisplayAreaSizeProvider) {
      return DisplayAreaSizeProvider(
        minHeight: widget.formViewHeight - _formInputHeight,
        child: Builder(builder: _buildForm),
      );
    } else {
      return _buildForm(context);
    }
  }

  Widget _buildForm(BuildContext context) {
    final theme = Theme.of(context);

    // get the constraints of the total display area
    // available for the form from the parent widget
    final pageSize = Provider.of<DisplayAreaSize>(context);
    if (kDebugMode && _debugLayout == 'true') {
      _logger.finest(
        'Form will be constrained within page size of controlling parent: '
        '${pageSize.width}x${pageSize.height}',
      );
    }

    double? scrollViewPortHeight;
    if (pageSize.height <= widget.formViewHeight) {
      scrollViewPortHeight = //
          _formInputHeight - (widget.formViewHeight - pageSize.height);
      if (scrollViewPortHeight < 0) {
        scrollViewPortHeight = null;
      }
    }
    if (kDebugMode && _debugLayout == 'true') {
      _logger.finest('Scroll view port height: $scrollViewPortHeight');
    }

    return Form(
      key: widget.formKey,
      child: Column(
        children: [
          Row(
            children: [
              const Expanded(
                child: SizedBox.shrink(),
              ),
              Text(
                widget.title,
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              Expanded(
                child: widget.onCloseDialog == null
                    ? const SizedBox.shrink()
                    : Align(
                        alignment: Alignment.centerRight,
                        child: IconButton(
                          onPressed: widget.onCloseDialog,
                          alignment: Alignment.center,
                          icon: const Icon(Icons.close),
                        ),
                      ),
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: ScrollViewDivider(
              metrics: scrollMetrics,
              position: ScrollViewDividerPosition.start,
              color: theme.colorScheme.outline,
            ),
          ),
          SizedBox(
            height: scrollViewPortHeight,
            child: NotificationListener<ScrollMetricsNotification>(
              onNotification: (ScrollMetricsNotification notification) {
                setState(
                  () => scrollMetrics = notification.metrics,
                );
                return true;
              },
              child: widget.isBodyScrollable
                  ? widget.body
                  : SingleChildScrollView(
                      child: widget.body,
                    ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: ScrollViewDivider(
              metrics: scrollMetrics,
              position: ScrollViewDividerPosition.end,
              color: theme.colorScheme.outline,
            ),
          ),
          Padding(
            padding: const EdgeInsets.fromLTRB(8.0, 0.0, 8.0, 0.0),
            child: widget.actions,
          ),
        ],
      ),
    );
  }
}

// declare outside the widget class so it can remain constant
final logging.Logger _logger = logging.Logger('DialogForm');

// retrieve enable form view size debugging flag
final String? _debugLayout =
    !kIsWeb ? Platform.environment['DEBUG_DIALOG_FORM_LAYOUT'] : null;
