import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

import '../view/markdown.dart';

/// A common alert dialog function that provides a reusable way to show
/// alert dialogs with customizable icon, text constraints, markdown content,
/// and flexible button options.
///
/// ## Usage Examples:
///
/// ### Basic usage with auto-sizing (no constraints):
/// ```dart
/// ui.AlertDialogHelper.showWarning(
///   context: context,
///   markdownText: '**Warning!** This action cannot be undone.',
///   onConfirm: () => print('Confirmed'),
///   // Dialog will automatically size to fit content
/// );
/// ```
///
/// ### Centered text with custom constraints:
/// ```dart
/// ui.AlertDialogHelper.showInfo(
///   context: context,
///   markdownText: '**Centered Information**\n\nThis text is centered.',
///   textAlignment: TextAlign.center,
///   textConstraints: BoxConstraints(
///     minWidth: 400.0,
///     maxWidth: 600.0,
///     minHeight: 200.0,
///     maxHeight: 500.0,
///   ),
/// );
/// ```
///
/// ### Full width dialog that fills available space:
/// ```dart
/// ui.AlertDialogHelper.showWarning(
///   context: context,
///   markdownText: '# Full Width Dialog\n\nContent that fills the space.',
///   textConstraints: BoxConstraints(
///     minWidth: 500.0,
///     maxWidth: 800.0,
///     minHeight: 300.0,
///     maxHeight: 600.0,
///   ),
///   textAlignment: TextAlign.center,
/// );
/// ```
///
/// ### Compact dialog with minimal space:
/// ```dart
/// ui.AlertDialogHelper.showError(
///   context: context,
///   markdownText: '**Compact Error**\n\nMinimal constraints.',
///   textConstraints: BoxConstraints(
///     minWidth: 200.0,
///     maxWidth: 300.0,
///     minHeight: 50.0,
///     maxHeight: 150.0,
///   ),
/// );
/// ```
class AlertDialogHelper {
  /// Shows an alert dialog with the specified parameters.
  ///
  /// [context] - The build context
  /// [icon] - The icon to display in the dialog title. Defaults to Icons.warning
  /// [iconColor] - The color of the icon. Defaults to Colors.amber
  /// [iconSize] - The size of the icon. Defaults to 64.0
  /// [markdownText] - The markdown text content to display
  /// [textConstraints] - Optional constraints for the text content area
  /// [textAlignment] - Alignment of the text content. Defaults to TextAlign.start
  /// [cancelText] - Text for the cancel button. If null, no cancel button is shown
  /// [confirmText] - Text for the confirm button. If null, no confirm button is shown
  /// [dismissText] - Text for the dismiss button. Used when both cancelText and confirmText are null
  /// [onCancel] - Callback when cancel button is pressed
  /// [onConfirm] - Callback when confirm button is pressed
  /// [onDismiss] - Callback when dismiss button is pressed
  /// [barrierDismissible] - Whether the dialog can be dismissed by tapping outside
  static Future<T?> show<T>({
    required BuildContext context,
    IconData? icon,
    Color? iconColor,
    double? iconSize,
    required String markdownText,
    BoxConstraints? textConstraints,
    TextAlign textAlignment = TextAlign.start,
    String? cancelText,
    String? confirmText,
    String? dismissText,
    VoidCallback? onCancel,
    VoidCallback? onConfirm,
    VoidCallback? onDismiss,
    bool barrierDismissible = true,
  }) {
    final theme = Theme.of(context);
    final style = theme.textTheme.bodyMedium;

    // Determine which buttons to show
    final hasCancelButton = cancelText != null;
    final hasConfirmButton = confirmText != null;
    final hasDismissButton = !hasCancelButton && !hasConfirmButton;

    // Use dismissText if provided, otherwise default to "Dismiss"
    final finalDismissText = dismissText ?? 'Dismiss';

    // Create the markdown widget
    final markdownWidget = MarkdownBody(
      styleSheet: createMarkDownStyleSheet(context).copyWith(
        strong: style?.copyWith(fontWeight: FontWeight.bold),
        em: style?.copyWith(fontStyle: FontStyle.italic),
        h1: style?.copyWith(
          fontSize: 24.0,
          fontWeight: FontWeight.bold,
        ),
        h2: style?.copyWith(
          fontSize: 20.0,
          fontWeight: FontWeight.bold,
        ),
        h3: style?.copyWith(
          fontSize: 18.0,
          fontWeight: FontWeight.bold,
        ),
      ),
      data: markdownText,
    );

    final containerWidget = Container(
      alignment: textAlignment == TextAlign.center
          ? Alignment.center
          : textAlignment == TextAlign.end
              ? Alignment.centerRight
              : Alignment.centerLeft,
      child: markdownWidget,
    );

    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Icon(
            icon ?? Icons.warning,
            color: iconColor ?? Colors.amber,
            size: iconSize ?? 64.0,
          ),
          content: SingleChildScrollView(
            child: textConstraints != null
                ? ConstrainedBox(
                    constraints: textConstraints,
                    child: containerWidget,
                  )
                : containerWidget,
          ),
          actions: <Widget>[
            if (hasCancelButton)
              TextButton(
                child: Text(cancelText),
                onPressed: () {
                  Navigator.of(dialogContext).pop();
                  onCancel?.call();
                },
              ),
            if (hasConfirmButton)
              TextButton(
                child: Text(confirmText),
                onPressed: () {
                  Navigator.of(dialogContext).pop();
                  onConfirm?.call();
                },
              ),
            if (hasDismissButton)
              TextButton(
                child: Text(finalDismissText),
                onPressed: () {
                  Navigator.of(dialogContext).pop();
                  onDismiss?.call();
                },
              ),
          ],
        );
      },
    );
  }

  /// Shows a simple alert dialog with just a dismiss button.
  ///
  /// [context] - The build context
  /// [markdownText] - The markdown text content to display
  /// [icon] - The icon to display. Defaults to Icons.info
  /// [iconColor] - The color of the icon. Defaults to Colors.blue
  /// [dismissText] - Text for the dismiss button. Defaults to "OK"
  /// [onDismiss] - Callback when dismiss button is pressed
  /// [barrierDismissible] - Whether the dialog can be dismissed by tapping outside
  /// [textConstraints] - Optional constraints for the text content area
  /// [textAlignment] - Alignment of the text content. Defaults to TextAlign.start
  static Future<void> showInfo({
    required BuildContext context,
    required String markdownText,
    IconData? icon,
    Color? iconColor,
    String? dismissText,
    VoidCallback? onDismiss,
    bool barrierDismissible = true,
    BoxConstraints? textConstraints,
    TextAlign textAlignment = TextAlign.start,
  }) {
    return show(
      context: context,
      icon: icon ?? Icons.info,
      iconColor: iconColor ?? Colors.blue,
      markdownText: markdownText,
      dismissText: dismissText ?? 'OK',
      onDismiss: onDismiss,
      barrierDismissible: barrierDismissible,
      textConstraints: textConstraints,
      textAlignment: textAlignment,
    );
  }

  /// Shows a warning alert dialog with cancel and confirm buttons.
  ///
  /// [context] - The build context
  /// [markdownText] - The markdown text content to display
  /// [icon] - The icon to display. Defaults to Icons.warning
  /// [iconColor] - The color of the icon. Defaults to Colors.amber
  /// [cancelText] - Text for the cancel button. Defaults to "Cancel"
  /// [confirmText] - Text for the confirm button. Defaults to "OK"
  /// [onCancel] - Callback when cancel button is pressed
  /// [onConfirm] - Callback when confirm button is pressed
  /// [barrierDismissible] - Whether the dialog can be dismissed by tapping outside
  /// [textConstraints] - Optional constraints for the text content area
  /// [textAlignment] - Alignment of the text content. Defaults to TextAlign.start
  static Future<void> showWarning({
    required BuildContext context,
    required String markdownText,
    IconData? icon,
    Color? iconColor,
    String? cancelText,
    String? confirmText,
    VoidCallback? onCancel,
    VoidCallback? onConfirm,
    bool barrierDismissible = true,
    BoxConstraints? textConstraints,
    TextAlign textAlignment = TextAlign.start,
  }) {
    return show(
      context: context,
      icon: icon ?? Icons.warning,
      iconColor: iconColor ?? Colors.amber,
      markdownText: markdownText,
      cancelText: cancelText ?? 'Cancel',
      confirmText: confirmText ?? 'OK',
      onCancel: onCancel,
      onConfirm: onConfirm,
      barrierDismissible: barrierDismissible,
      textConstraints: textConstraints,
      textAlignment: textAlignment,
    );
  }

  /// Shows an error alert dialog with just a dismiss button.
  ///
  /// [context] - The build context
  /// [markdownText] - The markdown text content to display
  /// [icon] - The icon to display. Defaults to Icons.error
  /// [iconColor] - The color of the icon. Defaults to Colors.red
  /// [dismissText] - Text for the dismiss button. Defaults to "OK"
  /// [onDismiss] - Callback when dismiss button is pressed
  /// [barrierDismissible] - Whether the dialog can be dismissed by tapping outside
  /// [textConstraints] - Optional constraints for the text content area
  /// [textAlignment] - Alignment of the text content. Defaults to TextAlign.start
  static Future<void> showError({
    required BuildContext context,
    required String markdownText,
    IconData? icon,
    Color? iconColor,
    String? dismissText,
    VoidCallback? onDismiss,
    bool barrierDismissible = true,
    BoxConstraints? textConstraints,
    TextAlign textAlignment = TextAlign.start,
  }) {
    return show(
      context: context,
      icon: icon ?? Icons.error,
      iconColor: iconColor ?? Colors.red,
      markdownText: markdownText,
      dismissText: dismissText ?? 'OK',
      onDismiss: onDismiss,
      barrierDismissible: barrierDismissible,
      textConstraints: textConstraints,
      textAlignment: textAlignment,
    );
  }
}
