import 'package:flutter/material.dart';

class ElevatedIconButton extends StatelessWidget {
  final Widget label;
  final IconData icon;
  final VoidCallback? onPressed;
  final VoidCallback? onLongPress;
  final ValueChanged<bool>? onHover;
  final ValueChanged<bool>? onFocusChange;
  final ButtonStyle? style;
  final FocusNode? focusNode;
  final bool? autofocus;
  final Clip? clipBehavior;
  final IconAlignment iconAlignment;

  final double iconSize;
  final bool isLoading;

  const ElevatedIconButton({
    super.key,
    required this.label,
    required this.icon,
    this.onPressed,
    this.onLongPress,
    this.onHover,
    this.onFocusChange,
    this.style,
    this.focusNode,
    this.autofocus,
    this.clipBehavior,
    this.iconAlignment = IconAlignment.end,
    this.isLoading = false,
    this.iconSize = 24.0,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    final loadingIconColor = style?.iconColor?.resolve({}) ??
        (onPressed != null ? theme.colorScheme.onPrimary : theme.disabledColor);

    return ElevatedButton.icon(
      label: label,
      onPressed: onPressed,
      onLongPress: onLongPress,
      onHover: onHover,
      onFocusChange: onFocusChange,
      style: style,
      focusNode: focusNode,
      autofocus: autofocus,
      clipBehavior: clipBehavior,
      iconAlignment: iconAlignment,
      icon: SizedBox(
        height: iconSize,
        width: iconSize,
        child: isLoading
            ? Padding(
                padding: const EdgeInsets.all(5.0),
                child: CircularProgressIndicator(
                  strokeWidth: 2.0,
                  color: loadingIconColor,
                ),
              )
            : Icon(
                icon,
                size: iconSize,
              ),
      ),
    );
  }
}
