import 'package:flutter/material.dart';

abstract class MultiFormController extends StatefulWidget {
  /// This callback is used to build the form container
  /// and it allows you to provide different constrainted
  /// containers for the forms being controlled.
  BuildContainerCallack? get buildContainer;

  const MultiFormController({super.key});

  @override
  MultiFormControllerState createState();
}

abstract class MultiFormControllerState<T extends MultiFormController>
    extends State<T> {
  /// This method is used to build the container for the form
  Widget buildContainer(
    Widget child,
    double width,
    double height,
  ) {
    if (widget.buildContainer != null) {
      return widget.buildContainer!(
        child,
        width,
        height,
      );
    } else {
      return child;
    }
  }
}

typedef BuildContainerCallack = Widget Function(
  Widget child,
  double width,
  double height,
);
