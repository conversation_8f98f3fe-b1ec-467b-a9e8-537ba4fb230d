import 'package:flutter/material.dart';

class TextLink extends StatelessWidget {
  final String text;
  final bool enabled;

  final VoidCallback? onPressed;

  final EdgeInsetsGeometry padding;

  const TextLink({
    super.key,
    required this.text,
    this.onPressed,
    this.enabled = true,
    this.padding = const EdgeInsets.only(top: 2, bottom: 8),
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.only(top: 4),
      child: TextButton(
        onPressed: enabled ? onPressed : null,
        style: TextButton.styleFrom(
          overlayColor: Colors.transparent,
        ),
        child: Text(
          text,
          style: theme.textTheme.labelLarge?.copyWith(
            color: enabled
                ? theme.colorScheme.primary
                : theme.colorScheme.onSurface.withValues(alpha: 0.5),
            decoration: TextDecoration.underline,
          ),
        ),
      ),
    );
  }
}
