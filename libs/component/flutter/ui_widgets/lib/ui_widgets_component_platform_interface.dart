import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'ui_widgets_component_method_channel.dart';

abstract class UiWidgetsComponentPlatform extends PlatformInterface {
  /// Constructs a UiWidgetsComponentPlatform.
  UiWidgetsComponentPlatform() : super(token: _token);

  static final Object _token = Object();

  static UiWidgetsComponentPlatform _instance = MethodChannelUiWidgetsComponent();

  /// The default instance of [UiWidgetsComponentPlatform] to use.
  ///
  /// Defaults to [MethodChannelUiWidgetsComponent].
  static UiWidgetsComponentPlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [UiWidgetsComponentPlatform] when
  /// they register themselves.
  static set instance(UiWidgetsComponentPlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  Future<String?> getPlatformVersion() {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }
}
