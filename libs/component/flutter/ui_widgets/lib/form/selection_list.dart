import 'package:flutter/material.dart';

class TrailingIcon<T> {
  final IconData? icon;
  final Widget? iconWidget;
  final Color? color;
  final String? tooltip;

  /// Called with the value of the list item when tapped
  final ValueChanged<T>? onTap;

  /// For backward compatibility: if provided, called with no arguments
  final VoidCallback? onTapVoid;

  const TrailingIcon({
    this.icon,
    this.iconWidget,
    this.color,
    this.tooltip,
    this.onTap,
    this.onTapVoid,
  });
}

class SelectedListItem<T> {
  final T value;
  final String title;
  final String? subtitle;

  final String? badge;
  final Color? badgeColor;

  final IconData? icon;
  final Color? iconColor;
  final Widget? iconWidget;

  final bool enabled;

  // New: List of trailing icons
  final List<TrailingIcon<T>>? trailingIcons;

  const SelectedListItem({
    required this.value,
    required this.title,
    this.subtitle,
    this.badge,
    this.badgeColor,
    this.icon,
    this.iconColor,
    this.iconWidget,
    this.enabled = true,
    this.trailingIcons,
  });
}

class SelectionList<T> extends StatefulWidget {
  /// List of items to display
  final List<SelectedListItem<T>> items;

  /// Currently selected item
  final T? selectedValue;

  /// Callback when selection changes
  final ValueChanged<T>? onSelectionChanged;

  /// Whether the entire list is enabled
  final bool enabled;

  /// Styling properties
  final double itemHeight;
  final double borderRadius;
  final EdgeInsets itemPadding;
  final EdgeInsets listPadding;
  final double spacing;

  /// Colors
  final Color? selectedBorderColor;
  final Color? selectedBackgroundColor;
  final Color? hoverBorderColor;
  final Color? hoverBackgroundColor;
  final Color? defaultBorderColor;
  final Color? defaultBackgroundColor;

  /// Text styles
  final TextStyle? titleStyle;
  final TextStyle? subtitleStyle;

  /// Icon properties
  final double iconSize;
  final EdgeInsets iconPadding;

  /// If true, trailing icons are only shown for the selected item
  final bool showTrailingIconsOnlyIfSelected;

  const SelectionList({
    super.key,
    required this.items,
    this.selectedValue,
    this.onSelectionChanged,
    this.itemHeight = 80.0,
    this.borderRadius = 12.0,
    this.itemPadding = const EdgeInsets.all(16.0),
    this.listPadding = const EdgeInsets.all(8.0),
    this.spacing = 8.0,
    this.selectedBorderColor,
    this.selectedBackgroundColor,
    this.hoverBorderColor,
    this.hoverBackgroundColor,
    this.defaultBorderColor,
    this.defaultBackgroundColor,
    this.titleStyle,
    this.subtitleStyle,
    this.iconSize = 24.0,
    this.iconPadding = const EdgeInsets.only(right: 12.0),
    this.enabled = true,
    this.showTrailingIconsOnlyIfSelected = false,
  });

  @override
  State<SelectionList<T>> createState() => _SelectionListState<T>();
}

class _SelectionListState<T> extends State<SelectionList<T>> {
  int? hoveredIndex;

  @override
  Widget build(BuildContext context) {
    final ThemeData theme = Theme.of(context);

    // Default colors based on theme
    final defaultBorderColor = widget.defaultBorderColor ??
        theme.colorScheme.outline.withValues(alpha: 0.3);
    final defaultBackgroundColor =
        widget.defaultBackgroundColor ?? theme.colorScheme.surface;
    final selectedBorderColor =
        widget.selectedBorderColor ?? theme.colorScheme.primary;
    final selectedBackgroundColor = widget.selectedBackgroundColor ??
        theme.colorScheme.primaryContainer.withValues(alpha: 0.3);
    final hoverBorderColor = widget.hoverBorderColor ??
        theme.colorScheme.primary.withValues(alpha: 0.5);
    final hoverBackgroundColor = widget.hoverBackgroundColor ??
        theme.colorScheme.surfaceContainerHighest;

    return Padding(
      padding: widget.listPadding,
      child: Column(
        children: widget.items.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          final isSelected = item.value == widget.selectedValue;
          final isHovered = hoveredIndex == index;
          final isEnabled = widget.enabled && item.enabled;

          final titleLabel = Text(
            item.title,
            style: (widget.titleStyle ?? theme.textTheme.titleMedium)?.copyWith(
              color: isSelected
                  ? selectedBorderColor
                  : theme.colorScheme.onSurface,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          );
          final title = item.badge != null
              ? Badge(
                  label: Text(item.badge!),
                  backgroundColor: item.badgeColor,
                  child: titleLabel,
                )
              : titleLabel;

          return Padding(
            padding: EdgeInsets.only(
                bottom: index < widget.items.length - 1 ? widget.spacing : 0),
            child: MouseRegion(
              cursor: widget.enabled
                  ? (isEnabled
                      ? SystemMouseCursors.click
                      : SystemMouseCursors.forbidden)
                  : SystemMouseCursors.forbidden,
              onEnter: (_) => setState(() => hoveredIndex = index),
              onExit: (_) => setState(() => hoveredIndex = null),
              child: GestureDetector(
                onTap: isEnabled
                    ? () {
                        if (widget.onSelectionChanged != null) {
                          widget.onSelectionChanged!(item.value);
                        }
                      }
                    : null,
                child: AnimatedOpacity(
                  duration: const Duration(milliseconds: 200),
                  opacity: isEnabled ? 1.0 : 0.5,
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    height: widget.itemHeight,
                    decoration: BoxDecoration(
                      color: isSelected
                          ? selectedBackgroundColor
                          : isHovered
                              ? hoverBackgroundColor
                              : defaultBackgroundColor,
                      border: Border.all(
                        color: isSelected
                            ? selectedBorderColor
                            : isHovered
                                ? hoverBorderColor
                                : defaultBorderColor,
                        width: isSelected ? 2.0 : 1.0,
                      ),
                      borderRadius: BorderRadius.circular(widget.borderRadius),
                    ),
                    child: Padding(
                      padding: widget.itemPadding,
                      child: Row(
                        children: [
                          if (item.iconWidget != null) ...[
                            Container(
                              padding: widget.iconPadding,
                              child: item.iconWidget,
                            ),
                          ] else if (item.icon != null) ...[
                            Container(
                              padding: widget.iconPadding,
                              child: Icon(
                                item.icon,
                                size: widget.iconSize,
                                color: item.iconColor ??
                                    (isSelected
                                        ? selectedBorderColor
                                        : theme.colorScheme.onSurface
                                            .withValues(
                                                alpha: isEnabled ? 1.0 : 0.5)),
                              ),
                            ),
                          ],
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                title,
                                if (item.subtitle != null) ...[
                                  const SizedBox(height: 4),
                                  Text(
                                    item.subtitle!,
                                    style: (widget.subtitleStyle ??
                                            theme.textTheme.bodySmall)
                                        ?.copyWith(
                                      color: isSelected
                                          ? selectedBorderColor.withValues(
                                              alpha: 0.8)
                                          : theme.colorScheme.onSurface
                                              .withValues(alpha: 0.7)
                                              .withValues(
                                                alpha: isEnabled ? 1.0 : 0.5,
                                              ),
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ],
                            ),
                          ),
                          // New: Trailing icons
                          if (item.trailingIcons != null &&
                              item.trailingIcons!.isNotEmpty &&
                              (!widget.showTrailingIconsOnlyIfSelected ||
                                  isSelected))
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: item.trailingIcons!.map((trailingIcon) {
                                final iconWidget = trailingIcon.iconWidget ??
                                    Icon(
                                      trailingIcon.icon,
                                      color: trailingIcon.color ??
                                          theme.iconTheme.color,
                                      size: widget.iconSize,
                                    );
                                final clickable = MouseRegion(
                                  cursor: (trailingIcon.onTap != null ||
                                          trailingIcon.onTapVoid != null)
                                      ? SystemMouseCursors.click
                                      : SystemMouseCursors.basic,
                                  child: GestureDetector(
                                    onTap: isEnabled
                                        ? () {
                                            if (trailingIcon.onTap != null) {
                                              trailingIcon.onTap!(item.value);
                                            } else if (trailingIcon.onTapVoid !=
                                                null) {
                                              trailingIcon.onTapVoid!();
                                            }
                                          }
                                        : null,
                                    child: trailingIcon.tooltip != null
                                        ? Tooltip(
                                            message: trailingIcon.tooltip!,
                                            child: iconWidget,
                                          )
                                        : iconWidget,
                                  ),
                                );
                                return Padding(
                                  padding: const EdgeInsets.only(left: 8.0),
                                  child: clickable,
                                );
                              }).toList(),
                            ),
                          // Always reserve space for the check icon at the end
                          Padding(
                            padding: const EdgeInsets.only(left: 8.0),
                            child: SizedBox(
                              width: 20,
                              height: 20,
                              child: isSelected
                                  ? Icon(
                                      Icons.check_circle,
                                      color: selectedBorderColor,
                                      size: 20,
                                    )
                                  : null,
                            ),
                          ),
                        ], // <-- Close Row children
                      ), // <-- Close Row
                    ),
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}
