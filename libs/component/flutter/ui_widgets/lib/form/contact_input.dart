import 'dart:math';
import 'package:flutter/material.dart';
import 'package:dlibphonenumber/dlibphonenumber.dart';

import '../l10n/l10n.dart';

import '../validators/validator.dart';
import '../validators/email_address_validator.dart';

import 'text_input.dart';
import 'email_address_input.dart';
import 'phone_number_input.dart';
import 'form_value.dart';
import 'focus_editing_controller.dart';

class ContactInput<T> extends StatefulWidget {
  final ContactLabels<T> labels;
  final ContactValue<T> value;

  /// Enable or disable the contact input
  /// fields
  final bool enabled;

  /// Optional contact type selection field
  final bool showTypeSelectionField;

  /// Optional contact description field
  final bool showDescriptionField;

  /// Optional display the phone number
  /// country flag and international dial
  /// code
  final bool showCountryForPhoneNumbers;

  /// Optional enable selecting the country
  final bool enableCountrySelectionForPhoneNumbers;

  /// Default country code for phone numbers
  final String defaultCountryCode;

  /// Optional validator for emails
  final Validator? emailValidator;

  /// Called when the an address input
  /// field comes into focus.
  final FocusCallback? onFocus;

  /// The focus node for the AddressInput
  /// widget.
  final FocusNode? focusNode;

  /// Called when the address value changes
  final ContactInputCallback<T> onChanged;

  /// Helper to calculate the height of the
  /// ContactInput widget
  static double calculateHeight({
    required bool showDescriptionField,
    required bool showTypeSelectionField,
  }) {
    double totalHeight = 210.0;
    if (!showDescriptionField) {
      totalHeight -= 85.0;
    }
    return totalHeight;
  }

  const ContactInput({
    super.key,
    this.labels = const ContactLabels(),
    this.value = const ContactValue._(),
    this.enabled = true,
    this.showTypeSelectionField = true,
    this.showDescriptionField = false,
    this.showCountryForPhoneNumbers = true,
    this.enableCountrySelectionForPhoneNumbers = true,
    this.defaultCountryCode = 'US',
    this.emailValidator,
    this.onFocus,
    this.focusNode,
    required this.onChanged,
  });

  @override
  State<ContactInput<T>> createState() => _ContactInputState<T>();
}

class _ContactInputState<T> extends State<ContactInput<T>> {
  FocusEditingController? typeController;
  FocusEditingController? descriptionController;
  FocusEditingController? valueController;

  final phoneUtil = PhoneNumberUtil.instance;

  @override
  void initState() {
    super.initState();

    if (widget.showDescriptionField) {
      descriptionController = FocusEditingController(
        onFocus: widget.onFocus,
      );
      descriptionController!.text = widget.value.description ?? '';
    }

    if (widget.showTypeSelectionField) {
      assert(
        (widget.labels.contactTypes != null &&
            widget.labels.contactTypes!.isNotEmpty),
        'If showTypeSelectionField is false, then '
        'laels.contactTypes list must be provided.',
      );

      typeController = FocusEditingController(
        onFocus: widget.onFocus,
      );
      typeController!.text = widget.labels.getTypeLabel(
        widget.value.type,
      );
    }

    valueController = FocusEditingController(
      focusNode: widget.focusNode,
      onFocus: widget.onFocus,
    );
  }

  @override
  void dispose() {
    typeController?.dispose();
    descriptionController?.dispose();
    valueController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final labels = !widget.labels.isValid
        ? ContactLabels.fromLocalization<T>(context)
        : widget.labels;

    final selectedType = labels.contactTypes?.firstWhere(
      (t) => t.value == widget.value.type,
      orElse: () => labels.contactTypes!.first,
    );

    return LayoutBuilder(
      builder: (context, constraints) {
        final typeFieldWidth = widget.showTypeSelectionField
            ? max<double>(170, constraints.maxWidth * 0.25)
            : 0.0;
        final valueFieldWidth = constraints.maxWidth - typeFieldWidth;

        return Wrap(
          children: [
            SizedBox(
              width: constraints.maxWidth,
              child: descriptionController != null
                  ? TextInput(
                      labelText: labels.description,
                      enabled: widget.enabled,
                      focusNode: descriptionController!.focusNode,
                      controller: descriptionController!.controller,
                      onChanged: (value, isValid) {
                        widget.onChanged(
                          widget.value.copyWith(
                            description: value,
                            isFieldValid: ('description', isValid),
                          ),
                        );
                      },
                    )
                  : const SizedBox.shrink(),
            ),
            if (widget.showTypeSelectionField)
              SizedBox(
                width: typeFieldWidth,
                child: TextInput(
                  labelText: labels.type,
                  initialValue: labels.getTypeLabel(
                    widget.value.type,
                  ),
                  isRequired: true,
                  enabled: widget.enabled,
                  focusNode: typeController!.focusNode,
                  controller: typeController!.controller,
                  options: labels.contactTypes
                      ?.map(
                        (t) => DropdownListItem(
                          id: t.label,
                          label: t.label,
                          iconWidget: t.icon,
                        ),
                      )
                      .toList(),
                  optionsSemanticLabel: labels.contactTypeSemanticLabel,
                  isOptionEditable: false,
                  onChanged: (value, isValid) {
                    // clear the value field when the type changes
                    valueController!.text = '';
                    widget.onChanged(
                      widget.value.copyWith(
                        type: labels.contactTypes!
                            .firstWhere(
                              (t) => t.label == value,
                            )
                            .value,
                        value: '',
                        isFieldValid: ('value', true),
                      ),
                    );
                  },
                ),
              ),
            if (selectedType?.inputType == ContactInputType.emailAddress)
              SizedBox(
                width: valueFieldWidth,
                child: EmailAddressInput(
                  labelText: labels.emailAddress,
                  initialValue: widget.value.value,
                  isRequired: true,
                  enabled: widget.enabled,
                  focusNode: valueController!.focusNode,
                  controller: valueController!.controller,
                  validator: widget.emailValidator ?? EmailAddressValidator(),
                  onChanged: (value, isValid) {
                    widget.onChanged(
                      widget.value.copyWith(
                        value: value,
                        isFieldValid: ('value', isValid),
                      ),
                    );
                  },
                ),
              ),
            if (selectedType?.inputType == ContactInputType.phoneNumber)
              SizedBox(
                width: valueFieldWidth,
                child: PhoneNumberInput(
                  labelText: labels.phoneNumber,
                  initialValue: widget.value.value,
                  defaultCountryCode: widget.defaultCountryCode,
                  isRequired: true,
                  enabled: widget.enabled,
                  showCountry: widget.showCountryForPhoneNumbers,
                  enableCountrySelection:
                      widget.enableCountrySelectionForPhoneNumbers,
                  focusNode: valueController!.focusNode,
                  controller: valueController!.controller,
                  onChanged: (value, isValid) {
                    final phoneNumberValue = phoneUtil.format(
                      value,
                      PhoneNumberFormat.e164,
                    );
                    if (phoneNumberValue == '+${value.countryCode}') {
                      widget.onChanged(
                        widget.value.copyWith(
                          value: '',
                          isFieldValid: ('value', isValid),
                        ),
                      );
                    } else {
                      widget.onChanged(
                        widget.value.copyWith(
                          value: phoneNumberValue,
                          isFieldValid: ('value', isValid),
                        ),
                      );
                    }
                  },
                ),
              ),
            if (selectedType?.inputType == ContactInputType.socialMediaHandle)
              SizedBox(
                width: valueFieldWidth,
                child: TextInput(
                  labelText: labels.phoneNumber,
                  isRequired: true,
                  enabled: widget.enabled,
                  focusNode: valueController!.focusNode,
                  controller: valueController!.controller,
                  onChanged: (value, isValid) {
                    widget.onChanged(
                      widget.value.copyWith(
                        value: value,
                        isFieldValid: ('value', isValid),
                      ),
                    );
                  },
                ),
              ),
          ],
        );
      },
    );
  }
}

class ContactValue<T> extends FormValue {
  /// the type of contact
  final T? type;

  /// a brief description of the address
  final String? description;

  /// phone number, email, social handle, etc.
  final String value;

  /// determines if all required fields are empty
  @override
  bool get isEmpty =>
      (description == null || description!.isEmpty) && value.isEmpty;

  /// determines if all required fields are not empty
  @override
  bool get isRequiredNotEmpty => value.isNotEmpty;

  /// captures the validity of field data
  @override
  final Map<String, bool> validFields;

  static ContactValue<T> create<T>({
    T? type,
    String? description,
    String? value,
  }) {
    return ContactValue<T>._(
      key: UniqueKey(),
      type: type,
      description: description,
      value: value ?? '',
    );
  }

  const ContactValue._({
    super.key,
    this.type,
    this.description,
    this.value = '',
    this.validFields = const {
      'value': true,
    },
  });

  ContactValue<T> copyWith({
    T? type,
    String? description,
    String? value,
    (String, bool)? isFieldValid,
  }) {
    return ContactValue._(
      key: key,
      type: type ?? this.type,
      description: description ?? this.description,
      value: value ?? this.value,
      // if isFieldValid is not null, update the validFields map
      validFields: isFieldValid != null
          ? {
              ...validFields,
              isFieldValid.$1: isFieldValid.$2,
            }
          : validFields,
    );
  }

  @override
  List<Object?> get props => [type, description, value, isValid];
}

class ContactLabels<T> {
  final String? type;
  final String? description;
  final String? emailAddress;
  final String? phoneNumber;
  final String? socialMediaHandle;

  /// a list of possible contact types
  final List<ContactTypeSelection<T>>? contactTypes;

  final String? contactTypeSemanticLabel;

  /// determines if all required labels are set
  bool get isValid =>
      type != null && (emailAddress != null || phoneNumber != null);

  const ContactLabels({
    this.type,
    this.description,
    this.emailAddress,
    this.phoneNumber,
    this.socialMediaHandle,
    this.contactTypes,
    this.contactTypeSemanticLabel,
  });

  static ContactLabels<T> fromLocalization<T>(
    BuildContext context,
  ) {
    final l10n = context.l10n;
    return ContactLabels<T>(
      type: l10n.typeFieldName,
      description: l10n.descriptionFieldName,
      emailAddress: l10n.emailAddressFieldName,
      phoneNumber: l10n.phoneNumberFieldName,
      socialMediaHandle: l10n.socialMediaHandleFieldName,
      contactTypeSemanticLabel: l10n.semContactTypePicker,
    );
  }

  ContactLabels<T> copyWith({
    String? type,
    String? description,
    String? emailAddress,
    String? phoneNumber,
    List<ContactTypeSelection<T>>? contactTypes,
    String? contactTypeSemanticLabel,
  }) {
    return ContactLabels<T>(
      type: type ?? this.type,
      description: description ?? this.description,
      emailAddress: emailAddress ?? this.emailAddress,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      contactTypes: contactTypes ?? this.contactTypes,
      contactTypeSemanticLabel:
          contactTypeSemanticLabel ?? this.contactTypeSemanticLabel,
    );
  }

  ContactInputType getInputType(T? type) {
    if (type == null) {
      return ContactInputType.emailAddress;
    } else {
      return contactTypes!
          .firstWhere(
            (e) => e.value == type,
          )
          .inputType;
    }
  }

  String getTypeLabel(T? type) {
    return contactTypes!
        .firstWhere(
          (e) => e.value == type,
          orElse: () => contactTypes!.first,
        )
        .label;
  }
}

class ContactTypeSelection<T> {
  final T value;
  final String label;

  final Widget? icon;

  final ContactInputType inputType;

  const ContactTypeSelection({
    required this.value,
    required this.label,
    this.icon,
    required this.inputType,
  });
}

enum ContactInputType {
  emailAddress,
  phoneNumber,
  socialMediaHandle,
}

typedef ContactInputCallback<T> = void Function(
  ContactValue<T> value,
);
