import 'package:flutter/material.dart';

import 'text_input.dart';

import '../l10n/l10n.dart';

class UsernameInput extends TextInput {
  UsernameInput({
    super.key,
    super.labelText,
    super.initialValue,
    super.isRequired,
    super.enabled,
    IconData icon = Icons.person,
    super.validator,
    super.onChanged,
    super.onEnter,
    super.padding = const EdgeInsets.fromLTRB(8.0, 8.0, 8.0, 0.0),
  }) : super(
          trailingIcon: InputIcon(
            icon: icon,
          ),
        );

  @override
  String lookupLabelText(BuildContext context) =>
      labelText ?? context.l10n.usernameFieldName;
}
