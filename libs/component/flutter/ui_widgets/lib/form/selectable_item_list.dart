import 'package:flutter/material.dart';

class SelectedListItem<T> {
  final T value;
  final String title;
  final String? subtitle;
  final IconData? icon;
  final Color? iconColor;

  const SelectedListItem({
    required this.value,
    required this.title,
    this.subtitle,
    this.icon,
    this.iconColor,
  });
}

class SelectedList<T> extends StatefulWidget {
  /// List of items to display
  final List<SelectedListItem<T>> items;

  /// Currently selected item
  final T? selectedValue;

  /// Callback when selection changes
  final ValueChanged<T>? onSelectionChanged;

  /// Styling properties
  final double itemHeight;
  final double borderRadius;
  final EdgeInsets itemPadding;
  final EdgeInsets listPadding;
  final double spacing;

  /// Colors
  final Color? selectedBorderColor;
  final Color? selectedBackgroundColor;
  final Color? hoverBorderColor;
  final Color? hoverBackgroundColor;
  final Color? defaultBorderColor;
  final Color? defaultBackgroundColor;

  /// Text styles
  final TextStyle? titleStyle;
  final TextStyle? subtitleStyle;

  /// Icon properties
  final double iconSize;
  final EdgeInsets iconPadding;

  const SelectedList({
    super.key,
    required this.items,
    this.selectedValue,
    this.onSelectionChanged,
    this.itemHeight = 80.0,
    this.borderRadius = 12.0,
    this.itemPadding = const EdgeInsets.all(16.0),
    this.listPadding = const EdgeInsets.all(8.0),
    this.spacing = 8.0,
    this.selectedBorderColor,
    this.selectedBackgroundColor,
    this.hoverBorderColor,
    this.hoverBackgroundColor,
    this.defaultBorderColor,
    this.defaultBackgroundColor,
    this.titleStyle,
    this.subtitleStyle,
    this.iconSize = 24.0,
    this.iconPadding = const EdgeInsets.only(right: 12.0),
  });

  @override
  State<SelectedList<T>> createState() => _SelectedListState<T>();
}

class _SelectedListState<T> extends State<SelectedList<T>> {
  int? hoveredIndex;

  @override
  Widget build(BuildContext context) {
    final ThemeData theme = Theme.of(context);

    // Default colors based on theme
    final defaultBorderColor =
        widget.defaultBorderColor ?? theme.colorScheme.outline.withOpacity(0.3);
    final defaultBackgroundColor =
        widget.defaultBackgroundColor ?? theme.colorScheme.surface;
    final selectedBorderColor =
        widget.selectedBorderColor ?? theme.colorScheme.primary;
    final selectedBackgroundColor = widget.selectedBackgroundColor ??
        theme.colorScheme.primaryContainer.withOpacity(0.3);
    final hoverBorderColor =
        widget.hoverBorderColor ?? theme.colorScheme.primary.withOpacity(0.5);
    final hoverBackgroundColor =
        widget.hoverBackgroundColor ?? theme.colorScheme.surfaceVariant;

    return Padding(
      padding: widget.listPadding,
      child: Column(
        children: widget.items.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          final isSelected = item.value == widget.selectedValue;
          final isHovered = hoveredIndex == index;

          return Padding(
            padding: EdgeInsets.only(
                bottom: index < widget.items.length - 1 ? widget.spacing : 0),
            child: MouseRegion(
              onEnter: (_) => setState(() => hoveredIndex = index),
              onExit: (_) => setState(() => hoveredIndex = null),
              child: GestureDetector(
                onTap: () {
                  if (widget.onSelectionChanged != null) {
                    widget.onSelectionChanged!(item.value);
                  }
                },
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  height: widget.itemHeight,
                  decoration: BoxDecoration(
                    color: isSelected
                        ? selectedBackgroundColor
                        : isHovered
                            ? hoverBackgroundColor
                            : defaultBackgroundColor,
                    border: Border.all(
                      color: isSelected
                          ? selectedBorderColor
                          : isHovered
                              ? hoverBorderColor
                              : defaultBorderColor,
                      width: isSelected ? 2.0 : 1.0,
                    ),
                    borderRadius: BorderRadius.circular(widget.borderRadius),
                  ),
                  child: Padding(
                    padding: widget.itemPadding,
                    child: Row(
                      children: [
                        if (item.icon != null) ...[
                          Container(
                            padding: widget.iconPadding,
                            child: Icon(
                              item.icon,
                              size: widget.iconSize,
                              color: item.iconColor ??
                                  (isSelected
                                      ? selectedBorderColor
                                      : theme.colorScheme.onSurface),
                            ),
                          ),
                        ],
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                item.title,
                                style: (widget.titleStyle ??
                                        theme.textTheme.titleMedium)
                                    ?.copyWith(
                                  color: isSelected
                                      ? selectedBorderColor
                                      : theme.colorScheme.onSurface,
                                  fontWeight: isSelected
                                      ? FontWeight.w600
                                      : FontWeight.normal,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              if (item.subtitle != null) ...[
                                const SizedBox(height: 4),
                                Text(
                                  item.subtitle!,
                                  style: (widget.subtitleStyle ??
                                          theme.textTheme.bodySmall)
                                      ?.copyWith(
                                    color: isSelected
                                        ? selectedBorderColor.withOpacity(0.8)
                                        : theme.colorScheme.onSurface
                                            .withOpacity(0.7),
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ],
                          ),
                        ),
                        if (isSelected)
                          Icon(
                            Icons.check_circle,
                            color: selectedBorderColor,
                            size: 20,
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}
