import 'package:flutter/material.dart';

class FocusEditingController {
  String get text => controller.text;
  set text(String value) => controller.text = value;

  final FocusNode focusNode;
  final TextEditingController controller;

  final FocusCallback? onFocus;

  final bool _disposeFocusNode;

  FocusEditingController({
    this.onFocus,
    FocusNode? focusNode,
  })  : focusNode = focusNode ?? FocusNode(),
        _disposeFocusNode = focusNode == null,
        controller = TextEditingController() {
    if (onFocus != null) {
      this.focusNode.addListener(() {
        onFocus!(this.focusNode.hasFocus);
      });
    }
  }

  void dispose() {
    controller.dispose();
    if (_disposeFocusNode) {
      focusNode.dispose();
    }
  }
}

typedef FocusCallback = void Function(bool hasFocus);
