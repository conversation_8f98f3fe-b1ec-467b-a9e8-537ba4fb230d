import 'package:flutter/material.dart';
import 'package:pinput/pinput.dart';

import '../l10n/l10n.dart';

class CodeInput extends StatefulWidget {
  final int length;

  final bool isRequired;
  final bool enabled;

  final IconData? icon;

  final ValueChanged<String>? onCompleted;

  final EdgeInsetsGeometry padding;

  const CodeInput({
    super.key,
    required this.length,
    this.isRequired = false,
    this.enabled = true,
    this.icon = Icons.phone,
    this.onCompleted,
    this.padding = const EdgeInsets.fromLTRB(8.0, 8.0, 8.0, 16.0),
  });

  @override
  State<StatefulWidget> createState() => _CodeInputState();
}

class _CodeInputState extends State<CodeInput> {
  final TextEditingController controller = TextEditingController();
  final focusNode = FocusNode();

  @override
  void dispose() {
    controller.dispose();
    focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = context.l10n;

    final borderColor = theme.colorScheme.outline;
    final focusedBorderColor = theme.colorScheme.primary;

    final defaultPinTheme = PinTheme(
      width: 44,
      height: 48,
      textStyle: theme.textTheme.headlineSmall,
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: borderColor),
      ),
    );

    return Padding(
      padding: widget.padding,
      child: SizedBox(
        height: 56,
        child: Semantics(
          label: widget.isRequired
              ? l10n.semCodeInputRequired(widget.length)
              : l10n.semCodeInput(widget.length),
          excludeSemantics: true,
          blockUserActions: true,
          child: Pinput(
            length: widget.length,
            controller: controller,
            focusNode: focusNode,
            defaultPinTheme: defaultPinTheme,
            onCompleted: widget.onCompleted,
            onTap: () => controller.clear(),
            focusedPinTheme: defaultPinTheme.copyWith(
              height: 56,
              width: 52,
              decoration: defaultPinTheme.decoration!.copyWith(
                border: Border.all(
                  width: 2.0,
                  color: focusedBorderColor,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
