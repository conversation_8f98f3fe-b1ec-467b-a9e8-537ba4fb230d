import 'package:flutter/material.dart';
import 'package:flutter/services.dart' hide TextInput;
import 'package:intl/intl.dart';
import 'package:country_pickers/country_pickers.dart';
import 'package:country_pickers/country.dart';
import 'package:dlibphonenumber/dlibphonenumber.dart';

import '../l10n/l10n.dart';
import '../utils/uuid.dart';
import '../utils/text.dart';
import '../validators/callback_validator.dart';

import 'text_input.dart';

class PhoneNumberInput extends StatefulWidget {
  final String? labelText;

  final String? initialValue;

  /// country code (ISO 3166-1 alpha-2)
  final String? defaultCountryCode;

  /// List of country codes to be displayed
  /// at the top of the selecton
  final List<String>? priorityList;

  final bool isRequired;
  final bool enabled;

  final bool showCountry;
  final bool enableCountrySelection;

  final IconData? icon;
  final Color? iconColor;
  final String? iconTooltip;
  final VoidCallback? onIconTap;

  final FocusNode? focusNode;
  final TextEditingController? controller;
  final ValidatedValueChanged<PhoneNumber>? onChanged;

  final EdgeInsets padding;

  const PhoneNumberInput({
    super.key,
    this.labelText,
    this.initialValue,
    this.defaultCountryCode,
    this.priorityList,
    this.isRequired = false,
    this.enabled = true,
    this.showCountry = true,
    this.enableCountrySelection = true,
    this.icon = Icons.phone,
    this.iconColor,
    this.iconTooltip,
    this.onIconTap,
    this.focusNode,
    this.controller,
    this.onChanged,
    this.padding = const EdgeInsets.fromLTRB(8.0, 8.0, 8.0, 0.0),
  });

  String lookupLabelText(BuildContext context) =>
      labelText ?? context.l10n.mobileNumberFieldName;

  @override
  State<StatefulWidget> createState() => _PhoneNumberInputState();
}

class _PhoneNumberInputState extends State<PhoneNumberInput> {
  final key = Key(uuid());

  final phoneUtil = PhoneNumberUtil.instance;

  late final TextInputFormatter _phoneNumberInputFormatter;
  late final TextEditingController? controller;

  final countrySelectionFocus = FocusNode();

  Country country = CountryPickerUtils.getCountryByIsoCode('US');

  PhoneNumber phoneNumber = PhoneNumber();

  @override
  void initState() {
    super.initState();
    controller = widget.controller ?? TextEditingController();

    _phoneNumberInputFormatter = _PhoneNumberInputFormatter(this);

    countrySelectionFocus.addListener(() {
      setState(() {});
    });

    if (widget.defaultCountryCode != null) {
      if (widget.defaultCountryCode!.length == 3) {
        country = CountryPickerUtils.getCountryByIso3Code(
          widget.defaultCountryCode!,
        );
      } else {
        country = CountryPickerUtils.getCountryByIsoCode(
          widget.defaultCountryCode!,
        );
      }
    }
    if (widget.initialValue != null && widget.initialValue!.isNotEmpty) {
      phoneNumber = phoneUtil.parse(
        widget.initialValue!,
        country.isoCode,
      );
      country = CountryPickerUtils.getCountryByPhoneCode(
        phoneNumber.countryCode.toString(),
      );
      if (country.isoCode == 'CA' && Intl.getCurrentLocale() == 'en_US') {
        // The intl_phone_field defaults +1 to CA so we need
        // to explicitly set it to US if the locale is en_US
        country = CountryPickerUtils.getCountryByIsoCode('US');
      } else if (country.isoCode.length != 2) {
        // Pick primary country code if the iso code is not
        // a primary country code
        country = CountryPickerUtils.getCountryByIsoCode(
          country.isoCode.substring(0, 2),
        );
      }
      controller!.text = phoneUtil
          .format(
            phoneNumber,
            PhoneNumberFormat.national,
          )
          .replaceAll(RegExp(r'^0*'), '');
    } else {
      phoneNumber = PhoneNumber(
        countryCode: phoneUtil.getCountryCodeForRegion(country.isoCode),
      );
    }
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      controller!.dispose();
    }
    countrySelectionFocus.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final labelName = widget.lookupLabelText(context);

    return TextInput(
      padding: widget.padding,
      enabled: widget.enabled,
      labelText: labelName,
      isRequired: widget.isRequired,
      leadingIcon: widget.showCountry //
          ? _buildCountryDecoration(context)
          : null,
      trailingIcon: InputIcon(
        icon: widget.icon,
        iconColor: widget.iconColor,
        iconTooltip: widget.iconTooltip,
        onIconTap: widget.onIconTap,
      ),
      focusNode: widget.focusNode,
      controller: controller,
      inputFormatters: [_phoneNumberInputFormatter],
      validator: CallbackValidator(
        callback: (value) {
          return phoneUtil.isValidNumber(phoneNumber);
        },
        longErrorMessage: l10n.invalidPhoneNumberErrorMessage,
        shortErrorMessage: l10n.invalidMessage,
      ),
      onChanged: (value, isValid) {
        if (widget.onChanged != null) {
          widget.onChanged!(phoneNumber, isValid);
        }
      },
    );
  }

  InputIcon _buildCountryDecoration(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = context.l10n;

    TextStyle countryCodeTextStyle = theme.textTheme.titleMedium ??
        const TextStyle(
          fontSize: 16,
        );
    if (countrySelectionFocus.hasFocus) {
      countryCodeTextStyle = countryCodeTextStyle.copyWith(
        fontWeight: FontWeight.bold,
      );
    }

    final countryCode = '+${phoneNumber.countryCode}';
    final countryCodeTextSize = getTextSize(
      countryCode,
      style: countryCodeTextStyle,
    );

    return widget.enabled && widget.enableCountrySelection
        ? InputIcon(
            iconWidget: Focus(
              focusNode: countrySelectionFocus,
              onKeyEvent: (node, event) {
                if (event.logicalKey == LogicalKeyboardKey.enter) {
                  _openCountryPickerDialog(context);
                  return KeyEventResult.handled;
                }
                return KeyEventResult.ignored;
              },
              child: Container(
                width: countryCodeTextSize.width + 50,
                color: countrySelectionFocus.hasFocus ? theme.focusColor : null,
                child: Row(
                  children: [
                    Padding(
                      padding: const EdgeInsets.fromLTRB(
                        8.0,
                        16.0,
                        8.0,
                        16.0,
                      ),
                      child: CountryPickerUtils.getDefaultFlagImage(
                        country,
                      ),
                    ),
                    Text(
                      countryCode,
                      style: countryCodeTextStyle,
                    ),
                  ],
                ),
              ),
            ),
            semanticLabel: l10n.semCountryCodePicker,
            onIconTap: () {
              _openCountryPickerDialog(context);
            },
          )
        : InputIcon(
            iconWidget: SizedBox(
              width: countryCodeTextSize.width + 50,
              child: Row(
                children: [
                  Padding(
                    padding: const EdgeInsets.fromLTRB(
                      8.0,
                      16.0,
                      8.0,
                      16.0,
                    ),
                    child: CountryPickerUtils.getDefaultFlagImage(
                      country,
                    ),
                  ),
                  Text(
                    countryCode,
                    style: countryCodeTextStyle,
                  ),
                ],
              ),
            ),
          );
  }

  void _openCountryPickerDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => CountryPickerDialog(
        titlePadding: const EdgeInsets.all(8.0),
        searchInputDecoration: const InputDecoration(
          hintText: 'Search Country...',
        ),
        isSearchable: true,
        title: const Text('Select the International Dial Code'),
        onValuePicked: (Country country) {
          setState(() {
            this.country = country;
            controller!.text = '';
            phoneNumber = PhoneNumber(
              countryCode: phoneUtil.getCountryCodeForRegion(country.isoCode),
            );
          });
          if (widget.onChanged != null) {
            widget.onChanged!(
              phoneNumber,
              phoneUtil.isValidNumber(phoneNumber),
            );
          }
        },
        itemBuilder: (country) => Row(
          children: <Widget>[
            CountryPickerUtils.getDefaultFlagImage(country),
            const SizedBox(width: 8.0),
            Text("+${country.phoneCode}"),
            const SizedBox(width: 8.0),
            Flexible(child: Text(country.name))
          ],
        ),
        priorityList: widget.priorityList?.map(
          (isoCode) {
            return CountryPickerUtils.getCountryByIsoCode(isoCode);
          },
        ).toList(),
      ),
    );
  }
}

class _PhoneNumberInputFormatter extends TextInputFormatter {
  final _PhoneNumberInputState state;

  const _PhoneNumberInputFormatter(this.state);

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // accept only numerics for new data entry
    if (!oldValue.text.startsWith(newValue.text)) {
      int index = newValue.selection.start - 1;
      if (index >= 0 && !newValue.text[index].contains(RegExp(r'[0-9]'))) {
        return oldValue;
      }
    }
    try {
      // parse new raw number without formatting
      final rawNumber = newValue.text.replaceAll(RegExp(r'[^\d]'), '');
      state.phoneNumber = state.phoneUtil.parse(
        rawNumber,
        state.country.isoCode,
      );
      // format new number
      final formatted = state.phoneUtil
          .format(
            state.phoneNumber,
            PhoneNumberFormat.national,
          )
          .replaceAll(RegExp(r'^0*'), '');

      // re-calculate cursor position once formatted
      int cursorAt = newValue.selection.baseOffset;
      if (formatted.length > newValue.text.length) {
        cursorAt += formatted.length - newValue.text.length;
      } else {
        cursorAt -= newValue.text.length - formatted.length;
      }

      return newValue.copyWith(
        text: formatted,
        selection: TextSelection.collapsed(
          offset: cursorAt,
        ),
      );
    } catch (e) {
      // if parsing fails, then create a raw
      // phone number and accept new value
      state.phoneNumber = PhoneNumber(
        rawInput: newValue.text,
        countryCode: state.phoneUtil.getCountryCodeForRegion(
          state.country.isoCode,
        ),
      );
      return newValue;
    }
  }
}
