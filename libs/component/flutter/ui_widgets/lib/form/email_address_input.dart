import 'package:flutter/material.dart';

import 'text_input.dart';

import '../l10n/l10n.dart';

class EmailAddressInput extends TextInput {
  EmailAddressInput({
    super.key,
    super.labelText,
    super.initialValue,
    super.isRequired,
    super.enabled,
    IconData icon = Icons.email,
    Color? iconColor,
    String? iconTooltip,
    VoidCallback? onIconTap,
    super.focusNode,
    super.controller,
    super.onChanged,
    super.validator,
    super.padding = const EdgeInsets.fromLTRB(8.0, 8.0, 8.0, 0.0),
  }) : super(
          trailingIcon: InputIcon(
            icon: icon,
            iconColor: iconColor,
            iconTooltip: iconTooltip,
            onIconTap: onIconTap,
          ),
        );

  @override
  String lookupLabelText(BuildContext context) =>
      labelText ?? context.l10n.emailAddressFieldName;
}
