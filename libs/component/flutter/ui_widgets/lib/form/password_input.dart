import 'package:flutter/material.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../utils/uuid.dart';
import '../validators/validator.dart';
import '../l10n/l10n.dart';

class PasswordInput extends StatefulWidget {
  final String? labelText;
  final bool isRequired;
  final bool enabled;

  final FocusNode? focusNode;
  final TextEditingController? controller;

  final ValueChanged<String>? onChanged;
  final Validator? validator;

  final ValueChanged<String>? onEnter;

  final EdgeInsetsGeometry padding;

  const PasswordInput({
    super.key,
    this.labelText,
    this.isRequired = false,
    this.enabled = true,
    this.focusNode,
    this.controller,
    this.onChanged,
    this.validator,
    this.onEnter,
    this.padding = const EdgeInsets.fromLTRB(8.0, 8.0, 8.0, 4.0),
  });

  String lookupLabelText(BuildContext context) =>
      labelText ?? context.l10n.passwordFieldName;

  @override
  State<StatefulWidget> createState() => _PasswordInputState();
}

class _PasswordInputState extends State<PasswordInput> {
  final key = Key(uuid());

  bool passwordVisible = false;
  String? errorTooltip;

  late final TextEditingController? controller;

  _PasswordInputState();

  @override
  void initState() {
    super.initState();
    controller = widget.controller ?? TextEditingController();
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      controller!.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.controller == null
        ? VisibilityDetector(
            key: key,
            onVisibilityChanged: (VisibilityInfo info) {
              if (info.visibleFraction > 0 && mounted) {
                setState(() {
                  controller!.text = '';
                });
              }
            },
            child: _buildTextFormField(context),
          )
        : _buildTextFormField(context);
  }

  Widget _buildTextFormField(BuildContext context) {
    final validator = widget.validator;
    final labelName = widget.lookupLabelText(context);

    return Padding(
      padding: widget.padding,
      child: TextFormField(
        enabled: widget.enabled,
        obscureText: !passwordVisible,
        autovalidateMode: AutovalidateMode.onUserInteraction,
        decoration: InputDecoration(
          suffixIcon: _buildSuffixIcon(errorTooltip),
          labelText: widget.isRequired ? '$labelName *' : labelName,
          helperText: validator != null ? ' ' : null,
          border: const OutlineInputBorder(
            borderSide: BorderSide(),
          ),
        ),
        focusNode: widget.focusNode,
        controller: controller,
        onFieldSubmitted: widget.onEnter,
        onChanged: widget.onChanged != null
            ? (value) {
                if (mounted) {
                  widget.onChanged!(value);
                }
              }
            : null,
        validator: (text) {
          if (validator != null) {
            validator.validate(context, labelName, text);
            Future.delayed(
              const Duration(seconds: 0),
              () {
                if (mounted) {
                  if (validator.isValid) {
                    setState(
                      () => errorTooltip = null,
                    );
                  } else {
                    setState(
                      () => errorTooltip = validator.longErrorMessage,
                    );
                  }
                }
              },
            );
            return validator.shortErrorMessage;
          }
          return null;
        },
      ),
    );
  }

  Widget _buildSuffixIcon(String? errorTooltip) {
    final theme = Theme.of(context);
    final l10n = context.l10n;

    if (errorTooltip == null) {
      return IconButton(
        icon: Icon(
          passwordVisible //
              ? Icons.visibility_off
              : Icons.visibility,
          semanticLabel: passwordVisible //
              ? l10n.semHidePassword
              : l10n.semShowPassword,
        ),
        onPressed: () {
          setState(() {
            passwordVisible = !passwordVisible;
          });
        },
      );
    } else {
      return IconButton(
        icon: Tooltip(
          message: errorTooltip,
          child: Icon(
            passwordVisible //
                ? Icons.visibility_off
                : Icons.visibility,
            semanticLabel: passwordVisible //
                ? l10n.semHidePassword
                : l10n.semShowPassword,
            color: theme.colorScheme.error,
          ),
        ),
        onPressed: () {
          setState(() {
            passwordVisible = !passwordVisible;
          });
        },
      );
    }
  }
}

class ConfirmPasswordInput extends PasswordInput {
  const ConfirmPasswordInput({
    super.key,
    super.labelText,
    super.isRequired,
    super.enabled,
    super.validator,
    super.onChanged,
    super.padding,
  });

  @override
  String lookupLabelText(BuildContext context) =>
      labelText ?? context.l10n.confirmPasswordFieldName;
}
