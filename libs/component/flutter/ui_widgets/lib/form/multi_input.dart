import 'package:flutter/material.dart';

import '../view/group_box.dart';

import 'form_value.dart';

class MultiInput<T extends FormValue> extends StatefulWidget {
  /// The initial value of the multi-input
  /// list
  final List<T> values;

  /// The builder function that will be
  /// called for each value in the list
  final MultiInputBuilder<T> builder;

  /// Called when the user adds a new address
  final AddInputCallback onAdd;

  /// Called when the user removes an address
  final RemoveInputCallback onRemove;

  /// Enables or disables the multi
  final bool enabled;

  /// Encloses the input in a group box
  final bool groubBox;

  /// The background color of the multi-
  /// input. This needs to be set if the
  /// input is rendered in a dialog
  final Color? groupBoxBackground;

  /// The margin of the group box enclosing
  /// a single input
  final EdgeInsets groupBoxMargin;

  /// The padding of the group box enclosing
  /// a single input
  final EdgeInsets groupBoxPadding;

  /// The alignment of the action buttons when
  /// input is enclosed in a group box
  final MainAxisAlignment groupBoxActionAlignment;

  /// The padding of the action buttons when
  /// not within a group box
  final EdgeInsets actionsPadding;

  /// Semantics label for the add button
  final String? semanticLabelAddButton;

  /// Semantics label for the delete button
  final String? semanticLabelDeleteButton;

  const MultiInput({
    super.key,
    required this.values,
    required this.builder,
    required this.onAdd,
    required this.onRemove,
    this.enabled = true,
    this.groubBox = true,
    this.groupBoxBackground,
    this.groupBoxMargin = const EdgeInsets.all(8.0),
    this.groupBoxPadding = const EdgeInsets.all(10.0),
    this.groupBoxActionAlignment = MainAxisAlignment.end,
    this.actionsPadding = const EdgeInsets.all(0.0),
    this.semanticLabelAddButton,
    this.semanticLabelDeleteButton,
  });

  @override
  State<MultiInput<T>> createState() => _MultiInputState<T>();
}

class _MultiInputState<T extends FormValue> extends State<MultiInput<T>> {
  int focusedIndex = -1;
  late List<FocusNode> focusNodes;

  @override
  void initState() {
    super.initState();

    focusNodes = List.generate(
      widget.values.length,
      (index) => FocusNode(),
    );
  }

  @override
  void dispose() {
    for (var focusNode in focusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        for (var i = 0; i < widget.values.length; i++)
          if (widget.groubBox)
            _buildGroupBoxedInput(context, i)
          else
            _buildInput(context, i),
      ],
    );
  }

  Widget _buildGroupBoxedInput(
    BuildContext context,
    int index,
  ) {
    final theme = Theme.of(context);
    final value = widget.values[index];

    final canDelete = !value.isReadOnly && //
        (value.isNotEmpty || widget.values.length > 1);

    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        GroupBox(
          margin: widget.groupBoxMargin,
          padding: widget.groupBoxPadding,
          backgroundColor: widget.groupBoxBackground,
          borderColor: focusedIndex == index //
              ? theme.colorScheme.primary
              : null,
          child: widget.builder(
            context,
            value,
            index,
            focusNodes[index],
            (hasFocus) {
              setState(() {
                focusedIndex = hasFocus ? index : -1;
              });
            },
          ),
        ),
        Positioned(
          child: Row(
            mainAxisAlignment: widget.groupBoxActionAlignment,
            children: [
              if (canDelete)
                SizedBox(
                  width: 28.0,
                  height: 28.0,
                  child: FloatingActionButton(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    backgroundColor: widget.enabled
                        ? theme.colorScheme.primary.withValues(alpha: 0.8)
                        : theme.colorScheme.onSurface.withValues(alpha: 0.3),
                    hoverColor: theme.colorScheme.primary,
                    splashColor:
                        widget.groupBoxBackground?.withValues(alpha: 0.3) ??
                            theme.colorScheme.surface.withValues(alpha: 0.3),
                    onPressed: widget.enabled
                        ? () {
                            final notLastInput = widget.values.length > 1;

                            widget.onRemove(index);
                            if (index > 0 || notLastInput) {
                              // Simply add the focus node of the removed
                              // input widget to the end of the list so
                              // it can be reused.
                              focusNodes.add(focusNodes.removeAt(index));
                            } else {
                              // This will reset the first input with
                              // an empty input
                              widget.onAdd();
                              focusNodes[0].requestFocus();
                            }
                          }
                        : null,
                    child: Icon(
                      Icons.delete,
                      semanticLabel: widget.semanticLabelDeleteButton,
                      size: 20.0,
                    ),
                  ),
                ),
              if (canDelete && index == widget.values.length - 1)
                const SizedBox(width: 4.0),
              if (index == widget.values.length - 1)
                SizedBox(
                  width: 28.0,
                  height: 28.0,
                  child: FloatingActionButton(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    backgroundColor: widget.enabled
                        ? theme.colorScheme.primary.withValues(alpha: 0.8)
                        : theme.colorScheme.onSurface.withValues(alpha: 0.3),
                    hoverColor: theme.colorScheme.primary,
                    splashColor:
                        widget.groupBoxBackground?.withValues(alpha: 0.3) ??
                            theme.colorScheme.surface.withValues(alpha: 0.3),
                    onPressed: widget.enabled
                        ? () {
                            final last = widget.values.length;
                            // reuse the last focus node if it exists
                            if (focusNodes.length == last) {
                              focusNodes.add(FocusNode());
                            }
                            widget.onAdd();
                            focusNodes[last].requestFocus();
                          }
                        : null,
                    child: Icon(
                      Icons.add,
                      semanticLabel: widget.semanticLabelAddButton,
                      size: 20.0,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildInput(
    BuildContext context,
    int index,
  ) {
    final theme = Theme.of(context);
    final value = widget.values[index];

    final canDelete = !value.isReadOnly && //
        (value.isNotEmpty || widget.values.length > 1);

    return Row(
      children: [
        Expanded(
          child: widget.builder(
            context,
            value,
            index,
            focusNodes[index],
            (hasFocus) {
              setState(() {
                focusedIndex = hasFocus ? index : -1;
              });
            },
          ),
        ),
        Padding(
          padding: widget.actionsPadding,
          child: Column(
            children: [
              if (canDelete)
                SizedBox(
                  width: 28.0,
                  height: 28.0,
                  child: FloatingActionButton(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    backgroundColor:
                        theme.colorScheme.primary.withValues(alpha: 0.8),
                    hoverColor: theme.colorScheme.primary,
                    splashColor:
                        widget.groupBoxBackground?.withValues(alpha: 0.3) ??
                            theme.colorScheme.surface.withValues(alpha: 0.3),
                    onPressed: () {
                      final notLastInput = widget.values.length > 1;

                      widget.onRemove(index);
                      if (index > 0 || notLastInput) {
                        // Simply add the focus node of the removed
                        // input widget to the end of the list so
                        // it can be reused.
                        focusNodes.add(focusNodes.removeAt(index));
                      } else {
                        // This will reset the first input with to
                        // a an empty input
                        widget.onAdd();
                        focusNodes[0].requestFocus();
                      }
                    },
                    child: Icon(
                      Icons.delete,
                      semanticLabel: widget.semanticLabelDeleteButton,
                      size: 20.0,
                    ),
                  ),
                ),
              if (canDelete && index == widget.values.length - 1)
                const SizedBox(height: 4.0),
              if (index == widget.values.length - 1)
                SizedBox(
                  width: 28.0,
                  height: 28.0,
                  child: FloatingActionButton(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    backgroundColor: widget.enabled
                        ? theme.colorScheme.primary.withValues(alpha: 0.8)
                        : theme.colorScheme.onSurface.withValues(alpha: 0.3),
                    hoverColor: theme.colorScheme.primary,
                    splashColor:
                        widget.groupBoxBackground?.withValues(alpha: 0.3) ??
                            theme.colorScheme.surface.withValues(alpha: 0.3),
                    onPressed: widget.enabled
                        ? () {
                            final last = widget.values.length;
                            // reuse the last focus node if it exists
                            if (focusNodes.length == last) {
                              focusNodes.add(FocusNode());
                            }
                            widget.onAdd();
                            focusNodes[last].requestFocus();
                          }
                        : null,
                    child: Icon(
                      Icons.add,
                      semanticLabel: widget.semanticLabelAddButton,
                      size: 20.0,
                    ),
                  ),
                ),
              // if none of the above then add a space
              if (!canDelete && index < widget.values.length - 1)
                const SizedBox(width: 24.0),
            ],
          ),
        ),
      ],
    );
  }
}

typedef MultiInputBuilder<T> = Widget Function(
  BuildContext context,
  T value,
  int index,
  FocusNode focusNode,
  Function(bool hasFocus) focusHandler,
);

typedef AddInputCallback = void Function();
typedef RemoveInputCallback = void Function(int index);
