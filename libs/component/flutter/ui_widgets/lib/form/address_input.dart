import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart' as services;
import 'package:country_pickers/country_pickers.dart';
import 'package:country_pickers/country.dart';

import '../l10n/l10n.dart';
import '../validators/validator.dart';
import '../validators/callback_validator.dart';
import '../validators/list_validator.dart';

import 'text_input.dart';
import 'form_value.dart';
import 'focus_editing_controller.dart';

class AddressInput<T> extends StatefulWidget {
  final AddressLabels<T> labels;
  final AddressValue<T> value;

  /// Enable or disable the address
  /// input fields
  final bool enabled;

  /// Optional address description
  /// field
  final bool showDescriptionField;

  /// Optional county field
  final bool showCountyField;

  /// Optional country field
  final bool showCountryField;

  /// Optional postal code validator
  final Validator? postalCodeValidator;

  /// Country selection priority list
  final List<Country>? countryPriorityList;

  /// Called when the an address input
  /// field comes into focus.
  final FocusCallback? onFocus;

  /// The focus node for the AddressInput widget.
  final FocusNode? focusNode;

  /// Called when the address value changes.
  final AddressInputCallback<T> onChanged;

  /// Helper to calculate the height of the
  /// AddressInput widget
  static double calculateHeight({
    bool showTypesSelection = true,
    bool showDescriptionField = false,
    bool showCountyField = false,
    bool showCountryField = false,
  }) {
    double totalHeight = 517.0;
    if (!showTypesSelection) {
      totalHeight -= 52.0;
    }
    if (!showDescriptionField) {
      totalHeight -= 85.0;
    }
    if (!showCountyField && !showCountryField) {
      totalHeight -= 85.0;
    }
    return totalHeight;
  }

  const AddressInput({
    super.key,
    this.labels = const AddressLabels(),
    this.value = const AddressValue._(),
    this.enabled = true,
    this.showDescriptionField = false,
    this.showCountyField = false,
    this.showCountryField = false,
    this.postalCodeValidator,
    this.countryPriorityList,
    this.onFocus,
    this.focusNode,
    required this.onChanged,
  });

  @override
  State<AddressInput<T>> createState() => _AddressInputState<T>();
}

class _AddressInputState<T> extends State<AddressInput<T>> {
  FocusEditingController? descriptionController;
  FocusEditingController? numberController;
  FocusEditingController? streetController;
  FocusEditingController? otherController;
  FocusEditingController? municipalityController;
  FocusEditingController? countyController;
  FocusEditingController? provinceController;
  FocusEditingController? postalCodeController;
  FocusEditingController? countryCodeController;

  Country country = CountryPickerUtils.getCountryByIsoCode('US');

  late List<Country> countryPriorityList;

  @override
  void initState() {
    super.initState();

    final countryCode = widget.value.countryCode;
    country = _getCountryName(countryCode);

    // Number, Street, Other, Municipality,
    // Province, Postal Code are required

    numberController = FocusEditingController(
      onFocus: widget.onFocus,
      focusNode: widget.focusNode,
    );
    numberController!.text = widget.value.number;

    streetController = FocusEditingController(
      onFocus: widget.onFocus,
    );
    streetController!.text = widget.value.street;

    otherController = FocusEditingController(
      onFocus: widget.onFocus,
    );
    otherController!.text = widget.value.other;

    municipalityController = FocusEditingController(
      onFocus: widget.onFocus,
    );
    municipalityController!.text = widget.value.municipality;

    provinceController = FocusEditingController(
      onFocus: widget.onFocus,
    );
    provinceController!.text = widget.value.province;

    postalCodeController = FocusEditingController(
      onFocus: widget.onFocus,
    );
    postalCodeController!.text = widget.value.postalCode;

    if (widget.showDescriptionField) {
      descriptionController = FocusEditingController(
        onFocus: widget.onFocus,
      );
      descriptionController!.text = widget.value.description ?? '';
    }
    if (widget.showCountyField) {
      countyController = FocusEditingController(
        onFocus: widget.onFocus,
      );
      countyController!.text = widget.value.county ?? '';
    }
    if (widget.showCountryField) {
      countryCodeController = FocusEditingController(
        onFocus: widget.onFocus,
      );
      countryCodeController!.text = country.name;
    }

    if (widget.countryPriorityList != null) {
      countryPriorityList = widget.countryPriorityList!;
    } else {
      countryPriorityList = [
        CountryPickerUtils.getCountryByIsoCode('US'),
        CountryPickerUtils.getCountryByIsoCode('GB'),
      ];
    }
  }

  @override
  void dispose() {
    descriptionController?.dispose();
    numberController?.dispose();
    streetController?.dispose();
    otherController?.dispose();
    municipalityController?.dispose();
    countyController?.dispose();
    provinceController?.dispose();
    postalCodeController?.dispose();
    countryCodeController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final labels = !widget.labels.isValid
        ? AddressLabels.fromLocalization<T>(context)
        : widget.labels;

    Validator? postalCodeValidator;
    if (widget.postalCodeValidator == null) {
      final countryCode = country.isoCode;
      switch (countryCode) {
        case 'US':
          postalCodeValidator = CallbackValidator(
            callback: (value) => _usZipCodeMatcher.hasMatch(value ?? ''),
            longErrorMessage: l10n.invalidZipCodeErrorMessage,
            shortErrorMessage: l10n.invalidMessage,
          );
          break;
        case 'GB':
          postalCodeValidator = CallbackValidator(
            callback: (value) => _gbPostalCodeMatcher.hasMatch(value ?? ''),
            longErrorMessage: l10n.invalidPostalCodeErrorMessage,
            shortErrorMessage: l10n.invalidMessage,
          );
          break;
        default:
          postalCodeValidator = widget.postalCodeValidator;
      }
    }

    (String, InputIcon?)? selectedProvince;
    if (widget.labels.provinceList != null) {
      selectedProvince = _getSelectedProvice();
      Future.microtask(() {
        provinceController!.text = selectedProvince?.$1 ?? '';
      });
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        final halfWidth = constraints.maxWidth / 2;

        final numberFieldWidth = max<double>(125, constraints.maxWidth * 0.2);
        final streetFieldWidth = constraints.maxWidth - numberFieldWidth;
        final otherWidth =
            (countyController == null && countryCodeController != null) ||
                    (countyController != null && countryCodeController == null)
                ? constraints.maxWidth
                : halfWidth;

        return Wrap(
          children: [
            if (widget.labels.addressTypes != null)
              Container(
                padding: const EdgeInsets.fromLTRB(8.0, 8.0, 8.0, 16.0),
                width: constraints.maxWidth,
                child: SegmentedButton<T>(
                  segments: widget.labels.addressTypes!
                      .map(
                        (t) => t.tooltip == null
                            ? ButtonSegment<T>(
                                value: t.value,
                                label: Text(t.label),
                                icon: t.icon,
                                enabled: widget.enabled && t.enabled,
                              )
                            : ButtonSegment<T>(
                                value: t.value,
                                label: Tooltip(
                                  message: t.tooltip!,
                                  child: Text(t.label),
                                ),
                                icon: t.icon != null
                                    ? Tooltip(
                                        message: t.tooltip!,
                                        child: t.icon,
                                      )
                                    : null,
                                enabled: widget.enabled && t.enabled,
                              ),
                      )
                      .toList(),
                  selected: widget.value.type == null //
                      ? <T>{}
                      // ignore: null_check_on_nullable_type_parameter
                      : <T>{widget.value.type!},
                  emptySelectionAllowed: true,
                  onSelectionChanged: (Set<T> newSeletion) {
                    widget.onChanged(
                      widget.value.copyWith(type: newSeletion.first),
                    );
                  },
                ),
              ),
            SizedBox(
              width: constraints.maxWidth,
              child: descriptionController != null
                  ? TextInput(
                      labelText: labels.description,
                      enabled: widget.enabled,
                      focusNode: descriptionController!.focusNode,
                      controller: descriptionController!.controller,
                      onChanged: (value, isValid) {
                        widget.onChanged(
                          widget.value.copyWith(
                            description: value,
                            isFieldValid: ('description', isValid),
                          ),
                        );
                      },
                    )
                  : const SizedBox.shrink(),
            ),
            SizedBox(
              width: numberFieldWidth,
              child: TextInput(
                labelText: labels.number,
                isRequired: true,
                enabled: widget.enabled,
                focusNode: numberController!.focusNode,
                controller: numberController!.controller,
                onChanged: (value, isValid) {
                  widget.onChanged(
                    widget.value.copyWith(
                      number: value,
                      isFieldValid: ('number', isValid),
                    ),
                  );
                },
              ),
            ),
            SizedBox(
              width: streetFieldWidth,
              child: TextInput(
                labelText: labels.street,
                isRequired: true,
                enabled: widget.enabled,
                focusNode: streetController!.focusNode,
                controller: streetController!.controller,
                onChanged: (value, isValid) {
                  widget.onChanged(
                    widget.value.copyWith(
                      street: value,
                      isFieldValid: ('street', isValid),
                    ),
                  );
                },
              ),
            ),
            SizedBox(
              width: otherWidth,
              child: TextInput(
                labelText: labels.other,
                enabled: widget.enabled,
                focusNode: otherController!.focusNode,
                controller: otherController!.controller,
                onChanged: (value, isValid) {
                  widget.onChanged(
                    widget.value.copyWith(
                      other: value,
                      isFieldValid: ('other', isValid),
                    ),
                  );
                },
              ),
            ),
            SizedBox(
              width: halfWidth,
              child: TextInput(
                labelText: labels.municipality,
                isRequired: true,
                enabled: widget.enabled,
                focusNode: municipalityController!.focusNode,
                controller: municipalityController!.controller,
                onChanged: (value, isValid) {
                  widget.onChanged(
                    widget.value.copyWith(
                      municipality: value,
                      isFieldValid: ('municipality', isValid),
                    ),
                  );
                },
              ),
            ),
            if (countyController != null)
              SizedBox(
                width: halfWidth,
                child: TextInput(
                  labelText: labels.county,
                  enabled: widget.enabled,
                  focusNode: countyController!.focusNode,
                  controller: countyController!.controller,
                  onChanged: (value, isValid) {
                    widget.onChanged(
                      widget.value.copyWith(
                        county: value,
                        isFieldValid: ('county', isValid),
                      ),
                    );
                  },
                ),
              ),
            SizedBox(
              width: halfWidth,
              child: TextInput(
                labelText: labels.province,
                isRequired: true,
                enabled: widget.enabled,
                focusNode: provinceController!.focusNode,
                controller: provinceController!.controller,
                options: labels.provinceList
                    ?.map(
                      (p) => DropdownListItem(
                        id: p.$1,
                        label: p.$2,
                        iconWidget: p.$3,
                      ),
                    )
                    .toList(),
                optionsSemanticLabel: labels.provincePickerSemanticLabel,
                isOptionEditable: false,
                leadingIcon: selectedProvince?.$2,
                validator: labels.provinceList != null
                    ? ListValidator(
                        items: labels.provinceList!.map((p) => p.$1).toList(),
                      )
                    : null,
                onChanged: (value, isValid) {
                  widget.onChanged(
                    widget.value.copyWith(
                      province: value,
                      isFieldValid: ('province', isValid),
                    ),
                  );
                },
              ),
            ),
            SizedBox(
              width: halfWidth,
              child: TextInput(
                labelText: labels.postalCode,
                isRequired: true,
                enabled: widget.enabled,
                focusNode: postalCodeController!.focusNode,
                controller: postalCodeController!.controller,
                validator: postalCodeValidator,
                inputFormatters: [
                  services.TextInputFormatter.withFunction(
                    (TextEditingValue oldValue, TextEditingValue newValue) {
                      return TextEditingValue(
                        text: newValue.text.toUpperCase(),
                        selection: newValue.selection,
                      );
                    },
                  ),
                ],
                onChanged: (value, isValid) {
                  widget.onChanged(
                    widget.value.copyWith(
                      postalCode: value,
                      isFieldValid: ('postalCode', isValid),
                    ),
                  );
                },
              ),
            ),
            if (widget.showCountryField)
              SizedBox(
                width: halfWidth,
                child: TextInput(
                  labelText: labels.countryCode,
                  isRequired: true,
                  enabled: widget.enabled,
                  leadingIcon: InputIcon(
                    iconWidget: Padding(
                      padding: const EdgeInsets.fromLTRB(
                        8.0,
                        16.0,
                        8.0,
                        16.0,
                      ),
                      child: CountryPickerUtils.getDefaultFlagImage(
                        country,
                      ),
                    ),
                    semanticLabel: labels.countryPickerSemanticLabel,
                    onIconTap: () {
                      _openCountryPickerDialog(context);
                    },
                  ),
                  focusNode: countryCodeController!.focusNode,
                  controller: countryCodeController!.controller,
                  validator: CallbackValidator(
                    callback: (value) {
                      try {
                        CountryPickerUtils.getCountryByName(value ?? '');
                        return true;
                      } catch (_) {
                        return false;
                      }
                    },
                    longErrorMessage: l10n.invalidCountryErrorMessage,
                    shortErrorMessage: l10n.invalidMessage,
                  ),
                  onChanged: (value, isValid) {
                    try {
                      final country = CountryPickerUtils.getCountryByName(
                        value ?? '',
                      );
                      setState(() {
                        this.country = country;
                      });
                      widget.onChanged(
                        widget.value.copyWith(
                          countryCode: widget.value.countryCode.length == 3
                              ? country.iso3Code
                              : country.isoCode,
                          isFieldValid: ('countryCode', isValid),
                        ),
                      );
                    } catch (_) {
                      widget.onChanged(
                        widget.value.copyWith(
                          isFieldValid: ('countryCode', false),
                        ),
                      );
                    }
                  },
                ),
              ),
          ],
        );
      },
    );
  }

  void _openCountryPickerDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => CountryPickerDialog(
        titlePadding: const EdgeInsets.all(8.0),
        searchInputDecoration: const InputDecoration(
          hintText: 'Search Country...',
        ),
        priorityList: countryPriorityList,
        isSearchable: true,
        title: const Text('Select the Address Country'),
        onValuePicked: (Country country) {
          setState(() {
            this.country = country;
            countryCodeController!.text = country.name;
          });
          widget.onChanged(
            widget.value.copyWith(
              countryCode: widget.value.countryCode.length == 3
                  ? country.iso3Code
                  : country.isoCode,
            ),
          );
        },
        itemBuilder: (country) => Row(
          children: <Widget>[
            CountryPickerUtils.getDefaultFlagImage(country),
            const SizedBox(width: 8.0),
            Text(country.name),
          ],
        ),
      ),
    );
  }

  Country _getCountryName(String countryCode) {
    return countryCode.length == 3
        ? CountryPickerUtils.getCountryByIso3Code(countryCode)
        : CountryPickerUtils.getCountryByIsoCode(countryCode);
  }

  (String, InputIcon?) _getSelectedProvice() {
    final selectedProvince = widget.labels.provinceList!.firstWhere(
      (p) => p.$1 == widget.value.province,
      orElse: () => const ('', '', null),
    );
    if (selectedProvince.$3 != null) {
      return (
        selectedProvince.$2,
        InputIcon(
          iconWidget: Padding(
            padding: const EdgeInsets.fromLTRB(
              8.0,
              16.0,
              8.0,
              16.0,
            ),
            child: selectedProvince.$3,
          ),
        ),
      );
    } else {
      return (selectedProvince.$2, null);
    }
  }
}

class AddressValue<T> extends FormValue {
  /// the type of address
  final T? type;

  /// a brief description of the address
  final String? description;

  /// house number, building number, etc.
  final String number;

  /// street name
  final String street;

  /// apartment, suite, unit, etc.
  final String other;

  /// city or town
  final String municipality;

  /// county
  final String? county;

  /// province or state
  final String province;

  /// postal code or zip+4 code
  final String postalCode;

  /// country code (ISO 3166-1 alpha-2)
  final String countryCode;

  /// determines if all required fields are empty
  @override
  bool get isEmpty =>
      (description == null || description!.isEmpty) &&
      number.isEmpty &&
      street.isEmpty &&
      municipality.isEmpty &&
      (county == null || county!.isEmpty) &&
      province.isEmpty &&
      postalCode.isEmpty;

  /// determines if all required fields are not empty
  @override
  bool get isRequiredNotEmpty =>
      type != null &&
      number.isNotEmpty &&
      street.isNotEmpty &&
      municipality.isNotEmpty &&
      province.isNotEmpty &&
      postalCode.isNotEmpty;

  /// captures the validity of field data
  @override
  final Map<String, bool> validFields;

  static AddressValue<T> create<T>({
    T? type,
    String? description,
    String? number,
    String? street,
    String? other,
    String? municipality,
    String? county,
    String? province,
    String? postalCode,
    String countryCode = 'US',
  }) {
    return AddressValue<T>._(
      key: UniqueKey(),
      type: type,
      description: description,
      number: number ?? '',
      street: street ?? '',
      other: other ?? '',
      municipality: municipality ?? '',
      county: county,
      province: province ?? '',
      postalCode: postalCode ?? '',
      countryCode: countryCode,
    );
  }

  const AddressValue._({
    super.key,
    this.type,
    this.description,
    this.number = '',
    this.street = '',
    this.other = '',
    this.municipality = '',
    this.county,
    this.province = '',
    this.postalCode = '',
    this.countryCode = 'US',
    this.validFields = const {
      'number': true,
      'street': true,
      'municipality': true,
      'province': true,
      'postalCode': true,
    },
  });

  AddressValue<T> copyWith({
    T? type,
    String? description,
    String? number,
    String? street,
    String? other,
    String? municipality,
    String? county,
    String? province,
    String? postalCode,
    String? countryCode,
    (String, bool)? isFieldValid,
  }) {
    return AddressValue._(
      key: key,
      type: type ?? this.type,
      description: description ?? this.description,
      number: number ?? this.number,
      street: street ?? this.street,
      other: other ?? this.other,
      municipality: municipality ?? this.municipality,
      county: county ?? this.county,
      province: province ?? this.province,
      postalCode: postalCode ?? this.postalCode,
      countryCode: countryCode ?? this.countryCode,
      // if isFieldValid is not null, update the validFields map
      validFields: isFieldValid != null
          ? {
              ...validFields,
              isFieldValid.$1: isFieldValid.$2,
            }
          : validFields,
    );
  }

  @override
  List<Object?> get props => [
        type,
        description,
        number,
        street,
        other,
        municipality,
        county,
        province,
        postalCode,
        countryCode,
        isValid
      ];
}

class AddressLabels<T> {
  final String? description;
  final String? number;
  final String? street;
  final String? other;
  final String? municipality;
  final String? county;
  final String? province;
  final String? postalCode;
  final String? countryCode;

  final String? provincePickerSemanticLabel;
  final String? countryPickerSemanticLabel;

  /// a list of possible address types
  final List<AddressTypeSelection<T>>? addressTypes;

  /// provice or state list.
  /// (province code, province name, flag image)
  final List<(String, String, Image?)>? provinceList;

  /// determines if all required labels are set
  bool get isValid =>
      number != null &&
      street != null &&
      other != null &&
      municipality != null &&
      province != null &&
      postalCode != null &&
      countryCode != null;

  const AddressLabels({
    this.description,
    this.number,
    this.street,
    this.other,
    this.municipality,
    this.county,
    this.province,
    this.postalCode,
    this.countryCode,
    this.provincePickerSemanticLabel,
    this.countryPickerSemanticLabel,
    this.addressTypes,
    this.provinceList,
  });

  static AddressLabels<T> fromLocalization<T>(
    BuildContext context, {
    String? countryCode = 'US',
  }) {
    final l10n = context.l10n;
    if (countryCode == 'US' || countryCode == 'USA') {
      return AddressLabels<T>(
        description: l10n.descriptionFieldName,
        number: l10n.addrNumberFieldName,
        street: l10n.addrStreetFieldName,
        other: l10n.addrOtherFieldName,
        municipality: l10n.addrCityFieldName,
        county: l10n.addrCountyFieldName,
        province: l10n.addrStateFieldName,
        postalCode: l10n.addrZipCodeFieldName,
        countryCode: l10n.countryCodeFieldName,
        provinceList: _usStates,
        provincePickerSemanticLabel: l10n.semUSStateCodePicker,
        countryPickerSemanticLabel: l10n.semCountryCodePicker,
      );
    }
    if (countryCode == 'GB' || countryCode == 'GBR') {
      return AddressLabels<T>(
        description: l10n.descriptionFieldName,
        number: l10n.addrNumberFieldName,
        street: l10n.addrStreetFieldName,
        other: l10n.addrOtherFieldName,
        municipality: l10n.addrTownFieldName,
        county: l10n.addrLocalityFieldName,
        province: l10n.countryCodeFieldName,
        postalCode: l10n.addrPostalCodeFieldName,
        countryCode: l10n.sovereignStateCodeFieldName,
        provinceList: gbCountries,
        provincePickerSemanticLabel: l10n.semGBCountryPicker,
        countryPickerSemanticLabel: l10n.semGBSovereignStateCodePicker,
      );
    } else {
      return AddressLabels<T>(
        description: l10n.descriptionFieldName,
        number: l10n.addrNumberFieldName,
        street: l10n.addrStreetFieldName,
        other: l10n.addrOtherFieldName,
        municipality: l10n.addrMunicipalityFieldName,
        county: l10n.addrCountyFieldName,
        province: l10n.addrProvinceFieldName,
        postalCode: l10n.addrPostalCodeFieldName,
        countryCode: l10n.countryCodeFieldName,
        provincePickerSemanticLabel: l10n.semProviceCodePicker,
        countryPickerSemanticLabel: l10n.semCountryCodePicker,
      );
    }
  }

  AddressLabels<T> copyWith({
    String? description,
    String? number,
    String? street,
    String? other,
    String? municipality,
    String? county,
    String? province,
    String? postalCode,
    String? countryCode,
    String? provincePickerSemanticLabel,
    String? countryPickerSemanticLabel,
    List<AddressTypeSelection<T>>? addressTypes,
    String? addressTypeTooltip,
    List<(String, String, Image?)>? provinceList,
  }) {
    return AddressLabels<T>(
      description: description ?? this.description,
      number: number ?? this.number,
      street: street ?? this.street,
      other: other ?? this.other,
      municipality: municipality ?? this.municipality,
      county: county ?? this.county,
      province: province ?? this.province,
      postalCode: postalCode ?? this.postalCode,
      countryCode: countryCode ?? this.countryCode,
      provincePickerSemanticLabel:
          provincePickerSemanticLabel ?? this.provincePickerSemanticLabel,
      countryPickerSemanticLabel:
          countryPickerSemanticLabel ?? this.countryPickerSemanticLabel,
      addressTypes: addressTypes ?? this.addressTypes,
      provinceList: provinceList ?? this.provinceList,
    );
  }
}

class AddressTypeSelection<T> {
  final T value;

  final String label;
  final String? tooltip;

  final Widget? icon;
  final bool enabled;

  const AddressTypeSelection({
    required this.value,
    required this.label,
    this.tooltip,
    this.icon,
    this.enabled = false,
  });

  AddressTypeSelection<T> copyWith({
    T? value,
    String? label,
    String? tooltip,
    Widget? icon,
    bool? enabled,
  }) {
    return AddressTypeSelection<T>(
      value: value ?? this.value,
      label: label ?? this.label,
      tooltip: tooltip ?? this.tooltip,
      icon: icon ?? this.icon,
      enabled: enabled ?? this.enabled,
    );
  }
}

typedef AddressInputCallback<T> = void Function(
  AddressValue<T> value,
);

// US States

List<(String, String, Image?)> _usStates = [
  ('AL', 'Alabama', _flagImage('us-flags/al')),
  ('AK', 'Alaska', _flagImage('us-flags/ak')),
  ('AZ', 'Arizona', _flagImage('us-flags/az')),
  ('AR', 'Arkansas', _flagImage('us-flags/ar')),
  ('CA', 'California', _flagImage('us-flags/ca')),
  ('CO', 'Colorado', _flagImage('us-flags/co')),
  ('CT', 'Connecticut', _flagImage('us-flags/ct')),
  ('DE', 'Delaware', _flagImage('us-flags/de')),
  ('FL', 'Florida', _flagImage('us-flags/fl')),
  ('GA', 'Georgia', _flagImage('us-flags/ga')),
  ('HI', 'Hawaii', _flagImage('us-flags/hi')),
  ('ID', 'Idaho', _flagImage('us-flags/id')),
  ('IL', 'Illinois', _flagImage('us-flags/il')),
  ('IN', 'Indiana', _flagImage('us-flags/in')),
  ('IA', 'Iowa', _flagImage('us-flags/ia')),
  ('KS', 'Kansas', _flagImage('us-flags/ks')),
  ('KY', 'Kentucky', _flagImage('us-flags/ky')),
  ('LA', 'Louisiana', _flagImage('us-flags/la')),
  ('ME', 'Maine', _flagImage('us-flags/me')),
  ('MD', 'Maryland', _flagImage('us-flags/md')),
  ('MA', 'Massachusetts', _flagImage('us-flags/ma')),
  ('MI', 'Michigan', _flagImage('us-flags/mi')),
  ('MN', 'Minnesota', _flagImage('us-flags/mn')),
  ('MS', 'Mississippi', _flagImage('us-flags/ms')),
  ('MO', 'Missouri', _flagImage('us-flags/mo')),
  ('MT', 'Montana', _flagImage('us-flags/mt')),
  ('NE', 'Nebraska', _flagImage('us-flags/ne')),
  ('NV', 'Nevada', _flagImage('us-flags/nv')),
  ('NH', 'New Hampshire', _flagImage('us-flags/nh')),
  ('NJ', 'New Jersey', _flagImage('us-flags/nj')),
  ('NM', 'New Mexico', _flagImage('us-flags/nm')),
  ('NY', 'New York', _flagImage('us-flags/ny')),
  ('NC', 'North Carolina', _flagImage('us-flags/nc')),
  ('ND', 'North Dakota', _flagImage('us-flags/nd')),
  ('OH', 'Ohio', _flagImage('us-flags/oh')),
  ('OK', 'Oklahoma', _flagImage('us-flags/ok')),
  ('OR', 'Oregon', _flagImage('us-flags/or')),
  ('PA', 'Pennsylvania', _flagImage('us-flags/pa')),
  ('RI', 'Rhode Island', _flagImage('us-flags/ri')),
  ('SC', 'South Carolina', _flagImage('us-flags/sc')),
  ('SD', 'South Dakota', _flagImage('us-flags/sd')),
  ('TN', 'Tennessee', _flagImage('us-flags/tn')),
  ('TX', 'Texas', _flagImage('us-flags/tx')),
  ('UT', 'Utah', _flagImage('us-flags/ut')),
  ('VT', 'Vermont', _flagImage('us-flags/vt')),
  ('VA', 'Virginia', _flagImage('us-flags/va')),
  ('WA', 'Washington', _flagImage('us-flags/wa')),
  ('WV', 'West Virginia', _flagImage('us-flags/wv')),
  ('WI', 'Wisconsin', _flagImage('us-flags/wi')),
  ('WY', 'Wyoming', _flagImage('us-flags/wy')),
  ('DC', 'District of Columbia', _flagImage('us-flags/dc')),
];
final _usZipCodeMatcher = RegExp(r'^\d{5}(-\d{4})?$');

// GB Countries

List<(String, String, Image?)> gbCountries = [
  ('England', 'England', _flagImage('uk-flags/gb-eng')),
  ('Scotland', 'Scotland', _flagImage('uk-flags/gb-sct')),
  ('Wales', 'Wales', _flagImage('uk-flags/gb-wls')),
  ('Northern Ireland', 'Northern Ireland', _flagImage('uk-flags/gb-nir')),
];
final _gbPostalCodeMatcher = RegExp(
  r'^[A-Z]{1,2}[0-9]{1,2}[A-Z]?(\s*[0-9][A-Z]{1,2})?$',
);

// Helper Functions

Image _flagImage(String name) {
  return Image.asset(
    'assets/${name.toLowerCase()}.png',
    height: 20.0,
    width: 30.0,
    fit: BoxFit.fill,
    package: 'ui_widgets_component',
  );
}
