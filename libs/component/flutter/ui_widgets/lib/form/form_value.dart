import 'package:flutter/foundation.dart';
import 'package:equatable/equatable.dart';

/// A form value that can be used in a form
/// to capture the a composite input value.
abstract class FormValue extends Equatable {
  /// true if the form value is read only
  bool get isReadOnly => false;

  /// determines if all fields are not empty
  bool get isNotEmpty => !isEmpty;

  /// determines if all fields are empty
  bool get isEmpty;

  /// determines if all required fields are not empty
  bool get isRequiredNotEmpty;

  /// determines if all required fields are valie
  bool get isValid => validFields.entries.fold<bool>(
        true,
        (prev, e) => prev && e.value,
      );

  /// captures the validity of field data
  Map<String, bool> get validFields;

  final Key? key;

  const FormValue({
    this.key,
  });
}

/// A form value that captures a single input value
class InputValue extends FormValue {
  final String value;

  @override
  bool get isEmpty => value.isEmpty;

  @override
  bool get isRequiredNotEmpty {
    assert(isRequired, 'isRequired is false');
    return value.isNotEmpty;
  }

  @override
  final Map<String, bool> validFields;

  final bool isRequired;

  static InputValue create({
    String value = '',
    bool isRequired = true,
  }) {
    return InputValue._(
      key: UniqueKey(),
      isRequired: isRequired,
      value: value,
    );
  }

  const InputValue._({
    super.key,
    required this.isRequired,
    required this.value,
    this.validFields = const {
      'value': true,
    },
  });

  InputValue copyWith({
    String? value,
    (String, bool)? isFieldValid,
  }) {
    return InputValue._(
      key: key,
      isRequired: isRequired,
      value: value ?? this.value,
      // if isFieldValid is not null, update the validFields map
      validFields: isFieldValid != null
          ? {
              ...validFields,
              isFieldValid.$1: isFieldValid.$2,
            }
          : validFields,
    );
  }

  @override
  List<Object?> get props => [
        value,
      ];
}

final class FormValues {
  static bool isDirty(List<FormValue> values, List<FormValue> initialValues) {
    int i = 0; // index to compare with initialValues
    for (final value in values) {
      if (value.isNotEmpty /* ignore empty values */) {
        bool isDirty = //
            i >= initialValues.length //
                ||
                (i < initialValues.length //
                    &&
                    values[i] != initialValues[i]);

        if (!value.isRequiredNotEmpty || !value.isValid) {
          // if value's required fields are not empty or
          // it is not valid then abort and mark form
          // values as not dirty so that the form cannot
          // be submitted.
          return false;
        }
        if (isDirty) {
          return true;
        }
        i++;
      }
    }
    return i != initialValues.length;
  }
}
