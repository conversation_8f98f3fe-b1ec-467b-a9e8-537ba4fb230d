import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../utils/uuid.dart';
import '../utils/text.dart';
import '../validators/validator.dart';

class TextInput extends StatefulWidget {
  final String? labelText;
  final String? initialValue;

  final TextInputType? keyboardType;

  final int? maxLength;

  final int? maxCharsInLine;
  final int maxLines;

  final bool hExpandable;
  final bool vExpandable;

  final List<DropdownListItem>? options;
  final bool isOptionEditable;

  final String? optionsSemanticLabel;

  final bool isRequired;
  final bool readOnly;
  final bool enabled;

  // Ensure hieght of input field is fixed to
  // include error/hint text decorations
  // regardless of whether they are shown.
  final bool fixedHeight;

  final InputIcon? leadingIcon;
  final InputIcon? trailingIcon;

  final TextStyle? textStyle;
  final Color? focusedBorderColor;

  final FocusNode? focusNode;
  final TextEditingController? controller;

  final List<TextInputFormatter>? inputFormatters;

  final Validator? validator;
  final ValidatedValueChanged<String?>? onChanged;

  final ValueChanged<String>? onEnter;

  final EdgeInsets padding;

  const TextInput({
    super.key,
    this.labelText,
    this.initialValue,
    this.keyboardType,
    this.maxCharsInLine,
    this.maxLines = 1,
    this.maxLength,
    this.hExpandable = false,
    this.vExpandable = false,
    this.options,
    this.isOptionEditable = true,
    this.optionsSemanticLabel,
    this.isRequired = false,
    this.readOnly = false,
    this.enabled = true,
    this.fixedHeight = true,
    this.leadingIcon,
    this.trailingIcon,
    this.textStyle,
    this.focusedBorderColor,
    this.focusNode,
    this.controller,
    this.inputFormatters,
    this.validator,
    this.onChanged,
    this.onEnter,
    this.padding = const EdgeInsets.fromLTRB(8.0, 8.0, 8.0, 0.0),
  })  : assert(
          !hExpandable || (maxCharsInLine != null && maxCharsInLine > 1),
          'When hExpandable is true, maxCharsInLine must be greater than 1',
        ),
        assert(
          options == null || (!hExpandable && !vExpandable),
          'Dropdown options are not supported when input is expandable',
        );

  String lookupLabelText(BuildContext context) => labelText ?? '';

  @override
  State<StatefulWidget> createState() => _TextInputState();
}

class _TextInputState extends State<TextInput> {
  final key = Key(uuid());

  double inputWindowWidthMargin = 0;
  double inputWindowHeightMargin = 0;

  double? minTextWidth;
  double minTextHeight = 0;

  double? textInputWidth;
  double textInputHeight = 0;

  String? errorText;
  String? errorTooltip;

  late final FocusNode focusNode;
  late final TextEditingController controller;

  MenuController? menuController;

  _TextInputState();

  @override
  void initState() {
    super.initState();

    focusNode = widget.focusNode ?? FocusNode();
    focusNode.addListener(() {
      if (mounted &&
          !focusNode.hasFocus &&
          menuController != null &&
          menuController!.isOpen) {
        Future.delayed(
          const Duration(milliseconds: 500),
          () => menuController?.close(),
        );
      }
    });

    controller = widget.controller ?? TextEditingController();
    if (widget.initialValue != null) {
      controller.text = widget.initialValue!;
    }

    inputWindowWidthMargin = widget.padding.left + 26 + widget.padding.right;
    inputWindowHeightMargin = widget.padding.top + 58 + widget.padding.bottom;

    final textSize = getTextSize(
      'A',
      style: widget.textStyle ?? const TextTheme().bodyLarge,
    );

    minTextWidth = widget.maxCharsInLine != null
        ? textSize.width * widget.maxCharsInLine!
        : null;

    minTextHeight = (textSize.height + 3) * widget.maxLines;

    textInputWidth = minTextWidth;
    textInputHeight = minTextHeight;
  }

  @override
  void dispose() {
    super.dispose();

    if (widget.focusNode == null) {
      focusNode.dispose();
    }
    if (widget.controller == null) {
      controller.dispose();
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.controller == null //
        ? VisibilityDetector(
            key: key,
            onVisibilityChanged: (VisibilityInfo info) {
              if (info.visibleFraction > 0 &&
                  widget.initialValue != null &&
                  mounted) {
                setState(() {
                  controller.text = widget.initialValue!;
                });
              }
            },
            child: _buildInput(context),
          )
        : _buildInput(context);
  }

  Widget _buildInput(BuildContext context) {
    final width = textInputWidth != null
        ? textInputWidth! + inputWindowWidthMargin
        : double.infinity;
    final height = textInputHeight + inputWindowHeightMargin;

    if (widget.hExpandable || widget.vExpandable) {
      return LayoutBuilder(builder: (context, constraints) {
        return Container(
          padding: widget.padding,
          constraints: widget.fixedHeight //
              ? BoxConstraints(
                  minWidth: width,
                  maxWidth: width,
                  minHeight: height,
                  maxHeight: height,
                )
              : null,
          child: _buildExpandableTextFormField(
            context,
            maxWidth: constraints.maxWidth,
            maxHeight: constraints.maxHeight,
          ),
        );
      });
    } else {
      return Container(
        padding: widget.padding,
        constraints: widget.fixedHeight //
            ? BoxConstraints(
                minWidth: width,
                maxWidth: width,
                minHeight: height,
                maxHeight: height,
              )
            : null,
        child: widget.options == null
            ? _buildTextFormField(
                context,
                maxLines: widget.maxLines,
                expands: false,
              )
            : _buildTextFormFieldWithDropdown(context),
      );
    }
  }

  Widget _buildExpandableTextFormField(
    BuildContext context, {
    required double maxWidth,
    required double maxHeight,
  }) {
    final theme = Theme.of(context);

    final dragHandle = SvgPicture.asset(
      'assets/resize-right.svg',
      height: 16.0,
      width: 16.0,
      colorFilter: ColorFilter.mode(
        theme.colorScheme.onSurface.withValues(alpha: 0.5),
        BlendMode.srcIn,
      ),
      fit: BoxFit.fill,
      package: 'ui_widgets_component',
    );

    return Stack(
      children: [
        _buildTextFormField(
          context,
          maxLines: null,
          expands: true,
        ),
        Positioned(
          bottom: 26,
          right: 0,
          child: MouseRegion(
            cursor: widget.vExpandable && widget.hExpandable
                ? SystemMouseCursors.resizeUpLeftDownRight
                : widget.vExpandable
                    ? SystemMouseCursors.resizeUpDown
                    : SystemMouseCursors.resizeLeftRight,
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              child: dragHandle,
              onPanUpdate: (details) {
                setState(() {
                  if (widget.hExpandable) {
                    textInputWidth = (textInputWidth! + details.delta.dx).clamp(
                      minTextWidth!,
                      maxWidth - inputWindowWidthMargin,
                    );
                  }
                  if (widget.vExpandable) {
                    textInputHeight =
                        (textInputHeight + details.delta.dy).clamp(
                      minTextHeight,
                      maxHeight - inputWindowHeightMargin,
                    );
                  }
                });
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTextFormField(
    BuildContext context, {
    required int? maxLines,
    required bool expands,
  }) {
    final theme = Theme.of(context);

    final validator = widget.validator;
    final labelName = widget.lookupLabelText(context);

    final isMultiLine = maxLines == null || maxLines > 1;

    return TextFormField(
      enabled: widget.enabled,
      readOnly: widget.readOnly,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      decoration: InputDecoration(
        prefixIcon: _buildInputIcon(theme, widget.leadingIcon),
        suffixIcon: _buildTrailingIcon(theme, errorTooltip),
        labelText: widget.isRequired ? '$labelName *' : labelName,
        alignLabelWithHint: isMultiLine,
        border: const OutlineInputBorder(
          borderSide: BorderSide(),
        ),
        focusedBorder: widget.focusedBorderColor != null
            ? OutlineInputBorder(
                borderSide: BorderSide(
                  color: widget.focusedBorderColor!,
                  width: 2.0,
                ),
              )
            : null,
        floatingLabelStyle: _floatingLabelStyle(theme),
        // When TextFormField is expanded the input area fills the
        // entire height of the constrained box including space for
        // the error text. This is a workaround to ensure the error
        // text padding is always visible.
        helper: expands //
            ? const SizedBox(
                height: 18.0,
              )
            : null,
        contentPadding: expands //
            ? const EdgeInsets.fromLTRB(12, 20, 12, 12)
            : null,
      ),
      keyboardType: widget.keyboardType,
      style: widget.textStyle,
      textAlignVertical: isMultiLine ? TextAlignVertical.top : null,
      focusNode: focusNode,
      controller: controller,
      inputFormatters: widget.inputFormatters,
      maxLines: maxLines,
      expands: expands,
      maxLength: widget.maxLength,
      maxLengthEnforcement: widget.maxLength == null
          ? MaxLengthEnforcement.none
          : MaxLengthEnforcement.enforced,
      validator: (_) {
        return errorText;
      },
      onChanged: (value) {
        bool isValid = true;
        if (validator != null) {
          validator.validate(
            context,
            labelName,
            value,
          );
          isValid = validator.isValid;
          if (isValid) {
            setState(() {
              errorText = null;
              errorTooltip = null;
            });
          } else {
            setState(() {
              errorText = validator.shortErrorMessage;
              errorTooltip = validator.longErrorMessage;
            });
          }
        }
        if (widget.onChanged != null) {
          widget.onChanged!(value, isValid);
        }
      },
      onFieldSubmitted: widget.onEnter,
    );
  }

  Widget _buildTextFormFieldWithDropdown(
    BuildContext context,
  ) {
    final theme = Theme.of(context);

    final validator = widget.validator;
    final labelName = widget.lookupLabelText(context);

    void validateAndSet(String newValue) {
      bool isValid = true;
      if (validator != null) {
        validator.validate(
          context,
          labelName,
          newValue,
        );
        isValid = validator.isValid;
        if (isValid) {
          setState(() {
            errorText = null;
            errorTooltip = null;
          });
        } else {
          setState(() {
            errorText = validator.shortErrorMessage;
            errorTooltip = validator.longErrorMessage;
          });
        }
      }
      if (widget.onChanged != null) {
        widget.onChanged!(newValue, isValid);
      }
    }

    return DropdownMenu<String>(
      enabled: widget.enabled,
      requestFocusOnTap: true,
      focusNode: focusNode,
      expandedInsets: EdgeInsets.zero,
      leadingIcon: _buildInputIcon(theme, widget.leadingIcon),
      trailingIcon: Builder(builder: (context) {
        // This is a workaround to get the menu controller so that
        // the dropdown menu is closed when the focus is lost.
        menuController ??= MenuController.maybeOf(context);

        return _buildTrailingIcon(theme, errorTooltip) ??
            Icon(
              Icons.arrow_drop_down,
              semanticLabel: widget.optionsSemanticLabel,
            );
      }),
      label: Text(
        widget.isRequired ? '$labelName *' : labelName,
      ),
      textStyle: widget.textStyle ??
          theme.textTheme.bodyLarge?.copyWith(
            fontSize: 16,
          ),
      errorText: validator == null ? null : errorText,
      controller: controller,
      initialSelection: widget.initialValue,
      inputDecorationTheme: widget.focusedBorderColor != null
          ? const InputDecorationTheme().copyWith(
              border: const OutlineInputBorder(
                borderSide: BorderSide(),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(
                  color: widget.focusedBorderColor!,
                  width: 2.0,
                ),
              ),
              floatingLabelStyle: _floatingLabelStyle(theme),
            )
          : null,
      inputFormatters: [
        TextInputFormatter.withFunction(
          (oldValue, newValue) {
            if (widget.isOptionEditable) {
              if (widget.maxLength != null) {
                if (newValue.text.length > widget.maxLength!) {
                  newValue = oldValue;
                }
              }
              validateAndSet(newValue.text);
              return newValue;
            } else {
              return oldValue;
            }
          },
        ),
      ],
      onSelected: (value) {
        setState(() {
          errorText = null;
          errorTooltip = null;
        });
        validateAndSet(value ?? '');
      },
      dropdownMenuEntries: widget.options!.map((entry) {
        return DropdownMenuEntry<String>(
          value: entry.id,
          label: entry.label ?? '',
          labelWidget: entry.labelWidget,
          leadingIcon: entry.iconWidget ??
              (entry.icon == null ? null : Icon(entry.icon)),
        );
      }).toList(),
    );
  }

  Widget? _buildTrailingIcon(ThemeData theme, String? errorTooltip) {
    if (errorTooltip == null) {
      return _buildInputIcon(theme, widget.trailingIcon);
    } else {
      return Tooltip(
        message: errorTooltip,
        child: Icon(
          Icons.error,
          color: theme.colorScheme.error,
        ),
      );
    }
  }

  Widget? _buildInputIcon(ThemeData theme, InputIcon? inputIcon) {
    if (inputIcon != null) {
      if (inputIcon.icon != null || inputIcon.iconWidget != null) {
        final iconWidget = inputIcon.onIconTap == null
            ? inputIcon.iconWidget ??
                Icon(
                  inputIcon.icon,
                  semanticLabel: inputIcon.semanticLabel,
                  color: inputIcon.iconColor,
                )
            : MouseRegion(
                cursor: SystemMouseCursors.click,
                child: GestureDetector(
                  onTap: inputIcon.onIconTap,
                  child: inputIcon.iconWidget ??
                      Icon(
                        inputIcon.icon,
                        semanticLabel: inputIcon.semanticLabel,
                        color: inputIcon.iconColor,
                      ),
                ),
              );

        return inputIcon.iconTooltip == null
            ? iconWidget
            : Tooltip(
                message: inputIcon.iconTooltip ?? '',
                waitDuration: const Duration(seconds: 1),
                child: iconWidget,
              );
      }
    }
    return null;
  }

  TextStyle? _floatingLabelStyle(ThemeData theme) =>
      widget.focusedBorderColor != null
          ? WidgetStateTextStyle.resolveWith(
              (
                Set<WidgetState> states,
              ) =>
                  states.contains(WidgetState.error)
                      ? TextStyle(
                          color: theme.colorScheme.error,
                        )
                      : states.contains(WidgetState.focused)
                          ? TextStyle(
                              color: widget.focusedBorderColor,
                            )
                          : const TextStyle(),
            )
          : null;
}

typedef ValidatedValueChanged<T> = void Function(T value, bool isValid);

class InputIcon {
  final IconData? icon;
  final Widget? iconWidget;

  final Color? iconColor;
  final String? iconTooltip;
  final String? semanticLabel;
  final VoidCallback? onIconTap;

  const InputIcon({
    this.icon,
    this.iconWidget,
    this.iconColor,
    this.iconTooltip,
    this.semanticLabel,
    this.onIconTap,
  });
}

/// Dropdown list item represents a menu item
/// that will be displayed in the dropdown list.
class DropdownListItem {
  final String id;
  final String? label;
  final Widget? labelWidget;

  final IconData? icon;
  final Widget? iconWidget;

  const DropdownListItem({
    required this.id,
    String? label,
    this.labelWidget,
    this.icon,
    this.iconWidget,
  })  : assert(
          (icon == null && iconWidget == null) ||
              (icon != null && iconWidget == null) ||
              (icon == null && iconWidget != null),
          'icon and iconWidget are mutually exclusive',
        ),
        label = label ?? (labelWidget == null ? id : null);
}
