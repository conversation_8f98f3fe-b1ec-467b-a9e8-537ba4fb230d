// import 'package:flutter/material.dart';

// import '../utils/text.dart';

// // TODO: validate this component is needed or not

// class SectionTypeToggle<T> extends StatefulWidget {
//   /// Toggle values and labels
//   final List<(T, String)> options;

//   /// Initial selected value
//   final T? value;

//   // Toggle button properties
//   final TextStyle? textStyle;
//   final Color? selectedBorderColor;
//   final Color? selectedColor;
//   final Color? fillColor;

//   // Divider properties
//   final bool showDivider;
//   final double? height;
//   final double? thickness;
//   final double? indent;
//   final Color? color;

//   /// Callback when the selected value changes
//   final ValueChanged<T>? onChanged;

//   const SectionTypeToggle({
//     super.key,
//     this.value,
//     required this.options,
//     this.textStyle,
//     this.selectedBorderColor,
//     this.selectedColor,
//     this.fillColor,
//     this.showDivider = false,
//     this.height,
//     this.thickness,
//     this.indent,
//     this.color,
//     this.onChanged,
//   });

//   @override
//   State<SectionTypeToggle<T>> createState() => _SectionTypeToggleState<T>();
// }

// class _SectionTypeToggleState<T> extends State<SectionTypeToggle<T>> {
//   late final List<bool> selected;

//   @override
//   void initState() {
//     super.initState();
//     selected = List.generate(widget.options.length, (index) {
//       return widget.options[index].$1 == widget.value;
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     final ThemeData theme = Theme.of(context);

//     // calculate the width of the toggle buttons
//     final textStyle = widget.textStyle ?? theme.textTheme.bodySmall;
//     final toggleTextSize = widget.options.map((option) => option.$2).fold<Size>(
//       Size.zero,
//       (size, label) {
//         final s = getTextSize(
//           label,
//           style: textStyle,
//         );
//         return s.width > size.width ? s : size;
//       },
//     );
//     final toggleWidth = toggleTextSize.width * widget.options.length;

//     return Row(
//       children: [
//         Expanded(child: _buildLeftDivider()),
//         ToggleButtons(
//           direction: Axis.horizontal,
//           onPressed: (index) {
//             setState(() {
//               for (int i = 0; i < selected.length; i++) {
//                 selected[i] = i == index;
//               }
//             });
//             if (widget.onChanged != null) {
//               widget.onChanged!(widget.options[index].$1);
//             }
//           },
//           borderRadius: const BorderRadius.all(Radius.circular(8)),
//           selectedBorderColor: widget.selectedBorderColor,
//           selectedColor: widget.selectedColor,
//           fillColor: widget.fillColor,
//           color: widget.color,
//           textStyle: textStyle,
//           constraints: BoxConstraints(
//             minWidth: toggleWidth,
//             minHeight: toggleTextSize.height + 6,
//           ),
//           isSelected: selected,
//           children: widget.options.map((option) {
//             return Text(option.$2);
//           }).toList(),
//         ),
//         Expanded(child: _buildRightDivider()),
//       ],
//     );
//   }

//   Widget _buildLeftDivider() {
//     return widget.showDivider
//         ? Divider(
//             height: widget.height,
//             thickness: widget.thickness,
//             indent: widget.indent,
//             color: widget.color?.withValues(alpha: 0.5),
//           )
//         : const SizedBox.shrink();
//   }

//   Widget _buildRightDivider() {
//     return widget.showDivider
//         ? Divider(
//             height: widget.height,
//             thickness: widget.thickness,
//             endIndent: widget.indent,
//             color: widget.color?.withValues(alpha: 0.5),
//           )
//         : const SizedBox.shrink();
//   }
// }

// enum ScrollViewDirection {
//   horizontal,
//   vertical,
// }

// enum ScrollViewDividerPosition {
//   start,
//   end,
// }
