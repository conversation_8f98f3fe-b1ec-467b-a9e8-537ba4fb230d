import 'package:flutter/material.dart';

Size getTextSize(
  String text, {
  TextStyle? style,
}) {
  final TextPainter textPainter = TextPainter(
    text: TextSpan(
      text: text,
      style: style,
    ),
    maxLines: 1,
    textDirection: TextDirection.ltr,
  )..layout(minWidth: 0, maxWidth: double.infinity);

  return textPainter.size;
}

List<Widget> splitTextToFitWidth(
  String text, {
  required double maxWidth,
  double lineSpacing = 8.0,
  TextStyle? style,
}) {
  final List<Widget> lines = <Widget>[];
  final List<String> words = text.split(' ');
  final StringBuffer buffer = StringBuffer();

  for (final String word in words) {
    final String currentText = buffer.isNotEmpty //
        ? '${buffer.toString()} $word'
        : word;
    final Size textSize = getTextSize(currentText, style: style);

    if (textSize.width <= maxWidth) {
      buffer.write(buffer.isEmpty ? word : ' $word');
    } else {
      lines.add(
        Text(buffer.toString(), style: style),
      );
      if (lineSpacing > 0) {
        lines.add(
          SizedBox(height: lineSpacing),
        );
      }
      buffer.clear();
      buffer.write(word);
    }
  }
  lines.add(
    Text(buffer.toString(), style: style),
  );
  return lines;
}
