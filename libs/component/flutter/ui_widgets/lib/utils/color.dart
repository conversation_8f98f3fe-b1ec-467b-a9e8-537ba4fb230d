import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import 'package:ui_widgets_component/ui_widgets.dart';

import '../l10n/l10n.dart';

class HexColor extends Color {
  static int _getColorFromHex(String hexColor) {
    hexColor = hexColor.toUpperCase().replaceAll('#', '');
    if (hexColor.length <= 6) {
      hexColor = 'FF${hexColor.padLeft(6, '0')}';
    } else {
      throw ArgumentError('Hex color must be in the format #RRGGBB');
    }
    return int.parse(hexColor, radix: 16);
  }

  HexColor(final String hexColor) : super(_getColorFromHex(hexColor));
}

Color darken(
  Color color, [
  double amount = .1,
]) {
  assert(amount >= 0 && amount <= 1);

  final hsl = HSLColor.fromColor(color);
  final hslDark = hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0));

  return hslDark.toColor();
}

Color lighten(Color color, [double amount = .1]) {
  assert(amount >= 0 && amount <= 1);

  final hsl = HSLColor.fromColor(color);
  final hslLight = hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0));

  return hslLight.toColor();
}

bool isColorDark(Color color) {
  final value = color.toARGB32();
  double luminance = 0.299 * ((0x00ff0000 & value) >> 16) +
      0.587 * ((0x0000ff00 & value) >> 8) +
      0.114 * ((0x000000ff & value) >> 0);

  // Determine if the color is dark or light
  return luminance < 128;
}

void showColorPickerDialog(
  BuildContext context, {
  required String title,
  required Color selectedColor,
  required void Function(Color color) onColorChanged,
  String? semanticsLabel,
}) async {
  final l10n = context.l10n;

  showDialog(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        titlePadding: const EdgeInsets.only(
          left: 8.0,
          right: 8.0,
          top: 12.0,
        ),
        title: Center(
          child: Text(title),
        ),
        contentPadding: const EdgeInsets.only(
          left: 8.0,
          right: 8.0,
          bottom: 0.0,
        ),
        content: SingleChildScrollView(
          child: Column(
            children: [
              const Divider(),
              Padding(
                padding: const EdgeInsets.only(
                  left: 8.0,
                  right: 8.0,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: splitTextToFitWidth(
                    l10n.colorPickerPrompt,
                    maxWidth: 250,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
              ),
              const Divider(),
              Padding(
                padding: const EdgeInsets.only(left: 8.0),
                child: Semantics(
                  label: semanticsLabel,
                  excludeSemantics: semanticsLabel != null,
                  blockUserActions: true,
                  child: MaterialPicker(
                    pickerColor: selectedColor,
                    onColorChanged: onColorChanged,
                    enableLabel: false,
                    portraitOnly: true,
                  ),
                ),
              ),
              const Divider(),
            ],
          ),
        ),
        actionsPadding: const EdgeInsets.only(
          left: 8.0,
          right: 8.0,
          bottom: 8.0,
        ),
        actionsAlignment: MainAxisAlignment.center,
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.pop(context, 'OK'),
            child: const Text('OK'),
          ),
        ],
      );
    },
  );
}
