library ui_widgets_component;

export 'l10n/ui_widget_localizations.dart';

export 'validators/validator.dart';
export 'validators/callback_validator.dart';
export 'validators/chained_validator.dart';
export 'validators/regex_validator.dart';
export 'validators/list_validator.dart';
export 'validators/number_validator.dart';
export 'validators/min_length_validator.dart';
export 'validators/confirm_password_validator.dart';
export 'validators/email_address_validator.dart';
export 'validators/password_validator.dart';

export 'form/form_value.dart';
export 'form/multi_input.dart';
export 'form/text_input.dart';
export 'form/code_input.dart';
export 'form/username_input.dart';
export 'form/password_input.dart';
export 'form/phone_number_input.dart';
export 'form/email_address_input.dart';
export 'form/address_input.dart';
export 'form/contact_input.dart';
export 'form/selection_list.dart';

export 'dynamic/form_input_group.dart';
export 'dynamic/input_group_config.dart';
export 'dynamic/input_config.dart';
export 'dynamic/validator_config.dart';

export 'view/display_area_size.dart';
export 'view/themed_card.dart';
export 'view/folder_tab.dart';
export 'view/tabbed_folders.dart';
export 'view/group_box.dart';
export 'view/markdown.dart';

export 'dialog/elevated_icon_button.dart';
export 'dialog/avatar_button.dart';
export 'dialog/text_link.dart';
export 'dialog/scroll_view_divider.dart';
export 'dialog/dailog_form.dart';
export 'dialog/multi_form_controller.dart';
export 'dialog/alert_dialog.dart';

export 'menu/avatar_popup_menu.dart';
export 'menu/tools_app_bar.dart';
export 'menu/tools_scaffold.dart';
export 'menu/tools_view_bar.dart';
export 'menu/status_view_bar.dart';
export 'menu/tool.dart';
export 'menu/button_tool.dart';
export 'menu/status_text_tool.dart';
export 'menu/text_button_tool.dart';
export 'menu/divider_tool.dart';
export 'menu/spacer_tool.dart';

export 'utils/uuid.dart';
export 'utils/text.dart';
export 'utils/color.dart';
