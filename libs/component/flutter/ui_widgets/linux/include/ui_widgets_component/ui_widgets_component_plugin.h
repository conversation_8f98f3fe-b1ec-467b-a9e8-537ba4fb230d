#ifndef FLUTTER_PLUGIN_UI_WIDGETS_COMPONENT_PLUGIN_H_
#define FLUTTER_PLUGIN_UI_WIDGETS_COMPONENT_PLUGIN_H_

#include <flutter_linux/flutter_linux.h>

G_BEGIN_DECLS

#ifdef FLUTTER_PLUGIN_IMPL
#define FLUTTER_PLUGIN_EXPORT __attribute__((visibility("default")))
#else
#define FLUTTER_PLUGIN_EXPORT
#endif

typedef struct _UiWidgetsComponentPlugin UiWidgetsComponentPlugin;
typedef struct {
  GObjectClass parent_class;
} UiWidgetsComponentPluginClass;

FLUTTER_PLUGIN_EXPORT GType ui_widgets_component_plugin_get_type();

FLUTTER_PLUGIN_EXPORT void ui_widgets_component_plugin_register_with_registrar(
    FlPluginRegistrar* registrar);

G_<PERSON>ND_DECLS

#endif  // FLUTTER_PLUGIN_UI_WIDGETS_COMPONENT_PLUGIN_H_
