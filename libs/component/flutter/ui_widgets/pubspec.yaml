name: ui_widgets_component
description: "MyCS common user interface widgets"
version: 0.1.0
homepage: https://github.com/novassist-ai/novassist/libs/component/flutter/ui_widgets

environment:
  sdk: '>=3.4.1 <4.0.0'
  flutter: '>=3.3.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  plugin_platform_interface: ^2.0.2

  intl: ^0.20.2
  web: ^1.1.1
  logging: ^1.2.0
  json_annotation: ^4.9.0
  uuid: ^4.4.0
  provider: ^6.1.2
  visibility_detector: ^0.4.0+2
  equatable: ^2.0.5
  expressions: ^0.2.5+2
  flutter_markdown: ^0.7.7+1
  country_pickers: ^3.0.1
  dlibphonenumber: ^1.1.26
  pinput: ^5.0.1
  flutter_svg: ^2.0.17
  flutter_colorpicker: ^1.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  build_runner: ^2.4.13
  json_serializable: ^6.8.0

flutter:
  generate: true
  assets:
    - assets/
    - assets/us-flags/
    - assets/uk-flags/

  plugin:
    platforms:
      android:
        package: ai.novassist.component.flutter.ui_widgets.ui_widgets_component
        pluginClass: UiWidgetsComponentPlugin
      ios:
        pluginClass: UiWidgetsComponentPlugin
      linux:
        pluginClass: UiWidgetsComponentPlugin
      macos:
        pluginClass: UiWidgetsComponentPlugin
      windows:
        pluginClass: UiWidgetsComponentPluginCApi
      web:
        pluginClass: UiWidgetsComponentWeb
        fileName: ui_widgets_component_web.dart
