import 'package:flutter_test/flutter_test.dart';
import 'package:ui_widgets_component/ui_widgets_component.dart';
import 'package:ui_widgets_component/ui_widgets_component_platform_interface.dart';
import 'package:ui_widgets_component/ui_widgets_component_method_channel.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

class MockUiWidgetsComponentPlatform
    with MockPlatformInterfaceMixin
    implements UiWidgetsComponentPlatform {

  @override
  Future<String?> getPlatformVersion() => Future.value('42');
}

void main() {
  final UiWidgetsComponentPlatform initialPlatform = UiWidgetsComponentPlatform.instance;

  test('$MethodChannelUiWidgetsComponent is the default instance', () {
    expect(initialPlatform, isInstanceOf<MethodChannelUiWidgetsComponent>());
  });

  test('getPlatformVersion', () async {
    UiWidgetsComponent uiWidgetsComponentPlugin = UiWidgetsComponent();
    MockUiWidgetsComponentPlatform fakePlatform = MockUiWidgetsComponentPlatform();
    UiWidgetsComponentPlatform.instance = fakePlatform;

    expect(await uiWidgetsComponentPlugin.getPlatformVersion(), '42');
  });
}
