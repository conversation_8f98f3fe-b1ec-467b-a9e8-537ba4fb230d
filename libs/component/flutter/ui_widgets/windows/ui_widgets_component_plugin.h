#ifndef FLUTTER_PLUGIN_UI_WIDGETS_COMPONENT_PLUGIN_H_
#define FLUTTER_PLUGIN_UI_WIDGETS_COMPONENT_PLUGIN_H_

#include <flutter/method_channel.h>
#include <flutter/plugin_registrar_windows.h>

#include <memory>

namespace ui_widgets_component {

class UiWidgetsComponentPlugin : public flutter::Plugin {
 public:
  static void RegisterWithRegistrar(flutter::PluginRegistrarWindows *registrar);

  UiWidgetsComponentPlugin();

  virtual ~UiWidgetsComponentPlugin();

  // Disallow copy and assign.
  UiWidgetsComponentPlugin(const UiWidgetsComponentPlugin&) = delete;
  UiWidgetsComponentPlugin& operator=(const UiWidgetsComponentPlugin&) = delete;

  // Called when a method is called on this plugin's channel from Dart.
  void HandleMethodCall(
      const flutter::MethodCall<flutter::EncodableValue> &method_call,
      std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result);
};

}  // namespace ui_widgets_component

#endif  // FLUTTER_PLUGIN_UI_WIDGETS_COMPONENT_PLUGIN_H_
