#include "include/ui_widgets_component/ui_widgets_component_plugin_c_api.h"

#include <flutter/plugin_registrar_windows.h>

#include "ui_widgets_component_plugin.h"

void UiWidgetsComponentPluginCApiRegisterWithRegistrar(
    FlutterDesktopPluginRegistrarRef registrar) {
  ui_widgets_component::UiWidgetsComponentPlugin::RegisterWithRegistrar(
      flutter::PluginRegistrarManager::GetInstance()
          ->GetRegistrar<flutter::PluginRegistrarWindows>(registrar));
}
