import 'package:app_framework_component/app_framework.dart' as app;

class TestState extends app.State<TestState> {
  @override
  TestState copyWith({
    Set<app.Message>? messages,
    Set<app.LoadingState>? loadingStates,
  }) {
    return TestState(
      messages: messages ?? super.messages,
      loadingStates: loadingStates ?? super.loadingStates,
    );
  }

  @override
  List<Object?> get additionalProps => [];

  const TestState({
    super.loadingStates = const {},
    super.messages = const {},
  });
}

class TestService extends app.Service<TestState, app.NullProvider> {
  TestService()
      : super(
          initialState: const TestState(),
          provider: app.NullProvider(),
        );

  void sendTestNotification() {
    emit(
      state.addMessage(
        app.Message.info('Test Notification'),
      ),
    );
  }
}
