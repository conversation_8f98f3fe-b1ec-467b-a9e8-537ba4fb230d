import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import 'package:nav_layouts_component/nav_layouts.dart';

class SignUpVerifyTestPage extends StatelessNavigableWidget {
  @override
  String get routeName => name;
  static const String name = 'SignUpVerifyTestPage';

  @override
  String get routePath => path;
  static const String path = 'sign-up-verify-test';

  const SignUpVerifyTestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'Sign Up Verify Test Page',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.headlineLarge,
          ),
          const SizedBox(height: 20),
          ElevatedButton(
            onPressed: () => context.pop(),
            child: const Text('Go Back'),
          ),
        ],
      ),
    );
  }
}
