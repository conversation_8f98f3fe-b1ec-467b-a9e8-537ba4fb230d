import 'package:flutter/material.dart';

import 'package:nav_layouts_component/nav_layouts.dart';

import '../config/app_config.dart';

import 'nav_home_list_view.dart';

class HomePage extends StatelessNavigableWidget {
  @override
  String get routeName => name;
  static const String name = 'HomePage';

  @override
  String get routePath => path;
  static const String path = '/';

  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return RootSplashLayout(
      body: const NavHomeListView(),
      splash: Image.asset(
        'assets/matrix.jpeg',
        height: double.infinity,
        width: double.infinity,
        fit: BoxFit.cover,
      ),
      splashSeperator: true,
      splashScale: 2.0,
      rightAlignedSplash: false,
      minWidthToShowSplash: 600,
      bodyMinWidth: 320,
      title: AppConfig.title,
    );
  }
}
