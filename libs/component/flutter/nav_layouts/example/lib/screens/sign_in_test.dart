import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import 'package:nav_layouts_component/nav_layouts.dart';

import 'sign_in_verify_test.dart';
import '../bloc/test_service.dart';

class SignInTestPage extends StatelessNavigableWidget {
  @override
  String get routeName => name;
  static const String name = 'SignInTestPage';

  @override
  String get routePath => path;
  static const String path = '/sign-in-test';

  @override
  List<StatelessNavigableWidget> get childRoutes => [
        const SignInVerifyTestPage(),
      ];

  const SignInTestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'Sign In Test Page',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.headlineLarge,
          ),
          const SizedBox(height: 20),
          ElevatedButton(
            onPressed: () {
              final rootState = Provider.of<RootState>(context, listen: false);
              rootState.setBusy();
              Future.delayed(
                const Duration(milliseconds: 3000),
                () {
                  rootState.resetBusy();
                  context.go('/sign-in-test/sign-in-verify-test');
                },
              );
            },
            child: const Text('Verify Sign In'),
          ),
          const SizedBox(height: 20),
          ElevatedButton(
            onPressed: () {
              final testService =
                  Provider.of<TestService>(context, listen: false);
              testService.sendTestNotification();
            },
            child: const Text('Send Notification'),
          ),
        ],
      ),
    );
  }
}
