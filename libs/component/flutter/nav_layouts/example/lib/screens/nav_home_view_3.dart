import 'package:flutter/material.dart';

import 'package:ui_widgets_component/ui_widgets.dart' as ui;
import 'package:nav_layouts_component/nav_layouts.dart' as nav;

import 'home_page.dart';
import 'applications_test.dart';

class NavHomeView3 extends nav.AppScaffold {
  @override
  String get routeName => name;
  static const String name = 'ApplicationsPage';

  @override
  String get routePath => '/applications-page';

  @override
  List<nav.AppTitleTool>? buildTitleBarTools(BuildContext context) => [
        nav.AppTitleTool(
          hookName: '',
          order: 0,
          title: 'Go Back',
          tooltip: 'go back',
          iconData: Icons.arrow_back_ios_new,
          onPressed: (_) {
            nav.GlobalNavigator.goTo(HomePage.name);
          },
        ),
        nav.AppTitleTool(
          hookName: '',
          order: 0,
          title: 'Print Open Document',
          tooltip: 'print open document',
          iconData: Icons.print_outlined,
          onPressed: (_) {},
        ),
      ];

  @override
  List<Widget>? buildTitleBarActions(BuildContext context) => [
        const Builder(builder: _buildSearchBar),
        _buildProfileMenu(context),
      ];

  @override
  List<ui.Tool> buildStatusToolsLeft(BuildContext context) {
    return [
      ui.TextButtonTool.statusbar(
        iconData: (_) => Icons.person,
        text: (_) => 'John Doe',
        onPressed: (_) {},
      ),
      ui.TextButtonTool.statusbar(
        iconData: (_) => Icons.business,
        text: (_) => 'Acme Organization',
        onPressed: (_) {},
      ),
      const ui.DividerTool(),
    ];
  }

  @override
  List<ui.Tool> buildStatusToolsCenter(BuildContext context) {
    return [
      ui.TextButtonTool.statusbar(
        text: (_) => '© Appbricks, Inc.',
        textShadow: true,
      ),
    ];
  }

  @override
  List<ui.Tool> buildStatusToolsRight(BuildContext context) {
    return [
      const ui.DividerTool(),
      ui.TextButtonTool.statusbar(
        enabled: (_) => false,
        text: (_) => 'Charges: \$222',
      ),
      const ui.DividerTool(),
      ui.TextButtonTool.statusbar(
        enabled: (_) => false,
        text: (_) => 'Usage: 10 GB',
      ),
    ];
  }

  const NavHomeView3({
    super.key,
  }) : super(
          title: 'Applications',
          body: const ApplicationsTestPage(),
        );
}

Widget _buildSearchBar(BuildContext context) {
  final mediaQuery = MediaQuery.of(context);
  double searchWidth = 400.0;
  if (mediaQuery.size.width < 670) {
    searchWidth = 400 - (670 - mediaQuery.size.width) / 1.9;
  }
  final searchConstraints = BoxConstraints(
    maxWidth: searchWidth,
  );

  return Padding(
    padding: const EdgeInsets.all(8.0),
    child: SearchAnchor(
      viewConstraints: searchConstraints,
      builder: (BuildContext context, SearchController controller) {
        return ConstrainedBox(
          constraints: searchConstraints,
          child: SearchBar(
            controller: controller,
            padding: const WidgetStatePropertyAll<EdgeInsets>(
              EdgeInsets.all(2.0),
            ),
            onTap: () {
              controller.openView();
            },
            onChanged: (_) {
              controller.openView();
            },
            leading: const Padding(
              padding: EdgeInsets.only(left: 5.0),
              child: Icon(Icons.search),
            ),
          ),
        );
      },
      suggestionsBuilder: (
        BuildContext context,
        SearchController controller,
      ) {
        return List<ListTile>.generate(
          5,
          (int index) {
            final String item = 'item $index';
            return ListTile(
              title: Text(item),
              onTap: () {},
            );
          },
        );
      },
    ),
  );
}

Widget _buildProfileMenu(BuildContext context) {
  const avatarImage = AssetImage('assets/hacker.png');

  return Padding(
    padding: const EdgeInsets.fromLTRB(8.0, 0, 20.0, 0.0),
    child: ui.AvatarPopupMenu<Menu>(
      avatarBgImage: avatarImage,
      offset: const Offset(15, 10),
      menuItems: const <PopupMenuEntry<Menu>>[
        PopupMenuItem<Menu>(
          value: Menu.profile,
          child: ListTile(
            leading: Icon(Icons.manage_accounts_outlined),
            title: Text('Profile'),
          ),
        ),
        PopupMenuItem<Menu>(
          value: Menu.security,
          child: ListTile(
            leading: Icon(Icons.admin_panel_settings_outlined),
            title: Text('Security'),
          ),
        ),
        PopupMenuDivider(),
        PopupMenuItem<Menu>(
          value: Menu.logout,
          child: ListTile(
            leading: Icon(Icons.logout_outlined),
            title: Text('Logout'),
          ),
        ),
      ],
      onSelected: (Menu result) {
        if (result == Menu.logout) {
          nav.GlobalNavigator.goTo(HomePage.name);
        } else {
          print('Selected pofile menu option: ${result.name}');
        }
      },
    ),
  );
}

enum Menu { profile, security, logout }
