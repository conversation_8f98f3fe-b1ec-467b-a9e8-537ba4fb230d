import 'package:flutter/material.dart';

import 'package:ui_widgets_component/ui_widgets.dart' as ui;
import 'package:nav_layouts_component/nav_layouts.dart' as nav;

import '../config/app_config.dart';
import 'home_page.dart';
import 'sign_up_test.dart';
import 'sign_in_test.dart';

class NavHomeView1 extends nav.RootNavLayout {
  /// The initial path within the navigation shell
  static const String name = SignInTestPage.name;

  static const AssetImage avatarImage = AssetImage('assets/hacker.png');
  static const String avatarName = 'Anon Hacker';
  static const String avatarEmail = '<EMAIL>';

  @override
  List<nav.NavTarget> get navTargets => [
        nav.NavTarget(
          body: const SignInTestPage(),
          destBuilder: (_) => nav.NavDest(
            iconData: Icons.login,
            selectedIconData: Icons.login_sharp,
            label: 'Sign In',
          ),
        ),
        nav.NavTarget(
          body: const SignUpTestPage(),
          destBuilder: (_) => nav.NavDest(
            iconData: Icons.person_add,
            selectedIconData: Icons.person_add_sharp,
            label: 'Sign Up',
          ),
        ),
      ];

  @override
  nav.NavProperties get navProperties => const nav.NavProperties(
        mobileNavType: nav.MobileNavType.drawer,
        showExtended: nav.ShowExtended.dynamic,
        showLabels: nav.ShowLabels.always,
      );

  @override
  nav.NavTitleBar? buildNavTitleBar(BuildContext context) {
    return nav.NavTitleBar(
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back_ios_new,
          color: Theme.of(context).textTheme.titleLarge!.color,
        ),
        onPressed: () => nav.GlobalNavigator.goTo(HomePage.name),
      ),
      actions: [
        IconButton(
          onPressed: () => nav.GlobalNavigator.goTo(HomePage.name),
          icon: Icon(
            Icons.logout_outlined,
            color: Theme.of(context).textTheme.titleLarge!.color,
          ),
        ),
      ],
      leftToolBar: ui.ToolBar(
        tools: [
          ui.ToolIconButton(
            title: 'Print Open Document',
            tooltip: 'print open document',
            iconData: Icons.print_outlined,
            onPressed: (_) {},
          ),
        ],
      ),
    );
  }

  @override
  Widget? buildNavTrailTrailingWidget(
    BuildContext context,
    BoxConstraints constraints,
    bool isExtended,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0),
      child: CircleAvatar(
        backgroundColor: Theme.of(context).colorScheme.primary,
        backgroundImage: avatarImage,
        radius: isExtended ? 40.0 : 20.0,
      ),
    );
  }

  @override
  Widget? buildNavDrawerHeaderWidget(
    BuildContext context,
    BoxConstraints constraints,
  ) {
    return UserAccountsDrawerHeader(
      currentAccountPicture: CircleAvatar(
        backgroundColor: Theme.of(context).colorScheme.secondary,
        backgroundImage: avatarImage,
        radius: 40.0,
      ),
      accountName: Text(
        avatarName,
        style: Theme.of(context).textTheme.titleMedium,
      ),
      accountEmail: const Text(
        avatarEmail,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
      ),
    );
  }

  @override
  ui.StatusViewBar? buildStatusBar(BuildContext context) {
    return ui.StatusViewBar(
      items: [
        ui.TextButtonTool.statusbar(
          iconData: (_) => Icons.person,
          text: (_) => 'John Doe',
          onPressed: (_) {},
        ),
        ui.TextButtonTool.statusbar(
          iconData: (_) => Icons.business,
          text: (_) => 'Acme Organization',
          onPressed: (_) {},
        ),
        const ui.SpacerTool(),
        const ui.DividerTool(),
        ui.TextButtonTool.statusbar(
          enabled: (_) => false,
          text: (_) => 'Charges: \$222',
        ),
        const ui.DividerTool(),
        ui.TextButtonTool.statusbar(
          enabled: (_) => false,
          text: (_) => 'Usage: 10 GB',
        ),
      ],
    );
  }

  NavHomeView1() : super(AppConfig.title);
}
