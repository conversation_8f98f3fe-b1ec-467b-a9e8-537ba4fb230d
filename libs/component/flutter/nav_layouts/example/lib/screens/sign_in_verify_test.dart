import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import 'package:nav_layouts_component/nav_layouts.dart';

class SignInVerifyTestPage extends StatelessNavigableWidget {
  @override
  String get routeName => name;
  static const String name = 'SignInVerifyTestPage';

  @override
  String get routePath => path;
  static const String path = 'sign-in-verify-test';

  const SignInVerifyTestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'Sign In Verify Test Page',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.headlineLarge,
          ),
          const SizedBox(height: 20),
          ElevatedButton(
            onPressed: () => context.pop(),
            child: const Text('Go Back'),
          ),
        ],
      ),
    );
  }
}
