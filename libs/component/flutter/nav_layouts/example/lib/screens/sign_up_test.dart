import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import 'package:nav_layouts_component/nav_layouts.dart';

import 'sign_up_verify_test.dart';

class SignUpTestPage extends StatelessNavigableWidget {
  @override
  String get routeName => name;
  static const String name = 'SignUpTestPage';

  @override
  String get routePath => path;
  static const String path = '/sign-up-test';

  @override
  List<StatelessNavigableWidget> get childRoutes => [
        const SignUpVerifyTestPage(),
      ];

  const SignUpTestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'Sign Up Test Page',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.headlineLarge,
          ),
          const SizedBox(height: 20),
          ElevatedButton(
            onPressed: () {
              final rootState = Provider.of<RootState>(context, listen: false);
              rootState.setBusy();
              Future.delayed(
                const Duration(milliseconds: 3000),
                () {
                  rootState.resetBusy();
                  context.go('/sign-up-test/sign-up-verify-test');
                },
              );
            },
            child: const Text('Verify Sign Up'),
          ),
        ],
      ),
    );
  }
}
