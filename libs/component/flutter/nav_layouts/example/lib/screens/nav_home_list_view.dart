import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import 'package:nav_layouts_component/nav_layouts.dart';

import 'nav_home_view_1.dart';
import 'nav_home_view_2.dart';
import 'nav_home_view_3.dart';

class NavHomeListView extends StatelessWidget {
  const NavHomeListView({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<RootSplashState>(
      builder: (
        context,
        state,
        _,
      ) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            children: [
              const Expanded(
                child: SizedBox(),
              ),
              Center(
                child: Image.asset(
                  'assets/test-app-icon.png',
                  height: 150,
                  fit: BoxFit.fitHeight,
                ),
              ),
              const SizedBox(
                height: 15,
              ),
              Center(
                child: Text('Select a Nav Example to Continue',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.titleMedium),
              ),
              const SizedBox(
                height: 20,
              ),
              TextButton(
                onPressed: () {
                  context.goNamed(NavHomeView1.name);
                },
                style: TextButton.styleFrom(
                  minimumSize: const Size(double.infinity, 50),
                  backgroundColor:
                      Theme.of(context).colorScheme.primaryContainer,
                ),
                child: const Text(
                  'Nav Home View #1',
                  style: TextStyle(
                    fontSize: 20,
                  ),
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              TextButton(
                onPressed: () {
                  context.goNamed(NavHomeView2.name);
                },
                style: TextButton.styleFrom(
                  minimumSize: const Size(double.infinity, 50),
                  backgroundColor:
                      Theme.of(context).colorScheme.primaryContainer,
                ),
                child: const Text(
                  'Nav Home View #2',
                  style: TextStyle(
                    fontSize: 20,
                  ),
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              TextButton(
                onPressed: () {
                  context.goNamed(NavHomeView3.name);
                },
                style: TextButton.styleFrom(
                  minimumSize: const Size(double.infinity, 50),
                  backgroundColor:
                      Theme.of(context).colorScheme.primaryContainer,
                ),
                child: const Text(
                  'Nav Home View #3',
                  style: TextStyle(
                    fontSize: 20,
                  ),
                ),
              ),
              const SizedBox(
                height: 30,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Switch(
                    value: state.showSplash,
                    onChanged: (bool value) => Provider.of<RootSplashState>(
                      context,
                      listen: false,
                    ).setShowSplash(
                      value,
                    ),
                  ),
                  const Text(
                    'Toggle Splash Screen',
                    style: TextStyle(
                      fontSize: 20,
                    ),
                  ),
                ],
              ),
              const Expanded(
                child: SizedBox(),
              ),
            ],
          ),
        );
      },
    );
  }
}
