import 'package:flutter/material.dart';

import 'package:ui_widgets_component/ui_widgets.dart' as ui;
import 'package:nav_layouts_component/nav_layouts.dart' as nav;

import '../config/app_config.dart';
import 'home_page.dart';
import 'applications_test.dart';

class NavHomeView2 extends nav.RootNavLayout {
  /// The initial path within the navigation shell
  static const String name = ApplicationsTestPage.name;

  static const AssetImage avatarImage = AssetImage('assets/hacker.png');

  @override
  String get hookName => featureHookName;
  static const String featureHookName = 'nav_home_view_2';

  @override
  List<nav.NavTarget> get navTargets => [
        nav.NavTarget(
          body: const ApplicationsTestPage(),
          destBuilder: (_) => nav.NavDest(
            iconData: Icons.apps,
            selectedIconData: Icons.apps_sharp,
            label: 'Applications',
          ),
        ),
        // additional targets added via the feature hook
      ];

  @override
  nav.NavProperties get navProperties => const nav.NavProperties(
        mobileNavType: nav.MobileNavType.bottom,
        showExtended: nav.ShowExtended.dynamic,
        showLabels: nav.ShowLabels.always,
      );

  @override
  nav.NavTitleBar? buildNavTitleBar(BuildContext context) {
    return nav.NavTitleBar(
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back_ios_new,
          color: Theme.of(context).textTheme.titleLarge!.color,
        ),
        onPressed: () {
          nav.GlobalNavigator.goTo(HomePage.name);
        },
      ),
      actions: [
        const Builder(builder: _buildSearchBar),
        _buildProfileMenu(context),
      ],
      leftToolBar: ui.ToolBar(
        tools: [
          ui.ToolIconButton(
            title: 'Print Open Document',
            tooltip: 'print open document',
            iconData: Icons.print_outlined,
            onPressed: (_) {},
          ),
        ],
      ),
    );
  }

  NavHomeView2() : super(AppConfig.title);
}

Widget _buildSearchBar(BuildContext context) {
  final mediaQuery = MediaQuery.of(context);
  double searchWidth = 400.0;
  if (mediaQuery.size.width < 670) {
    searchWidth = 400 - (670 - mediaQuery.size.width) / 1.9;
  }
  final searchConstraints = BoxConstraints(
    maxWidth: searchWidth,
  );

  return Padding(
    padding: const EdgeInsets.all(8.0),
    child: SearchAnchor(
      viewConstraints: searchConstraints,
      builder: (BuildContext context, SearchController controller) {
        return ConstrainedBox(
          constraints: searchConstraints,
          child: SearchBar(
            controller: controller,
            padding: const WidgetStatePropertyAll<EdgeInsets>(
              EdgeInsets.all(2.0),
            ),
            onTap: () {
              controller.openView();
            },
            onChanged: (_) {
              controller.openView();
            },
            leading: const Padding(
              padding: EdgeInsets.only(left: 5.0),
              child: Icon(Icons.search),
            ),
          ),
        );
      },
      suggestionsBuilder: (
        BuildContext context,
        SearchController controller,
      ) {
        return List<ListTile>.generate(
          5,
          (int index) {
            final String item = 'item $index';
            return ListTile(
              title: Text(item),
              onTap: () {},
            );
          },
        );
      },
    ),
  );
}

Widget _buildProfileMenu(BuildContext context) {
  const avatarImage = AssetImage('assets/hacker.png');

  return Padding(
    padding: const EdgeInsets.fromLTRB(8.0, 0, 20.0, 0.0),
    child: ui.AvatarPopupMenu<Menu>(
      avatarBgImage: avatarImage,
      offset: const Offset(15, 10),
      menuItems: const <PopupMenuEntry<Menu>>[
        PopupMenuItem<Menu>(
          value: Menu.profile,
          child: ListTile(
            leading: Icon(Icons.manage_accounts_outlined),
            title: Text('Profile'),
          ),
        ),
        PopupMenuItem<Menu>(
          value: Menu.security,
          child: ListTile(
            leading: Icon(Icons.admin_panel_settings_outlined),
            title: Text('Security'),
          ),
        ),
        PopupMenuDivider(),
        PopupMenuItem<Menu>(
          value: Menu.logout,
          child: ListTile(
            leading: Icon(Icons.logout_outlined),
            title: Text('Logout'),
          ),
        ),
      ],
      onSelected: (Menu result) {
        if (result == Menu.logout) {
          nav.GlobalNavigator.goTo(HomePage.name);
        } else {
          print('Selected pofile menu option: ${result.name}');
        }
      },
    ),
  );
}

enum Menu { profile, security, logout }
