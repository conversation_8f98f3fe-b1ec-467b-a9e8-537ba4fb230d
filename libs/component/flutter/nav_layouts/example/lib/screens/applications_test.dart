import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'package:nav_layouts_component/nav_layouts.dart';

import '../bloc/test_service.dart';

class ApplicationsTestPage extends StatelessNavigableWidget {
  @override
  String get routeName => name;
  static const String name = 'applications-test';

  @override
  String get routePath => path;
  static const String path = '/applications-test';

  const ApplicationsTestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'Applications Test Page',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.headlineLarge,
          ),
          const SizedBox(height: 20),
          ElevatedButton(
            onPressed: () {
              final testService =
                  Provider.of<TestService>(context, listen: false);
              testService.sendTestNotification();
            },
            child: const Text('Send Notification'),
          ),
        ],
      ),
    );
  }
}
