import 'dart:io';
import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:window_manager/window_manager.dart';
import 'package:logging/logging.dart';

import 'package:utilities_ab/logging/init_logging.dart';
import 'package:platform_utilities_component/platform_utilities.dart';

import 'package:app_framework_component/app_framework.dart' as app;
import 'package:nav_layouts_component/nav_layouts.dart' as nav;

import 'config/app_config.dart';
import 'features/test_feature.dart';
import 'features/devices_feature.dart';
import 'features/workflow_feature.dart';
import 'screens/home_page.dart';
import 'screens/nav_home_view_1.dart';
import 'screens/nav_home_view_2.dart';
import 'screens/nav_home_view_3.dart';

void main() async {
  initLogging(
    Level.ALL,
    logToConsole: true,
  );

  // On Desktop platforms the minimum size
  // of the window is fixed at 240x400
  if (!AppPlatform.isWeb && !AppPlatform.isMobile) {
    WidgetsFlutterBinding.ensureInitialized();
    await windowManager.ensureInitialized();

    WindowManager.instance.setMinimumSize(AppConfig.minWindowSize);
    WindowManager.instance.setTitle(AppConfig.title);
  }

  // Create and initialize the
  // feature registry singleton
  app.FeatureRegistry.intialize(featureConfigLoader);

  await TestFeature.register();
  await DevicesFeature.register();
  await WorkflowFeature.register();

  AppPlatform.init().then(
    (_) => runApp(
      nav.StatefulWrapper(
        child: MainApp(),
      ),
    ),
  );
}

class MainApp extends StatelessWidget {
  late final GoRouter _router;

  MainApp({super.key}) {
    _router = GoRouter(
      navigatorKey: nav.GlobalNavigator.key,
      initialLocation: '/',
      routes: [
        const HomePage().route(),
        NavHomeView1().route(),
        NavHomeView2().route(),
        const NavHomeView3().route(),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    AppPlatform.initOnBuild(context);

    if (AppPlatform.isDesktop && Platform.isMacOS) {
      // On macOS a system menu is a required part of every application
      final List<PlatformMenuItem> menus = <PlatformMenuItem>[
        PlatformMenu(
          label: '', // In macOS the application name is shown in the menu bar
          menus: <PlatformMenuItem>[
            PlatformMenuItemGroup(
              members: <PlatformMenuItem>[
                PlatformMenuItem(
                  label: 'About',
                  onSelected: () {
                    showAboutDialog(
                      context: context,
                      applicationName: AppConfig.title,
                      applicationVersion: AppConfig.version,
                    );
                  },
                ),
              ],
            ),
            if (PlatformProvidedMenuItem.hasMenu(
                PlatformProvidedMenuItemType.quit))
              const PlatformProvidedMenuItem(
                  type: PlatformProvidedMenuItemType.quit),
          ],
        ),
      ];
      WidgetsBinding.instance.platformMenuDelegate.setMenus(menus);
    }

    return app.FeatureRegistry.instance().scope(
      context,
      child: MaterialApp.router(
        debugShowCheckedModeBanner: false,
        title: AppConfig.title,
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: Colors.indigo,
          ),
          useMaterial3: false,
        ),
        darkTheme: ThemeData(
          colorScheme: ColorScheme.fromSeed(
            brightness: Brightness.dark,
            seedColor: Colors.indigo,
          ),
          useMaterial3: false,
        ),
        routerConfig: _router,
      ),
    );
  }
}

Future<Map<String, dynamic>> featureConfigLoader(
  String featureName,
) async {
  /// Load the feature configuration json from the assets
  return jsonDecode(
    await rootBundle.loadString('assets/${featureName}_feature.json'),
  );
}
