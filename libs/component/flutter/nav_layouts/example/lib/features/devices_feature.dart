import 'package:flutter/material.dart';

import 'package:app_framework_component/app_framework.dart' as app;
import 'package:nav_layouts_component/nav_layouts.dart';

import '../screens/nav_home_view_2.dart';

class DevicesFeature extends app.Feature {
  static String get featureName => 'devices';

  @override
  String get name => featureName;

  @override
  app.FeatureConfigDeserializer? get configDeserializer => null;

  @override
  List<LocalizationsDelegate> get localizationsDelegates => [];

  @override
  Future<void> initialize(
    app.FeatureConfig? config,
    Map<String, app.Feature> registeredFeatures,
  ) async {}

  @override
  List<app.FeatureAnchor> anchors(
    BuildContext? context,
  ) {
    return [
      NavTarget(
        hookName: NavHomeView2.featureHookName,
        order: 1,
        body: const DevicesTestPage(),
        destBuilder: (_) => NavDest(
          iconData: Icons.devices,
          selectedIconData: Icons.devices_sharp,
          label: 'Devices',
        ),
      ),
    ];
  }

  @override
  void dispose() {}

  // Feature helper methods

  static Future<void> register() async {
    final featureRegistry = app.FeatureRegistry.instance();
    await featureRegistry.registerFeature(DevicesFeature._());
  }

  DevicesFeature._();
}

class DevicesTestPage extends StatelessNavigableWidget {
  @override
  String get routeName => name;
  static const String name = 'devices-test';

  @override
  String get routePath => path;
  static const String path = '/devices-test';

  const DevicesTestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Devices Test Page',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.headlineLarge,
            ),
          ],
        ),
      ),
    );
  }
}
