import 'package:flutter/material.dart';

import 'package:app_framework_component/app_framework.dart' as app;

import '../bloc/test_service.dart';

class TestFeature extends app.Feature {
  static String get featureName => 'test';

  @override
  String get name => featureName;

  @override
  app.FeatureConfigDeserializer? get configDeserializer => null;

  @override
  List<LocalizationsDelegate> get localizationsDelegates => [];

  @override
  Future<void> initialize(
    app.FeatureConfig? config,
    Map<String, app.Feature> registeredFeatures,
  ) async {}

  @override
  void dispose() {}

  // Feature helper methods

  static Future<void> register() async {
    final featureRegistry = app.FeatureRegistry.instance();
    await featureRegistry.registerFeature(TestFeature._());
  }

  TestFeature._() {
    // Register the identity service provider
    services.add<TestService>((context) {
      return TestService();
    });
  }
}
