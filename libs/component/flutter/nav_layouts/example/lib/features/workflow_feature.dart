import 'package:flutter/material.dart';

import 'package:app_framework_component/app_framework.dart' as app;
import 'package:nav_layouts_component/nav_layouts.dart';

import '../screens/nav_home_view_2.dart';

class WorkflowFeature extends app.Feature {
  static String get featureName => 'workflow';

  @override
  String get name => featureName;

  @override
  app.FeatureConfigDeserializer? get configDeserializer => null;

  @override
  List<LocalizationsDelegate> get localizationsDelegates => [];

  @override
  List<app.FeatureAnchor> anchors(
    BuildContext? context,
  ) {
    return [
      NavTarget(
        hookName: NavHomeView2.featureHookName,
        order: 2,
        body: const WorkflowTestPage(),
        destBuilder: (_) => NavDest(
          icon: const ImageIcon(
            AssetImage('assets/ai-workflow.png'),
            // size: 26,
          ),
          selectedIcon: const ImageIcon(
            AssetImage('assets/ai-workflow-outline.png'),
            // size: 26,
          ),
          label: 'AI Workflow',
        ),
      ),
    ];
  }

  @override
  Future<void> initialize(
    app.FeatureConfig? config,
    Map<String, app.Feature> registeredFeatures,
  ) async {}

  @override
  void dispose() {}

  // Feature helper methods

  static Future<void> register() async {
    final featureRegistry = app.FeatureRegistry.instance();
    await featureRegistry.registerFeature(WorkflowFeature._());
  }

  WorkflowFeature._();
}

class WorkflowTestPage extends StatelessNavigableWidget {
  @override
  String get routeName => name;
  static const String name = 'workflow-test';

  @override
  String get routePath => path;
  static const String path = '/workflow-test';

  const WorkflowTestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Workflow Test Page',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.headlineLarge,
            ),
          ],
        ),
      ),
    );
  }
}
