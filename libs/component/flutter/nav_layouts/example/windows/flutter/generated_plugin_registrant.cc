//
//  Generated file. Do not edit.
//

// clang-format off

#include "generated_plugin_registrant.h"

#include <app_framework_component/app_framework_component_plugin_c_api.h>
#include <flutter_secure_storage_windows/flutter_secure_storage_windows_plugin.h>
#include <nav_layouts_component/nav_layouts_component_plugin_c_api.h>
#include <platform_utilities_component/platform_utilities_component_plugin_c_api.h>
#include <screen_retriever_windows/screen_retriever_windows_plugin_c_api.h>
#include <ui_widgets_component/ui_widgets_component_plugin_c_api.h>
#include <window_manager/window_manager_plugin.h>

void RegisterPlugins(flutter::PluginRegistry* registry) {
  AppFrameworkComponentPluginCApiRegisterWithRegistrar(
      registry->GetRegistrarForPlugin("AppFrameworkComponentPluginCApi"));
  FlutterSecureStorageWindowsPluginRegisterWithRegistrar(
      registry->GetRegistrarForPlugin("FlutterSecureStorageWindowsPlugin"));
  NavLayoutsComponentPluginCApiRegisterWithRegistrar(
      registry->GetRegistrarForPlugin("NavLayoutsComponentPluginCApi"));
  PlatformUtilitiesComponentPluginCApiRegisterWithRegistrar(
      registry->GetRegistrarForPlugin("PlatformUtilitiesComponentPluginCApi"));
  ScreenRetrieverWindowsPluginCApiRegisterWithRegistrar(
      registry->GetRegistrarForPlugin("ScreenRetrieverWindowsPluginCApi"));
  UiWidgetsComponentPluginCApiRegisterWithRegistrar(
      registry->GetRegistrarForPlugin("UiWidgetsComponentPluginCApi"));
  WindowManagerPluginRegisterWithRegistrar(
      registry->GetRegistrarForPlugin("WindowManagerPlugin"));
}
