//
//  Generated file. Do not edit.
//

// clang-format off

#include "generated_plugin_registrant.h"

#include <app_framework_component/app_framework_component_plugin.h>
#include <flutter_secure_storage_linux/flutter_secure_storage_linux_plugin.h>
#include <nav_layouts_component/nav_layouts_component_plugin.h>
#include <platform_utilities_component/platform_utilities_component_plugin.h>
#include <screen_retriever_linux/screen_retriever_linux_plugin.h>
#include <ui_widgets_component/ui_widgets_component_plugin.h>
#include <window_manager/window_manager_plugin.h>

void fl_register_plugins(FlPluginRegistry* registry) {
  g_autoptr(FlPluginRegistrar) app_framework_component_registrar =
      fl_plugin_registry_get_registrar_for_plugin(registry, "AppFrameworkComponentPlugin");
  app_framework_component_plugin_register_with_registrar(app_framework_component_registrar);
  g_autoptr(FlPluginRegistrar) flutter_secure_storage_linux_registrar =
      fl_plugin_registry_get_registrar_for_plugin(registry, "FlutterSecureStorageLinuxPlugin");
  flutter_secure_storage_linux_plugin_register_with_registrar(flutter_secure_storage_linux_registrar);
  g_autoptr(FlPluginRegistrar) nav_layouts_component_registrar =
      fl_plugin_registry_get_registrar_for_plugin(registry, "NavLayoutsComponentPlugin");
  nav_layouts_component_plugin_register_with_registrar(nav_layouts_component_registrar);
  g_autoptr(FlPluginRegistrar) platform_utilities_component_registrar =
      fl_plugin_registry_get_registrar_for_plugin(registry, "PlatformUtilitiesComponentPlugin");
  platform_utilities_component_plugin_register_with_registrar(platform_utilities_component_registrar);
  g_autoptr(FlPluginRegistrar) screen_retriever_linux_registrar =
      fl_plugin_registry_get_registrar_for_plugin(registry, "ScreenRetrieverLinuxPlugin");
  screen_retriever_linux_plugin_register_with_registrar(screen_retriever_linux_registrar);
  g_autoptr(FlPluginRegistrar) ui_widgets_component_registrar =
      fl_plugin_registry_get_registrar_for_plugin(registry, "UiWidgetsComponentPlugin");
  ui_widgets_component_plugin_register_with_registrar(ui_widgets_component_registrar);
  g_autoptr(FlPluginRegistrar) window_manager_registrar =
      fl_plugin_registry_get_registrar_for_plugin(registry, "WindowManagerPlugin");
  window_manager_plugin_register_with_registrar(window_manager_registrar);
}
