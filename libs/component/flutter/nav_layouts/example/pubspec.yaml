name: nav_layouts_component_example
description: "Demonstrates how to use the name: nav_layouts_component plugin."
publish_to: "none"

version: 0.1.0

environment:
  sdk: ">=3.2.6 <4.0.0"

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.2
  window_manager: ^0.4.3
  logging: ^1.2.0
  provider: ^6.1.1
  bloc: ^9.0.0
  go_router: ^14.1.2

  platform_utilities_component:
    path: ../../platform_utilities
  utilities_ab:
    path: ../../../../commons/dart/utilities
  ui_widgets_component:
    path: ../../ui_widgets
  app_framework_component:
    path: ../../app_framework
  nav_layouts_component:
    path: ../

dev_dependencies:
  integration_test:
    sdk: flutter
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.3
  build_runner: ">=2.4.8 <4.0.0"

flutter:
  generate: true
  uses-material-design: true

  assets:
    - assets/

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/test-app-icon.png"
  min_sdk_android: 21
  web:
    generate: true
  windows:
    generate: true
    icon_size: 48
  macos:
    generate: true
