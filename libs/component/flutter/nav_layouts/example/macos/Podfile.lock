PODS:
  - app_framework_component (0.0.1):
    - FlutterMacOS
  - flutter_secure_storage_macos (6.1.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - nav_layouts_component (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - platform_utilities_component (0.0.1):
    - FlutterMacOS
  - screen_retriever (0.0.1):
    - FlutterMacOS
  - smart_auth (0.0.1):
    - FlutterMacOS
  - ui_widgets_component (0.0.1):
    - FlutterMacOS
  - window_manager (0.2.0):
    - FlutterMacOS

DEPENDENCIES:
  - app_framework_component (from `Flutter/ephemeral/.symlinks/plugins/app_framework_component/macos`)
  - flutter_secure_storage_macos (from `Flutter/ephemeral/.symlinks/plugins/flutter_secure_storage_macos/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - nav_layouts_component (from `Flutter/ephemeral/.symlinks/plugins/nav_layouts_component/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - platform_utilities_component (from `Flutter/ephemeral/.symlinks/plugins/platform_utilities_component/macos`)
  - screen_retriever (from `Flutter/ephemeral/.symlinks/plugins/screen_retriever/macos`)
  - smart_auth (from `Flutter/ephemeral/.symlinks/plugins/smart_auth/macos`)
  - ui_widgets_component (from `Flutter/ephemeral/.symlinks/plugins/ui_widgets_component/macos`)
  - window_manager (from `Flutter/ephemeral/.symlinks/plugins/window_manager/macos`)

EXTERNAL SOURCES:
  app_framework_component:
    :path: Flutter/ephemeral/.symlinks/plugins/app_framework_component/macos
  flutter_secure_storage_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_secure_storage_macos/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  nav_layouts_component:
    :path: Flutter/ephemeral/.symlinks/plugins/nav_layouts_component/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  platform_utilities_component:
    :path: Flutter/ephemeral/.symlinks/plugins/platform_utilities_component/macos
  screen_retriever:
    :path: Flutter/ephemeral/.symlinks/plugins/screen_retriever/macos
  smart_auth:
    :path: Flutter/ephemeral/.symlinks/plugins/smart_auth/macos
  ui_widgets_component:
    :path: Flutter/ephemeral/.symlinks/plugins/ui_widgets_component/macos
  window_manager:
    :path: Flutter/ephemeral/.symlinks/plugins/window_manager/macos

SPEC CHECKSUMS:
  app_framework_component: 2609c1dc52844c7313b2d5eb6751735732fa0a99
  flutter_secure_storage_macos: 59459653abe1adb92abbc8ea747d79f8d19866c9
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  nav_layouts_component: 43f424aa2aa3319e5db8371b10314ad4732cb7c5
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  platform_utilities_component: ce2e5110f9b66f0822d20cd97447eee489a752ab
  screen_retriever: 59634572a57080243dd1bf715e55b6c54f241a38
  smart_auth: b38e3ab4bfe089eacb1e233aca1a2340f96c28e9
  ui_widgets_component: 2a6413461141c044780a7b4827df7f2e3a6dad85
  window_manager: 3a1844359a6295ab1e47659b1a777e36773cd6e8

PODFILE CHECKSUM: 9ebaf0ce3d369aaa26a9ea0e159195ed94724cf3

COCOAPODS: 1.15.2
