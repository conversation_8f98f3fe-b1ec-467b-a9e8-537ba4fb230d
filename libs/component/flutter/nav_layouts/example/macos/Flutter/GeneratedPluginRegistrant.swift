//
//  Generated file. Do not edit.
//

import FlutterMacOS
import Foundation

import app_framework_component
import flutter_secure_storage_macos
import nav_layouts_component
import path_provider_foundation
import platform_utilities_component
import screen_retriever_macos
import ui_widgets_component
import window_manager

func RegisterGeneratedPlugins(registry: FlutterPluginRegistry) {
  AppFrameworkComponentPlugin.register(with: registry.registrar(forPlugin: "AppFrameworkComponentPlugin"))
  FlutterSecureStoragePlugin.register(with: registry.registrar(forPlugin: "FlutterSecureStoragePlugin"))
  NavLayoutsComponentPlugin.register(with: registry.registrar(forPlugin: "NavLayoutsComponentPlugin"))
  PathProviderPlugin.register(with: registry.registrar(forPlugin: "PathProviderPlugin"))
  PlatformUtilitiesComponentPlugin.register(with: registry.registrar(forPlugin: "PlatformUtilitiesComponentPlugin"))
  ScreenRetrieverMacosPlugin.register(with: registry.registrar(forPlugin: "ScreenRetrieverMacosPlugin"))
  UiWidgetsComponentPlugin.register(with: registry.registrar(forPlugin: "UiWidgetsComponentPlugin"))
  WindowManagerPlugin.register(with: registry.registrar(forPlugin: "WindowManagerPlugin"))
}
