# nav_layouts_component

Common layouts with built in navigation capabilities

## Layout Structures

The following class diagrams outline how the layout widgets are stacked to create different layout and navigation experiences.

### Splash Layout

```mermaid
classDiagram
  class RootLayout
  class RootState
  class RootSplashLayout
  class RootSplashState
  class Scaffold
  class NotificationScope

  RootLayout --> RootState
  RootState <|-- RootSplashState

  RootSplashLayout --> RootLayout
  RootLayout --> Scaffold
  RootSplashLayout --> RootSplashState
  Scaffold --> NotificationScope
```

### Navigation Layout

```mermaid
classDiagram
  class RootLayout
  class RootState
  class RootNavLayout
  class NavLayout
  class Scaffold
  class NotificationScope

  RootLayout --> RootState

  RootNavLayout --> RootLayout
  RootLayout --> NavLayout
  NavLayout --> NotificationScope
  NotificationScope --> Scaffold
```
