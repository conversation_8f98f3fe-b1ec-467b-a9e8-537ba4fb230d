name: nav_layouts_component
description: "MyCS common layouts with built in navigation capabilities"
publish_to: 'none'
version: 0.1.0
homepage: https://github.com/novassist-ai/novassist/libs/component/flutter/nav_layouts

environment:
  sdk: '>=3.3.0 <4.0.0'
  flutter: '>=3.3.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  plugin_platform_interface: ^2.0.2

  web: ^1.1.1
  provider: ^6.1.2
  go_router: ^14.1.2
  logging: ^1.2.0
  visibility_detector: ^0.4.0+2
  multi_split_view: 3.6.0

  platform_utilities_component:
    path: ../platform_utilities
  ui_widgets_component:
    path: ../ui_widgets
  app_framework_component:
    path: ../app_framework

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

flutter:
  generate: true

  plugin:
    platforms:
      android:
        package: ai.novassist.component.flutter.nav_layouts.nav_layouts_component
        pluginClass: NavLayoutsComponentPlugin
      ios:
        pluginClass: NavLayoutsComponentPlugin
      linux:
        pluginClass: NavLayoutsComponentPlugin
      macos:
        pluginClass: NavLayoutsComponentPlugin
      windows:
        pluginClass: NavLayoutsComponentPluginCApi
      web:
        pluginClass: NavLayoutsComponentWeb
        fileName: nav_layouts_component_web.dart
