import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:logging/logging.dart' as logging;

class RootNavRouter {
  static final logging.Logger _logger = logging.Logger('RootNavRouter');

  static RootNavRouter? _rootRouter;

  final GoRouter? _goRouter;
  GoRouter get goRouter => _goRouter!;

  factory RootNavRouter([GoRouter Function()? createRouter]) {
    if (_rootRouter == null) {
      if (createRouter != null) {
        _rootRouter = RootNavRouter._(createRouter());
      } else if (_rootRouter == null) {
        // if singleton has not bean created then
        // return a new instance of RootNavRouter
        // that can be used only as a helper for
        // navigating to named routes using a
        // navigator key
        return RootNavRouter._();
      }
    }
    // return the singleton instance
    // of RootNavRouter
    return _rootRouter!;
  }

  RootNavRouter._([this._goRouter]);

  /// Helper function to navigate to a named route
  void goToNamed(
    String name, {
    GlobalKey<NavigatorState>? navigatorKey,
    Map<String, String> pathParameters = const <String, String>{},
    Map<String, dynamic> queryParameters = const <String, dynamic>{},
  }) {
    if (navigatorKey != null && navigatorKey.currentContext != null) {
      navigatorKey.currentContext!.goNamed(
        name,
        pathParameters: pathParameters,
        queryParameters: queryParameters,
      );
    } else {
      final reason = navigatorKey == null
          ? 'parentNavigatorKey was null'
          : 'parentNavigatorKey.currentContext was null';
      _logger.fine(
        'Could not navigate to named route "$name" as $reason. '
        'Navigating to the route directly using the application\'s '
        'root navigation router.',
      );
      if (_goRouter != null) {
        _goRouter.goNamed(
          name,
          pathParameters: pathParameters,
          queryParameters: queryParameters,
        );
      } else {
        final msg = 'Could not navigate to named route '
            '"$name" as the root nav goRouter was null.';

        _logger.severe(msg);
        throw Exception(msg);
      }
    }
  }

  /// Helper function to navigate to a named route
  static goNamed(
    String name, {
    GlobalKey<NavigatorState>? navigatorKey,
    Map<String, String> pathParameters = const <String, String>{},
    Map<String, dynamic> queryParameters = const <String, dynamic>{},
  }) {
    RootNavRouter().goToNamed(
      name,
      navigatorKey: navigatorKey,
      pathParameters: pathParameters,
      queryParameters: queryParameters,
    );
  }
}
