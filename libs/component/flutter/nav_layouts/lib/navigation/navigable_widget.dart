import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:logging/logging.dart' as logging;

import 'root_nav_router.dart';

abstract class Navigable {
  /// The name of this page for named navigation
  String get routeName;

  /// The path and route to navigate to this page
  String get routePath;

  /// Creates the [GoRoute] for this navigable widget
  RouteBase route();
}

abstract class StatelessNavigableWidget extends StatelessWidget
    implements Navigable {
  /// The key for the navigator that will contain this page
  final GlobalKey<NavigatorState>? parentNavigatorKey;

  /// The page builder for this navigable widget. This
  /// builder is used to create a page for this widget
  /// that customizes the page's behavior when navigating.
  final NavigableRoutePageBuilder? pageBuilder;

  const StatelessNavigableWidget({
    super.key,
    this.parentNavigatorKey,
    this.pageBuilder,
  });

  /// Child routes that you can navigate to
  /// from this widget
  List<Navigable> get childRoutes => [];

  @override
  RouteBase route() {
    return _route(
      parentNavigatorKey,
      routeName,
      routePath,
      childRoutes,
      pageBuilder,
      null,
      this,
    );
  }

  @nonVirtual
  void goTo(
    String name, {
    Map<String, String> pathParameters = const <String, String>{},
    Map<String, dynamic> queryParameters = const <String, dynamic>{},
  }) {
    logging.Logger(
      runtimeType.toString(),
    ).fine(
      'Navigating to named route: $name'
      '(pathParameters: $pathParameters, '
      'queryParameters: $queryParameters)',
    );

    RootNavRouter().goToNamed(
      name,
      navigatorKey: parentNavigatorKey,
      pathParameters: pathParameters,
      queryParameters: queryParameters,
    );
  }
}

abstract class StatefulNavigableWidget extends StatefulWidget
    implements Navigable {
  /// The key for the navigator that will contain this page
  final GlobalKey<NavigatorState>? parentNavigatorKey;

  /// The page builder for this navigable widget. This
  /// builder is used to create a page for this widget
  /// that customizes the page's behavior when navigating.
  final NavigableRoutePageBuilder? pageBuilder;

  const StatefulNavigableWidget({
    super.key,
    this.parentNavigatorKey,
    this.pageBuilder,
  });

  /// Child routes that you can navigate to from this page
  List<Navigable> get childRoutes => [];

  @override
  RouteBase route() {
    return _route(
      parentNavigatorKey,
      routeName,
      routePath,
      childRoutes,
      pageBuilder,
      redirect,
      this,
    );
  }

  /// Function to handle redirection logic if any for this page
  @nonVirtual
  Future<String?> redirect(
    BuildContext context,
    GoRouterState state,
  ) async {
    if (super.key is GlobalKey) {
      final GlobalKey key = super.key as GlobalKey;

      if (key.currentState == null) {
        // when bloc state changes go_router can call redirect
        // multiple times. once before state is created and then
        // after multiple times after state is created every time
        // the widget is rebuilt. We handle redirect only on the
        // first call to redirect before state is created.

        final namedRoute = await evaluateRedirect(
          context,
          state,
        );
        if (namedRoute != null) {
          logging.Logger(
            runtimeType.toString(),
          ).fine(
            'Redirecting to named route: $namedRoute',
          );
          goTo(namedRoute);
        }
      }
    }

    return null;
  }

  Future<String?> evaluateRedirect(
    BuildContext context,
    GoRouterState state,
  ) async {
    return null;
  }

  @nonVirtual
  void goTo(
    String name, {
    Map<String, String> pathParameters = const <String, String>{},
    Map<String, dynamic> queryParameters = const <String, dynamic>{},
  }) {
    logging.Logger(
      runtimeType.toString(),
    ).fine(
      'Navigating to named route: $name'
      '(pathParameters: $pathParameters, '
      'queryParameters: $queryParameters)',
    );

    RootNavRouter().goToNamed(
      name,
      navigatorKey: parentNavigatorKey,
      pathParameters: pathParameters,
      queryParameters: queryParameters,
    );
  }
}

/// Helper function to create a GoRoute from a Navigable widget
RouteBase _route(
  GlobalKey<NavigatorState>? parentNavigatorKey,
  String routeName,
  String routePath,
  List<Navigable> childRoutes,
  ShellRoutePageBuilder? pageBuilder,
  GoRouterRedirect? redirect,
  Widget navigableWidget,
) {
  if (pageBuilder == null) {
    return GoRoute(
      parentNavigatorKey: parentNavigatorKey,
      name: routeName,
      path: routePath,
      routes: childRoutes
          .map(
            (child) => child.route(),
          )
          .toList(),
      redirect: redirect,
      builder: (context, state) {
        return navigableWidget;
      },
    );
  } else {
    return GoRoute(
      parentNavigatorKey: parentNavigatorKey,
      name: routeName,
      path: routePath,
      routes: childRoutes
          .map(
            (child) => child.route(),
          )
          .toList(),
      redirect: redirect,
      pageBuilder: (context, state) {
        return pageBuilder(context, state, navigableWidget);
      },
    );
  }
}

/// A function type for building a page for
/// the shell route of a navigable widget
typedef NavigableRoutePageBuilder = Page<dynamic> Function(
  BuildContext context,
  GoRouterState state,
  Widget navigableWidget,
);
