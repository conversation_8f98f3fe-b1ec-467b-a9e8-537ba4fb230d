import 'package:flutter/material.dart';

Size calculateTextSize({
  required BuildContext context,
  required String text,
  TextStyle? style,
}) {
  final TextPainter textPainter = TextPainter(
    text: TextSpan(text: text, style: style),
    textDirection: Directionality.of(
      context,
    ),
    textScaler: MediaQuery.of(
      context,
    ).textScaler,
  )..layout(minWidth: 0, maxWidth: double.infinity);

  return textPainter.size;
}
