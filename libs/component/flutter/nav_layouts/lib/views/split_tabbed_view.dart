import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'package:visibility_detector/visibility_detector.dart';
import 'package:multi_split_view/multi_split_view.dart';

import 'package:app_framework_component/app_framework.dart' as app;
import 'package:ui_widgets_component/ui_widgets.dart';

import '../layouts/root_layout.dart';

class SplitTabbedView extends StatefulWidget {
  String? get hookName => _hookName;
  final String? _hookName;

  final List<SplitViewFolderTab> folderTabs;
  final List<SplitViewContentArea> contentAreas;

  final double folderTabsFlex;
  final SplitViewPosition position;

  final void Function(int)? onTabChanged;

  const SplitTabbedView({
    super.key,
    String? hookName,
    this.folderTabs = const [],
    this.contentAreas = const [],
    this.folderTabsFlex = 2.0,
    this.position = SplitViewPosition.none,
    this.onTabChanged,
  }) : _hookName = hookName;

  @override
  State<StatefulWidget> createState() => _SplitTabbedViewState();
}

class _SplitTabbedViewState extends State<SplitTabbedView> {
  MultiSplitViewController? controller;

  int currentTabIndex = 0;

  @override
  void initState() {
    super.initState();
    assert(
      widget.hookName != null,
      'hookName must be provided',
    );
  }

  @override
  void dispose() {
    controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<RootState>(
      builder: (context, rootState, child) {
        return FutureBuilder(
          future: rootState.bootstrap,
          builder: (context, snapshot) {
            return snapshot.connectionState == ConnectionState.done
                ? _buildContainer(context)
                : const SizedBox.shrink();
          },
        );
      },
    );
  }

  Widget _buildContainer(BuildContext context) {
    return app.FeatureContainer<SplitViewFolderTab>(
      hookName: widget.hookName!,
      initializeAnchors: (context, anchors) {
        controller?.dispose();
        controller = MultiSplitViewController();

        List<SplitViewFolderTab> folderTabs = List.from(widget.folderTabs);
        folderTabs.addAll(anchors);
        folderTabs.sort((a, b) => a._order.compareTo(b._order));
        folderTabs.retainWhere(
          (tab) =>
              tab._isTabVisible != null ? tab._isTabVisible(context) : true,
        );
        return folderTabs;
      },
      builder: (context, ftAnchors) {
        return app.FeatureContainer<SplitViewContentArea>(
          hookName: widget.hookName!,
          initializeAnchors: (context, caAnchors) {
            // This is called once when the feature container is
            // added to widget tree. So we can safely construct
            // and add the the list of areas for the multi split
            // view controller here.

            List<SplitViewContentArea> contentAreas =
                List.from(widget.contentAreas);
            contentAreas.addAll(caAnchors);
            contentAreas.sort((a, b) => a._order.compareTo(b._order));

            final numAreas = contentAreas.length;
            final leftPadding = widget.position == SplitViewPosition.none ||
                    widget.position == SplitViewPosition.left
                ? 8.0
                : 0.0;
            final rightPadding = widget.position == SplitViewPosition.none ||
                    widget.position == SplitViewPosition.right
                ? 8.0
                : 0.0;

            // Create and add the folder tab view
            // to the multi-split view controller
            final folderTabs = Area(
              flex: widget.folderTabsFlex,
              builder: (context, area) {
                return Padding(
                  padding: EdgeInsets.fromLTRB(
                    leftPadding,
                    8.0,
                    rightPadding,
                    numAreas > 0 ? 0.0 : 8.0,
                  ),
                  child: TabbedFolders(
                    tabs: ftAnchors,
                    onInit: (controller) => _onInitTabController(
                      controller,
                      ftAnchors,
                    ),
                    onTabChanged: (index) => _onTabChanged(
                      index,
                      ftAnchors,
                    ),
                  ),
                );
              },
            );
            controller!.addArea(folderTabs);

            // Create and add the content area views
            // to the multi-split view controller
            int count = 0;
            for (var ca in contentAreas) {
              final bottomPadding = ++count < numAreas ? 0.0 : 8.0;
              controller!.addArea(
                Area(
                  flex: ca._flex,
                  size: ca._size,
                  min: ca._min,
                  max: ca._max,
                  builder: (context, area) {
                    return Padding(
                      padding: EdgeInsets.fromLTRB(
                        leftPadding,
                        0.0,
                        rightPadding,
                        bottomPadding,
                      ),
                      child: ca,
                    );
                  },
                ),
              );
            }

            return [];
          },
          builder: (context, _) {
            return VisibilityDetector(
              key: Key(widget.hookName!),
              onVisibilityChanged: (info) {
                final folderTab = ftAnchors[currentTabIndex];
                if (info.visibleFraction == 0) {
                  if (folderTab._onTabHidden != null) {
                    folderTab._onTabHidden(context);
                  }
                } else {
                  if (folderTab._onTabVisible != null) {
                    folderTab._onTabVisible(context);
                  }
                }
              },
              child: MultiSplitView(
                axis: Axis.vertical,
                controller: controller,
              ),
            );
          },
        );
      },
    );
  }

  void _onInitTabController(
    TabController controller,
    List<SplitViewFolderTab> tabs,
  ) {
    for (var tab in tabs) {
      if (tab._onInitTabController != null) {
        tab._onInitTabController(controller);
      }
    }
  }

  void _onTabChanged(
    int index,
    List<SplitViewFolderTab> tabs,
  ) {
    if (index == currentTabIndex) {
      return;
    }
    final folderTabPrev = tabs[currentTabIndex];
    if (folderTabPrev._onTabHidden != null) {
      folderTabPrev._onTabHidden(context);
    }
    final folderTabCurr = tabs[index];
    if (folderTabCurr._onTabVisible != null) {
      folderTabCurr._onTabVisible(context);
    }
    currentTabIndex = index;
    if (widget.onTabChanged != null) {
      widget.onTabChanged!(currentTabIndex);
    }
  }
}

class SplitViewFolderTab extends FolderTab implements app.FeatureAnchor {
  @override
  String get hookName => _hookName;
  final String _hookName;

  final int _order;

  final void Function(TabController)? _onInitTabController;

  final bool Function(BuildContext)? _isTabVisible;
  final void Function(BuildContext)? _onTabVisible;
  final void Function(BuildContext)? _onTabHidden;

  const SplitViewFolderTab({
    required String hookName,
    required super.title,
    super.icon,
    super.iconData,
    void Function(TabController)? onInitTabController,
    bool Function(BuildContext)? isTabVisible,
    void Function(BuildContext)? onTabVisible,
    void Function(BuildContext)? onTabHidden,
    required Widget super.content,
    int order = 0,
  })  : _hookName = hookName,
        _order = order,
        _onInitTabController = onInitTabController,
        _isTabVisible = isTabVisible,
        _onTabVisible = onTabVisible,
        _onTabHidden = onTabHidden;
}

class SplitViewContentArea extends StatelessWidget
    implements app.FeatureAnchor {
  @override
  String get hookName => _hookName;
  final String _hookName;

  final int _order;
  final double? _flex;
  final double? _size;
  final double? _min;
  final double? _max;
  final Widget? _child;

  @override
  Widget build(BuildContext context) {
    final ThemeData theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border.all(
          color: theme.colorScheme.primary,
        ),
        borderRadius: const BorderRadius.all(
          Radius.circular(5),
        ),
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.all(
          Radius.circular(5),
        ),
        child: _child,
      ),
    );
  }

  const SplitViewContentArea({
    super.key,
    required String hookName,
    required Widget child,
    double? flex,
    double? size,
    double? min,
    double? max,
    int order = 0,
  })  : _hookName = hookName,
        _order = order,
        _flex = flex,
        _size = size,
        _min = min,
        _max = max,
        _child = child;
}

enum SplitViewPosition { none, left, center, right }
