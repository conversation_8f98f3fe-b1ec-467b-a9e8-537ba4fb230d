import 'package:flutter/material.dart';

import 'package:app_framework_component/app_framework.dart' as app;
import 'package:ui_widgets_component/ui_widgets.dart' as ui;

import 'app_status_tool.dart';

class AppStatusToolBar {
  static const String featureHookName = 'app_status_tool_bar';

  static ui.StatusViewBar? buildToolBar(
    BuildContext context, {
    List<AppStatusToolAnchor>? anchors,
    List<ui.Tool> toolsLeft = const [],
    List<ui.Tool> toolsCenter = const [],
    List<ui.Tool> toolsRight = const [],
  }) {
    anchors ??= app.FeatureRegistry.instance().getAnchors<AppStatusToolAnchor>(
      AppStatusToolBar.featureHookName,
      context: context,
    );
    if (anchors.isEmpty && toolsLeft.isEmpty && toolsRight.isEmpty) {
      return null;
    }
    final anchorsLeft = anchors
        .where((anchor) => anchor.alignment == AppStatusToolAlignment.left)
        .toList();
    final anchorsCenter = anchors
        .where((anchor) => anchor.alignment == AppStatusToolAlignment.center)
        .toList();
    final anchorsRight = anchors
        .where((anchor) => anchor.alignment == AppStatusToolAlignment.right)
        .toList();

    anchorsLeft.sort((a, b) => a.order.compareTo(b.order));
    anchorsCenter.sort((a, b) => a.order.compareTo(b.order));
    anchorsRight.sort((a, b) => a.order.compareTo(b.order));

    int centerCount = 0;

    return ui.StatusViewBar(
      items: [
        ...toolsLeft,
        ...anchorsLeft
            .map(
              (tool) => [
                if (tool.divider == AppStatusToolDivider.left)
                  const ui.DividerTool(),
                tool.statusTool,
                if (tool.divider == AppStatusToolDivider.auto ||
                    tool.divider == AppStatusToolDivider.right)
                  const ui.DividerTool(),
              ],
            )
            .expand((t) => t),
        const ui.SpacerTool(),
        ...toolsCenter,
        ...anchorsCenter
            .map(
              (tool) => [
                if ((tool.divider == AppStatusToolDivider.auto &&
                        centerCount++ == 0) ||
                    tool.divider == AppStatusToolDivider.left)
                  const ui.DividerTool(),
                tool.statusTool,
                if (tool.divider == AppStatusToolDivider.auto ||
                    tool.divider == AppStatusToolDivider.right)
                  const ui.DividerTool(),
              ],
            )
            .expand((t) => t),
        const ui.SpacerTool(),
        ...anchorsRight
            .map(
              (tool) => [
                if (tool.divider == AppStatusToolDivider.auto ||
                    tool.divider == AppStatusToolDivider.left)
                  const ui.DividerTool(),
                tool.statusTool,
                if (tool.divider == AppStatusToolDivider.right)
                  const ui.DividerTool(),
              ],
            )
            .expand((t) => t),
        ...toolsRight,
      ],
    );
  }
}
