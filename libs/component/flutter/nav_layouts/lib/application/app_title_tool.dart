import 'package:ui_widgets_component/ui_widgets.dart' as ui;
import 'package:app_framework_component/app_framework.dart' as app;

class AppTitleTool extends ui.ToolIconButton implements app.FeatureAnchor {
  @override
  String get hookName => _hookName;
  final String _hookName;

  final int order;

  const AppTitleTool({
    String hookName = '',
    this.order = 0,
    required super.title,
    super.subTitle,
    super.tooltip,
    super.icon,
    super.iconData,
    super.onPressed,
    super.enabled,
  }) : _hookName = hookName;
}
