import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'package:ui_widgets_component/ui_widgets.dart' as ui;

import '../layouts/root_nav_layout.dart';
import '../layouts/nav_layout.dart';
import 'app_title_tool_bar.dart';
import 'app_status_tool_bar.dart';

class AppNavLayout extends RootNavLayout {
  @override
  String? get hookName => featureHookName;
  static const String featureHookName = 'app_nav_layout';

  @override
  List<NavTarget> get navTargets => [];

  // Whether the title bar tools
  // are on the left or right
  final bool _titleBarToolsLeft;

  // How much to indent the title
  // bar tools
  final double? _titleBarToolsIndent;

  @override
  NavProperties get navProperties => const NavProperties(
        mobileNavType: MobileNavType.bottom,
        showExtended: ShowExtended.dynamic,
        showLabels: ShowLabels.always,
      );

  @nonVirtual
  @override
  NavTitleBar? buildNavTitleBar(BuildContext context) {
    final toolbar = AppTitleToolBar.buildToolBar(
      context,
    );
    final titleBarLeading = buildTitleBarLeading(context);
    final titleBarActions = buildTitleBarActions(context);

    return NavTitleBar(
      leading: titleBarLeading,
      actions: titleBarActions,
      leftToolBar: _titleBarToolsLeft ? toolbar : null,
      leftIndent: _titleBarToolsLeft ? _titleBarToolsIndent ?? 40.0 : 40.0,
      rightToolBar: _titleBarToolsLeft ? null : toolbar,
      rightIndent: _titleBarToolsLeft ? 0.0 : _titleBarToolsIndent ?? 0.0,
    );
  }

  /// Override to build the title bar leading widget
  Widget? buildTitleBarLeading(BuildContext context) => null;

  /// Override to build the title bar actions
  List<Widget>? buildTitleBarActions(BuildContext context) => null;

  @nonVirtual
  @override
  ui.StatusViewBar? buildStatusBar(
    BuildContext context,
  ) {
    return AppStatusToolBar.buildToolBar(
      context,
      toolsLeft: buildStatusToolsLeft(context),
      toolsCenter: buildStatusToolsCenter(context),
      toolsRight: buildStatusToolsRight(context),
    );
  }

  /// Override to return additional status tools
  /// show to left of statuses anchored by features
  List<ui.Tool> buildStatusToolsLeft(BuildContext context) => [];

  /// Override to return additional status tools
  /// show to centered and left of center anchored
  /// features
  List<ui.Tool> buildStatusToolsCenter(BuildContext context) => [];

  /// Override to return additional status tools
  /// show to right of statuses anchored by features
  List<ui.Tool> buildStatusToolsRight(BuildContext context) => [];

  AppNavLayout(
    super.title, {
    bool titleBarToolsLeft = true,
    double? titleBarToolsIndent,
  })  : _titleBarToolsLeft = titleBarToolsLeft,
        _titleBarToolsIndent = titleBarToolsIndent;
}
