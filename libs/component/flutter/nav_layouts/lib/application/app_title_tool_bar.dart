import 'package:flutter/material.dart';

import 'package:app_framework_component/app_framework.dart' as app;
import 'package:ui_widgets_component/ui_widgets.dart' as ui;

import 'app_title_tool.dart';

class AppTitleToolBar {
  static const String featureHookName = 'app_title_bar';

  static ui.ToolBar buildToolBar(
    BuildContext context, {
    List<AppTitleTool>? anchors,
  }) {
    anchors ??= app.FeatureRegistry.instance().getAnchors<AppTitleTool>(
      AppTitleToolBar.featureHookName,
      context: context,
    );
    anchors.sort((a, b) => a.order.compareTo(b.order));

    return ui.ToolBar(
      tools: anchors,
    );
  }
}
