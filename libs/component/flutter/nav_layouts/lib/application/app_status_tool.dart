import 'package:ui_widgets_component/ui_widgets.dart' as ui;
import 'package:app_framework_component/app_framework.dart' as app;

abstract class AppStatusToolAnchor implements app.FeatureAnchor {
  @override
  String get hookName => _hookName;
  final String _hookName;

  final int order;
  final AppStatusToolAlignment alignment;
  final AppStatusToolDivider divider;

  ui.Tool get statusTool;

  const AppStatusToolAnchor({
    String hookName = '',
    required this.order,
    this.alignment = AppStatusToolAlignment.left,
    this.divider = AppStatusToolDivider.none,
  }) : _hookName = hookName;
}

class AppStatusTool extends AppStatusToolAnchor {
  @override
  ui.Tool get statusTool => _statusTool;
  final ui.Tool _statusTool;

  const AppStatusTool({
    super.hookName,
    required super.order,
    super.alignment = AppStatusToolAlignment.left,
    super.divider = AppStatusToolDivider.none,
    required ui.Tool statusTool,
  }) : _statusTool = statusTool;
}

enum AppStatusToolAlignment {
  left,
  center,
  right,
}

enum AppStatusToolDivider {
  none,
  auto,
  left,
  right,
}
