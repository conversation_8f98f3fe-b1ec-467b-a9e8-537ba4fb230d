import 'package:flutter/material.dart';

import 'package:ui_widgets_component/ui_widgets.dart' as ui;
import 'package:app_framework_component/app_framework.dart' as app;

import '../navigation/navigable_widget.dart';
import '../layouts/root_layout.dart';
import '../layouts/root_nav_layout.dart';
import 'app_title_tool_bar.dart';
import 'app_title_tool.dart';
import 'app_status_tool_bar.dart';

class AppScaffold extends StatelessNavigableWidget {
  final String title;

  final Widget? body;

  // overide if this view needs to be navigable
  @override
  String get routeName => throw UnimplementedError();

  // overide if this view needs to be navigable
  @override
  String get routePath => throw UnimplementedError();

  /// Override to build the title bar tools
  List<AppTitleTool>? buildTitleBarTools(BuildContext context) => null;

  /// Override to build the title bar actions
  List<Widget>? buildTitleBarActions(BuildContext context) => null;

  /// Override to return additional status tools
  /// show to left of anchored by features
  List<ui.Tool> buildStatusToolsLeft(BuildContext context) => [];

  /// Override to return additional status tools
  /// show to centered and left of center anchored
  /// features
  List<ui.Tool> buildStatusToolsCenter(BuildContext context) => [];

  /// Override to return additional status tools
  /// show to right of anchored by features
  List<ui.Tool> buildStatusToolsRight(BuildContext context) => [];

  const AppScaffold({
    super.key,
    required this.title,
    this.body,
  });

  @override
  Widget build(BuildContext context) {
    return app.FeatureContainer<AppTitleTool>(
      hookName: AppTitleToolBar.featureHookName,
      initializeAnchors: (context, anchors) {
        List<AppTitleTool> appTools = List.from(
          buildTitleBarTools(context) ?? [],
        );
        appTools.addAll(anchors);
        appTools.sort((a, b) => a.order.compareTo(b.order));
        return appTools;
      },
      builder: (context, anchors) {
        final ThemeData theme = Theme.of(context);
        final statusBar = AppStatusToolBar.buildToolBar(
          context,
          toolsLeft: buildStatusToolsLeft(context),
          toolsCenter: buildStatusToolsCenter(context),
          toolsRight: buildStatusToolsRight(context),
        );

        return RootLayout(
          body: Scaffold(
            appBar: ui.ToolsAppBar(
              context: context,
              title: title,
              leftToolBar: AppTitleToolBar.buildToolBar(
                context,
                anchors: anchors,
              ),
              actions: buildTitleBarActions(context),
              backgroundColor: theme.colorScheme.primaryContainer,
              centerTitle: true,
            ),
            body: statusBar == null
                ? app.NotificationScope(
                    child: body ?? Container(),
                  )
                : app.NotificationScope(
                    floatSnackBar: true,
                    bottomOffset: statusBar.height,
                    child: Column(
                      children: [
                        Expanded(
                          child: body ?? Container(),
                        ),
                        statusBar,
                      ],
                    ),
                  ),
          ),
          createRootState: () => RootState(),
          hookName: RootNavLayout.postLoginHookName,
        );
      },
    );
  }
}
