import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:web/web.dart' as web;

import 'package:app_framework_component/app_framework.dart' as app;

class RootLayout<S extends RootState> extends StatefulWidget {
  /// Create a [S] instance that extends [RootState].
  final S Function() createRootState;

  /// The main body of the layout.
  final Widget body;

  /// Bootstrap target hook. This hook can be used to
  /// execute bootstrap logic of a feature when the
  /// app starts up after login.
  final String? hookName;

  const RootLayout({
    super.key,
    required this.body,
    required this.createRootState,
    this.hookName,
  });

  @override
  State<RootLayout<S>> createState() => _RootLayoutState<S>();
}

class _RootLayoutState<S extends RootState> extends State<RootLayout<S>> {
  late S state;

  List<BootstrapTarget>? bootstrapAnchors;
  late int bootstrapIndex = 0;

  final app.FeatureRegistry featureRegistry = app.FeatureRegistry.instance();

  @override
  void initState() {
    super.initState();

    state = widget.createRootState();
    if (widget.hookName == null) {
      // If bootstrap target hook is not provided then the
      // mark the bootstrap process as complete.
      state._bootstrapCompleter.complete();
      bootstrapAnchors = [];
      _updateBootstrapState();
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => state,
      child: Consumer<S>(
        builder: (
          context,
          state,
          _,
        ) {
          // Show the bootstrap target if bootstrapping is not
          // complete. Bootstrapping is complete when all bootstrap
          // targets have been displayed and dismissed or the
          // bootstrap chain is aborted explicitly by calling
          // the `complete` callback with false.
          List<Widget>? bootstrapWidgets;
          if (bootstrapAnchors == null) {
            // Get the bootstrap anchors for the current hook
            // and sort them if not already available done.
            bootstrapAnchors = featureRegistry.getAnchors<BootstrapTarget>(
              widget.hookName!,
              context: context,
            );
            bootstrapAnchors!.sort((a, b) => a._order.compareTo(b._order));
            _updateBootstrapState();
          }

          if (bootstrapIndex < bootstrapAnchors!.length) {
            final bootstrapTarget = bootstrapAnchors![bootstrapIndex];

            bootstrapWidgets = [
              Opacity(
                opacity: 0.5,
                child: ModalBarrier(
                  dismissible: bootstrapTarget.dismissible,
                  onDismiss: () {
                    if (bootstrapTarget.onDismiss != null) {
                      bootstrapTarget.onDismiss!();
                    }
                    setState(() {
                      bootstrapIndex++;
                      _updateBootstrapState();
                    });
                  },
                  color: Colors.black,
                ),
              ),
              // Builds the bootstrap target widget. The
              // bootstrap target widget is a widget that
              // will be displayed while the bootstrap
              // process is running. The bootstrap process
              // can be continued by calling the `complete`
              // callback with true or aborted by calling
              // the `complete` callback with false.
              // Aborting the process does not prevent the
              // app from starting up. The widget must take
              // explicit action to exist/sign out from the
              // application before aborting the process.
              bootstrapTarget.builder(
                context,
                (continueBootstrap) {
                  if (continueBootstrap) {
                    setState(() {
                      bootstrapIndex = bootstrapAnchors!.length;
                      _updateBootstrapState();
                    });
                  } else {
                    setState(() {
                      bootstrapIndex++;
                      _updateBootstrapState();
                    });
                  }
                },
              ),
            ];
          }

          if (kIsWeb && (bootstrapIndex == bootstrapAnchors!.length)) {
            // If app url has query parameters they should all be
            // handled by the bootstrap process. If any remaining
            // query parameters are present then they should be
            // removed from the url.
            final appHref = web.window.location.href;
            final queryIndex = appHref.indexOf('?');
            if (queryIndex != -1) {
              web.window.open(appHref.substring(0, queryIndex), '_self');
            }
          }

          return Stack(
            alignment: Alignment.center,
            children: [
              widget.body,

              // Show the bootstrap target.
              if (bootstrapWidgets != null) ...bootstrapWidgets,

              // Show a modal progress indicator when waiting.
              if (state.isBusy) ...[
                const Opacity(
                  opacity: 0.5,
                  child: ModalBarrier(
                    dismissible: false,
                    color: Colors.black,
                  ),
                ),
                const Center(
                  child: CircularProgressIndicator(),
                ),
              ],

              // Show a modal background.
              if (state.isModalBackdrop) ...[
                const Opacity(
                  opacity: 0.5,
                  child: ModalBarrier(
                    dismissible: false,
                    color: Colors.black,
                  ),
                ),
              ],
            ],
          );
        },
      ),
    );
  }

  void _updateBootstrapState() {
    if (bootstrapIndex == (bootstrapAnchors?.length ?? 0)) {
      state._bootstrapCompleter.complete();
      state._isBusy = false;
    } else {
      state._isBusy = true;
    }
  }
}

class RootState extends ChangeNotifier {
  bool _isBusy = false;
  bool get isBusy => _isBusy;

  bool _isModalBackdrop = false;
  bool get isModalBackdrop => _isModalBackdrop;

  final Completer<void> _bootstrapCompleter = Completer<void>();
  Future<void> get bootstrap => _bootstrapCompleter.future;

  void setBusy([value = true]) {
    _isBusy = value;
    notifyListeners();
  }

  void resetBusy() {
    _isBusy = false;
    notifyListeners();
  }

  void setModalBackdrop() {
    _isModalBackdrop = true;
    notifyListeners();
  }

  void resetModalBackdrop() {
    _isModalBackdrop = false;
    notifyListeners();
  }

  void notify() {
    notifyListeners();
  }
}

/// A bootstrap target. This is a feature anchor that
/// features can use to register to execute bootstrap
/// logic when the app starts up after login. If
/// [dismissible] is true then the user can dismiss
/// the bootstrap target widget by tapping on the modal
/// barrier. If [onDismiss] is provided then it will
/// be called when the user dismisses the bootstrap
/// target widget.
class BootstrapTarget extends app.FeatureAnchor {
  final BootstrapBuilder builder;

  final bool dismissible;
  final VoidCallback? onDismiss;

  @override
  String get hookName {
    if (_hookName == null) {
      throw StateError(
        'hookName is null for BootstrapTarget',
      );
    }
    return _hookName;
  }

  final String? _hookName;
  final int _order;

  const BootstrapTarget({
    required this.builder,
    this.dismissible = false,
    this.onDismiss,
    String? hookName,
    int order = 0,
  })  : _hookName = hookName,
        _order = order;
}

/// The bootstrap builder should return a widget that
/// will be displayed while the bootstrap process is
/// running. The builder should call the `complete`
/// callback when the bootstrap process is complete
/// for the current bootstrap target.
typedef BootstrapBuilder = Widget Function(
  BuildContext context,
  // A callback to signal that the bootstrap process
  // is complete. If called with true then the bootstrap
  // process will continue to the next bootstrap target.
  // If called with false then the bootstrap process
  // will be aborted and marked as complete.
  void Function(bool) complete,
);
