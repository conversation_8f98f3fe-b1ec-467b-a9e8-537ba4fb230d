import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'package:platform_utilities_component/platform/app_platform.dart';
import 'package:app_framework_component/app_framework.dart' as app;

import 'root_layout.dart';

class RootSplashLayout extends StatelessWidget {
  final String title;
  final Widget body;
  final Widget? splash;
  final Decoration? background;

  /// Show a seperator between the splash and the body.
  final bool splashSeperator;

  /// Flex value of splash w.r.t. body.
  /// This value is used to calculate is
  /// a factor of the body width.
  final double splashScale;
  final double breakColumns;

  /// Align splash view to the right
  /// of the body.
  final bool rightAlignedSplash;

  /// Min width of the body.
  /// -1 means no min width.
  final double bodyMinWidth;

  /// Max width of the body.
  final double bodyMaxWidth;

  /// Min width view should
  /// be to show splash.
  final double minWidthToShowSplash;

  /// Create a [RootSplashState] instance.
  final RootSplashState Function() createRootSplashState;
  static RootSplashState _defaultRootSplashState() => RootSplashState();

  /// Optional pre-login bootstrap hook name
  String? get preLoginBootstrapHook => preLoginHookName;
  static const String preLoginHookName = 'pre_login_bootstrap_hook';

  const RootSplashLayout({
    super.key,
    required this.title,
    required this.body,
    this.splash,
    this.background,
    this.splashScale = 1.0, // w.r.t. the width of the body
    this.rightAlignedSplash = false,
    this.bodyMinWidth = 0.0,
    double? bodyMaxWidth,
    this.minWidthToShowSplash = double.infinity,
    this.splashSeperator = false,
    this.createRootSplashState = _defaultRootSplashState,
  })  : assert(
          !splashSeperator || splash != null,
          'splashSeperator was true but '
          'no splash widget provided.',
        ),
        bodyMaxWidth = bodyMaxWidth ?? minWidthToShowSplash,
        breakColumns = splashScale + 1;

  @override
  Widget build(BuildContext context) {
    return RootLayout<RootSplashState>(
      body: _buildSplashLayout(context),
      createRootState: createRootSplashState,
      hookName: preLoginBootstrapHook,
    );
  }

  Widget _buildSplashLayout(context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Consumer<RootSplashState>(builder: (
          context,
          state,
          _,
        ) {
          final animateSplash = state.animateSplash;
          final isSplash = splash != null;
          final showSplash = isSplash &&
              state.showSplash &&
              constraints.maxWidth >= minWidthToShowSplash &&
              (AppPlatform.isWeb || !AppPlatform.isMobile);

          // width of a flex column
          final breakColumnWidth = constraints.maxWidth / breakColumns;
          // width of the body
          final bodyWidth = min(
            bodyMaxWidth,
            max(breakColumnWidth, bodyMinWidth),
          );
          // width of balance space on the left and
          // right of the body when no splash is shown
          final spaceWidth = bodyMaxWidth > constraints.maxWidth
              ? 0.0
              : (constraints.maxWidth - bodyWidth) / 2;
          // width of the splash
          final splashWidth = bodyWidth > breakColumnWidth
              ? constraints.maxWidth - bodyWidth
              : breakColumnWidth * splashScale;

          return app.NotificationScope(
            child: Scaffold(
              appBar: AppPlatform.isMobile
                  ? AppBar(
                      title: Text(title),
                    )
                  : null,
              body: Container(
                decoration: background,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (isSplash || bodyMaxWidth != double.infinity) ...[
                      AnimatedContainer(
                        duration: animateSplash
                            ? const Duration(seconds: 1)
                            : const Duration(seconds: 0),
                        curve: Curves.fastOutSlowIn,
                        width: !showSplash
                            ? spaceWidth
                            : !rightAlignedSplash
                                ? splashWidth
                                : 0,
                        child: rightAlignedSplash
                            ? null
                            : animateSplash || showSplash
                                ? splash
                                : null,
                        onEnd: () {
                          if (animateSplash) state.notify();
                        },
                      ),
                      if (showSplash && !rightAlignedSplash)
                        buildSplashSeperator(constraints),
                    ],
                    Expanded(
                      child: Container(
                        constraints: BoxConstraints(
                          minWidth: bodyMinWidth,
                          maxWidth: bodyMaxWidth,
                        ),
                        child: body,
                      ),
                    ),
                    if (isSplash || bodyMaxWidth != double.infinity) ...[
                      if (showSplash && rightAlignedSplash)
                        buildSplashSeperator(constraints),
                      AnimatedContainer(
                        duration: animateSplash
                            ? const Duration(seconds: 1)
                            : const Duration(seconds: 0),
                        curve: Curves.fastOutSlowIn,
                        width: !showSplash
                            ? spaceWidth
                            : rightAlignedSplash
                                ? splashWidth
                                : 0,
                        child: !rightAlignedSplash
                            ? null
                            : animateSplash || showSplash
                                ? splash
                                : null,
                        onEnd: () {
                          if (animateSplash) state.notify();
                        },
                      ),
                    ],
                  ],
                ),
              ),
            ),
          );
        });
      },
    );
  }

  Widget buildSplashLeft(
    BoxConstraints constraints,
    bool animate,
    bool showSplash,
    double splashWidth,
    double spaceWidth,
  ) {
    return AnimatedContainer(
      duration:
          animate ? const Duration(seconds: 1) : const Duration(seconds: 0),
      curve: Curves.fastOutSlowIn,
      child: Container(
        width: !showSplash
            ? spaceWidth
            : !rightAlignedSplash
                ? splashWidth
                : 0,
        color: Colors.amber,
      ),
    );
  }

  Widget buildSplashRight(
    BoxConstraints constraints,
    bool showSplash,
  ) {
    if (showSplash) {
      return AnimatedContainer(
          duration: const Duration(seconds: 1),
          child: Row(
            children: [
              if (splashSeperator) buildSplashSeperator(constraints),
              Expanded(
                child: splash!,
              ),
            ],
          ));
    } else {
      return AnimatedContainer(
        duration: const Duration(seconds: 1),
        child: const SizedBox.shrink(),
      );
    }
  }

  Widget buildSplashSeperator(BoxConstraints constraints) {
    return Container(
      height: constraints.maxHeight,
      decoration: const BoxDecoration(
        color: Colors.black,
        boxShadow: [
          BoxShadow(
            color: Colors.black38,
            spreadRadius: 3,
            blurRadius: 3,
            offset: Offset(0, 3), // changes position of shadow
          ),
        ],
      ),
    );
  }
}

class RootSplashState extends RootState {
  /// This is used to determine if
  /// the splash screen is shown
  bool _showSplash = true;
  bool get showSplash {
    _animateSplash = false;
    return _showSplash;
  }

  // This is used to determine if
  // the splash screen is animated
  bool _animateSplash = false;
  bool get animateSplash => _animateSplash;

  void setShowSplash(bool show) {
    _animateSplash = true;
    _showSplash = show;
    notifyListeners();
  }
}
