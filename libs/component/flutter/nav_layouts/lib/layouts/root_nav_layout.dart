import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import 'package:ui_widgets_component/ui_widgets.dart' as ui;
import 'package:app_framework_component/app_framework.dart' as app;

import '../navigation/navigable_widget.dart';
import 'root_layout.dart';
import 'nav_layout.dart';

abstract class RootNavLayout implements Navigable {
  @override
  String get routeName {
    assert(navTargets.isNotEmpty);
    return navTargets[0]._body.routeName;
  }

  @override
  String get routePath {
    assert(navTargets.isNotEmpty);
    return navTargets[0]._body.routeName;
  }

  /// Optional feature hook name for this navigation shell
  String? get hookName => null;

  /// Optional post-login bootstrap hook name
  String? get postLoginBootstrapHook => postLoginHookName;
  static const String postLoginHookName = 'post_login_bootstrap_hook';

  /// A pre-defined list of navigation targets to route nav
  /// menu selections. Additional targets may be added via
  /// feature hooks if [hookName] is not null and registered
  /// [app.Feature]s provide [NavTarget] feature anchors.
  List<NavTarget> get navTargets;

  /// Optional navigation properties to customize the layout
  /// navigation ui
  NavProperties get navProperties;

  /// The root view title
  final String title;

  /// Optional app bar to display at the top of the layout
  NavTitleBar? buildNavTitleBar(
    BuildContext context,
  ) =>
      null;

  /// Optional build leading widget for navigation trail
  Widget? buildNavTrailLeadingWidget(
    BuildContext context,
    BoxConstraints constraints,
    bool isExtended,
  ) =>
      null;

  /// Optional build trailing widget for navigation trail
  Widget? buildNavTrailTrailingWidget(
    BuildContext context,
    BoxConstraints constraints,
    bool isExtended,
  ) =>
      null;

  /// Optional build header widget for navigation drawer
  Widget? buildNavDrawerHeaderWidget(
    BuildContext context,
    BoxConstraints constraints,
  ) =>
      null;

  /// Optional status bar to display at the bottom of the layout
  ui.StatusViewBar? buildStatusBar(
    BuildContext context,
  ) =>
      null;

  /// Shell navigation key for this root layout
  final _shellNavigatorKey = GlobalKey<NavigatorState>();

  RootNavLayout(
    this.title,
  );

  @override
  RouteBase route() {
    late final List<NavTarget> navTargets;
    if (hookName != null) {
      // Get any navTarget anchors from the feature
      // registry and merge them with the predefined
      // navTargets from this class
      navTargets = List.from(this.navTargets);
      final navTargetAnchors =
          app.FeatureRegistry.instance().getAnchors<NavTarget>(
        hookName!,
      );
      navTargets.addAll(navTargetAnchors);
      navTargets.sort((a, b) => a._order.compareTo(b._order));
    } else {
      navTargets = this.navTargets;
    }
    assert(
      navTargets.length >= 2,
      'At least two nav targets are required',
    );

    return StatefulShellRoute.indexedStack(
      builder: (context, navState, navShell) {
        final navTitleBar = buildNavTitleBar(context);
        final navDests = navTargets
            .map(
              (navTarget) => navTarget._destBuilder(context),
            )
            .toList();
        final statusBar = buildStatusBar(context);

        return RootLayout<RootState>(
          body: NavLayout(
            navShell,
            navDests,
            titleBar: navTitleBar,
            navProperties: navProperties,
            buildNavTrailLeadingWidgetFn: buildNavTrailLeadingWidget,
            buildNavTrailTrailingWidgetFn: buildNavTrailTrailingWidget,
            buildNavDrawerHeaderWidgetFn: buildNavDrawerHeaderWidget,
            statusBar: statusBar,
          ),
          createRootState: () => RootState(),
          hookName: postLoginBootstrapHook,
        );
      },
      branches: navTargets.map((navTarget) {
        if (routeName == navTarget._body.routeName) {
          return StatefulShellBranch(
            navigatorKey: _shellNavigatorKey,
            routes: [
              navTarget._body.route(),
            ],
          );
        } else {
          return StatefulShellBranch(
            routes: [
              navTarget._body.route(),
            ],
          );
        }
      }).toList(),
    );
  }
}

/// A navigation target. This is a feature anchor that
/// features can use to register navigation destinations
/// for a navigation shell. Features that provide these
/// anchors should assume that the BuildContext for
/// the callback to retrieve anchors will be null as
/// NavTargets are enumerated before a context is
/// available.
class NavTarget extends app.FeatureAnchor {
  final Navigable _body;
  final NavDestBuilder _destBuilder;

  @override
  String get hookName {
    if (_hookName == null) {
      throw StateError(
        'hookName is null for NavTarget for ${_body.runtimeType}',
      );
    }
    return _hookName;
  }

  final String? _hookName;
  final int _order;

  const NavTarget({
    required Navigable body,
    required NavDestBuilder destBuilder,
    String? hookName,
    int order = 0,
  })  : _body = body,
        _destBuilder = destBuilder,
        _hookName = hookName,
        _order = order;
}

typedef NavDestBuilder = NavDest Function(BuildContext context);

/// Global navigation helper
final class GlobalNavigator {
  static final _rootNavigatorKey = GlobalKey<NavigatorState>();
  static get key => _rootNavigatorKey;

  static void goTo(String name) {
    _rootNavigatorKey.currentContext!.goNamed(name);
  }

  // Not instantiable
  GlobalNavigator._();
}
