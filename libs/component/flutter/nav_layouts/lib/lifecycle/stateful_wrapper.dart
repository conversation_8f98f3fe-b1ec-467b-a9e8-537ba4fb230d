import 'package:flutter/material.dart';

// use this wrapper to wrap widgets that need to
// handle initialization and dispose events during
// the widget lifecycle
class StatefulWrapper extends StatefulWidget {
  final Future<void> Function(BuildContext context)? onInit;
  final Future<void> Function()? onDispose;
  final Widget child;

  const StatefulWrapper({
    super.key,
    this.onInit,
    this.onDispose,
    required this.child,
  });

  @override
  State<StatefulWrapper> createState() => _StatefulWrapperState();
}

class _StatefulWrapperState extends State<StatefulWrapper> {
  Future _initFuture = Future.value();

  @override
  void initState() {
    if (widget.onInit != null) {
      _initFuture = widget.onInit!(context);
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: _initFuture,
      builder: (context, AsyncSnapshot<void> snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          return widget.child;
        } else {
          return const Center(
            child: Column(
              children: <Widget>[
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(),
                ),
              ],
            ),
          );
        }
      },
    );
  }

  @override
  void dispose() {
    if (widget.onDispose != null) {
      widget.onDispose!();
    }
    super.dispose();
  }
}
