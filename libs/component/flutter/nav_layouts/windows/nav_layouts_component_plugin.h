#ifndef FLUTTER_PLUGIN_NAV_LAYOUTS_COMPONENT_PLUGIN_H_
#define FLUTTER_PLUGIN_NAV_LAYOUTS_COMPONENT_PLUGIN_H_

#include <flutter/method_channel.h>
#include <flutter/plugin_registrar_windows.h>

#include <memory>

namespace nav_layouts_component {

class NavLayoutsComponentPlugin : public flutter::Plugin {
 public:
  static void RegisterWithRegistrar(flutter::PluginRegistrarWindows *registrar);

  NavLayoutsComponentPlugin();

  virtual ~NavLayoutsComponentPlugin();

  // Disallow copy and assign.
  NavLayoutsComponentPlugin(const NavLayoutsComponentPlugin&) = delete;
  NavLayoutsComponentPlugin& operator=(const NavLayoutsComponentPlugin&) = delete;

  // Called when a method is called on this plugin's channel from Dart.
  void HandleMethodCall(
      const flutter::MethodCall<flutter::EncodableValue> &method_call,
      std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result);
};

}  // namespace nav_layouts_component

#endif  // FLUTTER_PLUGIN_NAV_LAYOUTS_COMPONENT_PLUGIN_H_
