{"name": "identity_service", "$schema": "../../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/service/flutter/identity/src", "projectType": "library", "targets": {"analyze": {"executor": "nx:run-commands", "options": {"command": "flutter analyze", "cwd": "libs/shared/service/flutter/identity"}}, "clean": {"executor": "nx:run-commands", "options": {"command": "flutter clean", "cwd": "libs/shared/service/flutter/identity"}}, "format": {"executor": "nx:run-commands", "options": {"command": "dart format libs/shared/service/flutter/identity/*", "cwd": "libs/shared/service/flutter/identity"}}, "test": {"executor": "nx:run-commands", "options": {"command": "flutter test", "cwd": "libs/shared/service/flutter/identity"}}, "doctor": {"executor": "nx:run-commands", "options": {"command": "flutter doctor", "cwd": "libs/shared/service/flutter/identity"}}, "build-aar": {"executor": "nx:run-commands", "options": {"command": "flutter build aar", "cwd": "libs/shared/service/flutter/identity"}, "outputs": ["{workspaceRoot}/libs/shared/service/flutter/identity/build"]}, "build-apk": {"executor": "nx:run-commands", "options": {"command": "flutter build apk", "cwd": "libs/shared/service/flutter/identity"}, "outputs": ["{workspaceRoot}/libs/shared/service/flutter/identity/build"]}, "build-appbundle": {"executor": "nx:run-commands", "options": {"command": "flutter build appbundle", "cwd": "libs/shared/service/flutter/identity"}, "outputs": ["{workspaceRoot}/libs/shared/service/flutter/identity/build"]}, "build-bundle": {"executor": "nx:run-commands", "options": {"command": "flutter build bundle", "cwd": "libs/shared/service/flutter/identity"}, "outputs": ["{workspaceRoot}/libs/shared/service/flutter/identity/build"]}, "build-ios": {"executor": "nx:run-commands", "options": {"command": "flutter build ios", "cwd": "libs/shared/service/flutter/identity"}, "outputs": ["{workspaceRoot}/libs/shared/service/flutter/identity/build"]}, "build-ios-framework": {"executor": "nx:run-commands", "options": {"command": "flutter build ios-framework", "cwd": "libs/shared/service/flutter/identity"}, "outputs": ["{workspaceRoot}/libs/shared/service/flutter/identity/build"]}, "build-ipa": {"executor": "nx:run-commands", "options": {"command": "flutter build ipa", "cwd": "libs/shared/service/flutter/identity"}, "outputs": ["{workspaceRoot}/libs/shared/service/flutter/identity/build"]}}, "tags": []}