import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'identity_localizations_en.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of IdentityLocalizations
/// returned by `IdentityLocalizations.of(context)`.
///
/// Applications need to include `IdentityLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/identity_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: IdentityLocalizations.localizationsDelegates,
///   supportedLocales: IdentityLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the IdentityLocalizations.supportedLocales
/// property.
abstract class IdentityLocalizations {
  IdentityLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static IdentityLocalizations of(BuildContext context) {
    return Localizations.of<IdentityLocalizations>(
      context,
      IdentityLocalizations,
    )!;
  }

  static const LocalizationsDelegate<IdentityLocalizations> delegate =
      _IdentityLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[Locale('en')];

  /// No description provided for @userNameExists.
  ///
  /// In en, this message translates to:
  /// **'A user with that username already exists!'**
  String get userNameExists;

  /// No description provided for @signUpError.
  ///
  /// In en, this message translates to:
  /// **'There was an error signing up: {error}'**
  String signUpError(String error);

  /// No description provided for @resendSignUpCodeLimit.
  ///
  /// In en, this message translates to:
  /// **'You have exceeded the maximum number of requests for a new sign up code. Please try again later.'**
  String get resendSignUpCodeLimit;

  /// No description provided for @resendSignUpCodeError.
  ///
  /// In en, this message translates to:
  /// **'There was an error requesting for a new sign up code: {error}'**
  String resendSignUpCodeError(String error);

  /// No description provided for @unableToConfirmSignUp.
  ///
  /// In en, this message translates to:
  /// **'Unable to confirm sign-up. Please contact support.'**
  String get unableToConfirmSignUp;

  /// No description provided for @invalidVerificationCode.
  ///
  /// In en, this message translates to:
  /// **'Invalid verification code provided, please try again.'**
  String get invalidVerificationCode;

  /// No description provided for @expiredVerificationCode.
  ///
  /// In en, this message translates to:
  /// **'Expired verification code provided, please request a new one and try again.'**
  String get expiredVerificationCode;

  /// No description provided for @confirmSignUpError.
  ///
  /// In en, this message translates to:
  /// **'There was an error confirming sign up: {error}'**
  String confirmSignUpError(String error);

  /// No description provided for @resetPasswordLimit.
  ///
  /// In en, this message translates to:
  /// **'You have exceeded the maximum number of requests for a password reset. Please try again later.'**
  String get resetPasswordLimit;

  /// No description provided for @resetPasswordError.
  ///
  /// In en, this message translates to:
  /// **'There was an error resetting your password: {error}'**
  String resetPasswordError(String error);

  /// No description provided for @updatePasswordError.
  ///
  /// In en, this message translates to:
  /// **'There was an error updating your password: {error}'**
  String updatePasswordError(String error);

  /// No description provided for @sessionValidationFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to validate session!'**
  String get sessionValidationFailed;

  /// No description provided for @signInNotAllowed.
  ///
  /// In en, this message translates to:
  /// **'Login failed. Invalid username or password.'**
  String get signInNotAllowed;

  /// No description provided for @signInError.
  ///
  /// In en, this message translates to:
  /// **'There was an error signing in: {error}'**
  String signInError(String error);

  /// No description provided for @invalidMFACode.
  ///
  /// In en, this message translates to:
  /// **'Invalid MFA code provided, please try again.'**
  String get invalidMFACode;

  /// No description provided for @mfaCodeValidationError.
  ///
  /// In en, this message translates to:
  /// **'There was an error validating the MFA code provided: {error}'**
  String mfaCodeValidationError(String error);

  /// No description provided for @signOutError.
  ///
  /// In en, this message translates to:
  /// **'There was an error signing out: {error}'**
  String signOutError(String error);

  /// No description provided for @sendVerificationCodeError.
  ///
  /// In en, this message translates to:
  /// **'There was an error sending a verification code: {error}'**
  String sendVerificationCodeError(String error);

  /// No description provided for @confirmUserAttributeError.
  ///
  /// In en, this message translates to:
  /// **'There was an error confirming user attribute: {error}'**
  String confirmUserAttributeError(String error);

  /// No description provided for @totpSetupError.
  ///
  /// In en, this message translates to:
  /// **'There was an error setting up TOTP: {error}'**
  String totpSetupError(String error);

  /// No description provided for @totpVerifyError.
  ///
  /// In en, this message translates to:
  /// **'There was an error verifying TOTP: {error}'**
  String totpVerifyError(String error);

  /// No description provided for @saveUserError.
  ///
  /// In en, this message translates to:
  /// **'There was an error saving user: {error}'**
  String saveUserError(String error);

  /// No description provided for @readUserError.
  ///
  /// In en, this message translates to:
  /// **'Failed to read details of logged in user!'**
  String get readUserError;

  /// No description provided for @unknownError.
  ///
  /// In en, this message translates to:
  /// **'An unknown error occurred. Please contact support.'**
  String get unknownError;
}

class _IdentityLocalizationsDelegate
    extends LocalizationsDelegate<IdentityLocalizations> {
  const _IdentityLocalizationsDelegate();

  @override
  Future<IdentityLocalizations> load(Locale locale) {
    return SynchronousFuture<IdentityLocalizations>(
      lookupIdentityLocalizations(locale),
    );
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en'].contains(locale.languageCode);

  @override
  bool shouldReload(_IdentityLocalizationsDelegate old) => false;
}

IdentityLocalizations lookupIdentityLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return IdentityLocalizationsEn();
  }

  throw FlutterError(
    'IdentityLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
