{"@@locale": "en", "userNameExists": "A user with that username already exists!", "signUpError": "There was an error signing up: {error}", "@signUpError": {"placeholders": {"error": {"type": "String"}}}, "resendSignUpCodeLimit": "You have exceeded the maximum number of requests for a new sign up code. Please try again later.", "resendSignUpCodeError": "There was an error requesting for a new sign up code: {error}", "@resendSignUpCodeError": {"placeholders": {"error": {"type": "String"}}}, "unableToConfirmSignUp": "Unable to confirm sign-up. Please contact support.", "invalidVerificationCode": "Invalid verification code provided, please try again.", "expiredVerificationCode": "Expired verification code provided, please request a new one and try again.", "confirmSignUpError": "There was an error confirming sign up: {error}", "@confirmSignUpError": {"placeholders": {"error": {"type": "String"}}}, "resetPasswordLimit": "You have exceeded the maximum number of requests for a password reset. Please try again later.", "resetPasswordError": "There was an error resetting your password: {error}", "@resetPasswordError": {"placeholders": {"error": {"type": "String"}}}, "updatePasswordError": "There was an error updating your password: {error}", "@updatePasswordError": {"placeholders": {"error": {"type": "String"}}}, "sessionValidationFailed": "Failed to validate session!", "signInNotAllowed": "<PERSON><PERSON> failed. Invalid username or password.", "signInError": "There was an error signing in: {error}", "@signInError": {"placeholders": {"error": {"type": "String"}}}, "invalidMFACode": "Invalid MFA code provided, please try again.", "mfaCodeValidationError": "There was an error validating the MFA code provided: {error}", "@mfaCodeValidationError": {"placeholders": {"error": {"type": "String"}}}, "signOutError": "There was an error signing out: {error}", "@signOutError": {"placeholders": {"error": {"type": "String"}}}, "sendVerificationCodeError": "There was an error sending a verification code: {error}", "@sendVerificationCodeError": {"placeholders": {"error": {"type": "String"}}}, "confirmUserAttributeError": "There was an error confirming user attribute: {error}", "@confirmUserAttributeError": {"placeholders": {"error": {"type": "String"}}}, "totpSetupError": "There was an error setting up TOTP: {error}", "@totpSetupError": {"placeholders": {"error": {"type": "String"}}}, "totpVerifyError": "There was an error verifying TOTP: {error}", "@totpVerifyError": {"placeholders": {"error": {"type": "String"}}}, "saveUserError": "There was an error saving user: {error}", "@saveUserError": {"placeholders": {"error": {"type": "String"}}}, "readUserError": "Failed to read details of logged in user!", "unknownError": "An unknown error occurred. Please contact support."}