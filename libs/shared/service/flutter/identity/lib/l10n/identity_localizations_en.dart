// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'identity_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class IdentityLocalizationsEn extends IdentityLocalizations {
  IdentityLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get userNameExists => 'A user with that username already exists!';

  @override
  String signUpError(String error) {
    return 'There was an error signing up: $error';
  }

  @override
  String get resendSignUpCodeLimit =>
      'You have exceeded the maximum number of requests for a new sign up code. Please try again later.';

  @override
  String resendSignUpCodeError(String error) {
    return 'There was an error requesting for a new sign up code: $error';
  }

  @override
  String get unableToConfirmSignUp =>
      'Unable to confirm sign-up. Please contact support.';

  @override
  String get invalidVerificationCode =>
      'Invalid verification code provided, please try again.';

  @override
  String get expiredVerificationCode =>
      'Expired verification code provided, please request a new one and try again.';

  @override
  String confirmSignUpError(String error) {
    return 'There was an error confirming sign up: $error';
  }

  @override
  String get resetPasswordLimit =>
      'You have exceeded the maximum number of requests for a password reset. Please try again later.';

  @override
  String resetPasswordError(String error) {
    return 'There was an error resetting your password: $error';
  }

  @override
  String updatePasswordError(String error) {
    return 'There was an error updating your password: $error';
  }

  @override
  String get sessionValidationFailed => 'Failed to validate session!';

  @override
  String get signInNotAllowed => 'Login failed. Invalid username or password.';

  @override
  String signInError(String error) {
    return 'There was an error signing in: $error';
  }

  @override
  String get invalidMFACode => 'Invalid MFA code provided, please try again.';

  @override
  String mfaCodeValidationError(String error) {
    return 'There was an error validating the MFA code provided: $error';
  }

  @override
  String signOutError(String error) {
    return 'There was an error signing out: $error';
  }

  @override
  String sendVerificationCodeError(String error) {
    return 'There was an error sending a verification code: $error';
  }

  @override
  String confirmUserAttributeError(String error) {
    return 'There was an error confirming user attribute: $error';
  }

  @override
  String totpSetupError(String error) {
    return 'There was an error setting up TOTP: $error';
  }

  @override
  String totpVerifyError(String error) {
    return 'There was an error verifying TOTP: $error';
  }

  @override
  String saveUserError(String error) {
    return 'There was an error saving user: $error';
  }

  @override
  String get readUserError => 'Failed to read details of logged in user!';

  @override
  String get unknownError =>
      'An unknown error occurred. Please contact support.';
}
