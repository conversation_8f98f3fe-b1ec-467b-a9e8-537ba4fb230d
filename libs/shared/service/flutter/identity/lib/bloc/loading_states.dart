import 'package:app_framework_component/app_framework.dart';

/// Sign-up loading state
const signUpLoading = LoadingState(
  'Sign-up in progress',
);

/// Sign-up confirmation loading state
const confirmSignUpLoading = LoadingState(
  'Confirm sign-up in progress',
);

/// Resend sign-up code loading state
const resendSignUpCodeLoading = LoadingState(
  'Resend sign-up code in progress',
);

/// Reset password loading state
const resetPasswordLoading = LoadingState(
  'Reset password in progress',
);

/// Update password loading state
const updatePasswordLoading = LoadingState(
  'Update password in progress',
);

/// Login loading state
const loginLoading = LoadingState(
  'Login in progress',
);

/// Validate MFA loading state
const validateMFALoading = LoadingState(
  'Validate MFA in progress',
);

/// Initiate User attribute verification loading state
const sendVerificationCodeLoading = LoadingState(
  'Send user attribute verification code in progress',
);

/// Confirm User attribute verification code loading state
const confirmVerificationCodeLoading = LoadingState(
  'Confirm user attribute verification code in progress',
);

/// TOTP setup loading state
const totpSetupLoading = LoadingState(
  'TOTP setup in progress',
);

/// TOTP verification loading state
const verifyTOTPLoading = LoadingState(
  'TOTP verification in progress',
);

/// Save user loading state
const saveUserLoading = LoadingState(
  'Saving logged-in user in progress',
);
