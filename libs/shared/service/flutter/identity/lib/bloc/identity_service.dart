import 'dart:async';
import 'package:flutter/material.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:logging/logging.dart' as logging;
import 'package:flutter_gravatar/flutter_gravatar.dart';

import 'package:identity_service/provider/identity_provider.dart';
import 'package:identity_service/provider/provider_events.dart';
import 'package:identity_service/provider/exceptions.dart';
import 'package:identity_service/model/user.dart';
import 'package:identity_service/model/verification.dart';

import 'package:app_framework_component/app_framework.dart';

import '../l10n/identity_localizations.dart';
import 'identity_state.dart';
import 'loading_states.dart';

class IdentityService extends Service<IdentityState, IdentityProvider>
    with HydratedMixin {
  final logging.Logger _logger = logging.Logger('IdentityService');

  // Localizations for the identity service
  IdentityLocalizations _localizations = lookupIdentityLocalizations(
    const Locale('en'),
  );

  late final StreamSubscription<ProviderEvent> _providerEventSubscription;

  final List<SignOutHook> _signOutHooks = [];

  IdentityService(
    IdentityProvider provider,
  ) : super(
          initialState: const IdentityState(),
          provider: provider,
        ) {
    // Hydrate the saved state
    hydrate();
    // Validate the current session and if it
    // is valid then check if hydrated session
    // and auth provider session are in sync
    provider.validateSession().then(
      (isValid) {
        if (isValid) {
          provider.getLoggedInUsername().then(
            (username) {
              if (!state.isLoggedIn ||
                  (state.isLoggedIn && username != state.user.username)) {
                _logger.info(
                  'Session state username does not match provider\'s '
                  'logged in username "$username". Signing out user '
                  'and clearing state.',
                );
                provider.signOut();
              }
            },
          );
        } else if (state.isLoggedIn) {
          _logger.info(
            'Session state is logged in but the provider\'s '
            'session was not valid. Clearing state',
          );
          emit(const IdentityState());
        }
      },
    );

    // Listen to events from the provider
    // and update the state accordingly
    _providerEventSubscription = provider.providerEventStream.listen((event) {
      _handleEventsFromProvider(event);
    });
  }

  void _handleEventsFromProvider(
    ProviderEvent event,
  ) {
    _logger.fine('Recieved provider event: $event');
    switch (event) {
      case final LoggedOutEvent _:
        // reset user in state
        emit(
          state.copyWith(
            user: const User(),
            resetToken: true,
          ),
        );
        // call all sign out hooks
        for (final hook in _signOutHooks) {
          hook();
        }
        break;

      case final TokenRefreshEvent _:
        emit(
          state.copyWith(
            accessToken: event.accessToken,
            defaultOrgId: event.defaultOrgId,
            orgMemberships: event.orgMemberships,
            defaultSpaceId: event.defaultSpaceId,
            spaceMemberships: event.spaceMemberships,
          ),
        );
        break;

      case final UserEventType _:
        emit(
          state.copyWith(
            user: event.user,
          ),
        );
        break;

      case final ErrorEventType e:
        if (e is ReadUserErrorEvent) {
          _logger.severe('Error reading user: ${e.error}');
          emit(
            state.addMessage(
              Message.error(_localizations.readUserError),
            ),
          );
        } else {
          _logger.severe('Recieved an error event: $e');
          emit(
            state.addMessage(
              Message.error(_localizations.unknownError),
            ),
          );
        }
        break;

      default:
        _logger.warning('Ignoring unknown event: $event');
    }
  }

  @override
  void setCurrentContext(BuildContext context) {
    // Update the localizations for the identity
    // service from the given [context]
    _localizations = IdentityLocalizations.of(context);
  }

  @override
  Future<void> close() async {
    await _providerEventSubscription.cancel();
    return super.close();
  }

  /// Registers a sign out hook that will be called
  /// when the user is signed out
  void registerSignOutHook(SignOutHook hook) {
    _signOutHooks.add(hook);
  }

  /// Unregisters a sign out hook
  void unregisterSignOutHook(SignOutHook hook) {
    _signOutHooks.remove(hook);
  }

  /// Updates the session timeout
  void touchSession() {
    if (state.isLoggedIn) {
      emit(state.copyWith(
        session: state.session.touch(),
      ));
    }
  }

  /// Updates user instance in state
  void updateUserInState(User user) {
    emit(state.copyWith(
      user: user,
    ));
  }

  /// Clear verification state
  Future<void> clearAwaitingVerification() async {
    if (state.awaitingVerification != null) {
      emit(state.copyWith(
        resetAwaitingVerification: true,
      ));
      // wait for the state to be updated
      await stream.firstWhere(
        (state) => state.awaitingVerification == null,
      );
    }
  }

  /// Signs in the user with the given [username] and [password]
  Future<void> signUp(
    User user,
    String password,
  ) async {
    emit(state.startLoading(signUpLoading));

    try {
      user = setProfilePictureUrl(user);
      final verification = await provider.signUp(user, password);
      emit(state.copyWith(
        awaitingVerification: verification,
      ));
    } on UsernameExistsException catch (error, stackTrace) {
      emit(state.addMessage(
        Message.error(_localizations.userNameExists),
        error,
        stackTrace,
      ));
    } on SignUpException catch (error, stackTrace) {
      emit(state.addMessage(
        Message.error(
          _localizations.signUpError(error.message),
        ),
        error,
        stackTrace,
      ));
    } finally {
      emit(state.endLoading(signUpLoading));
    }
  }

  /// Resend sign-up code to the given [username] for verification
  Future<void> resendSignUpCode(
    String username,
  ) async {
    emit(state.startLoading(resendSignUpCodeLoading));

    try {
      final verification = await provider.resendSignUpCode(username);
      emit(state.copyWith(
        awaitingVerification: verification,
      ));
    } on ResendSignUpCodeLimitException catch (error, stackTrace) {
      emit(state.addMessage(
        Message.error(_localizations.resendSignUpCodeLimit),
        error,
        stackTrace,
      ));
    } on ResendSignUpCodeException catch (error, stackTrace) {
      emit(state.addMessage(
        Message.error(
          _localizations.resendSignUpCodeError(error.message),
        ),
        error,
        stackTrace,
      ));
    } finally {
      emit(state.endLoading(resendSignUpCodeLoading));
    }
  }

  /// Confirm the registration of a particular [username] with the
  /// given [code]
  ///
  /// Returns [bool] indicating if the registration was successful
  Future<void> confirmSignUpCode(
    String username,
    String code,
  ) async {
    emit(state.startLoading(confirmSignUpLoading));

    try {
      final isConfirmed = await provider.confirmSignUpCode(username, code);
      if (isConfirmed) {
        if (state.awaitingVerification?.type == VerificationType.email) {
          emit(state.copyWith(
            user: state.user.copyWith(
              emailAddressVerified: true,
            ),
            resetAwaitingVerification: true,
          ));
        } else if (state.awaitingVerification?.type == VerificationType.sms) {
          emit(state.copyWith(
            user: state.user.copyWith(
              mobilePhoneVerified: true,
            ),
            resetAwaitingVerification: true,
          ));
        } else {
          emit(state.copyWith(
            resetAwaitingLogin: true,
            resetAwaitingVerification: true,
          ));
        }
      } else {
        emit(
          state
              .addMessage(
                Message.alert(_localizations.unableToConfirmSignUp),
              )
              .copyWith(
                resetAwaitingVerification: true,
              ),
        );
      }
    } on InvalidCodeException catch (error, stackTrace) {
      emit(state.addMessage(
        Message.error(_localizations.invalidVerificationCode),
        error,
        stackTrace,
      ));
    } on ExpiredCodeException catch (error, stackTrace) {
      emit(state.addMessage(
        Message.error(_localizations.expiredVerificationCode),
        error,
        stackTrace,
      ));
    } on ConfirmSignUpCodeException catch (error, stackTrace) {
      emit(state.addMessage(
        Message.error(
          _localizations.confirmSignUpError(error.message),
        ),
        error,
        stackTrace,
      ));
    } finally {
      emit(state.endLoading(confirmSignUpLoading));
    }
  }

  /// Initiates a password reset flow for the given [username]
  Future<void> resetPassword(
    String username,
  ) async {
    emit(state.startLoading(resetPasswordLoading));

    try {
      final verificationCode = await provider.resetPassword(username);
      emit(state.copyWith(
        awaitingVerification: verificationCode,
      ));
    } on ResetPasswordLimitException catch (error, stackTrace) {
      emit(state.addMessage(
        Message.error(_localizations.resetPasswordLimit),
        error,
        stackTrace,
      ));
    } on ResetPasswordException catch (error, stackTrace) {
      emit(state.addMessage(
        Message.error(
          _localizations.resetPasswordError(error.message),
        ),
        error,
        stackTrace,
      ));
    } finally {
      emit(state.endLoading(resetPasswordLoading));
    }
  }

  /// Updates the given [username]'s [password] validating the
  /// change with the given [code]
  Future<void> updatePassword(
    String username,
    String password,
    String code,
  ) async {
    emit(state.startLoading(updatePasswordLoading));

    try {
      await provider.updatePassword(username, password, code);
      emit(state.copyWith(
        resetAwaitingVerification: true,
      ));
    } on InvalidCodeException catch (error, stackTrace) {
      emit(state.addMessage(
        Message.error(_localizations.invalidVerificationCode),
        error,
        stackTrace,
      ));
    } on ExpiredCodeException catch (error, stackTrace) {
      emit(state.addMessage(
        Message.error(_localizations.expiredVerificationCode),
        error,
        stackTrace,
      ));
    } on ResetPasswordLimitException catch (error, stackTrace) {
      emit(state.addMessage(
        Message.error(_localizations.resetPasswordLimit),
        error,
        stackTrace,
      ));
    } on UpdatePasswordException catch (error, stackTrace) {
      emit(state.addMessage(
        Message.error(
          _localizations.updatePasswordError(error.message),
        ),
        error,
        stackTrace,
      ));
    } finally {
      emit(state.endLoading(updatePasswordLoading));
    }
  }

  // Checks if the current session is valid
  Future<bool> isSessionValid() async {
    return provider.validateSession();
  }

  // Validates the current session and updates timeout
  Future<void> validateSession() async {
    try {
      if (await provider.validateSession()) {
        final user = state.user;
        if (!user.isValid ||
            (user.username != await provider.getLoggedInUsername()) ||
            state.session.isTimedout(user)) {
          // if user is not valid or session
          // as timedout sign out the user
          provider.signOut();
        } else {
          // session and user in context is valid
          return;
        }
      }

      // reset the state
      emit(const IdentityState());
    } catch (error, stackTrace) {
      _logger.severe('Error validating session: ', error, stackTrace);

      emit(state.addMessage(
        Message.error(_localizations.sessionValidationFailed),
        error,
        stackTrace,
      ));
    }
  }

  /// Signs in the given [username] with the given [password]
  Future<void> signIn(
    String username,
    String password,
  ) async {
    emit(
      state
          .startLoading(loginLoading)
          .copyWith(awaitingLogin: AuthType.authInProgress),
    );

    try {
      final authType = await provider.signIn(username, password);

      final isLoggedIn = authType == AuthType.authDone;
      emit(state.copyWith(
        user: state.user.copyWith(
          username: username,
        ),
        session: isLoggedIn ? state.session.touch() : null,
        isLoggedIn: isLoggedIn,
        resetAwaitingLogin: isLoggedIn,
        // ignored if resetAwaitingLogin is true
        awaitingLogin: authType,
      ));
    } on SignInNotAllowedException catch (error, stackTrace) {
      emit(
        state
            .addMessage(
              Message.error(_localizations.signInNotAllowed),
              error,
              stackTrace,
            )
            .copyWith(resetAwaitingLogin: true),
      );
    } on SignInException catch (error, stackTrace) {
      emit(
        state
            .addMessage(
              Message.error(
                _localizations.signInError(error.message),
              ),
              error,
              stackTrace,
            )
            .copyWith(resetAwaitingLogin: true),
      );
    } finally {
      emit(state.endLoading(loginLoading));
    }
  }

  /// Validates the given multi-factor authentication [code]
  Future<void> validateMFACode(
    String code,
  ) async {
    emit(state.startLoading(validateMFALoading));

    try {
      final authType = await provider.validateMFACode(code);

      final isLoggedIn = authType == AuthType.authDone;
      emit(state.copyWith(
        session: isLoggedIn ? state.session.touch() : null,
        isLoggedIn: isLoggedIn,
        resetAwaitingLogin: isLoggedIn,
        // ignored if resetAwaitingLogin is true
        awaitingLogin: authType,
      ));
    } on InvalidCodeException catch (error, stackTrace) {
      emit(state.addMessage(
        Message.error(_localizations.invalidMFACode),
        error,
        stackTrace,
      ));
    } on ExpiredCodeException catch (error, stackTrace) {
      emit(state.addMessage(
        Message.error(_localizations.expiredVerificationCode),
        error,
        stackTrace,
      ));
    } on MFACodeValidationException catch (error, stackTrace) {
      emit(state.addMessage(
        Message.error(
          _localizations.mfaCodeValidationError(error.message),
        ),
        error,
        stackTrace,
      ));
    } finally {
      emit(state.endLoading(validateMFALoading));
    }
  }

  /// Signs out the logged in user
  Future<void> signOut() async {
    try {
      await provider.signOut();

      // reset the state
      emit(const IdentityState());
    } on SignOutException catch (error, stackTrace) {
      emit(state.addMessage(
        Message.error(
          _localizations.signOutError(error.message),
        ),
        error,
        stackTrace,
      ));
    }
  }

  /// Sends a verification code to verify the given [attribute]
  Future<void> sendVerificationCode(
    String attribute,
  ) async {
    emit(state.startLoading(sendVerificationCodeLoading));

    try {
      final verification = await provider.sendVerificationCodeForAttribute(
        attribute,
      );
      emit(state.copyWith(
        awaitingVerification: verification,
      ));
    } on VerifyUserAttributeException catch (error, stackTrace) {
      emit(state.addMessage(
        Message.error(
          _localizations.sendVerificationCodeError(error.message),
        ),
        error,
        stackTrace,
      ));
    } finally {
      emit(state.endLoading(sendVerificationCodeLoading));
    }
  }

  /// Confirms the given [attribute] with the given [code]
  Future<void> confirmVerificationCode(
    String attribute,
    String code,
  ) async {
    emit(state.startLoading(confirmVerificationCodeLoading));

    try {
      await provider.confirmVerificationCodeForAttribute(
        attribute,
        code,
      );
      emit(state.copyWith(
        resetAwaitingVerification: true,
      ));
    } on InvalidCodeException catch (error, stackTrace) {
      emit(state.addMessage(
        Message.error(_localizations.invalidVerificationCode),
        error,
        stackTrace,
      ));
    } on ExpiredCodeException catch (error, stackTrace) {
      emit(state.addMessage(
        Message.error(_localizations.expiredVerificationCode),
        error,
        stackTrace,
      ));
    } on ConfirmUserAttributeException catch (error, stackTrace) {
      emit(state.addMessage(
        Message.error(
          _localizations.confirmUserAttributeError(error.message),
        ),
        error,
        stackTrace,
      ));
    } finally {
      emit(state.endLoading(confirmVerificationCodeLoading));
    }
  }

  /// Sets up TOTP for the logged in user
  Future<void> setupTOTP({String? appName}) async {
    emit(state.startLoading(totpSetupLoading));

    try {
      final (totpSecret, totpUri) = await provider.setupTOTP(
        appName: appName,
      );
      emit(state.copyWith(
        totpSetupSecret: totpSecret,
        totpSetupUri: totpUri,
      ));
    } on TOTPSetupException catch (error, stackTrace) {
      emit(state.addMessage(
        Message.error(
          _localizations.totpSetupError(error.message),
        ),
        error,
        stackTrace,
      ));
    } finally {
      emit(state.endLoading(totpSetupLoading));
    }
  }

  /// Verifies the TOTP setup with the given [code]
  Future<void> verifyTOTP(
    String code,
  ) async {
    emit(state.startLoading(verifyTOTPLoading));

    try {
      await provider.verifyTOTP(code);
      emit(state.copyWith(
        resetTOTPSetup: true,
      ));
    } on InvalidCodeException catch (error, stackTrace) {
      emit(state.addMessage(
        Message.error(_localizations.invalidVerificationCode),
        error,
        stackTrace,
      ));
    } on ExpiredCodeException catch (error, stackTrace) {
      emit(state.addMessage(
        Message.error(_localizations.expiredVerificationCode),
        error,
        stackTrace,
      ));
    } on TOTPVerifyException catch (error, stackTrace) {
      emit(state.addMessage(
        Message.error(
          _localizations.totpVerifyError(error.message),
        ),
        error,
        stackTrace,
      ));
    } finally {
      emit(state.endLoading(verifyTOTPLoading));
    }
  }

  /// Cancels the TOTP setup
  cancelTOTPSetup() {
    emit(state.copyWith(
      resetTOTPSetup: true,
    ));
  }

  /// Saves the given [user] attributes to the backend
  Future<void> saveUser(User user) async {
    emit(state.startLoading(saveUserLoading));

    try {
      user = setProfilePictureUrl(user);
      await provider.saveUser(user);
    } on SaveUserException catch (error, stackTrace) {
      emit(state.addMessage(
        Message.error(
          _localizations.saveUserError(error.message),
        ),
        error,
        stackTrace,
      ));
    } finally {
      emit(state.endLoading(saveUserLoading));
    }
  }

  User setProfilePictureUrl(User user) {
    if (user.profilePictureUrl == null ||
        user.profilePictureUrl!.startsWith('https://www.gravatar.com')) {
      try {
        // fetch Gravatar image if profile picture is not set
        // or if it is a Gravatar url then simply update it
        // with the latest Gravatar image based on the email
        final gravatar = Gravatar(user.emailAddress);
        final profilePictureUrl = gravatar.imageUrl(
          size: 128,
          defaultImage: '404',
        );
        return user.copyWith(
          profilePictureUrl: profilePictureUrl,
        );
      } catch (e) {
        _logger.warning(
          'Failed to fetch Gravatar image for user '
          'with email ${user.emailAddress}: $e',
        );
      }
    }
    return user;
  }

  @override
  IdentityState? fromJson(Map<String, dynamic> json) {
    return const IdentityState().copyWith(
      isLoggedIn: json['isLoggedIn'] ?? false,
      user: json.containsKey('user') ? User.fromJson(json['user']) : null,
    );
  }

  @override
  Map<String, dynamic>? toJson(IdentityState state) {
    return {
      'isLoggedIn': state.isLoggedIn,
      'user': state.user.toJson(),
    };
  }
}

typedef SignOutHook = void Function();
