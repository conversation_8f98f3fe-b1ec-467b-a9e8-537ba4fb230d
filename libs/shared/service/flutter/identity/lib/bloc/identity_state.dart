import 'package:app_framework_component/app_framework.dart';

import '../bloc/loading_states.dart';
import '../provider/identity_provider.dart';
import '../model/session.dart';
import '../model/user.dart';
import '../model/verification.dart';

class IdentityState extends State<IdentityState> {
  final Session session;

  final bool isLoggedIn;
  final User user;

  final String? accessToken;

  final String? defaultOrgId;
  final List<String>? orgMemberships;

  final String? defaultSpaceId;
  final List<String>? spaceMemberships;

  final Verification? awaitingVerification;

  final AuthType? awaitingLogin;

  final String? totpSetupSecret;
  final Uri? totpSetupUri;

  bool get isLoginInProgress =>
      awaitingLogin != null && awaitingLogin != AuthType.authDone;
  bool get isSignUpInProgress =>
      !isLoading([signUpLoading, resendSignUpCodeLoading]) &&
      awaitingVerification != null &&
      awaitingVerification!.flow == VerificationFlow.signUp;
  bool get isAttribVerificationProgress =>
      !isLoading([sendVerificationCodeLoading]) &&
      awaitingVerification != null &&
      awaitingVerification!.flow == VerificationFlow.verifyAttribute;
  bool get needsSignUpConfirmation =>
      awaitingLogin == AuthType.needsSignUpConfirmation;

  bool get isPasswordResetInitiated =>
      isLoading([resetPasswordLoading]) &&
      awaitingVerification != null &&
      awaitingVerification!.flow == VerificationFlow.resetPassword;
  bool get isPasswordResetInProgress =>
      !isLoading([resetPasswordLoading]) &&
      awaitingVerification != null &&
      awaitingVerification!.flow == VerificationFlow.resetPassword;

  bool get isTOTPSetupInitiated =>
      !isLoading([totpSetupLoading]) && totpSetupSecret != null;
  bool get isTOTPSetupInProgress => totpSetupSecret != null;

  const IdentityState({
    super.loadingStates = const {},
    super.messages = const {},
    this.session = const Session(),
    this.isLoggedIn = false,
    this.user = const User(),
    this.accessToken,
    this.defaultOrgId,
    this.orgMemberships,
    this.defaultSpaceId,
    this.spaceMemberships,
    this.awaitingVerification,
    this.awaitingLogin,
    this.totpSetupSecret,
    this.totpSetupUri,
  });

  @override
  IdentityState copyWith({
    Set<LoadingState>? loadingStates,
    Set<Message>? messages,
    Session? session,
    bool? isLoggedIn,
    User? user,
    String? accessToken,
    String? defaultOrgId,
    List<String>? orgMemberships,
    String? defaultSpaceId,
    List<String>? spaceMemberships,
    resetToken = false,
    Verification? awaitingVerification,
    resetAwaitingVerification = false,
    AuthType? awaitingLogin,
    resetAwaitingLogin = false,
    String? totpSetupSecret,
    Uri? totpSetupUri,
    resetTOTPSetup = false,
  }) {
    return IdentityState(
      loadingStates: loadingStates ?? super.loadingStates,
      messages: messages ?? super.messages,
      session: session ?? this.session,
      isLoggedIn: isLoggedIn ?? this.isLoggedIn,
      user: user ?? this.user,
      accessToken: resetToken //
          ? null
          : accessToken ?? this.accessToken,
      defaultOrgId: resetToken //
          ? null
          : defaultOrgId ?? this.defaultOrgId,
      orgMemberships: resetToken //
          ? null
          : orgMemberships ?? this.orgMemberships,
      defaultSpaceId: resetToken //
          ? null
          : defaultSpaceId ?? this.defaultSpaceId,
      spaceMemberships: resetToken //
          ? null
          : spaceMemberships ?? this.spaceMemberships,
      awaitingVerification: //
          resetAwaitingVerification //
              ? null
              : awaitingVerification ?? this.awaitingVerification,
      awaitingLogin: //
          resetAwaitingLogin //
              ? null
              : awaitingLogin ?? this.awaitingLogin,
      totpSetupSecret: //
          resetTOTPSetup //
              ? null
              : totpSetupSecret ?? this.totpSetupSecret,
      totpSetupUri: //
          resetTOTPSetup //
              ? null
              : totpSetupUri ?? this.totpSetupUri,
    );
  }

  @override
  List<Object?> get additionalProps => [
        session,
        isLoggedIn,
        user,
        accessToken,
        defaultOrgId,
        orgMemberships,
        defaultSpaceId,
        spaceMemberships,
        awaitingVerification,
        awaitingLogin,
        totpSetupSecret,
        totpSetupUri,
      ];
}
