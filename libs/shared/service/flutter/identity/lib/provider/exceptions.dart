import 'package:flutter/foundation.dart';

import 'package:utilities_ab/error/app_exception.dart';

class SignUpException extends AppException {
  SignUpException({
    required String super.message,
    super.innerException,
    super.innerStackTrace,
    bool log = kDebugMode,
  }) : super(
          stackTrace: StackTrace.current,
        );
}

class UsernameExistsException extends SignUpException {
  UsernameExistsException({
    required super.message,
    super.innerException,
    super.innerStackTrace,
    super.log,
  });
}

class ResendSignUpCodeException extends AppException {
  ResendSignUpCodeException({
    required String super.message,
    super.innerException,
    super.innerStackTrace,
    bool log = kDebugMode,
  }) : super(
          stackTrace: StackTrace.current,
        );
}

class ResendSignUpCodeLimitException extends ResendSignUpCodeException {
  ResendSignUpCodeLimitException({
    required super.message,
    super.innerException,
    super.innerStackTrace,
    bool log = kDebugMode,
  });
}

class ConfirmSignUpCodeException extends AppException {
  ConfirmSignUpCodeException({
    required String super.message,
    super.innerException,
    super.innerStackTrace,
    bool log = kDebugMode,
  }) : super(
          stackTrace: StackTrace.current,
        );
}

class InvalidCodeException extends AppException {
  InvalidCodeException({
    required String super.message,
    super.innerException,
    super.innerStackTrace,
    bool log = kDebugMode,
  }) : super(
          stackTrace: StackTrace.current,
        );
}

class ExpiredCodeException extends AppException {
  ExpiredCodeException({
    required String super.message,
    super.innerException,
    super.innerStackTrace,
    bool log = kDebugMode,
  }) : super(
          stackTrace: StackTrace.current,
        );
}

class ResetPasswordException extends AppException {
  ResetPasswordException({
    required String super.message,
    super.innerException,
    super.innerStackTrace,
    bool log = kDebugMode,
  }) : super(
          stackTrace: StackTrace.current,
        );
}

class ResetPasswordLimitException extends ResetPasswordException {
  ResetPasswordLimitException({
    required super.message,
    super.innerException,
    super.innerStackTrace,
    bool log = kDebugMode,
  });
}

class UpdatePasswordException extends AppException {
  UpdatePasswordException({
    required String super.message,
    super.innerException,
    super.innerStackTrace,
    bool log = kDebugMode,
  }) : super(
          stackTrace: StackTrace.current,
        );
}

class SignInException extends AppException {
  SignInException({
    required String super.message,
    super.innerException,
    super.innerStackTrace,
    bool log = kDebugMode,
  }) : super(
          stackTrace: StackTrace.current,
        );
}

class SignInNotAllowedException extends SignInException {
  SignInNotAllowedException({
    required super.message,
    super.innerException,
    super.innerStackTrace,
    bool log = kDebugMode,
  });
}

class MFACodeValidationException extends AppException {
  MFACodeValidationException({
    required String super.message,
    super.innerException,
    super.innerStackTrace,
    bool log = kDebugMode,
  }) : super(
          stackTrace: StackTrace.current,
        );
}

class SignOutException extends AppException {
  SignOutException({
    required String super.message,
    super.innerException,
    super.innerStackTrace,
    bool log = kDebugMode,
  }) : super(
          stackTrace: StackTrace.current,
        );
}

class VerifyUserAttributeException extends AppException {
  VerifyUserAttributeException({
    required String super.message,
    super.innerException,
    super.innerStackTrace,
    bool log = kDebugMode,
  }) : super(
          stackTrace: StackTrace.current,
        );
}

class ConfirmUserAttributeException extends AppException {
  ConfirmUserAttributeException({
    required String super.message,
    super.innerException,
    super.innerStackTrace,
    bool log = kDebugMode,
  }) : super(
          stackTrace: StackTrace.current,
        );
}

class TOTPSetupException extends AppException {
  TOTPSetupException({
    required String super.message,
    super.innerException,
    super.innerStackTrace,
    bool log = kDebugMode,
  }) : super(
          stackTrace: StackTrace.current,
        );
}

class TOTPVerifyException extends AppException {
  TOTPVerifyException({
    required String super.message,
    super.innerException,
    super.innerStackTrace,
    bool log = kDebugMode,
  }) : super(
          stackTrace: StackTrace.current,
        );
}

class MFAConfigurationException extends AppException {
  MFAConfigurationException({
    required String super.message,
    super.innerException,
    super.innerStackTrace,
    bool log = kDebugMode,
  }) : super(
          stackTrace: StackTrace.current,
        );
}

class SaveUserException extends AppException {
  SaveUserException({
    required String super.message,
    super.innerException,
    super.innerStackTrace,
    bool log = kDebugMode,
  }) : super(
          stackTrace: StackTrace.current,
        );
}

class ReadUserException extends AppException {
  ReadUserException({
    required String super.message,
    super.innerException,
    super.innerStackTrace,
    bool log = kDebugMode,
  }) : super(
          stackTrace: StackTrace.current,
        );
}

class AuthTokenException extends AppException {
  AuthTokenException({
    required String super.message,
    super.innerException,
    super.innerStackTrace,
    bool log = kDebugMode,
  }) : super(
          stackTrace: StackTrace.current,
        );
}
