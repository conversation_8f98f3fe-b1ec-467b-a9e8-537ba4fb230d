import 'dart:async';
import 'dart:convert';
import 'package:amplify_flutter/amplify_flutter.dart' hide AuthSession;
import 'package:amplify_auth_cognito/amplify_auth_cognito.dart' as aws_cognito;
import 'package:logging/logging.dart' as logging;
import 'package:dio/dio.dart' as dio;

import 'package:utilities_ab/utilities.dart';
import 'package:app_framework_component/app_framework.dart';

import '../model/user.dart';
import '../model/verification.dart';

import 'identity_provider.dart';
import 'provider_events.dart';
import 'exceptions.dart';

final class AWSCognitoService extends AuthSession implements IdentityProvider {
  final logging.Logger _logger = logging.Logger('AWSProvider');

  // AWSCognitoService singleton instance
  static AWSCognitoService? _instance;

  // Amplify Auth category abstraction
  final AuthCategory _amplifyAuth;

  // The registered [AmplifyAuthCognito] plugin.
  late final aws_cognito.AmplifyAuthCognito _cognitoPlugin;

  // Streams the logged in [User] when the user logs in. When
  // the user logs out the stream will emit an invalid User
  // object that can be checked by calling User.isInvalid.
  final _authEventStream = StreamController<ProviderEvent>.broadcast();

  @override
  Stream<ProviderEvent> get providerEventStream => _authEventStream.stream;

  @override
  String? get accessToken => _accessToken;
  String? _accessToken;

  @override
  String? get userId {
    final jwt = jwtToken;
    return jwt?.payload['custom:userID'];
  }

  @override
  dio.Interceptor get authInterceptor => dio.InterceptorsWrapper(
        onRequest: (
          dio.RequestOptions options,
          dio.RequestInterceptorHandler handler,
        ) async {
          try {
            // Fetch the current auth session
            final session = await _cognitoPlugin.fetchAuthSession();
            if (session.isSignedIn) {
              options.headers['Authorization'] = 'Bearer '
                  '${session.userPoolTokensResult.value.accessToken.raw}';
            }

            return handler.next(options);
          } catch (error, stackTrace) {
            _logger.severe(
              'Failed to fetch AWS Cognito JWT for request: ${options.uri}',
              error,
              stackTrace,
            );
            signOut();

            throw AuthTokenException(
              message: 'Failed to fetch AWS Cognito JWT for request',
              innerException: error is Exception ? error : Exception(error),
              innerStackTrace: stackTrace,
            );
          }
        },
      );

  /// Factory constructor for [AWSCognitoService]
  factory AWSCognitoService({AuthCategory? auth}) {
    _instance = _instance ?? AWSCognitoService._(auth: auth);
    return _instance!;
  }

  /// Private constructor for [AWSCognitoService]
  AWSCognitoService._({AuthCategory? auth})
      : _amplifyAuth = auth ?? Amplify.Auth {
    _cognitoPlugin = _amplifyAuth.getPlugin(
      aws_cognito.AmplifyAuthCognito.pluginKey,
    );
  }

  @override
  Future<Verification> signUp(
    User user,
    String password,
  ) async {
    final completer = Completer<Verification>();

    _amplifyAuth
        .signUp(
      username: user.username,
      password: password,
      options: aws_cognito.SignUpOptions(
        userAttributes: {
          AuthUserAttributeKey.email: user.emailAddress,
          AuthUserAttributeKey.phoneNumber: user.mobilePhone,
        },
      ),
    )
        .then(
      (result) {
        _logger.fine('Successful sign up: $result');

        final nextStep = result.nextStep;
        switch (nextStep.signUpStep) {
          case aws_cognito.AuthSignUpStep.confirmSignUp:
            _enqueueEvent(SignedUpEvent(user));
            completer.complete(_createVerificationResult(
              VerificationFlow.signUp,
              nextStep.codeDeliveryDetails,
            ));
            break;

          case aws_cognito.AuthSignUpStep.done:
            _enqueueEvent(SignedUpEvent(user));
            completer.complete(
              Verification(
                isConfirmed: true,
                flow: VerificationFlow.signUp,
              ),
            );
            break;
        }
      },
    ).onError<aws_cognito.UsernameExistsException>((e, stackTrace) {
      completer.completeError(
        UsernameExistsException(
          message: 'username already exists',
          innerException: e,
          innerStackTrace: stackTrace,
        ),
      );
    }).onError<aws_cognito.AuthException>((e, stackTrace) {
      completer.completeError(
        SignUpException(
          message: 'failed to sign up user',
          innerException: e,
          innerStackTrace: stackTrace,
        ),
      );
    });

    return completer.future;
  }

  @override
  Future<Verification> resendSignUpCode(
    String username,
  ) async {
    try {
      final result = await _amplifyAuth.resendSignUpCode(
        username: username,
      );
      _logger.fine('Successfully resent sign up code: $result');

      return _createVerificationResult(
        VerificationFlow.signUp,
        result.codeDeliveryDetails,
      );
    } on aws_cognito.LimitExceededException catch (e) {
      throw ResendSignUpCodeLimitException(
        message: 'number of resend attempts exceeded',
        innerException: e,
        innerStackTrace: StackTrace.current,
      );
    } on aws_cognito.AuthException catch (e) {
      throw ResendSignUpCodeException(
        message: 'failed to sign up user',
        innerException: e,
        innerStackTrace: StackTrace.current,
      );
    }
  }

  @override
  Future<bool> confirmSignUpCode(
    String username,
    String code,
  ) async {
    try {
      final result = await _amplifyAuth.confirmSignUp(
        username: username,
        confirmationCode: code,
      );
      _logger.fine('Successfully confirmed sign up code: $result');

      return result.isSignUpComplete;
    } on aws_cognito.CodeMismatchException catch (e) {
      throw InvalidCodeException(
        message: e.message,
        innerException: e,
        innerStackTrace: StackTrace.current,
      );
    } on aws_cognito.ExpiredCodeException catch (e) {
      throw ExpiredCodeException(
        message: e.message,
        innerException: e,
        innerStackTrace: StackTrace.current,
      );
    } on aws_cognito.AuthException catch (e) {
      throw ConfirmSignUpCodeException(
        message: 'failed to confirm sign up',
        innerException: e,
        innerStackTrace: StackTrace.current,
      );
    }
  }

  @override
  Future<Verification> resetPassword(
    String username,
  ) async {
    try {
      final result = await _amplifyAuth.resetPassword(
        username: username,
      );
      _logger.fine('Successfully initiated reset password flow: $result');

      return _createVerificationResult(
        VerificationFlow.resetPassword,
        result.nextStep.codeDeliveryDetails,
      );
    } on aws_cognito.LimitExceededException catch (e) {
      throw ResetPasswordLimitException(
        message: e.message,
        innerException: e,
        innerStackTrace: StackTrace.current,
      );
    } on aws_cognito.AuthException catch (e) {
      throw ResetPasswordException(
        message: e.message,
        innerException: e,
        innerStackTrace: StackTrace.current,
      );
    }
  }

  @override
  Future<void> updatePassword(
    String username,
    String password,
    String code,
  ) async {
    try {
      final result = await _amplifyAuth.confirmResetPassword(
        username: username,
        newPassword: password,
        confirmationCode: code,
      );
      _logger.fine('Successfully initiated reset password flow: $result');
    } on aws_cognito.CodeMismatchException catch (e) {
      throw InvalidCodeException(
        message: e.message,
        innerException: e,
        innerStackTrace: StackTrace.current,
      );
    } on aws_cognito.ExpiredCodeException catch (e) {
      throw ExpiredCodeException(
        message: e.message,
        innerException: e,
        innerStackTrace: StackTrace.current,
      );
    } on aws_cognito.AuthException catch (e) {
      throw UpdatePasswordException(
        message: 'failed to update password for user $username',
        innerException: e,
        innerStackTrace: StackTrace.current,
      );
    }
  }

  @override
  Future<bool> validateSession() async {
    final result = await _cognitoPlugin.fetchAuthSession();
    if (result.isSignedIn) {
      _accessToken = result.userPoolTokensResult.value.accessToken.raw;

      List<String>? memberships(dynamic memberships) {
        List<String>? membershipList;
        if (memberships != null) {
          membershipList = [];
          for (var membership in memberships) {
            membershipList.add(membership.toString());
          }
        }
        return membershipList;
      }

      _enqueueEvent(
        TokenRefreshEvent(
          _accessToken!,
          defaultOrgId: super.jwtToken?.payload['org:default']?.toString(),
          orgMemberships: memberships(
            jwtToken?.payload['org:memberships'],
          ),
          defaultSpaceId: jwtToken?.payload['space:default']?.toString(),
          spaceMemberships: memberships(
            jwtToken?.payload['space:memberships'],
          ),
        ),
      );
      return true;
    } else {
      return false;
    }
  }

  @override
  Future<bool> isLoggedIn(String username) async {
    return username == await getLoggedInUsername();
  }

  @override
  Future<String?> getLoggedInUsername() async {
    if (await validateSession()) {
      final user = await _amplifyAuth.getCurrentUser();
      return user.username;
    } else {
      return null;
    }
  }

  @override
  Future<AuthType> signIn(
    String username,
    String password,
  ) async {
    final completer = Completer<AuthType>();

    _amplifyAuth
        .signIn(
      username: username,
      password: password,
    )
        .then(
      (result) async {
        _logger.fine('Successfully signed in: $result');
        await _completeSignInNextStep(result.nextStep, completer);
      },
    ).onError<aws_cognito.NotAuthorizedServiceException>(
      (e, stackTrace) {
        completer.completeError(
          SignInNotAllowedException(
            message: e.message,
            innerException: e,
            innerStackTrace: stackTrace,
          ),
        );
      },
    ).onError<aws_cognito.InvalidStateException>(
      (e, stackTrace) {
        // sign out the user to
        // clear the invalid state
        signOut();

        completer.completeError(
          SignInException(
            message: 'failed to sign in user: ${e.message}',
            innerException: e,
            innerStackTrace: stackTrace,
          ),
        );
      },
    ).onError<aws_cognito.AuthException>(
      (e, stackTrace) {
        completer.completeError(
          SignInException(
            message: 'failed to sign in user: ${e.message}',
            innerException: e,
            innerStackTrace: stackTrace,
          ),
        );
      },
    );

    return completer.future;
  }

  @override
  Future<AuthType> validateMFACode(
    String code,
  ) async {
    final completer = Completer<AuthType>();

    _amplifyAuth
        .confirmSignIn(
      confirmationValue: code,
    )
        .then(
      (result) async {
        _logger.fine('Successfully validated MFA code: $result');
        await _completeSignInNextStep(result.nextStep, completer);
      },
    ).onError<aws_cognito.CodeMismatchException>((e, stackTrace) {
      completer.completeError(
        InvalidCodeException(
          message: e.message,
          innerException: e,
          innerStackTrace: StackTrace.current,
        ),
      );
    }).onError<aws_cognito.ExpiredCodeException>((e, stackTrace) {
      completer.completeError(
        ExpiredCodeException(
          message: e.message,
          innerException: e,
          innerStackTrace: StackTrace.current,
        ),
      );
    }).onError<aws_cognito.AuthException>((e, stackTrace) {
      completer.completeError(
        MFACodeValidationException(
          message: 'Failed to validate MFA code for user login',
          innerException: e,
          innerStackTrace: StackTrace.current,
        ),
      );
    });

    return completer.future;
  }

  Future<void> _completeSignInNextStep(
    AuthNextSignInStep nextStep,
    Completer<AuthType> completer,
  ) async {
    switch (nextStep.signInStep) {
      case aws_cognito.AuthSignInStep.done:
        // notify listeners of user stream by reading and
        // adding the logged in user's User instance to the
        // user stream controller
        try {
          _enqueueEvent(
            LoggedInEvent(
              await readUser(),
            ),
          );
        } catch (e, stackTrace) {
          _enqueueErrorEvent(
            ReadUserErrorEvent(
              AppException(
                message: 'Failed to read user details after sign in',
                stackTrace: StackTrace.current,
                innerException: e is Exception ? e : Exception(e),
                innerStackTrace: stackTrace,
              ),
            ),
            StackTrace.current,
          );
        }
        completer.complete(AuthType.authDone);
        break;

      case aws_cognito.AuthSignInStep.confirmSignInWithSmsMfaCode:
        completer.complete(AuthType.authMFAwithSMS);
        break;

      case aws_cognito.AuthSignInStep.confirmSignInWithTotpMfaCode:
        completer.complete(AuthType.authMFAwithTOTP);
        break;

      case aws_cognito.AuthSignInStep.resetPassword:
        completer.complete(AuthType.needsPasswordReset);
        break;

      case aws_cognito.AuthSignInStep.confirmSignUp:
        completer.complete(AuthType.needsSignUpConfirmation);
        break;

      default:
        completer.completeError(
          SignInException(
            message: 'sign in failed with unhandled '
                'next step: ${nextStep.signInStep}',
          ),
        );
    }
  }

  @override
  Future<void> signOut() async {
    // notify listeners of user stream by adding an empty
    // User instance to the user stream controller
    await _enqueueEvent(const LoggedOutEvent());

    try {
      final result = await _amplifyAuth.signOut();
      _logger.fine('Successfully signed out: $result');
    } on aws_cognito.AuthException catch (e, stackTrace) {
      throw SignOutException(
        message: 'Failed to sign out user',
        innerException: e,
        innerStackTrace: stackTrace,
      );
    }
  }

  @override
  Future<Verification> sendVerificationCodeForAttribute(
    String attribute,
  ) async {
    try {
      final result = await _amplifyAuth.sendUserAttributeVerificationCode(
        userAttributeKey: _lookupUserAttributeKey(attribute),
      );
      _logger.fine(
        'Successfully requested verification code '
        'for user attribute key: $result',
      );

      return _createVerificationResult(
        VerificationFlow.verifyAttribute,
        result.codeDeliveryDetails,
      );
    } on aws_cognito.AuthException catch (e) {
      throw VerifyUserAttributeException(
        message: 'Failed to request verification code for attribute $attribute',
        innerException: e,
        innerStackTrace: StackTrace.current,
      );
    }
  }

  @override
  Future<void> confirmVerificationCodeForAttribute(
    String attribute,
    String code,
  ) async {
    final completer = Completer<void>();

    _amplifyAuth
        .confirmUserAttribute(
      userAttributeKey: _lookupUserAttributeKey(attribute),
      confirmationCode: code,
    )
        .then(
      (result) async {
        _logger.fine(
          'Successfully confirmed user attribute key: $result',
        );
        try {
          _enqueueEvent(
            LoggedInEvent(
              await readUser(),
            ),
          );
        } catch (e, stackTrace) {
          _enqueueErrorEvent(
            ReadUserErrorEvent(
              AppException(
                message: 'Failed to read user details after sign in',
                stackTrace: StackTrace.current,
                innerException: e is Exception ? e : Exception(e),
                innerStackTrace: stackTrace,
              ),
            ),
            StackTrace.current,
          );
        }
        completer.complete();
      },
    ).onError<aws_cognito.CodeMismatchException>((e, stackTrace) {
      completer.completeError(
        InvalidCodeException(
          message: e.message,
          innerException: e,
          innerStackTrace: StackTrace.current,
        ),
      );
    }).onError<aws_cognito.ExpiredCodeException>((e, stackTrace) {
      completer.completeError(
        ExpiredCodeException(
          message: e.message,
          innerException: e,
          innerStackTrace: StackTrace.current,
        ),
      );
    }).onError<aws_cognito.AuthException>(
      (e, stackTrace) {
        completer.completeError(
          ConfirmUserAttributeException(
            message: 'Failed to confirm user attribute $attribute',
            innerException: e,
            innerStackTrace: StackTrace.current,
          ),
        );
      },
    );

    return completer.future;
  }

  @override
  Future<(String, Uri?)> setupTOTP({String? appName}) async {
    try {
      final result = await _amplifyAuth.setUpTotp();
      _logger.fine(
        'Successfully set up TOTP for user: $result',
      );

      if (appName == null) {
        return (result.sharedSecret, null);
      } else {
        return (
          result.sharedSecret,
          result.getSetupUri(
            appName: appName,
            accountName: await getLoggedInUsername(),
          ),
        );
      }
    } on aws_cognito.AuthException catch (e) {
      throw TOTPSetupException(
        message: 'Failed to setup TOTP for user',
        innerException: e,
        innerStackTrace: StackTrace.current,
      );
    }
  }

  @override
  Future<void> verifyTOTP(
    String code,
  ) async {
    try {
      await _amplifyAuth.verifyTotpSetup(code);
      _logger.fine(
        'Successfully confirmed TOTP code',
      );
    } on aws_cognito.CodeMismatchException catch (e) {
      throw InvalidCodeException(
        message: e.message,
        innerException: e,
        innerStackTrace: StackTrace.current,
      );
    } on aws_cognito.ExpiredCodeException catch (e) {
      throw ExpiredCodeException(
        message: e.message,
        innerException: e,
        innerStackTrace: StackTrace.current,
      );
    } on aws_cognito.AuthException catch (e) {
      throw TOTPVerifyException(
        message: 'Failed to verify TOTP for user',
        innerException: e,
        innerStackTrace: StackTrace.current,
      );
    }
  }

  @override
  Future<void> saveUser(
    User user, {
    List<String>? attribNames,
  }) async {
    try {
      var reconfigureMFA = false;

      final userAttributes = [
        AuthUserAttributeKey.givenName,
        AuthUserAttributeKey.middleName,
        AuthUserAttributeKey.familyName,
        AuthUserAttributeKey.preferredUsername,
        AuthUserAttributeKey.picture,
        AuthUserAttributeKey.email,
        AuthUserAttributeKey.phoneNumber,
        preferences,
        userProfile,
      ]
          .where((key) =>
              attribNames == null ||
              attribNames
                  .firstWhere(
                    (name) => key.key == name,
                    orElse: () => '',
                  )
                  .isNotEmpty)
          .map(
            (key) {
              // get value for each user attribute key from User object

              switch (key) {
                case AuthUserAttributeKey.givenName:
                  if (user.firstName != null) {
                    return AuthUserAttribute(
                      userAttributeKey: key,
                      value: user.firstName!,
                    );
                  }

                case AuthUserAttributeKey.middleName:
                  if (user.middleName != null) {
                    return AuthUserAttribute(
                      userAttributeKey: key,
                      value: user.middleName!,
                    );
                  }

                case AuthUserAttributeKey.familyName:
                  if (user.familyName != null) {
                    return AuthUserAttribute(
                      userAttributeKey: key,
                      value: user.familyName!,
                    );
                  }

                case AuthUserAttributeKey.preferredUsername:
                  if (user.preferredName != null) {
                    return AuthUserAttribute(
                      userAttributeKey: key,
                      value: user.preferredName!,
                    );
                  }

                case AuthUserAttributeKey.picture:
                  if (user.profilePictureUrl != null) {
                    return AuthUserAttribute(
                      userAttributeKey: key,
                      value: user.profilePictureUrl!,
                    );
                  }

                case AuthUserAttributeKey.email:
                  return AuthUserAttribute(
                    userAttributeKey: key,
                    value: user.emailAddress,
                  );

                case AuthUserAttributeKey.phoneNumber:
                  return AuthUserAttribute(
                    userAttributeKey: key,
                    value: user.mobilePhone,
                  );

                case preferences:
                  reconfigureMFA = true;
                  return AuthUserAttribute(
                    userAttributeKey: key,
                    value: jsonEncode({
                      'enableBiometric': user.enableBiometric,
                      'enableMFA': user.enableMFA,
                      'enableTOTP': user.enableTOTP,
                      'rememberFor24h': user.rememberFor24h,
                      'defaultOrg': user.defaultOrg,
                      'defaultSpace': user.defaultSpace,
                    }),
                  );

                case userProfile:
                  return AuthUserAttribute(
                    userAttributeKey: key,
                    value: jsonEncode(user.userProfile),
                  );
              }
            },
          )
          .nonNulls
          .toList();

      if (reconfigureMFA) {
        var smsPreference = aws_cognito.MfaPreference.disabled;
        var totpPreference = aws_cognito.MfaPreference.disabled;
        if (user.enableMFA) {
          if (user.enableTOTP) {
            totpPreference = aws_cognito.MfaPreference.enabled;
          } else if (user.mobilePhoneVerified) {
            smsPreference = aws_cognito.MfaPreference.enabled;
          }
        }

        try {
          await _cognitoPlugin.updateMfaPreference(
            sms: smsPreference,
            totp: totpPreference,
          );
          _logger.fine(
            'Successfully configured MFA for the user: '
            'sms=${smsPreference.name}, totp=${totpPreference.name}',
          );
        } on aws_cognito.AuthException catch (e) {
          throw MFAConfigurationException(
            message: 'Failed to configure MFA for user',
            innerException: e,
            innerStackTrace: StackTrace.current,
          );
        }
      }
      final result = await _amplifyAuth.updateUserAttributes(
        attributes: userAttributes,
      );
      _logger.fine('Successfully saved user attributes: $result');

      _enqueueEvent(
        UserSavedEvent(user.copy()),
      );
    } on aws_cognito.AuthException catch (e) {
      throw SaveUserException(
        message: 'Failed to save user attributes',
        innerException: e,
        innerStackTrace: StackTrace.current,
      );
    }
  }

  @override
  Future<User> readUser() async {
    final username = await getLoggedInUsername();
    if (username == null) {
      throw ReadUserException(
        message: 'No User logged in',
      );
    }

    try {
      final result = await _amplifyAuth.fetchUserAttributes();
      _logger.fine('Successfully read user attributes: $result');

      return _mapAttributesToUser(username, result);
    } on aws_cognito.AuthException catch (e) {
      throw ReadUserException(
        message: 'Failed to read user attributes',
        innerException: e,
        innerStackTrace: StackTrace.current,
      );
    }
  }

  Verification _createVerificationResult(
    VerificationFlow flow,
    aws_cognito.AuthCodeDeliveryDetails? codeDeliveryDetails,
  ) {
    late final VerificationType type;
    switch (codeDeliveryDetails?.deliveryMedium) {
      case aws_cognito.DeliveryMedium.email:
        type = VerificationType.email;
        break;
      case aws_cognito.DeliveryMedium.sms:
        type = VerificationType.sms;
        break;
      default:
        _logger.warning(
          'Unknown verification type '
          '${codeDeliveryDetails?.deliveryMedium} '
          'for attribute '
          '${codeDeliveryDetails?.attributeKey}',
        );
        type = VerificationType.unknown;
    }
    return Verification(
      isConfirmed: false,
      flow: flow,
      type: type,
      destination: codeDeliveryDetails?.destination,
      attrName: codeDeliveryDetails?.attributeKey?.key,
    );
  }

  User _mapAttributesToUser(
    String username,
    List<AuthUserAttribute> attributes,
  ) {
    String userID = '';
    String? firstName = '';
    String? middleName;
    String? familyName;
    String? preferredName;
    String emailAddress = '';
    bool emailAddressVerified = false;
    String mobilePhone = '';
    bool mobilePhoneVerified = false;
    String? profilePictureUrl;
    String? defaultOrg;
    String? defaultSpace;
    bool rememberFor24h = false;
    bool enableBiometric = false;
    bool enableMFA = false;
    bool enableTOTP = false;
    Map<String, dynamic>? userProfileData;

    for (var attr in attributes) {
      _logger.fine(
        'Read user attribute: ${attr.userAttributeKey.key}=${attr.value}',
      );

      switch (attr.userAttributeKey) {
        case AuthUserAttributeKey.sub:
          userID = attr.value;
          break;
        case AuthUserAttributeKey.givenName:
          firstName = attr.value;
          break;
        case AuthUserAttributeKey.middleName:
          middleName = attr.value;
          break;
        case AuthUserAttributeKey.familyName:
          familyName = attr.value;
          break;
        case AuthUserAttributeKey.preferredUsername:
          preferredName = attr.value;
          break;
        case AuthUserAttributeKey.picture:
          profilePictureUrl = attr.value;
          break;
        case AuthUserAttributeKey.email:
          emailAddress = attr.value;
          break;
        case AuthUserAttributeKey.emailVerified:
          emailAddressVerified = attr.value == 'true';
          break;
        case AuthUserAttributeKey.phoneNumber:
          mobilePhone = attr.value;
          break;
        case AuthUserAttributeKey.phoneNumberVerified:
          mobilePhoneVerified = attr.value == 'true';
          break;
        case preferences:
          final prefs = jsonDecode(attr.value);
          enableBiometric = prefs['enableBiometric'];
          enableMFA = prefs['enableMFA'];
          enableTOTP = prefs['enableTOTP'];
          rememberFor24h = prefs['rememberFor24h'];
          defaultOrg = prefs['defaultOrg'];
          defaultSpace = prefs['defaultSpace'];
          break;
        case userProfile:
          userProfileData = jsonDecode(attr.value);
          break;
      }
    }

    return User(
      username: username,
      userID: userID,
      firstName: firstName,
      middleName: middleName,
      familyName: familyName,
      preferredName: preferredName,
      emailAddress: emailAddress,
      emailAddressVerified: emailAddressVerified,
      mobilePhone: mobilePhone,
      mobilePhoneVerified: mobilePhoneVerified,
      profilePictureUrl: profilePictureUrl,
      defaultOrg: defaultOrg ?? '',
      defaultSpace: defaultSpace ?? '',
      rememberFor24h: rememberFor24h,
      enableBiometric: enableBiometric,
      enableMFA: enableMFA,
      enableTOTP: enableTOTP,
      userProfile: userProfileData ?? {},
    );
  }

  Future<void> _enqueueEvent(
    ProviderEvent event,
  ) async {
    _authEventStream.add(event);
  }

  Future<void> _enqueueErrorEvent(
    ProviderEvent event,
    StackTrace? stackTrace,
  ) async {
    _authEventStream.addError(event, stackTrace);
  }

  @override
  Future<void> initialize() async {
    /// NoOp
  }

  @override
  Future<void> dispose() async {
    await _authEventStream.close();
  }
}

// Cognito user attribute keys

AuthUserAttributeKey _lookupUserAttributeKey(String key) =>
    _userAttributeKeys.firstWhere(
      (element) => element.key == key,
      orElse: () {
        throw Exception('Unknown user attribute key: $key');
      },
    );

List<AuthUserAttributeKey> _userAttributeKeys = [
  AuthUserAttributeKey.address,
  AuthUserAttributeKey.birthdate,
  AuthUserAttributeKey.email,
  AuthUserAttributeKey.emailVerified,
  AuthUserAttributeKey.familyName,
  AuthUserAttributeKey.gender,
  AuthUserAttributeKey.givenName,
  AuthUserAttributeKey.locale,
  AuthUserAttributeKey.middleName,
  AuthUserAttributeKey.name,
  AuthUserAttributeKey.nickname,
  AuthUserAttributeKey.phoneNumber,
  AuthUserAttributeKey.phoneNumberVerified,
  AuthUserAttributeKey.picture,
  AuthUserAttributeKey.preferredUsername,
  AuthUserAttributeKey.profile,
  AuthUserAttributeKey.sub,
  AuthUserAttributeKey.updatedAt,
  AuthUserAttributeKey.website,
  AuthUserAttributeKey.zoneinfo,
];

const preferences = _AuthUserCustomAttributeKey('custom:preferences');
const userProfile = _AuthUserCustomAttributeKey('custom:userProfile');

class _AuthUserCustomAttributeKey extends AuthUserAttributeKey {
  @override
  final String key;

  const _AuthUserCustomAttributeKey(this.key);
}
