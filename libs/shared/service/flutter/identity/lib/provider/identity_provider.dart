import 'package:dio/dio.dart' as dio;
import 'package:app_framework_component/app_framework.dart';

import 'package:identity_service/model/user.dart';
import 'package:identity_service/model/verification.dart';

////
/// Authentication Provider interface
///
/// The implementor of this interface would contain
/// the cloud service provider specific logic.
///
abstract interface class IdentityProvider implements RepositoryProvider {
  /// Returns the [dio.Interceptor] that is used by
  /// APIs to inject the necessary auth headers for APIs
  /// that are authorized by this identity provider.
  dio.Interceptor get authInterceptor;

  /// Signs up the given [user] with the given [password]
  Future<Verification> signUp(
    User user,
    String password,
  );

  /// Resend sign-up code to the given [username] for verification
  Future<Verification> resendSignUpCode(
    // the name of the user for whom the
    // sign-up code needs to be resent
    String username,
  );

  /// Confirm the registration of a particular [username] with the
  /// given [code]
  Future<bool> confirmSignUpCode(
    // the user's whose registration is to be verified
    String username,
    // the code that was sent for verification
    String code,
  );

  /// Initiates a password reset flow for the given [username]
  Future<Verification> resetPassword(
    // the user whose password needs to be reset
    String username,
  );

  /// Updates the given [username]'s [password] validating the
  /// change with the given [code]
  Future<void> updatePassword(
    // the user whose password is being reset
    String username,
    // the new password
    String password,
    // the confirmation code for password reset
    String code,
  );

  ///
  /// The following methods apply to the currently logged in user
  ///

  /// Returns [bool] indicating if this session is valid and has
  /// a logged in user.
  Future<bool> validateSession();

  /// Returns whether the given [username] is logged in. If a
  /// [username] is not provided then this method will return
  /// true if session is valid. This service method should
  /// also initialize the internal state with current
  /// logged in session state.
  Future<bool> isLoggedIn(
    // the username to check if logged in
    String username,
  );

  /// Returns the [username] of the underlying
  /// provider's logged in session. If the
  /// provider session is logged in then
  /// the name will be 'null'.
  Future<String?> getLoggedInUsername();

  /// Signs in the given [username] with the given [password]
  /// and returns the [AuthType] of the sign-in process.
  Future<AuthType> signIn(
    // the username to sign in with
    String username,
    // the password to sign in with
    String password,
  );

  /// Validates the given multi-factor authentication with
  /// the given [code] and returns the [AuthType] of the
  /// sign-in process.
  Future<AuthType> validateMFACode(
    // the MFA code to validate
    String code,
  );

  /// Signs out the logged in user
  Future<void> signOut();

  /// Sends a verifaction code to validate the given [attribute].
  Future<Verification> sendVerificationCodeForAttribute(
    // the user attribute to send the code for verification
    String attribute,
  );

  /// Verifies the given [attribute] with the [code] that was sent
  /// to the user
  Future<void> confirmVerificationCodeForAttribute(
    // the user attribute to verify
    String attribute,
    // the code to verify the attribute with
    String code,
  );

  /// Setup Time-based One Time Password MFA for the logged in
  /// user
  ///
  /// returns the secret to be used by a token
  /// generator app like Google Authenticate app
  /// and the URI for setting up an authenticator app
  Future<(String, Uri?)> setupTOTP({String? appName});

  /// Verifies the TOTP setup by validating a [code] generated
  /// by the token generator app with the current setup
  Future<void> verifyTOTP(
    // the code to validate the setup with
    String code,
  );

  /// Saves the [user] attributes to the AWS Cognito backend.
  /// If [attribNames] is provided then only those attributes
  /// will be saved.
  Future<void> saveUser(
    // the user to save to the backend
    User user, {
    // the list of attributes to save
    List<String>? attribNames,
  });

  /// Reads attributes of logged in user from the AWS Cognito
  /// backend. Returns a [User] object with the saved attributes
  /// of the currently logged in user.
  Future<User> readUser();
}

// Sign-in step types
enum AuthType {
  authUnknown,
  authDone,
  authInProgress,
  authMFAwithSMS,
  authMFAwithTOTP,
  needsPasswordReset,
  needsSignUpConfirmation,
}
