import 'package:equatable/equatable.dart';

import 'package:app_framework_component/app_framework.dart';

import '../model/user.dart';

/// The [UserEventType] is a [ProviderEvent] and is the base
/// class for all events related to a user.
sealed class UserEventType extends Equatable implements ProviderEvent {
  final User user;
  const UserEventType(this.user);

  @override
  List<Object?> get props => [user];

  @override
  String toString() => '$runtimeType: '
      'User(${user.username}/${user.status.name})';
}

/// The [SignedUpEvent] is emitted when a user signs up and
/// it returns the saved user object awaiting verification
final class SignedUpEvent extends UserEventType {
  const SignedUpEvent(super.user);
}

/// The [LoggedInEvent] is emitted when a user logs in
final class LoggedInEvent extends UserEventType {
  const LoggedInEvent(super.user);
}

/// The [ReadUserErrorEvent] is emitted when a user logs in
/// and there was an error when reading the user details
final class ReadUserErrorEvent extends ErrorEventType {
  const ReadUserErrorEvent(super.error);
}

/// The [UserSavedEvent] is emitted when a user has been
/// saved to the identity provider backend
final class UserSavedEvent extends UserEventType {
  const UserSavedEvent(super.user);
}

/// The [TokenRefreshEvent] is emitted when a the login in
/// user's access token is refreshed
final class TokenRefreshEvent extends Equatable implements ProviderEvent {
  final String accessToken;

  final String? defaultOrgId;
  final List<String>? orgMemberships;

  final String? defaultSpaceId;
  final List<String>? spaceMemberships;

  const TokenRefreshEvent(
    this.accessToken, {
    this.defaultOrgId,
    this.orgMemberships,
    this.defaultSpaceId,
    this.spaceMemberships,
  });

  @override
  String toString() => 'TokenRefreshEvent: $accessToken';

  @override
  List<Object?> get props => [
        accessToken,
        defaultOrgId,
        orgMemberships,
        defaultSpaceId,
        spaceMemberships,
      ];
}

/// The [LoggedOutEvent] is emitted when a user logs out
final class LoggedOutEvent extends Equatable implements ProviderEvent {
  const LoggedOutEvent();

  @override
  String toString() => 'LoggedOutEvent';

  @override
  List<Object?> get props => [];
}
