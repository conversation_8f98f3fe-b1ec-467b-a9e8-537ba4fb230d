import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'verification.g.dart';

enum VerificationFlow {
  none,
  signUp,
  resetPassword,
  verifyAttribute,
}

enum VerificationType {
  none,
  unknown,
  email,
  sms,
}

@JsonSerializable()
class Verification extends Equatable {
  final bool isConfirmed;

  final DateTime timestamp = DateTime.now();

  final VerificationFlow flow;
  final VerificationType type;

  final String? destination;
  final String? attrName;

  Verification({
    required this.isConfirmed,
    this.flow = VerificationFlow.none,
    this.type = VerificationType.none,
    this.destination,
    this.attrName,
  });

  factory Verification.fromJson(Map<String, dynamic> json) {
    return _$VerificationFromJson(json);
  }
  Map<String, dynamic> toJson() {
    return _$VerificationToJson(this);
  }

  @override
  List<Object?> get props => [
        isConfirmed,
        flow,
        type,
        destination,
        attrName,
      ];
}
