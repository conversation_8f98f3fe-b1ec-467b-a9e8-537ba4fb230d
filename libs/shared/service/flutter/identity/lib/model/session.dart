import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

import 'user.dart';

part 'session.g.dart';

const _timeout15m = 15 * 60 * 1000; // 15 minutes
const _timeout24h = 24 * 60 * 60 * 1000; // 24 hours

@JsonSerializable()
class Session extends Equatable {
  @JsonKey(
    name: 'activityTimestamp',
    includeFromJson: true,
    includeToJson: true,
  )
  final int _activityTimestamp;
  int get activityTimestamp => _activityTimestamp;

  bool get isActive => activityTimestamp > 0;

  const Session({
    int timestamp = -1,
  }) : _activityTimestamp = timestamp;

  Session touch() {
    return Session(
      timestamp: DateTime.now().millisecondsSinceEpoch,
    );
  }

  /// Returns true if the session is timed out
  bool isTimedout(User user) {
    if (activityTimestamp == -1) {
      return false;
    }

    int timeoutAt = user.rememberFor24h
        ? activityTimestamp + _timeout24h
        : activityTimestamp + _timeout15m;

    return DateTime.now().millisecondsSinceEpoch > timeoutAt;
  }

  /// Returns the how long until timeout in milliseconds
  int timeoutIn(User user) {
    if (activityTimestamp == -1) {
      return -1;
    }

    final now = DateTime.now().millisecondsSinceEpoch;
    final timestamp = activityTimestamp == -1 ? now : activityTimestamp;

    if (user.rememberFor24h) {
      return (_timeout24h - (now - timestamp));
    } else {
      // default session timeout is 15m
      return (_timeout15m - (now - timestamp));
    }
  }

  factory Session.fromJson(Map<String, dynamic> json) {
    return _$SessionFromJson(json);
  }
  Map<String, dynamic> toJson() {
    return _$SessionToJson(this);
  }

  @override
  List<Object?> get props => [isActive];
}
