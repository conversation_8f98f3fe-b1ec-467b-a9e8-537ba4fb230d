import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'user.g.dart';

@JsonSerializable()
class User extends Equatable {
  final String username;
  final String userID;

  final String? firstName;
  final String? middleName;
  final String? familyName;
  final String? preferredName;

  final String emailAddress;
  final bool emailAddressVerified;

  final String mobilePhone;
  final bool mobilePhoneVerified;

  final String? profilePictureUrl;

  final String defaultOrg;
  final String defaultSpace;

  final bool rememberFor24h;

  final bool enableBiometric;

  final bool enableMFA;
  final bool enableTOTP;

  final Map<String, dynamic> userProfile;

  @JsonKey(
    includeToJson: false,
    includeFromJson: false,
  )
  UserStatus get status =>
      username.isEmpty || (emailAddress.isEmpty && mobilePhone.isEmpty)
          ? UserStatus.unknown
          : (emailAddressVerified || mobilePhoneVerified)
              ? UserStatus.confirmed
              : UserStatus.unconfirmed;

  bool get isValid => username.isNotEmpty;

  String get initials {
    final buffer = StringBuffer();
    if (firstName != null && firstName!.isNotEmpty) {
      buffer.write(firstName![0]);
      if (familyName != null && familyName!.isNotEmpty) {
        buffer.write(familyName![0]);
      }
    } else if (username.isNotEmpty) {
      buffer.write(username[0]);
    }
    return buffer.toString().toUpperCase();
  }

  String get name {
    final buffer = StringBuffer();
    if (firstName != null && firstName!.isNotEmpty) {
      buffer.write(firstName);
      if (familyName != null && familyName!.isNotEmpty) {
        buffer.write(' ');
        buffer.write(familyName);
      }
    } else if (username.isNotEmpty) {
      buffer.write(username);
    }
    return buffer.toString();
  }

  const User({
    this.username = '',
    this.userID = '',
    this.firstName,
    this.middleName,
    this.familyName,
    this.preferredName,
    this.emailAddress = '',
    this.emailAddressVerified = false,
    this.mobilePhone = '',
    this.mobilePhoneVerified = false,
    this.defaultOrg = '',
    this.defaultSpace = '',
    this.profilePictureUrl,
    this.rememberFor24h = false,
    this.enableBiometric = false,
    // If MFA is enabled and TOTP is disabled
    // then SMS will be the preferred MFA type
    this.enableMFA = false,
    this.enableTOTP = false,
    // Custom user profile attributes
    this.userProfile = const {},
  });

  User copy() {
    return User(
      username: username,
      userID: userID,
      firstName: firstName,
      middleName: middleName,
      familyName: familyName,
      preferredName: preferredName,
      emailAddress: emailAddress,
      emailAddressVerified: emailAddressVerified,
      mobilePhone: mobilePhone,
      mobilePhoneVerified: mobilePhoneVerified,
      profilePictureUrl: profilePictureUrl,
      defaultOrg: defaultOrg,
      defaultSpace: defaultSpace,
      rememberFor24h: rememberFor24h,
      enableBiometric: enableBiometric,
      enableMFA: enableMFA,
      enableTOTP: enableTOTP,
      userProfile: Map<String, dynamic>.from(userProfile),
    );
  }

  User copyWith({
    String? username,
    String? userID,
    String? firstName,
    String? middleName,
    String? familyName,
    String? preferredName,
    String? emailAddress,
    bool? emailAddressVerified,
    String? mobilePhone,
    bool? mobilePhoneVerified,
    String? profilePictureUrl,
    String? defaultOrg,
    String? defaultSpace,
    bool? rememberFor24h,
    bool? enableBiometric,
    bool? enableMFA,
    bool? enableTOTP,
    Map<String, String?> userProfile = const {},
  }) {
    return User(
      username: username ?? this.username,
      userID: userID ?? this.userID,
      firstName: firstName ?? this.firstName,
      middleName: middleName ?? this.middleName,
      familyName: familyName ?? this.familyName,
      preferredName: preferredName ?? this.preferredName,
      emailAddress: emailAddress ?? this.emailAddress,
      emailAddressVerified: emailAddressVerified ?? this.emailAddressVerified,
      mobilePhone: mobilePhone ?? this.mobilePhone,
      mobilePhoneVerified: mobilePhoneVerified ?? this.mobilePhoneVerified,
      profilePictureUrl: profilePictureUrl ?? this.profilePictureUrl,
      defaultOrg: defaultOrg ?? this.defaultOrg,
      defaultSpace: defaultSpace ?? this.defaultSpace,
      rememberFor24h: rememberFor24h ?? this.rememberFor24h,
      enableBiometric: enableBiometric ?? this.enableBiometric,
      enableMFA: enableMFA ?? this.enableMFA,
      enableTOTP: enableTOTP ?? this.enableTOTP,
      userProfile: Map<String, String?>.from(this.userProfile)
        ..addAll(userProfile),
    );
  }

  factory User.fromJson(Map<String, dynamic> json) {
    return _$UserFromJson(json);
  }
  Map<String, dynamic> toJson() {
    return _$UserToJson(this);
  }

  @override
  List<Object?> get props => [
        username,
        userID,
        firstName,
        middleName,
        familyName,
        preferredName,
        emailAddress,
        emailAddressVerified,
        mobilePhone,
        mobilePhoneVerified,
        profilePictureUrl,
        defaultOrg,
        defaultSpace,
        rememberFor24h,
        enableBiometric,
        enableMFA,
        enableTOTP,
        userProfile,
      ];
}

enum UserStatus {
  unknown,
  unconfirmed,
  confirmed,
}
