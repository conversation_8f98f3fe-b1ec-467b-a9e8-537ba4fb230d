// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

User _$UserFromJson(Map<String, dynamic> json) => User(
      username: json['username'] as String? ?? '',
      userID: json['userID'] as String? ?? '',
      firstName: json['firstName'] as String?,
      middleName: json['middleName'] as String?,
      familyName: json['familyName'] as String?,
      preferredName: json['preferredName'] as String?,
      emailAddress: json['emailAddress'] as String? ?? '',
      emailAddressVerified: json['emailAddressVerified'] as bool? ?? false,
      mobilePhone: json['mobilePhone'] as String? ?? '',
      mobilePhoneVerified: json['mobilePhoneVerified'] as bool? ?? false,
      defaultOrg: json['defaultOrg'] as String? ?? '',
      defaultSpace: json['defaultSpace'] as String? ?? '',
      profilePictureUrl: json['profilePictureUrl'] as String?,
      rememberFor24h: json['rememberFor24h'] as bool? ?? false,
      enableBiometric: json['enableBiometric'] as bool? ?? false,
      enableMFA: json['enableMFA'] as bool? ?? false,
      enableTOTP: json['enableTOTP'] as bool? ?? false,
      userProfile: json['userProfile'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$UserToJson(User instance) => <String, dynamic>{
      'username': instance.username,
      'userID': instance.userID,
      'firstName': instance.firstName,
      'middleName': instance.middleName,
      'familyName': instance.familyName,
      'preferredName': instance.preferredName,
      'emailAddress': instance.emailAddress,
      'emailAddressVerified': instance.emailAddressVerified,
      'mobilePhone': instance.mobilePhone,
      'mobilePhoneVerified': instance.mobilePhoneVerified,
      'profilePictureUrl': instance.profilePictureUrl,
      'defaultOrg': instance.defaultOrg,
      'defaultSpace': instance.defaultSpace,
      'rememberFor24h': instance.rememberFor24h,
      'enableBiometric': instance.enableBiometric,
      'enableMFA': instance.enableMFA,
      'enableTOTP': instance.enableTOTP,
      'userProfile': instance.userProfile,
    };
