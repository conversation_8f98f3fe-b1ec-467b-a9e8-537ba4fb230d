// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'verification.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Verification _$VerificationFromJson(Map<String, dynamic> json) => Verification(
      isConfirmed: json['isConfirmed'] as bool,
      flow: $enumDecodeNullable(_$VerificationFlowEnumMap, json['flow']) ??
          VerificationFlow.none,
      type: $enumDecodeNullable(_$VerificationTypeEnumMap, json['type']) ??
          VerificationType.none,
      destination: json['destination'] as String?,
      attrName: json['attrName'] as String?,
    );

Map<String, dynamic> _$VerificationToJson(Verification instance) =>
    <String, dynamic>{
      'isConfirmed': instance.isConfirmed,
      'flow': _$VerificationFlowEnumMap[instance.flow]!,
      'type': _$VerificationTypeEnumMap[instance.type]!,
      'destination': instance.destination,
      'attrName': instance.attrName,
    };

const _$VerificationFlowEnumMap = {
  VerificationFlow.none: 'none',
  VerificationFlow.signUp: 'signUp',
  VerificationFlow.resetPassword: 'resetPassword',
  VerificationFlow.verifyAttribute: 'verifyAttribute',
};

const _$VerificationTypeEnumMap = {
  VerificationType.none: 'none',
  VerificationType.unknown: 'unknown',
  VerificationType.email: 'email',
  VerificationType.sms: 'sms',
};
