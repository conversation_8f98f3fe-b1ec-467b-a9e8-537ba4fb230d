name: identity_service
description: "MyCS cloud identity business logic service client"
publish_to: 'none'
version: 0.1.0
homepage: https://github.com/novassist-ai/novassist/tree/main/libs/shared/service/flutter/identity

environment:
  sdk: ">=3.2.6 <4.0.0"
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  http: ^1.2.2
  intl: ^0.20.2
  logging: ^1.2.0
  json_annotation: ^4.8.1
  dart_jsonwebtoken: ^3.2.0
  dio: ^5.8.0+1
  amplify_flutter: ^2.6.1
  amplify_auth_cognito: ^2.6.1
  bloc: ^9.0.0
  hydrated_bloc: ^10.0.0
  equatable: ^2.0.5
  flutter_gravatar: ^2.0.2

  app_framework_component:
    path: ../../../../component/flutter/app_framework
  utilities_ab:
    path: ../../../../commons/dart/utilities

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0

  build_runner: ^2.4.8
  json_serializable: ^6.7.1

  test: ^1.24.9
  bloc_test: ^10.0.0
  mockito: ^5.4.4

  oauth2: ^2.0.2
  googleapis: ^14.0.0
  dart_totp: ^1.0.0

flutter:
  generate: true
  assets: []
