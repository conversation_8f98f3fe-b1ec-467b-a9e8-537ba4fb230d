# Overview

This package implements client state management for user authentication and profile management use cases. It also implements a data provider that uses AWS cognito as the identity provider backend. Future versions of this package will include implementations for Google Cloud Firebase and Azure App Service.

The service is implemented using the following [Bloc](https://bloclibrary.dev/architecture/) based state management pattern for use by a Flutter feature plugin or application.

<br/>

![bloc architecture](../../../../../content/images/business-service-architecture.png)
