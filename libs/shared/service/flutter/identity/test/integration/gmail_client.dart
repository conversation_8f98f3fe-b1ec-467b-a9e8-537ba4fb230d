// ignore_for_file: avoid_print

import 'dart:io';
import 'dart:async';
import 'package:oauth2/oauth2.dart' as oauth2;
import 'package:googleapis/gmail/v1.dart' as gmail;
import 'package:logging/logging.dart';

const List<String> scopes = <String>[
  gmail.GmailApi.gmailReadonlyScope,
];

class GmailClient {
  final Logger logger = Logger('GmailClient');

  late final oauth2.Client _oauth2Client;
  gmail.GmailApi? _gmailApi;

  factory GmailClient() {
    return GmailClient._();
  }

  GmailClient._();

  Future<void> signIn() async {
    _oauth2Client = await _createGoogleOAuth2Client();
    _gmailApi = gmail.GmailApi(_oauth2Client);
  }

  Future<void> saveCredentials() async {
    await credentialsFile.writeAsString(
      _oauth2Client.credentials.toJson(),
    );
  }

  Future<List<GmailMessage>> fetchMail(int timeout) async {
    if (_gmailApi == null) {
      throw Exception('Not signed in');
    }

    final completer = Completer<List<GmailMessage>>();

    void getMessages() async {
      gmail.ListMessagesResponse results = await _gmailApi!.users.messages.list(
        'me',
        q: 'in:inbox is:unread -category:(promotions OR social)',
      );
      if (results.messages != null && results.messages!.isNotEmpty) {
        List<GmailMessage> messages = [];
        for (gmail.Message message in results.messages!) {
          if (message.id != null) {
            gmail.Message messageData = await _gmailApi!.users.messages.get(
              "me",
              message.id!,
            );
            messages.add(
              GmailMessage(
                message.id!,
                messageData,
              ),
            );
          }
        }
        logger.info('Got ${messages.length} new messages in the test inbox...');
        completer.complete(messages);
      } else {
        logger.info('No new messages in the test inbox...');
        if (--timeout >= 0) {
          Timer(const Duration(seconds: 1), getMessages);
        } else {
          completer.completeError(
            TimeoutException('Timed out fetching mail'),
          );
        }
      }
    }

    getMessages();
    return completer.future;
  }

  Future<void> markAsRead(GmailMessage message) async {
    await _gmailApi!.users.messages.modify(
      gmail.ModifyMessageRequest(removeLabelIds: ['UNREAD']),
      'me',
      message.id,
    );
  }

  Future<void> delete(GmailMessage message) async {
    await _gmailApi!.users.messages.delete(
      'me',
      message.id,
    );
  }
}

class GmailMessage {
  final String id;
  late final String from;
  late final String subject;
  late final String body;

  GmailMessage(
    this.id,
    gmail.Message message,
  ) {
    from = message.payload!.headers!
        .firstWhere((header) => header.name == 'From')
        .value!;
    subject = message.payload!.headers!
        .firstWhere((header) => header.name == 'Subject')
        .value!;
    body = message.snippet!;
  }

  @override
  String toString() {
    return 'From: $from\nSubject: $subject\nBody: $body';
  }
}

Future<oauth2.Client> _createGoogleOAuth2Client() async {
  final clientID = Platform.environment['GOOGLE_CLIENT_ID'];
  if (clientID == null) {
    throw Exception('Set the GOOGLE_CLIENT_ID environment variable');
  }
  final clientSecret = Platform.environment['GOOGLE_CLIENT_SECRET'];
  if (clientSecret == null) {
    throw Exception('Set the GOOGLE_CLIENT_SECRET environment variable');
  }

  if (credentialsFile.existsSync()) {
    var credentials = oauth2.Credentials.fromJson(
      await credentialsFile.readAsString(),
    );

    try {
      final oauth2.Client client = await oauth2.Client(
        credentials,
        identifier: clientID,
        secret: clientSecret,
      ).refreshCredentials();
      return client;
    } catch (_) {}

    print(
      '\n------------------------------------------------------------------------------'
      '\nUnable to refresh saved Google login credentials have. Please re-authenticate.'
      '\n------------------------------------------------------------------------------',
    );
    credentialsFile.deleteSync();
  }

  final grant = oauth2.AuthorizationCodeGrant(
    clientID,
    authorizationEndpoint,
    tokenEndpoint,
    secret: clientSecret,
  );
  final authorizationUrl = grant.getAuthorizationUrl(
    redirectUrl,
    scopes: <String>[
      gmail.GmailApi.mailGoogleComScope,
    ],
  );

  print(
    '\n--------------------------------------------------------------------'
    '\nNavigate to the following URL to complete the authorization process:'
    '\n--------------------------------------------------------------------',
  );
  print(authorizationUrl);

  // print(
  //   '\n---------------------------------------------------------------'
  //   '\nEnter the response URL copied from your browser\'s address bar:'
  //   '\n---------------------------------------------------------------',
  // );
  // String? url = stdin.readLineSync();
  // if (url == null) {
  //   throw Exception('No response URL provided');
  // }

  // **** readLineSync() does not return when enter is pressed
  // **** so we will use a file to store the response URL
  print(
    '\n--------------------------------------------------'
    '\nCreate a file named response.txt with the response'
    '\nURL from your browser\'s address bar:'
    '\n--------------------------------------------------',
  );
  final File responseFile = File('response.txt');
  while (!responseFile.existsSync()) {
    await Future.delayed(const Duration(seconds: 1));
  }
  final url = responseFile.readAsStringSync();
  responseFile.deleteSync();
  // **** remove this once readLineSync() is fixed

  var responseUrl = Uri.parse(url);

  // Pass the query parameters to the AuthorizationCodeGrant. It will
  // validate them and extract the authorization code to create a new Client.
  print(
    '---------------------------------------------'
    '\nCompleting the authorization process...',
  );
  final client = await grant.handleAuthorizationResponse(
    responseUrl.queryParameters,
  );
  print(
    'Authorization process completed successfully!'
    '\n---------------------------------------------',
  );

  return client;
}

// Google OAuth2 endpoints
final authorizationEndpoint = Uri.parse(
  'https://accounts.google.com/o/oauth2/auth',
);
final tokenEndpoint = Uri.parse(
  'https://oauth2.googleapis.com/token',
);

final redirectUrl = Uri.parse('http://localhost');
final credentialsFile = File('credentials.json');
