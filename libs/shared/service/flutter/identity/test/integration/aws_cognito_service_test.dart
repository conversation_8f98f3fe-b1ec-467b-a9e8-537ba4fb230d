@Timeout(Duration(seconds: 60))
import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:amplify_auth_cognito/amplify_auth_cognito.dart';
import 'package:dart_totp/dart_totp.dart';
import 'package:logging/logging.dart';
import 'package:dio/dio.dart';

import 'package:utilities_ab/logging/init_logging.dart';
import 'package:app_framework_component/app_framework.dart' as app;

import 'package:identity_service/model/user.dart';
import 'package:identity_service/model/verification.dart';
import 'package:identity_service/provider/exceptions.dart';
import 'package:identity_service/provider/identity_provider.dart';
import 'package:identity_service/provider/provider_events.dart';
import 'package:identity_service/provider/aws_cognito_service.dart';

import 'gmail_client.dart';
import 'aws_amplify_config.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  final Logger logger = Logger('AwsCognitoServiceTest');
  if (Platform.environment['DEBUG'] != null) {
    initLogging(
      Level.ALL,
      logToConsole: true,
    );
  } else {
    initLogging(
      Level.INFO,
      logToConsole: true,
    );
  }

  // Set a custom override that'll use an unmocked HTTP client
  HttpOverrides.global = _TestHttpOverrides();

  // Ensure the mandatory environment variables are set
  final testEmail = Platform.environment['TEST_EMAIL'];
  if (testEmail == null) {
    throw Exception(
      'Set the environment variable TEST_EMAIL '
      'with a test gmail email address.',
    );
  }
  final testPhone = Platform.environment['TEST_PHONE'];
  if (testPhone == null) {
    throw Exception(
      'Set the environment variable TEST_PHONE '
      'with the test phone number.',
    );
  }

  // Gmail client to read verification codes. The integration
  // tests uses a Gmail test account for sign-up which means
  // verification codes will be sent to gmail. Mobile phone
  // verification codes are also forward to the same gmail
  // account.
  final GmailClient gmailClient = GmailClient();

  // The integration tests will sign up a test user and
  // execute various user auth and management tasks against
  // live AWS cognito services against a test user pool.
  late final IdentityProvider provider;

  final password = Platform.environment['TEST_PASSWORD'] ?? 'TestPassword123!';
  late final String username;
  User user;

  if (Platform.environment['TEST_USERNAME'] == null) {
    username = 'testUser${DateTime.now().millisecondsSinceEpoch}';
    user = const User();
  } else {
    username = Platform.environment['TEST_USERNAME']!;
    user = User(
      username: username,
      emailAddress: testEmail,
      mobilePhone: testPhone,
    );
  }

  var totpSecret = '';

  // Set up the test suite

  setUpAll(() async {
    await gmailClient.signIn();

    final auth = AmplifyAuthCognito(
      secureStorageFactory: InMemoryStorage.new,
    );
    await Amplify.addPlugin(auth);
    await Amplify.configure(amplifyconfig);
    logger.info('AWS Amplify configured for tests');

    provider = AWSCognitoService();
    provider.providerEventStream.listen((event) {
      logger.info('Auth event received: $event');
      // Listen for all user updates and
      // update user instance in state
      if (event is UserEventType) {
        user = event.user;
      }
    });
  });

  tearDownAll(() {
    gmailClient.saveCredentials();
  });

  // Sign-In Helper functions

  Future<void> validateMFAwithTOTP() async {
    expect(totpSecret.length, greaterThan(0));
    expect(
      await provider.validateMFACode(TOTP.generateTOTP(totpSecret)),
      AuthType.authDone,
    );
  }

  Future<void> validateMFAwithSMS() async {
    final code = await _extractCodeFromFirstUnreadEmail(
      gmailClient,
      smsVerifySubject,
    );
    expect(code.length, greaterThanOrEqualTo(6));
    expect(
      await provider.validateMFACode(code),
      AuthType.authDone,
    );
  }

  signIn() async {
    // Sign in the user
    final authType = await provider.signIn(
      user.username,
      password,
    );
    // Check and handle the auth type
    switch (authType) {
      case AuthType.authDone:
        break;

      case AuthType.authMFAwithSMS:
        await validateMFAwithSMS();
        break;

      case AuthType.authMFAwithTOTP:
        await validateMFAwithTOTP();
        break;

      default:
        fail('Unexpected AuthType: $authType');
    }
  }

  signOut() async {
    await provider.signOut();
    expect(await provider.validateSession(), false);
  }

  // common validation function

  void validateUser(
    User u, {
    isConfirmed = false,
  }) {
    expect(u.username, user.username);
    expect(u.emailAddress, user.emailAddress);
    expect(u.mobilePhone, user.mobilePhone);

    if (isConfirmed) {
      expect(
        u.userID,
        matches(
          r'^[a-zA-Z0-9]{8}-[a-zA-Z0-9]{4}-[a-zA-Z0-9]{4}-[a-zA-Z0-9]{4}-[a-zA-Z0-9]{12}$',
        ),
      );
      expect(u.status, UserStatus.confirmed);
    } else {
      expect(u.status, UserStatus.unconfirmed);
    }
  }

  // Test Suite

  test('detects invalid initial session', () async {
    expect(await provider.validateSession(), false);
  });

  group('User Sign-up and Auth', () {
    test('registers a new user, confirms registration email', () async {
      logger.info(
        'Signing up user: $username',
      );

      int eventCounter = 1;
      provider.providerEventStream.listen((event) {
        eventCounter--;
        switch (eventCounter) {
          case 1:
            expect(event, isA<SignedUpEvent>());
            final u = (event as SignedUpEvent).user;
            validateUser(u);
            expect(u.status, UserStatus.unconfirmed);
            break;
        }
      });

      var verification = await provider.signUp(
        User(
          username: username,
          emailAddress: testEmail,
          mobilePhone: testPhone,
        ),
        password,
      );
      expect(verification.isConfirmed, false);
      expect(verification.flow, VerificationFlow.signUp);
      expect(verification.type, VerificationType.email);
      expect(verification.destination, 't***@g***');
      expect(verification.attrName, 'email');

      final firstCode = await _extractCodeFromFirstUnreadEmail(
        gmailClient,
        emailVerifySubject,
      );
      expect(firstCode.length, greaterThanOrEqualTo(6));

      try {
        final success = await provider.confirmSignUpCode(
          user.username,
          '99999', // Invalid code
        );

        fail('Expected an InvalidCodeException but got success: $success');
      } on InvalidCodeException catch (_) {
        logger.info('Failed confirm with invalid code, requesting new code.');

        verification = await provider.resendSignUpCode(user.username);
        expect(verification.isConfirmed, false);
        expect(verification.flow, VerificationFlow.signUp);
        expect(verification.type, VerificationType.email);
        expect(verification.destination, 't***@g***');
        expect(verification.attrName, 'email');
      } catch (e) {
        fail('Expected an InvalidCodeException but got: $e');
      }

      final secondCode = await _extractCodeFromFirstUnreadEmail(
        gmailClient,
        emailVerifySubject,
      );
      expect(secondCode.length, greaterThanOrEqualTo(6));
      expect(secondCode, isNot(equals(firstCode)));

      final success = await provider.confirmSignUpCode(
        user.username,
        secondCode,
      );
      expect(success, true);

      validateUser(user);
      expect(eventCounter, 0);
    });

    test('signs in the user and validates auth interceptor', () async {
      logger.info(
        'Signing in user: ${user.username}',
      );

      String accessToken = '';

      int eventCounter = 3;
      provider.providerEventStream.listen((event) {
        switch (eventCounter) {
          case 3:
            expect(event, isA<TokenRefreshEvent>());
            eventCounter--;

            accessToken = (event as TokenRefreshEvent).accessToken;
            expect(accessToken, matches(r'^[\w-]*\.[\w-]*\.[\w-]*$'));
            break;
          case 2:
            expect(event, isA<LoggedInEvent>());
            eventCounter--;

            final u = (event as LoggedInEvent).user;
            validateUser(u, isConfirmed: true);
            break;
          case 1:
            expect(event, isA<LoggedOutEvent>());
            eventCounter--;
            break;
        }
      });

      try {
        await provider.signIn(
          user.username,
          'InvalidPassword123!',
        );

        fail('Expected an AuthException but got success');
      } on SignInException catch (_) {
        logger.info(
          'Failed sign-in with invalid password, '
          'retrying with valid password.',
        );

        expect(
          await provider.signIn(
            user.username,
            password,
          ),
          AuthType.authDone,
        );
      } catch (e) {
        fail('Expected a SignInException but got: $e');
      }

      logger.info(
        'Validating interceptor for auth requests',
      );

      String? authToken;
      final dio = Dio();
      dio.interceptors.add(provider.authInterceptor);
      dio.interceptors.add(
        InterceptorsWrapper(
          onRequest: (options, handler) {
            expect(options.headers['Authorization'], isNotNull);
            authToken = options.headers['Authorization'];

            handler.resolve(
              Response(
                requestOptions: options,
                statusCode: 200,
              ),
            );
          },
        ),
      );

      await dio.get('https://example.com');
      expect(authToken, 'Bearer $accessToken');

      // Verify the Log in session and JWT token
      final authSession = provider as app.AuthSession;
      expect(authSession.loggedIn, isTrue);
      expect(authSession.userId, user.userID);

      final jwt = authSession.jwtToken;
      expect(jwt, isNotNull);
      expect(jwt!.subject, user.userID);

      await signOut();
      expect(eventCounter, 0);
      validateUser(user, isConfirmed: true);
    });
  });

  group('User Auth Managment', () {
    setUp(() async {
      await signIn();
    });

    tearDown(() async {
      await signOut();
    });

    test('verify mobile phone', () async {
      logger.info(
        'Verifying user\'s mobile phone: ${user.username}',
      );

      final verification = await provider.sendVerificationCodeForAttribute(
        'phone_number',
      );
      expect(verification.isConfirmed, false);
      expect(verification.flow, VerificationFlow.verifyAttribute);
      expect(verification.type, VerificationType.sms);
      expect(verification.destination, '+*******0705');
      expect(verification.attrName, 'phone_number');

      final code = await _extractCodeFromFirstUnreadEmail(
        gmailClient,
        smsVerifySubject,
      );
      expect(code.length, greaterThanOrEqualTo(6));

      try {
        await provider.confirmVerificationCodeForAttribute(
          'phone_number',
          '99999', // Invalid code
        );

        fail('Expected an InvalidCodeException but got success');
      } on InvalidCodeException catch (_) {
        await provider.confirmVerificationCodeForAttribute(
          'phone_number',
          code,
        );
        user = user.copyWith(mobilePhoneVerified: true);
      } catch (e) {
        fail('Expected an InvalidCodeException but got: $e');
      }
    });

    test('configure mfa settings and save additional attributes', () async {
      logger.info(
        'Configuring user\'s MFA settings: ${user.username}',
      );

      int eventCounter = 1;
      provider.providerEventStream.listen((event) {
        eventCounter--;
        switch (eventCounter) {
          case 1:
            expect(event, isA<UserSavedEvent>());
            final u = (event as UserSavedEvent).user;
            validateUser(u);
            break;
        }
      });

      user = user.copyWith(
        firstName: 'John',
        middleName: 'Kai',
        familyName: 'Doe',
        preferredName: 'JD',
        enableMFA: true,
        rememberFor24h: true,
      );
      await provider.saveUser(user);

      await provider.signOut();
      expect(
        await provider.signIn(
          user.username,
          password,
        ),
        AuthType.authMFAwithSMS,
      );
      try {
        await provider.validateMFACode('99999');
        fail('Expected an InvalidCodeException but got success');
      } on InvalidCodeException catch (_) {
        final code = await _extractCodeFromFirstUnreadEmail(
          gmailClient,
          smsVerifySubject,
        );
        expect(
          await provider.validateMFACode(code),
          AuthType.authDone,
        );
      } catch (e) {
        fail('Expected an InvalidCodeException but got: $e');
      }
    });

    test('setup and verify totp settings', () async {
      logger.info(
        'Setting up user\'s TOTP settings: ${user.username}',
      );

      Uri? totpUri;

      (totpSecret, totpUri) = await provider.setupTOTP(
        appName: 'TestApp',
      );
      expect(totpSecret.length, greaterThan(0));
      expect(
        totpUri.toString(),
        startsWith('otpauth://totp/TestApp:testUser'),
      );
      await provider.verifyTOTP(
        TOTP.generateTOTP(totpSecret),
      );

      // Enable TOTP
      await provider.saveUser(
        user.copyWith(enableTOTP: true),
      );
      await provider.signOut();

      logger.info(
        'Waiting for 30s to allow a new TOTP token to be generated',
      );
      sleep(const Duration(seconds: 30));

      expect(
        await provider.signIn(
          user.username,
          password,
        ),
        AuthType.authMFAwithTOTP,
      );
      try {
        await provider.validateMFACode('99999');
        fail('Expected an InvalidCodeException but got success');
      } on InvalidCodeException catch (_) {
        expect(
          await provider.validateMFACode(TOTP.generateTOTP(totpSecret)),
          AuthType.authDone,
        );
      } catch (e) {
        fail('Expected an InvalidCodeException but got: $e');
      }

      // Disable TOTP as once a token is used it can't be used
      // again and you need to wait 30s for the next token which
      // causes subsequent logins to fail. Also disable MFA as
      // we have already validated MFA with both SMS and TOTP.
      await provider.saveUser(
        user.copyWith(
          enableMFA: false,
          enableTOTP: false,
        ),
      );
    });

    test('read user attributes', () async {
      logger.info(
        'Reading user data: ${user.username}',
      );

      final u = await provider.readUser();
      expect(
        u.userID,
        matches(
          r'^[a-zA-Z0-9]{8}-[a-zA-Z0-9]{4}-[a-zA-Z0-9]{4}-[a-zA-Z0-9]{4}-[a-zA-Z0-9]{12}$',
        ),
      );
      expect(u.username, user.username);
      expect(u.emailAddress, user.emailAddress);
      expect(u.mobilePhone, user.mobilePhone);
      expect(u.firstName, user.firstName);
      expect(u.middleName, user.middleName);
      expect(u.familyName, user.familyName);
      expect(u.preferredName, user.preferredName);
      expect(u.emailAddressVerified, true);
      expect(u.mobilePhoneVerified, true);
      expect(u.enableMFA, false);
      expect(u.enableTOTP, false);
      expect(u.rememberFor24h, true);
      expect(u.enableBiometric, false);
    });

    test('reset password and sign-in using new password', () async {
      logger.info(
        'Resetting password of user: ${user.username}',
      );

      // Disable MFA
      await provider.saveUser(
        user.copyWith(enableMFA: false),
      );

      final verification = await provider.resetPassword(user.username);
      expect(verification.isConfirmed, false);
      expect(verification.flow, VerificationFlow.resetPassword);
      expect(verification.type, VerificationType.email);
      expect(verification.destination, 't***@g***');
      expect(verification.attrName, 'email');

      final code = await _extractCodeFromFirstUnreadEmail(
        gmailClient,
        emailVerifySubject,
      );
      expect(code.length, greaterThanOrEqualTo(6));
      await provider.updatePassword(
        user.username,
        'NewPassword123!',
        code,
      );
      await signOut();

      expect(
        await provider.signIn(
          user.username,
          'NewPassword123!',
        ),
        AuthType.authDone,
      );
    });
  });
}

// Regular expression to extract the verification
// code from the email body or text message
final codeRegExp = RegExp(
  r'(\d{6})',
);
final emailVerifySubject = RegExp(
  r'Your verification code for My Cloud Space',
);
final smsVerifySubject = RegExp(
  r'New text message from \(\d{3}\) \d{3}-\d{4}',
);

Future<String> _extractCodeFromFirstUnreadEmail(
  GmailClient gmailClient,
  RegExp subjectRegExp,
) async {
  final msgs = await gmailClient.fetchMail(30);
  final msg = msgs.firstWhere(
    (msg) => subjectRegExp.hasMatch(msg.subject),
  );
  await gmailClient.markAsRead(msg);
  await gmailClient.delete(msg);
  return _extractCodeFromMsgs(msg.body);
}

String _extractCodeFromMsgs(String body) {
  final match = codeRegExp.firstMatch(body);
  if (match != null) {
    final code = match.group(0);
    if (code != null) {
      return code;
    }
  }
  throw Exception('No verification code found in email');
}

// Custom client that overrides the Mock HTTP client
class _TestHttpOverrides extends HttpOverrides {}

class InMemoryStorage implements SecureStorageInterface {
  InMemoryStorage(this.scope);

  /// The scope of the item being stored.
  ///
  /// This can be used as a namespace for stored items.
  final AmplifySecureStorageScope scope;

  static final Map<String, String> _data = {};

  @override
  void write({required String key, required String value}) {
    _data['${scope.name}.$key'] = value;
  }

  @override
  String? read({required String key}) {
    return _data['${scope.name}.$key'];
  }

  @override
  void delete({required String key}) {
    _data.remove('${scope.name}.$key');
  }
}
