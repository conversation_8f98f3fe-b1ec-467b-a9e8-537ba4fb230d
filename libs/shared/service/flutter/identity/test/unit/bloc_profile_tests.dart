import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:mockito/mockito.dart';

import 'package:app_framework_component/bloc/state.dart';

import 'package:identity_service/identity_service.dart';

import 'mock_provider.dart';
import 'test_data.dart';

void runProfileTests() {
  group('IdentityService Profile Tests:', () {
    late MockIdentityProvider mockProvider;
    late IdentityService identityService;

    final activeSessionState = IdentityState(
      session: const Session().touch(),
      isLoggedIn: true,
      user: userWithDetail,
    );

    setUp(() async {
      mockProvider = createMockProvider();
      await mockProvider.initialize();

      identityService = IdentityService(mockProvider);
    });

    tearDown(() async {
      await identityService.close();
      await mockProvider.dispose();
    });

    blocTest(
      'verify a user attribute',
      setUp: () {
        when(
          mockProvider.sendVerificationCodeForAttribute(any),
        ).thenAnswer((Invocation i) async {
          final attribute = i.positionalArguments[0] as String;

          if (attribute == 'invalid') {
            throw VerifyUserAttributeException(
              message: 'invalid attribute',
            );
          }
          expect(attribute, 'phone_number');
          return attributeVerification;
        });
        when(
          mockProvider.confirmVerificationCodeForAttribute(any, any),
        ).thenAnswer((Invocation i) async {
          final attribute = i.positionalArguments[0] as String;
          final code = i.positionalArguments[1] as String;

          if (code == 'invalid') {
            throw InvalidCodeException(
              message: 'invalid code',
            );
          }
          expect(attribute, 'phone_number');
          expect(code, '123456');
          return;
        });
      },
      seed: () => activeSessionState,
      build: () => identityService,
      act: (IdentityService bloc) => bloc
          // should fail with an error message
          .sendVerificationCode('invalid')
          .then(
            (_) => bloc
                // should succeed
                .sendVerificationCode('phone_number')
                .then(
                  (_) => bloc
                      // should fail with an error message
                      .confirmVerificationCode('phone_number', 'invalid')
                      .then(
                        (_) => bloc
                            // should succeed
                            .confirmVerificationCode('phone_number', '123456'),
                      ),
                ),
          ),
      expect: () => [
        activeSessionState.copyWith(
          loadingStates: {sendVerificationCodeLoading},
        ),
        activeSessionState.copyWith(
          messages: {_sendVerificationCodeFailedMessage},
          loadingStates: {sendVerificationCodeLoading},
        ),
        activeSessionState.copyWith(
          messages: {_sendVerificationCodeFailedMessage},
        ),
        activeSessionState.copyWith(
          loadingStates: {sendVerificationCodeLoading},
        ),
        activeSessionState.copyWith(
          loadingStates: {sendVerificationCodeLoading},
          awaitingVerification: attributeVerification,
        ),
        activeSessionState.copyWith(
          awaitingVerification: attributeVerification,
        ),
        activeSessionState.copyWith(
          loadingStates: {confirmVerificationCodeLoading},
          awaitingVerification: attributeVerification,
        ),
        activeSessionState.copyWith(
          messages: {_sendConfirmVerificationCodeFailedMessage},
          loadingStates: {confirmVerificationCodeLoading},
          awaitingVerification: attributeVerification,
        ),
        activeSessionState.copyWith(
          messages: {_sendConfirmVerificationCodeFailedMessage},
          awaitingVerification: attributeVerification,
        ),
        activeSessionState.copyWith(
          loadingStates: {confirmVerificationCodeLoading},
          awaitingVerification: attributeVerification,
        ),
        activeSessionState.copyWith(
          loadingStates: {confirmVerificationCodeLoading},
        ),
        activeSessionState,
      ],
      verify: (bloc) {
        verify(mockProvider.sendVerificationCodeForAttribute('invalid'))
            .called(1);
        verify(mockProvider.sendVerificationCodeForAttribute('phone_number'))
            .called(1);
        verify(mockProvider.confirmVerificationCodeForAttribute(
                'phone_number', 'invalid'))
            .called(1);
        verify(mockProvider.confirmVerificationCodeForAttribute(
                'phone_number', '123456'))
            .called(1);
      },
    );

    blocTest(
      'setup totp',
      setUp: () {
        when(mockProvider.setupTOTP(appName: anyNamed('appName')))
            .thenAnswer((Invocation i) async {
          final appName = i.namedArguments[const Symbol('appName')] as String?;

          if (appName == 'invalid') {
            throw TOTPSetupException(
              message: 'invalid appName',
            );
          }

          expect(appName, 'testapp');
          return ('000000', Uri.parse('otpauth://totp/testapp?secret=000000'));
        });
        when(
          mockProvider.verifyTOTP(any),
        ).thenAnswer((Invocation i) async {
          final code = i.positionalArguments[0] as String;

          if (code == 'invalid') {
            throw InvalidCodeException(
              message: 'invalid code',
            );
          }
          expect(code, '000000');
        });
      },
      seed: () => activeSessionState,
      build: () => identityService,
      act: (IdentityService bloc) => bloc
          // should fail with an error message
          .setupTOTP(appName: 'invalid')
          .then(
            (_) => bloc
                // should succeed
                .setupTOTP(appName: 'testapp')
                .then(
                  (_) => bloc
                      // should fail with an error message
                      .verifyTOTP('invalid')
                      .then((_) => bloc
                          // should succeed
                          .verifyTOTP('000000')),
                ),
          ),
      expect: () => [
        activeSessionState.copyWith(
          loadingStates: {totpSetupLoading},
        ),
        activeSessionState.copyWith(
          messages: {_sendSetupTOTPFailedMessage},
          loadingStates: {totpSetupLoading},
        ),
        activeSessionState.copyWith(
          messages: {_sendSetupTOTPFailedMessage},
        ),
        activeSessionState.copyWith(
          loadingStates: {totpSetupLoading},
        ),
        activeSessionState.copyWith(
          loadingStates: {totpSetupLoading},
          totpSetupSecret: '000000',
          totpSetupUri: Uri.parse('otpauth://totp/testapp?secret=000000'),
        ),
        activeSessionState.copyWith(
          totpSetupSecret: '000000',
          totpSetupUri: Uri.parse('otpauth://totp/testapp?secret=000000'),
        ),
        activeSessionState.copyWith(
          loadingStates: {verifyTOTPLoading},
          totpSetupSecret: '000000',
          totpSetupUri: Uri.parse('otpauth://totp/testapp?secret=000000'),
        ),
        activeSessionState.copyWith(
          messages: {_sendVerifyTOTPFailedMessage},
          loadingStates: {verifyTOTPLoading},
          totpSetupSecret: '000000',
          totpSetupUri: Uri.parse('otpauth://totp/testapp?secret=000000'),
        ),
        activeSessionState.copyWith(
          messages: {_sendVerifyTOTPFailedMessage},
          totpSetupSecret: '000000',
          totpSetupUri: Uri.parse('otpauth://totp/testapp?secret=000000'),
        ),
        activeSessionState.copyWith(
          loadingStates: {verifyTOTPLoading},
          totpSetupSecret: '000000',
          totpSetupUri: Uri.parse('otpauth://totp/testapp?secret=000000'),
        ),
        activeSessionState.copyWith(
          loadingStates: {verifyTOTPLoading},
        ),
        activeSessionState,
      ],
      verify: (bloc) {
        verify(mockProvider.setupTOTP(appName: 'invalid')).called(1);
        verify(mockProvider.setupTOTP(appName: 'testapp')).called(1);
        verify(mockProvider.verifyTOTP('invalid')).called(1);
        verify(mockProvider.verifyTOTP('000000')).called(1);
      },
    );

    blocTest(
      'save user',
      setUp: () {
        // sign up
        when(
          mockProvider.saveUser(any),
        ).thenAnswer((Invocation i) async {
          final user = i.positionalArguments[0] as User;

          if (!user.isValid) {
            throw SaveUserException(
              message: 'invalid user',
            );
          }

          expect(user, userWithDetail);
          addEventToProviderStream(
            mockProvider,
            UserSavedEvent(user.copy()),
          );
        });
      },
      seed: () => activeSessionState,
      build: () => identityService,
      act: (IdentityService bloc) => bloc
          // should fail with an error message
          .saveUser(const User())
          .then((_) => bloc
              // should succeed
              .saveUser(userWithDetail)),
      expect: () => [
        activeSessionState.copyWith(
          loadingStates: {saveUserLoading},
        ),
        activeSessionState.copyWith(
          messages: {_sendSaveUserFailedMessage},
          loadingStates: {saveUserLoading},
        ),
        activeSessionState.copyWith(
          messages: {_sendSaveUserFailedMessage},
        ),
        activeSessionState.copyWith(
          loadingStates: {saveUserLoading},
        ),
        activeSessionState.copyWith(
          user: userWithDetail.copy(),
        ),
      ],
      verify: (bloc) {
        verify(mockProvider.saveUser(const User())).called(1);
        verify(mockProvider.saveUser(userWithDetail)).called(1);
      },
    );
  });
}

final _localizations = lookupIdentityLocalizations(
  const Locale('en'),
);
final _sendVerificationCodeFailedMessage = Message.error(
  _localizations.sendVerificationCodeError('invalid attribute'),
);
final _sendConfirmVerificationCodeFailedMessage = Message.error(
  _localizations.invalidVerificationCode,
);
final _sendSetupTOTPFailedMessage = Message.error(
  _localizations.totpSetupError('invalid appName'),
);
final _sendVerifyTOTPFailedMessage = Message.error(
  _localizations.invalidVerificationCode,
);
final _sendSaveUserFailedMessage = Message.error(
  _localizations.saveUserError('invalid user'),
);
