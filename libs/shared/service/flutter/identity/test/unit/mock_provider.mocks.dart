// Mocks generated by <PERSON><PERSON><PERSON> 5.4.4 from annotations
// in identity_service/test/unit/mock_provider.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;

import 'package:app_framework_component/app_framework.dart' as _i7;
import 'package:dio/dio.dart' as _i2;
import 'package:identity_service/model/user.dart' as _i4;
import 'package:identity_service/model/verification.dart' as _i3;
import 'package:identity_service/provider/identity_provider.dart' as _i5;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i8;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeInterceptor_0 extends _i1.SmartFake implements _i2.Interceptor {
  _FakeInterceptor_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeVerification_1 extends _i1.SmartFake implements _i3.Verification {
  _FakeVerification_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeUser_2 extends _i1.SmartFake implements _i4.User {
  _FakeUser_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [IdentityProvider].
///
/// See the documentation for Mockito's code generation for more information.
class MockIdentityProvider extends _i1.Mock implements _i5.IdentityProvider {
  MockIdentityProvider() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.Interceptor get authInterceptor => (super.noSuchMethod(
        Invocation.getter(#authInterceptor),
        returnValue: _FakeInterceptor_0(
          this,
          Invocation.getter(#authInterceptor),
        ),
      ) as _i2.Interceptor);

  @override
  _i6.Stream<_i7.ProviderEvent> get providerEventStream => (super.noSuchMethod(
        Invocation.getter(#providerEventStream),
        returnValue: _i6.Stream<_i7.ProviderEvent>.empty(),
      ) as _i6.Stream<_i7.ProviderEvent>);

  @override
  _i6.Future<_i3.Verification> signUp(
    _i4.User? user,
    String? password,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #signUp,
          [
            user,
            password,
          ],
        ),
        returnValue: _i6.Future<_i3.Verification>.value(_FakeVerification_1(
          this,
          Invocation.method(
            #signUp,
            [
              user,
              password,
            ],
          ),
        )),
      ) as _i6.Future<_i3.Verification>);

  @override
  _i6.Future<_i3.Verification> resendSignUpCode(String? username) =>
      (super.noSuchMethod(
        Invocation.method(
          #resendSignUpCode,
          [username],
        ),
        returnValue: _i6.Future<_i3.Verification>.value(_FakeVerification_1(
          this,
          Invocation.method(
            #resendSignUpCode,
            [username],
          ),
        )),
      ) as _i6.Future<_i3.Verification>);

  @override
  _i6.Future<bool> confirmSignUpCode(
    String? username,
    String? code,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #confirmSignUpCode,
          [
            username,
            code,
          ],
        ),
        returnValue: _i6.Future<bool>.value(false),
      ) as _i6.Future<bool>);

  @override
  _i6.Future<_i3.Verification> resetPassword(String? username) =>
      (super.noSuchMethod(
        Invocation.method(
          #resetPassword,
          [username],
        ),
        returnValue: _i6.Future<_i3.Verification>.value(_FakeVerification_1(
          this,
          Invocation.method(
            #resetPassword,
            [username],
          ),
        )),
      ) as _i6.Future<_i3.Verification>);

  @override
  _i6.Future<void> updatePassword(
    String? username,
    String? password,
    String? code,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updatePassword,
          [
            username,
            password,
            code,
          ],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<bool> validateSession() => (super.noSuchMethod(
        Invocation.method(
          #validateSession,
          [],
        ),
        returnValue: _i6.Future<bool>.value(false),
      ) as _i6.Future<bool>);

  @override
  _i6.Future<bool> isLoggedIn(String? username) => (super.noSuchMethod(
        Invocation.method(
          #isLoggedIn,
          [username],
        ),
        returnValue: _i6.Future<bool>.value(false),
      ) as _i6.Future<bool>);

  @override
  _i6.Future<String?> getLoggedInUsername() => (super.noSuchMethod(
        Invocation.method(
          #getLoggedInUsername,
          [],
        ),
        returnValue: _i6.Future<String?>.value(),
      ) as _i6.Future<String?>);

  @override
  _i6.Future<_i5.AuthType> signIn(
    String? username,
    String? password,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #signIn,
          [
            username,
            password,
          ],
        ),
        returnValue: _i6.Future<_i5.AuthType>.value(_i5.AuthType.authUnknown),
      ) as _i6.Future<_i5.AuthType>);

  @override
  _i6.Future<_i5.AuthType> validateMFACode(String? code) => (super.noSuchMethod(
        Invocation.method(
          #validateMFACode,
          [code],
        ),
        returnValue: _i6.Future<_i5.AuthType>.value(_i5.AuthType.authUnknown),
      ) as _i6.Future<_i5.AuthType>);

  @override
  _i6.Future<void> signOut() => (super.noSuchMethod(
        Invocation.method(
          #signOut,
          [],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<_i3.Verification> sendVerificationCodeForAttribute(
          String? attribute) =>
      (super.noSuchMethod(
        Invocation.method(
          #sendVerificationCodeForAttribute,
          [attribute],
        ),
        returnValue: _i6.Future<_i3.Verification>.value(_FakeVerification_1(
          this,
          Invocation.method(
            #sendVerificationCodeForAttribute,
            [attribute],
          ),
        )),
      ) as _i6.Future<_i3.Verification>);

  @override
  _i6.Future<void> confirmVerificationCodeForAttribute(
    String? attribute,
    String? code,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #confirmVerificationCodeForAttribute,
          [
            attribute,
            code,
          ],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<(String, Uri?)> setupTOTP({String? appName}) =>
      (super.noSuchMethod(
        Invocation.method(
          #setupTOTP,
          [],
          {#appName: appName},
        ),
        returnValue: _i6.Future<(String, Uri?)>.value((
          _i8.dummyValue<String>(
            this,
            Invocation.method(
              #setupTOTP,
              [],
              {#appName: appName},
            ),
          ),
          null
        )),
      ) as _i6.Future<(String, Uri?)>);

  @override
  _i6.Future<void> verifyTOTP(String? code) => (super.noSuchMethod(
        Invocation.method(
          #verifyTOTP,
          [code],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> saveUser(
    _i4.User? user, {
    List<String>? attribNames,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveUser,
          [user],
          {#attribNames: attribNames},
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<_i4.User> readUser() => (super.noSuchMethod(
        Invocation.method(
          #readUser,
          [],
        ),
        returnValue: _i6.Future<_i4.User>.value(_FakeUser_2(
          this,
          Invocation.method(
            #readUser,
            [],
          ),
        )),
      ) as _i6.Future<_i4.User>);

  @override
  _i6.Future<void> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> dispose() => (super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);
}
