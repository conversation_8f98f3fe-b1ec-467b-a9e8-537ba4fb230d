import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:mockito/mockito.dart';

import 'package:app_framework_component/bloc/state.dart';

import 'package:identity_service/identity_service.dart';

import 'mock_provider.dart';
import 'test_data.dart';

void runAuthTests() {
  group('IdentityService Auth Tests:', () {
    late MockIdentityProvider mockProvider;
    late IdentityService identityService;

    setUp(() async {
      mockProvider = createMockProvider();
      await mockProvider.initialize();

      identityService = IdentityService(mockProvider);
    });

    tearDown(() async {
      await identityService.close();
      await mockProvider.dispose();
    });

    blocTest(
      'sign in user with no MFA',
      setUp: () {
        when(
          mockProvider.signIn(any, any),
        ).thenAnswer((Invocation i) async {
          final username = i.positionalArguments[0] as String;
          final password = i.positionalArguments[1] as String;

          if (password == 'invalid') {
            throw SignInNotAllowedException(message: 'invalid password');
          }
          expect(username, 'testuser');
          expect(password, 'testpassword');
          addEventToProviderStream(
            mockProvider,
            LoggedInEvent(
              await mockProvider.readUser(),
            ),
          );
          return AuthType.authDone;
        });
        when(
          mockProvider.readUser(),
        ).thenAnswer(
          (_) async {
            addEventToProviderStream(
              mockProvider,
              const TokenRefreshEvent(
                'fake access token',
              ),
            );
            return userWithDetail.copy();
          },
        );
      },
      build: () => identityService,
      act: (IdentityService bloc) => bloc
          // should fail with an error message
          .signIn(user.username, 'invalid')
          .then((_) => bloc
              // should succeed
              .signIn(user.username, 'testpassword')),
      expect: () => [
        IdentityState(
          loadingStates: {loginLoading},
          awaitingLogin: AuthType.authInProgress,
        ),
        IdentityState(
          messages: {_signInFailedMessage},
          loadingStates: {loginLoading},
        ),
        IdentityState(
          messages: {_signInFailedMessage},
        ),
        IdentityState(
          loadingStates: {loginLoading},
          awaitingLogin: AuthType.authInProgress,
        ),
        IdentityState(
          loadingStates: {loginLoading},
          accessToken: 'fake access token',
          awaitingLogin: AuthType.authInProgress,
        ),
        IdentityState(
          loadingStates: {loginLoading},
          session: const Session().touch(),
          isLoggedIn: true,
          user: preLoginUser,
          accessToken: 'fake access token',
        ),
        IdentityState(
          session: const Session().touch(),
          isLoggedIn: true,
          user: preLoginUser,
          accessToken: 'fake access token',
        ),
        IdentityState(
          session: const Session().touch(),
          isLoggedIn: true,
          user: userWithDetail.copy(),
          accessToken: 'fake access token',
        ),
      ],
      verify: (bloc) {
        verify(mockProvider.signIn('testuser', 'invalid')).called(1);
        verify(mockProvider.signIn('testuser', 'testpassword')).called(1);
        verify(mockProvider.readUser()).called(1);
      },
    );

    blocTest(
      'sign in user with MFA',
      setUp: () {
        when(
          mockProvider.signIn(any, any),
        ).thenAnswer((Invocation i) async {
          final username = i.positionalArguments[0] as String;
          final password = i.positionalArguments[1] as String;

          expect(username, 'testuser');
          expect(password, 'testpassword');
          return AuthType.authMFAwithSMS;
        });
        // validate mfa
        when(
          mockProvider.validateMFACode(any),
        ).thenAnswer((Invocation i) async {
          final code = i.positionalArguments[0] as String;

          if (code == 'invalid') {
            throw InvalidCodeException(message: 'invalid code');
          }

          expect(code, '123456');
          addEventToProviderStream(
            mockProvider,
            LoggedInEvent(
              await mockProvider.readUser(),
            ),
          );
          return AuthType.authDone;
        });
        when(
          mockProvider.readUser(),
        ).thenAnswer(
          (_) async {
            addEventToProviderStream(
              mockProvider,
              const TokenRefreshEvent(
                'fake access token',
              ),
            );
            return userWithDetail.copy();
          },
        );
      },
      build: () => identityService,
      act: (IdentityService bloc) => bloc
          // should succeed
          .signIn(user.username, 'testpassword')
          .then(
            (_) => bloc
                // should fail with an error message
                .validateMFACode('invalid')
                .then(
                  (_) => bloc
                      // should succeed
                      .validateMFACode('123456'),
                ),
          ),
      expect: () => [
        IdentityState(
          loadingStates: {loginLoading},
          awaitingLogin: AuthType.authInProgress,
        ),
        IdentityState(
          loadingStates: {loginLoading},
          awaitingLogin: AuthType.authMFAwithSMS,
          user: preLoginUser,
        ),
        const IdentityState(
          awaitingLogin: AuthType.authMFAwithSMS,
          user: preLoginUser,
        ),
        IdentityState(
          loadingStates: {validateMFALoading},
          awaitingLogin: AuthType.authMFAwithSMS,
          user: preLoginUser,
        ),
        IdentityState(
          messages: {_validateMFACodeFailedMessage},
          loadingStates: {validateMFALoading},
          awaitingLogin: AuthType.authMFAwithSMS,
          user: preLoginUser,
        ),
        IdentityState(
          messages: {_validateMFACodeFailedMessage},
          awaitingLogin: AuthType.authMFAwithSMS,
          user: preLoginUser,
        ),
        IdentityState(
          loadingStates: {validateMFALoading},
          awaitingLogin: AuthType.authMFAwithSMS,
          user: preLoginUser,
        ),
        IdentityState(
          loadingStates: {validateMFALoading},
          awaitingLogin: AuthType.authMFAwithSMS,
          user: preLoginUser,
          accessToken: 'fake access token',
        ),
        IdentityState(
          loadingStates: {validateMFALoading},
          session: const Session().touch(),
          isLoggedIn: true,
          user: preLoginUser,
          accessToken: 'fake access token',
        ),
        IdentityState(
          session: const Session().touch(),
          isLoggedIn: true,
          user: preLoginUser,
          accessToken: 'fake access token',
        ),
        IdentityState(
          session: const Session().touch(),
          isLoggedIn: true,
          user: userWithDetail.copy(),
          accessToken: 'fake access token',
        ),
      ],
      verify: (bloc) {
        verify(mockProvider.signIn('testuser', 'testpassword')).called(1);
        verify(mockProvider.validateMFACode('invalid')).called(1);
        verify(mockProvider.validateMFACode('123456')).called(1);
        verify(mockProvider.readUser()).called(1);
      },
    );
  });
}

final _localizations = lookupIdentityLocalizations(
  const Locale('en'),
);
final _signInFailedMessage = Message.error(
  _localizations.signInNotAllowed,
);
final _validateMFACodeFailedMessage = Message.error(
  _localizations.invalidMFACode,
);
