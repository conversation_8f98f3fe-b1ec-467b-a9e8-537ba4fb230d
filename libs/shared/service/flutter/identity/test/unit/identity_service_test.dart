import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:logging/logging.dart';

import 'package:utilities_ab/logging/init_logging.dart';

import 'bloc_state_tests.dart';
import 'bloc_auth_tests.dart';
import 'bloc_profile_tests.dart';
import 'bloc_signup_tests.dart';
import 'bloc_session_tests.dart';
import 'mock_storage.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  if (Platform.environment['DEBUG'] != null) {
    initLogging(
      Level.ALL,
      logToConsole: true,
    );
  } else {
    initLogging(
      Level.INFO,
      logToConsole: true,
    );
  }

  initializeHydratedBloc();

  runStateTests();
  runSignUpTests();
  runSessionTests();
  runAuthTests();
  runProfileTests();
}
