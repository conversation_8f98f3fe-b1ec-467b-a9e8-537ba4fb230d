import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:mockito/mockito.dart';

import 'package:app_framework_component/bloc/state.dart';

import 'package:identity_service/identity_service.dart';

import 'mock_provider.dart';
import 'test_data.dart';

void runSignUpTests() {
  group('IdentityService Sign Up Tests:', () {
    late MockIdentityProvider mockProvider;
    late IdentityService identityService;

    setUp(() async {
      mockProvider = createMockProvider();
      await mockProvider.initialize();

      identityService = IdentityService(mockProvider);
    });

    tearDown(() async {
      await identityService.close();
      await mockProvider.dispose();
    });

    test('initial state is correct', () {
      expect(identityService.state, const IdentityState());
    });

    blocTest(
      'sign up user and expect awaiting verification to be set in state',
      setUp: () {
        // sign up
        when(
          mockProvider.signUp(any, any),
        ).thenAnswer((Invocation i) async {
          final user = i.positionalArguments[0] as User;
          final password = i.positionalArguments[1] as String;

          if (password == 'invalid') {
            throw SignUpException(
              message: 'invalid password',
            );
          }

          expect(user.username, 'testuser');
          expect(password, 'testpassword');
          addEventToProviderStream(
            mockProvider,
            SignedUpEvent(user),
          );
          return signUpVerification;
        });
      },
      build: () => identityService,
      act: (IdentityService bloc) =>
          // should fail with an error message
          bloc
              .signUp(user, 'invalid')
              // should succeed
              .then((_) => bloc.signUp(user, 'testpassword')),
      expect: () => [
        IdentityState(
          loadingStates: {signUpLoading},
        ),
        IdentityState(
          messages: {_signUpFailedMessage},
          loadingStates: {signUpLoading},
        ),
        IdentityState(
          messages: {_signUpFailedMessage},
        ),
        IdentityState(
          loadingStates: {signUpLoading},
        ),
        IdentityState(
          loadingStates: {signUpLoading},
          user: user,
        ),
        IdentityState(
          loadingStates: {signUpLoading},
          user: user,
          awaitingVerification: signUpVerification,
        ),
        IdentityState(
          user: user,
          awaitingVerification: signUpVerification,
        ),
      ],
      verify: (bloc) {
        expect(bloc.state.isLoginInProgress, isFalse,
            reason: 'isLoginInProgress');
        expect(bloc.state.isSignUpInProgress, isTrue,
            reason: 'isSignUpInProgress');
        expect(bloc.state.isAttribVerificationProgress, isFalse,
            reason: 'isAttribVerificationProgress');
      },
    );

    blocTest(
      'resend sign-up code and confirm sign-up code with alert',
      setUp: () {
        // resend sign-up code
        when(
          mockProvider.resendSignUpCode(any),
        ).thenAnswer((Invocation i) async {
          final username = i.positionalArguments[0] as String;

          if (username == 'invalid') {
            throw ResendSignUpCodeException(
              message: 'invalid username',
            );
          }

          expect(username, 'testuser');
          return signUpVerification;
        });
        // confirm sign-up code
        when(
          mockProvider.confirmSignUpCode(any, any),
        ).thenAnswer((Invocation i) async {
          final username = i.positionalArguments[0] as String;
          final code = i.positionalArguments[1] as String;

          if (code == 'invalid') {
            throw InvalidCodeException(
              message: 'invalid code',
            );
          }

          expect(username, 'testuser');
          if (code == '*123456*') {
            return false;
          }
          expect(code, '123456');
          return true;
        });
      },
      build: () => identityService,
      act: (IdentityService bloc) =>
          // should fail with an error message
          bloc.resendSignUpCode('invalid').then(
                // should succeed
                (_) => bloc.resendSignUpCode(user.username).then(
                      (_) => bloc
                          // should fail with an error message
                          .confirmSignUpCode(user.username, 'invalid')
                          .then(
                            (_) => bloc
                                // should succeed but get an alert message
                                .confirmSignUpCode(user.username, '*123456*'),
                          ),
                    ),
              ),
      expect: () => [
        IdentityState(
          loadingStates: {resendSignUpCodeLoading},
        ),
        IdentityState(
          messages: {_resendSignUpCodeFailedMessage},
          loadingStates: {resendSignUpCodeLoading},
        ),
        IdentityState(
          messages: {_resendSignUpCodeFailedMessage},
        ),
        IdentityState(
          loadingStates: {resendSignUpCodeLoading},
        ),
        IdentityState(
          loadingStates: {resendSignUpCodeLoading},
          awaitingVerification: signUpVerification,
        ),
        IdentityState(
          awaitingVerification: signUpVerification,
        ),
        IdentityState(
          loadingStates: {confirmSignUpLoading},
          awaitingVerification: signUpVerification,
        ),
        IdentityState(
          messages: {_confirmSignUpFailedMessage},
          loadingStates: {confirmSignUpLoading},
          awaitingVerification: signUpVerification,
        ),
        IdentityState(
          messages: {_confirmSignUpFailedMessage},
          awaitingVerification: signUpVerification,
        ),
        IdentityState(
          loadingStates: {confirmSignUpLoading},
          awaitingVerification: signUpVerification,
        ),
        IdentityState(
          messages: {_confirmSignUpAlert},
          loadingStates: {confirmSignUpLoading},
        ),
        IdentityState(
          messages: {_confirmSignUpAlert},
        ),
      ],
    );

    blocTest(
      'confirm sign-up code',
      setUp: () {
        // confirm sign-up code
        when(mockProvider.confirmSignUpCode(any, any))
            .thenAnswer((Invocation i) async {
          expect(i.positionalArguments[1], '123456');
          return true;
        });
      },
      build: () => identityService,
      seed: () => IdentityState(
        user: user,
        awaitingVerification: signUpVerification,
      ),
      act: (IdentityService bloc) =>
          bloc.confirmSignUpCode(user.username, '123456'),
      expect: () => [
        IdentityState(
          loadingStates: {confirmSignUpLoading},
          user: user,
          awaitingVerification: signUpVerification,
        ),
        IdentityState(
          loadingStates: {confirmSignUpLoading},
          user: user.copyWith(emailAddressVerified: true),
        ),
        IdentityState(
          user: user.copyWith(emailAddressVerified: true),
        ),
      ],
    );

    blocTest(
      'initiate password reset',
      setUp: () {
        // initiate password reset
        when(mockProvider.resetPassword(any)).thenAnswer((Invocation i) async {
          final username = i.positionalArguments[0] as String;

          if (username == 'invalid') {
            throw ResetPasswordException(
              message: 'invalid username',
            );
          }

          expect(username, 'testuser');
          return passwordResetVerification;
        });
      },
      build: () => identityService,
      act: (IdentityService bloc) =>
          // should fail with an error message
          bloc.resetPassword('invalid').then(
                // should succeed
                (_) => bloc.resetPassword(user.username),
              ),
      expect: () => [
        IdentityState(
          loadingStates: {resetPasswordLoading},
        ),
        IdentityState(
          loadingStates: {resetPasswordLoading},
          messages: {_resetPasswordFailedMessage},
        ),
        IdentityState(
          messages: {_resetPasswordFailedMessage},
        ),
        IdentityState(
          loadingStates: {resetPasswordLoading},
        ),
        IdentityState(
          loadingStates: {resetPasswordLoading},
          awaitingVerification: passwordResetVerification,
        ),
        IdentityState(
          awaitingVerification: passwordResetVerification,
        ),
      ],
    );

    blocTest(
      'update password',
      setUp: () {
        // update password
        when(mockProvider.updatePassword(any, any, any))
            .thenAnswer((Invocation i) async {
          final username = i.positionalArguments[0] as String;
          final password = i.positionalArguments[1] as String;
          final code = i.positionalArguments[2] as String;

          if (code == 'invalid') {
            throw UpdatePasswordException(
              message: 'invalid code',
            );
          }

          expect(username, 'testuser');
          expect(password, 'testpassword');
          expect(code, '123456');
        });
      },
      build: () => identityService,
      act: (IdentityService bloc) => bloc
          // should fail with an error message
          .updatePassword(user.username, 'testpassword', 'invalid')
          .then(
            // should succeed
            (_) => bloc.updatePassword(user.username, 'testpassword', '123456'),
          ),
      expect: () => [
        IdentityState(
          loadingStates: {updatePasswordLoading},
        ),
        IdentityState(
          loadingStates: {updatePasswordLoading},
          messages: {_updatePasswordFailedMessage},
        ),
        IdentityState(
          messages: {_updatePasswordFailedMessage},
        ),
        IdentityState(
          loadingStates: {updatePasswordLoading},
        ),
        const IdentityState(),
      ],
    );
  });
}

final _localizations = lookupIdentityLocalizations(
  const Locale('en'),
);
final _confirmSignUpAlert = Message.alert(
  _localizations.unableToConfirmSignUp,
);
final _signUpFailedMessage = Message.error(
  _localizations.signUpError('invalid password'),
);
final _resendSignUpCodeFailedMessage = Message.error(
  _localizations.resendSignUpCodeError('invalid username'),
);
final _confirmSignUpFailedMessage = Message.error(
  _localizations.invalidVerificationCode,
);
final _resetPasswordFailedMessage = Message.error(
  _localizations.resetPasswordError('invalid username'),
);
final _updatePasswordFailedMessage = Message.error(
  _localizations.updatePasswordError('invalid code'),
);
