import 'package:flutter_test/flutter_test.dart';
import 'package:bloc_test/bloc_test.dart';

import 'package:app_framework_component/bloc/state.dart';

import 'package:identity_service/identity_service.dart';

import 'mock_provider.dart';
import 'test_data.dart';

void runStateTests() {
  group('IdentityService State Tests:', () {
    late MockIdentityProvider mockProvider;
    late IdentityService identityService;

    setUp(() async {
      mockProvider = createMockProvider();
      await mockProvider.initialize();

      identityService = IdentityService(mockProvider);
    });

    tearDown(() async {
      await identityService.close();
      await mockProvider.dispose();
    });

    blocTest(
      'removes a state message',
      seed: () => IdentityState(messages: {
        Message.info('test message 1'),
        Message.info('test message 2'),
      }),
      build: () => identityService,
      act: (IdentityService bloc) =>
          bloc.removeStateMessage(Message.info('test message 2')),
      expect: () => [
        IdentityState(
          messages: {
            Message.info('test message 1'),
          },
        )
      ],
    );

    blocTest(
      'refreshes the session as active',
      seed: () => const IdentityState(
        isLoggedIn: true,
      ),
      build: () => identityService,
      act: (IdentityService bloc) => bloc.touchSession(),
      expect: () => [
        IdentityState(
          session: const Session().touch(),
          isLoggedIn: true,
        )
      ],
    );

    blocTest(
      'updates the user instance in the state',
      seed: () => IdentityState(
        session: const Session().touch(),
        isLoggedIn: true,
        user: userWithDetail.copy(),
      ),
      build: () => identityService,
      act: (IdentityService bloc) => bloc.updateUserInState(
        userWithDetail.copyWith(
          middleName: 'Xavier',
          preferredName: 'JD',
        ),
      ),
      expect: () => [
        IdentityState(
          session: const Session().touch(),
          isLoggedIn: true,
          user: userWithDetail.copyWith(
            middleName: 'Xavier',
            preferredName: 'JD',
          ),
        )
      ],
      verify: (bloc) {},
    );
  });
}
