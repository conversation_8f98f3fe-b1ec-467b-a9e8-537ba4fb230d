import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:mockito/mockito.dart';

import 'package:app_framework_component/bloc/state.dart';

import 'package:identity_service/identity_service.dart';

import 'mock_provider.dart';
import 'test_data.dart';

void runSessionTests() {
  group('IdentityService Session Tests:', () {
    late MockIdentityProvider mockProvider;
    late IdentityService identityService;

    setUp(() async {
      mockProvider = createMockProvider();
      await mockProvider.initialize();

      identityService = IdentityService(mockProvider);
    });

    tearDown(() async {
      await identityService.close();
      await mockProvider.dispose();
    });

    blocTest(
      'invalid session detection',
      setUp: () {
        // invalid session exception
        bool firstInvocation = true;
        when(mockProvider.validateSession()).thenAnswer((_) async {
          if (firstInvocation) {
            firstInvocation = false;
            throw Exception('session validation failed');
          }
          return false;
        });
      },
      build: () => identityService,
      act: (IdentityService bloc) => bloc
          // should fail with an error message
          .validateSession()
          .then(
            // should succeed
            (_) => bloc
                // should reset state as session is invalid
                .validateSession(),
          ),
      expect: () => [
        IdentityState(
          messages: {_validateFailedMessage},
        ),
        const IdentityState(),
      ],
    );

    blocTest(
      'session validation fails as user does not match session',
      setUp: () {
        // invalid session exception
        when(mockProvider.validateSession()).thenAnswer(
          (_) async => true,
        );
        when(mockProvider.getLoggedInUsername()).thenAnswer(
          (_) async => 'differenttestuser',
        );
        when(mockProvider.signOut()).thenAnswer(
          (_) async {},
        );
      },
      seed: () => const IdentityState(
        user: user,
      ),
      build: () => identityService,
      act: (IdentityService bloc) => bloc
          // should signout
          .validateSession(),
      expect: () => [
        const IdentityState(),
      ],
      verify: (bloc) {
        verify(mockProvider.validateSession()).called(2);
        verify(mockProvider.getLoggedInUsername()).called(1);
        verify(mockProvider.signOut()).called(1);
      },
    );

    blocTest(
      'session has timed out',
      setUp: () {
        // invalid session exception
        when(mockProvider.validateSession()).thenAnswer(
          (_) async => true,
        );
        when(mockProvider.getLoggedInUsername()).thenAnswer(
          (_) async => 'testuser',
        );
        when(mockProvider.signOut()).thenAnswer(
          (_) async {},
        );
      },
      seed: () => IdentityState(
        user: user,
        session: Session(
          timestamp: DateTime.now().millisecondsSinceEpoch - 60 * 60 * 1000,
        ),
      ),
      build: () => identityService,
      act: (IdentityService bloc) => bloc
          // should signout
          .validateSession(),
      expect: () => [
        const IdentityState(),
      ],
      verify: (bloc) {
        verify(mockProvider.validateSession()).called(2);
        verify(mockProvider.getLoggedInUsername()).called(1);
        verify(mockProvider.signOut()).called(1);
      },
    );

    blocTest(
      'session is valid',
      setUp: () {
        // invalid session exception
        when(mockProvider.validateSession()).thenAnswer(
          (_) async => true,
        );
        when(mockProvider.getLoggedInUsername()).thenAnswer(
          (_) async => 'testuser',
        );
        when(mockProvider.signOut()).thenAnswer(
          (_) async {},
        );
      },
      seed: () => IdentityState(
        user: user,
        session: const Session().touch(),
      ),
      build: () => identityService,
      act: (IdentityService bloc) => bloc
          // should signout
          .validateSession(),
      expect: () => [],
      verify: (bloc) {
        verify(mockProvider.validateSession()).called(2);
        verify(mockProvider.getLoggedInUsername()).called(1);
        verifyNever(mockProvider.signOut());
      },
    );
  });
}

final _localizations = lookupIdentityLocalizations(
  const Locale('en'),
);
final _validateFailedMessage = Message.error(
  _localizations.sessionValidationFailed,
);
