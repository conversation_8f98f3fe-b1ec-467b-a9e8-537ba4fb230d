import 'dart:async';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'package:app_framework_component/app_framework.dart';
import 'package:identity_service/provider/identity_provider.dart';

import 'mock_provider.mocks.dart';
export 'mock_provider.mocks.dart';

@GenerateMocks([IdentityProvider])
class _MockProvider extends MockIdentityProvider {
  final _authEventStream = StreamController<ProviderEvent>.broadcast();

  _MockProvider() {
    when(validateSession()).thenAnswer((_) async => false);
    when(providerEventStream).thenAnswer((_) => _authEventStream.stream);
  }

  @override
  Future<void> initialize() async {
    /// NoOp
  }

  @override
  Future<void> dispose() async {
    _authEventStream.close();
  }
}

MockIdentityProvider createMockProvider() => _MockProvider();

void addEventToProviderStream(
  MockIdentityProvider provider,
  ProviderEvent event,
) {
  (provider as _MockProvider)._authEventStream.add(event);
}
