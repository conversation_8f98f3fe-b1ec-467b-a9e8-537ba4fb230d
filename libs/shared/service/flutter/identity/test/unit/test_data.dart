import 'package:identity_service/identity_service.dart';

const user = User(
  username: 'testuser',
  emailAddress: '<EMAIL>',
  mobilePhone: '+1234567890',
  profilePictureUrl:
      'https://www.gravatar.com/avatar/7267499f7431a1328e5833d05cbb5d5c?s=128&r=g&d=identicon',
);

const preLoginUser = User(
  username: 'testuser',
);

final userWithDetail = user.copyWith(
  userID: '54321',
  firstName: 'John',
  middleName: 'X',
  familyName: 'Doe',
  emailAddressVerified: true,
);

final signUpVerification = Verification(
  isConfirmed: false,
  flow: VerificationFlow.signUp,
  type: VerificationType.email,
  destination: 't***@a***',
  attrName: 'email',
);

final passwordResetVerification = Verification(
  isConfirmed: false,
  flow: VerificationFlow.resetPassword,
  type: VerificationType.email,
  destination: 't***@a***',
  attrName: 'email',
);

final attributeVerification = Verification(
  isConfirmed: false,
  flow: VerificationFlow.verifyAttribute,
  type: VerificationType.email,
  destination: '+1***',
  attrName: 'phone_number',
);
