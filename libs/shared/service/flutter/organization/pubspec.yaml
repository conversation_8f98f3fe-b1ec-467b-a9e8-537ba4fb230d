name: organization_service
description: "MyCS organization management service module"
publish_to: 'none'
version: 0.1.0
homepage: https://github.com/novassist-ai/novassist/tree/main/libs/shared/service/flutter/organization

environment:
  sdk: ">=3.2.6 <4.0.0"
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  intl: ^0.20.2
  logging: ^1.2.0
  json_annotation: ^4.8.1
  dio: ^5.8.0+1
  amplify_flutter: ^2.6.1
  amplify_auth_cognito: ^2.6.1
  amplify_api: ^2.6.1
  get_it: ^8.0.3
  bloc: ^9.0.0
  equatable: ^2.0.5

  app_framework_component:
    path: ../../../../component/flutter/app_framework
  utilities_ab:
    path: ../../../../commons/dart/utilities
  user_space_model:
    path: ../user_space

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0

  build_runner: ^2.4.8
  json_serializable: ^6.7.1

  test: ^1.24.9
  bloc_test: ^10.0.0
  mockito: ^5.4.4

  http: ^1.2.2
  oauth2: ^2.0.2

flutter:
  generate: true
  assets: []
