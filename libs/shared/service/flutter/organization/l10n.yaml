#* This YAML file contains configuration settings for localization in the application.

#* - `arb-dir`: Specifies the directory where the ARB (Application Resource Bundle) files are located.
#* - `template-arb-file`: Specifies the name of the template ARB file.
#* - `output-localization-file`: Specifies the name of the output file that will contain the generated localizations.
#* - `nullable-getter`: Specifies whether the generated getter methods should be nullable or not.
#* - `untranslated-messages-file`: Specifies the name of the file that will contain the untranslated messages.

arb-dir: lib/l10n/arb
template-arb-file: organization_en.arb
output-dir: lib/l10n
output-localization-file: organization_localizations.dart
output-class: OrganizationLocalizations
synthetic-package: false
nullable-getter: false
