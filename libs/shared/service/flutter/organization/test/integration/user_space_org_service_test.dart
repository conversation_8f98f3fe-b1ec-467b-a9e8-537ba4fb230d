@Timeout(Duration(seconds: 60))
import 'dart:io';
import 'dart:math';
import 'package:flutter_test/flutter_test.dart';
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:amplify_auth_cognito/amplify_auth_cognito.dart';
import 'package:amplify_api/amplify_api.dart';
import 'package:logging/logging.dart';

import 'package:utilities_ab/logging/init_logging.dart';

import 'package:user_space_model/user_space_model.dart' as user_space;
import 'package:organization_service/organization_service.dart';

import 'aws_amplify_config.dart';

const testUserName = 'test1';
const testPassword = '@ppBr!ck5';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  final Logger logger = Logger('UserSpaceOrgServiceTest');
  if (Platform.environment['DEBUG'] != null) {
    initLogging(
      Level.ALL,
      logToConsole: true,
    );
  } else {
    initLogging(
      Level.INFO,
      logToConsole: true,
    );
  }

  final username = Platform.environment['TEST_USERNAME'] ?? testUserName;
  final password = Platform.environment['TEST_PASSWORD'] ?? testPassword;

  // Set a custom override that'll use an unmocked HTTP client
  HttpOverrides.global = _TestHttpOverrides();

  // The integration tests execute various test scenarios
  // on organization management.
  late final OrgProvider provider;

  // Test data
  final orgName = '$username\'s Test Organization ${Random().nextInt(1000)}';

  // Helper functions

  user_space.Contact? lookupContact(
    List<user_space.Contact?>? contacts,
    user_space.ContactType type,
  ) {
    expect(contacts, isNotNull);
    return contacts!.firstWhere(
      (contact) => contact?.type == type,
      orElse: () => null,
    );
  }

  user_space.Address? lookupAddress(
    List<user_space.Address?>? addresses,
    user_space.AddressType type,
  ) {
    expect(addresses, isNotNull);
    return addresses!.firstWhere(
      (address) => address?.type == type,
      orElse: () => null,
    );
  }

  // Set up the test suite

  setUpAll(() async {
    await Amplify.addPlugins([
      AmplifyAuthCognito(
        secureStorageFactory: InMemoryStorage.new,
      ),
      AmplifyAPI(),
    ]);
    await Amplify.configure(amplifyconfig);
    logger.info('AWS Amplify configured for tests');

    await Amplify.Auth.signIn(
      username: username,
      password: password,
    );

    provider = UserSpaceOrgService();
    provider.providerEventStream.listen((event) {
      logger.info('Org provider event received: $event');
      // Listen for all user updates and
      // update user instance in state
    });
  });

  tearDownAll(() async {
    await Amplify.Auth.signOut();
  });

  test('creates, gets, updates and deletes an org', () async {
    String? orgId;

    try {
      // Create an organization

      final orgIi = await provider.createOrg(
        orgName: orgName,
        orgKey: user_space.Key(publicKey: 'test-org-pk'),
        businessRef: '123456',
        contacts: [
          user_space.ContactInput(
            type: user_space.ContactType.email,
            description: '$username\'s test business email',
            emailAddress: 'contact@${username}sbusiness.com',
          ),
          user_space.ContactInput(
            type: user_space.ContactType.tollFree,
            description: '$username\'s test business toll free number',
            emailAddress: '***********',
          ),
        ],
        addresses: [
          user_space.AddressInput(
            type: user_space.AddressType.main,
            description: '$username\'s test head office address',
            number: '100',
            street: '100 Devonshire Pl',
            municipality: 'Boston',
            province: 'MA',
            postalCode: '02109',
            countryCode: 'US',
          ),
          user_space.AddressInput(
            type: user_space.AddressType.branch,
            description: '$username\'s test branch office address',
            number: '456',
            street: 'Elm Street',
            other: 'Suite 3',
            municipality: 'Los Angeles',
            province: 'CA',
            postalCode: '90001',
            countryCode: 'US',
          ),
        ],
      );
      expect(orgIi.idKey, isNotEmpty);
      expect(orgIi.orgUser.org, isNotNull);
      expect(orgIi.orgUser.org!.orgId, isNotEmpty);
      orgId = orgIi.orgUser.org!.orgId;

      expect(orgIi.orgUser.org!.orgName, orgName);
      expect(orgIi.orgUser.org!.verified, isTrue);

      // Get the organization

      final org = await provider.getOrgData(orgId);
      expect(org.orgId, orgId);
      expect(org.orgName, orgName);
      expect(org.publicKey, 'test-org-pk');
      expect(org.businessRef, '123456');
      expect(org.contacts, hasLength(2));
      expect(org.addresses, hasLength(2));
      expect(org.verified, isTrue);

      expect(org.owner, isNotNull);
      expect(
        org.owner!.userId,
        matches(RegExp(r'^[\w]{8}(-[\w]{4}){3}-[\w]{12}$')),
      );
      expect(org.admins, hasLength(1));
      expect(org.admins?.first?.userId, org.owner!.userId);
      expect(org.users?.orgUsers, hasLength(1));
      expect(org.users?.orgUsers?.first?.user?.userId, org.owner!.userId);

      var emContact = lookupContact(
        org.contacts,
        user_space.ContactType.email,
      );
      expect(emContact, isNotNull);
      expect(emContact!.description, '$username\'s test business email');
      expect(emContact.emailAddress, 'contact@${username}sbusiness.com');

      var tfContact = lookupContact(
        org.contacts,
        user_space.ContactType.tollFree,
      );
      expect(tfContact, isNotNull);
      expect(
        tfContact!.description,
        '$username\'s test business toll free number',
      );
      expect(tfContact.emailAddress, '***********');

      var hoAddress = lookupAddress(
        org.addresses,
        user_space.AddressType.main,
      );
      expect(hoAddress, isNotNull);
      expect(
        hoAddress!.description,
        '$username\'s test head office address',
      );
      expect(hoAddress.number, '100');
      expect(hoAddress.street, '100 Devonshire Pl');
      expect(hoAddress.municipality, 'Boston');
      expect(hoAddress.province, 'MA');
      expect(hoAddress.postalCode, '02109');
      expect(hoAddress.countryCode, 'US');

      var brAddress = lookupAddress(
        org.addresses,
        user_space.AddressType.branch,
      );
      expect(brAddress, isNotNull);
      expect(
        brAddress!.description,
        '$username\'s test branch office address',
      );
      expect(brAddress.number, '456');
      expect(brAddress.street, 'Elm Street');
      expect(brAddress.other, 'Suite 3');
      expect(brAddress.municipality, 'Los Angeles');
      expect(brAddress.province, 'CA');
      expect(brAddress.postalCode, '90001');
      expect(brAddress.countryCode, 'US');

      expect(org.users?.orgUsers, hasLength(1));
      expect(org.users?.orgUsers?.first?.user?.userId, org.owner!.userId);

      // Update the organization

      var updatedOrg = await provider.updateOrg(
        orgId: orgId,
        orgKey: user_space.Key(publicKey: 'test-org-pk-updated'),
        contacts: [
          user_space.ContactInput(
            type: user_space.ContactType.email,
            description: '$username\'s updated business email',
            emailAddress: 'contactupdated@${username}sbusiness.com',
          ),
        ],
        addresses: [
          user_space.AddressInput(
            type: user_space.AddressType.main,
            description: '$username\'s updated head office address',
            number: '100',
            street: '100 Devonshire Pl',
            municipality: 'Boston',
            province: 'MA',
            postalCode: '02109',
            countryCode: 'US',
          ),
          user_space.AddressInput(
            type: user_space.AddressType.branch,
            description: '$username\'s updated branch office address',
            number: '456',
            street: 'Elm Street',
            other: 'Suite 3',
            municipality: 'Los Angeles',
            province: 'CA',
            postalCode: '90001',
            countryCode: 'US',
          ),
        ],
        config: 'test-config-updated',
      );

      expect(updatedOrg.orgId, orgId);
      expect(updatedOrg.orgName, orgName);
      expect(updatedOrg.publicKey, 'test-org-pk-updated');
      expect(updatedOrg.contacts, hasLength(1));
      expect(updatedOrg.addresses, hasLength(2));
      expect(updatedOrg.config, 'test-config-updated');

      updatedOrg = await provider.getOrgData(orgId);
      expect(updatedOrg.orgId, orgId);
      expect(updatedOrg.orgName, orgName);
      expect(updatedOrg.publicKey, 'test-org-pk-updated');
      expect(updatedOrg.contacts, hasLength(1));
      expect(updatedOrg.addresses, hasLength(2));
      expect(updatedOrg.config, 'test-config-updated');

      emContact = lookupContact(
        updatedOrg.contacts,
        user_space.ContactType.email,
      );
      expect(emContact, isNotNull);
      expect(
        emContact!.description,
        '$username\'s updated business email',
      );
      expect(
        emContact.emailAddress,
        'contactupdated@${username}sbusiness.com',
      );

      tfContact = lookupContact(
        updatedOrg.contacts,
        user_space.ContactType.tollFree,
      );
      expect(tfContact, isNull);

      hoAddress = lookupAddress(
        updatedOrg.addresses,
        user_space.AddressType.main,
      );
      expect(hoAddress, isNotNull);
      expect(
        hoAddress!.description,
        '$username\'s updated head office address',
      );

      brAddress = lookupAddress(
        updatedOrg.addresses,
        user_space.AddressType.branch,
      );
      expect(brAddress, isNotNull);
      expect(
        brAddress!.description,
        '$username\'s updated branch office address',
      );

      // Get orgs for the current user (we should have only the one we created)
      final userOrgs = await provider.getOrgs();
      expect(userOrgs, hasLength(1));

      final userOrg = userOrgs.first;
      expect(userOrg.org?.orgId, orgId);
      expect(userOrg.org?.orgName, orgName);
      expect(userOrg.org?.verified, isTrue);
      expect(userOrg.org?.config, 'test-config-updated');
      expect(userOrg.isOwner, isTrue);
      expect(userOrg.isAdmin, isTrue);
    } finally {
      // Delete the organization

      if (orgId != null) {
        await provider.deleteOrg(orgId);
        try {
          await provider.getOrgData(orgId);
          fail('Expected OrgDoesNotExistError');
        } on OrgDoesNotExistError catch (_) {}
      }
    }
  });

  test('creates org and adds/deletes users from that org', () async {
    late final String orgId;

    try {
      // Create an organization

      final orgIi = await provider.createOrg(
        orgName: orgName,
        orgKey: user_space.Key(publicKey: 'test-org-pk'),
      );
      expect(orgIi.idKey, isNotEmpty);
      expect(orgIi.orgUser.org, isNotNull);
      expect(orgIi.orgUser.org!.orgId, isNotEmpty);
      orgId = orgIi.orgUser.org!.orgId;

      // Add a users to the organization

      var orgUserAdded1 = await provider.addOrgUser(
        orgId: orgId,
        userId: '<EMAIL>',
        isAdmin: true,
      );
      expect(
        orgUserAdded1.user?.userId,
        matches(
          RegExp(r'^[\w]{8}(-[\w]{4}){3}-[\w]{12}$'),
        ),
      );
      expect(orgUserAdded1.user?.emailAddress, '<EMAIL>');

      var orgUserAdded2 = await provider.addOrgUser(
        orgId: orgId,
        userId: '<EMAIL>',
        isAdmin: true,
      );
      expect(
        orgUserAdded2.user?.userId,
        matches(
          RegExp(r'^[\w]{8}(-[\w]{4}){3}-[\w]{12}$'),
        ),
      );
      expect(orgUserAdded2.user?.emailAddress, '<EMAIL>');

      var orgUserAdded3 = await provider.addOrgUser(
        orgId: orgId,
        userId: '<EMAIL>',
        isAdmin: true,
      );
      expect(
        orgUserAdded3.user?.userId,
        matches(
          RegExp(r'^[\w]{8}(-[\w]{4}){3}-[\w]{12}$'),
        ),
      );
      expect(orgUserAdded3.user?.emailAddress, '<EMAIL>');

      // Get the organization and verify the users

      var org = await provider.getOrgData(orgId);
      expect(org.orgId, orgId);

      var ownerOrg = org.users?.orgUsers?.firstWhere(
        (orgUser) => orgUser?.user?.userName == testUserName,
        orElse: () => null,
      );
      expect(ownerOrg, isNotNull);
      expect(ownerOrg?.isOwner, isTrue);
      expect(ownerOrg?.isAdmin, isTrue);

      expect(
        org.users?.orgUsers?.firstWhere(
          (orgUser) => orgUser?.user == orgUserAdded1.user,
          orElse: () => null,
        ),
        isNotNull,
      );
      expect(
        org.users?.orgUsers?.firstWhere(
          (orgUser) => orgUser?.user == orgUserAdded2.user,
          orElse: () => null,
        ),
        isNotNull,
      );
      expect(
        org.users?.orgUsers?.firstWhere(
          (orgUser) => orgUser?.user == orgUserAdded3.user,
          orElse: () => null,
        ),
        isNotNull,
      );

      // Remove a user from the organization

      await provider.deleteOrgUser(
        orgId: orgId,
        userId: orgUserAdded2.user!.userId,
      );

      // Get the organization and verify user was removed

      org = await provider.getOrgData(orgId);
      expect(org.users?.orgUsers, hasLength(3));

      expect(
        org.users?.orgUsers?.firstWhere(
          (orgUser) => orgUser?.user == orgUserAdded2.user,
          orElse: () => null,
        ),
        isNull,
      );
    } finally {
      // Delete the organization
      await provider.deleteOrg(orgId);
    }
  });
}

// Custom client that overrides the Mock HTTP client
class _TestHttpOverrides extends HttpOverrides {}

class InMemoryStorage implements SecureStorageInterface {
  InMemoryStorage(this.scope);

  /// The scope of the item being stored.
  ///
  /// This can be used as a namespace for stored items.
  final AmplifySecureStorageScope scope;

  static final Map<String, String> _data = {};

  @override
  void write({required String key, required String value}) {
    _data['${scope.name}.$key'] = value;
  }

  @override
  String? read({required String key}) {
    return _data['${scope.name}.$key'];
  }

  @override
  void delete({required String key}) {
    _data.remove('${scope.name}.$key');
  }
}
