import 'package:app_framework_component/app_framework.dart';

import 'package:user_space_model/models/user_space.dart';

import '../model/organization.dart';

class OrganizationState extends State<OrganizationState> {
  /// All orgs current logged in user is a member of
  final List<Organization> organizations;

  /// Current org user is viewing in profile
  final Org? selectedOrgData;

  /// Orgs user is an owner of
  get owned => organizations.where((org) => org.isOwner).toList();

  const OrganizationState({
    super.loadingStates = const {},
    super.messages = const {},
    this.organizations = const [],
    this.selectedOrgData,
  });

  String? getOrgUserName(String userId) {
    final user = selectedOrgData?.users?.orgUsers?.firstWhere(
      (user) => user?.user?.userId == userId,
      orElse: () => null,
    );

    if (user != null) {
      final fullName = StringBuffer();
      if (user.user?.firstName != null) {
        fullName.write(user.user?.firstName);
      }
      if (user.user?.middleName != null) {
        fullName.write(' ${user.user?.middleName}');
      }
      if (user.user?.familyName != null) {
        fullName.write(' ${user.user?.familyName}');
      }
      if (fullName.isEmpty) {
        return user.user?.userName;
      } else {
        return fullName.toString();
      }
    }

    return null;
  }

  @override
  OrganizationState copyWith({
    Set<LoadingState>? loadingStates,
    Set<Message>? messages,
    List<Organization>? organizations,
    Org? selectedOrgData,
    bool resetSelectedOrgData = false,
  }) {
    return OrganizationState(
      loadingStates: loadingStates ?? super.loadingStates,
      messages: messages ?? super.messages,
      organizations: organizations ?? this.organizations,
      selectedOrgData: resetSelectedOrgData //
          ? null
          : selectedOrgData ?? this.selectedOrgData,
    );
  }

  @override
  List<Object?> get additionalProps => [
        organizations,
        selectedOrgData,
      ];
}
