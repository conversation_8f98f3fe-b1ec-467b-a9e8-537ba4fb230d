import 'package:app_framework_component/app_framework.dart';

/// Loading user orgs loading state
const getUserOrgsLoading = LoadingState(
  'Loading user orgs in progress',
);

/// Reading org details loading state
const readOrgDataLoading = LoadingState(
  'Reading org data in progress',
);

/// Creating org loading state
const createOrgLoading = LoadingState(
  'Creating org in progress',
);

/// Updating org loading state
const updateOrgLoading = LoadingState(
  'Updating org in progress',
);

/// Updating org loading state
const updateOrgUsersLoading = LoadingState(
  'Updating org users in progress',
);

/// Loading org config loading state
const updateOrgConfigLoading = LoadingState(
  'Updating org config in progress',
);
