import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart' hide Key;
import 'package:logging/logging.dart' as logging;

import 'package:app_framework_component/app_framework.dart';
import 'package:user_space_model/user_space_model.dart';

import '../l10n/organization_localizations.dart';
import '../provider/organization_provider.dart';
import '../provider/exceptions.dart';

import 'organization_state.dart';
import 'loading_states.dart';

import '../model/organization.dart';

class OrganizationService extends Service<OrganizationState, OrgProvider> {
  final logging.Logger _logger = logging.Logger('OrganizationService');

  // Localizations for the identity service
  OrganizationLocalizations _localizations = lookupOrganizationLocalizations(
    const Locale('en'),
  );

  late final StreamSubscription<ProviderEvent> _providerEventSubscription;

  Timer? _subscriptionStatusPollingTimer;

  OrganizationService(
    OrgProvider provider,
  ) : super(
          initialState: const OrganizationState(),
          provider: provider,
        ) {
    // Listen to events from the provider
    // and update the state accordingly
    _providerEventSubscription = provider.providerEventStream.listen((event) {
      _handleEventsFromProvider(event);
    });
  }

  void _handleEventsFromProvider(
    ProviderEvent event,
  ) {
    _logger.fine('Recieved provider event: $event');
    switch (event) {
      default:
        _logger.warning('Ignoring unknown event: $event');
    }
  }

  @override
  void setCurrentContext(BuildContext context) {
    // Update the localizations for the identity
    // service from the given [context]
    _localizations = OrganizationLocalizations.of(context);
  }

  @override
  Future<void> close() async {
    _subscriptionStatusPollingTimer?.cancel();

    await _providerEventSubscription.cancel();
    return super.close();
  }

  Future<List<Organization>?> loadOrgs({
    bool notifyOnError = true,
  }) async {
    emit(state.startLoading(getUserOrgsLoading));

    try {
      final userOrgs = await provider.getOrgs();
      final organizations = userOrgs.map((ou) {
        return Organization.fromOrgUser(ou);
      }).toList();

      emit(
        state.copyWith(
          organizations: organizations,
        ),
      );
      return organizations;
    } on UserOrgsLoadError catch (_) {
      emit(
        state
            .addMessage(Message.error(
          _localizations.loadUserOrgsError,
        ))
            .copyWith(organizations: const []),
      );
    } catch (e) {
      _logger.severe('Error loading user orgs: $e');

      if (notifyOnError) {
        emit(
          state
              .addMessage(Message.error(_localizations.unknownError))
              .copyWith(organizations: const []),
        );
      }
    } finally {
      emit(state.endLoading(getUserOrgsLoading));
    }
    return null;
  }

  Future<Organization> readOrgData(Organization org) async {
    emit(state.startLoading(readOrgDataLoading));

    try {
      final orgData = await provider.getOrgData(org.orgId);
      final refreshedOrg = org.copyWithOrgData(orgData);
      emit(
        state.copyWith(
          selectedOrgData: orgData,
          organizations: state.organizations
              .map(
                (o) => o.orgId == org.orgId ? refreshedOrg : o,
              )
              .toList(),
        ),
      );
      return refreshedOrg;
    } on OrgDoesNotExistError catch (e) {
      _logger.severe('Error reading org data: $e');
      emit(
        state
            .addMessage(Message.error(
              _localizations.orgNotFoundError(org.name),
            ))
            .copyWith(resetSelectedOrgData: true),
      );
    } on OrgDataReadError catch (_) {
      emit(
        state
            .addMessage(Message.error(
              _localizations.readOrgDataError,
            ))
            .copyWith(selectedOrgData: null),
      );
    } catch (e) {
      _logger.severe('Error reading org data: $e');

      emit(
        state
            .addMessage(Message.error(
              _localizations.unknownError,
            ))
            .copyWith(selectedOrgData: null),
      );
    } finally {
      emit(state.endLoading(readOrgDataLoading));
    }

    return org;
  }

  Future<Organization?> createOrganization(
    String orgName,
    List<ContactInput>? contacts,
    List<AddressInput>? addresses,
    String? businessRef, {
    Key? orgKey,
  }) async {
    emit(state.startLoading(createOrgLoading));

    try {
      final orgIi = await provider.createOrg(
        orgName: orgName,
        orgKey: orgKey ?? Key(publicKey: ''),
        contacts: contacts,
        addresses: addresses,
        businessRef: businessRef,
      );
      emit(state.addMessage(
        Message.info(
          _localizations.createOrgSuccess(orgName),
        ),
      ));
      return Organization.fromOrgUser(orgIi.orgUser);
    } on OrgServiceError catch (_) {
      emit(
        state.addMessage(
          Message.error(
            _localizations.createOrgError,
          ),
        ),
      );
    } catch (e) {
      _logger.severe('Error creating organization: $e');

      emit(
        state.addMessage(Message.error(
          _localizations.unknownError,
        )),
      );
    } finally {
      emit(state.endLoading(createOrgLoading));
    }

    return null;
  }

  Future<void> updateOrganization(
    String orgId, {
    List<ContactInput>? contacts,
    List<AddressInput>? addresses,
    Key? orgKey,
  }) async {
    emit(state.startLoading(updateOrgLoading));

    try {
      await provider.updateOrg(
        orgId: orgId,
        orgKey: orgKey,
        contacts: contacts,
        addresses: addresses,
      );
      emit(state.copyWith(
        selectedOrgData: state.selectedOrgData?.copyWith(
          publicKey: orgKey?.publicKey,
          contacts: contacts
              ?.map((c) => Contact(
                    type: c.type,
                    description: c.description,
                    emailAddress: c.emailAddress,
                    phoneNumber: c.phoneNumber,
                  ))
              .toList(),
          addresses: addresses
              ?.map((c) => Address(
                    type: c.type,
                    description: c.description,
                    number: c.number,
                    street: c.street,
                    other: c.other,
                    municipality: c.municipality,
                    county: c.county,
                    province: c.province,
                    postalCode: c.postalCode,
                    countryCode: c.countryCode,
                  ))
              .toList(),
        ),
      ));
    } on OrgServiceError catch (_) {
      emit(
        state.addMessage(
          Message.error(
            _localizations.updateOrgError,
          ),
        ),
      );
    } catch (e) {
      _logger.severe('Error updating organization: $e');

      emit(
        state.addMessage(Message.error(
          _localizations.unknownError,
        )),
      );
    } finally {
      emit(state.endLoading(updateOrgLoading));
    }
  }

  Future<Organization> updateOrgUsers(
    Organization org, {
    List<String>? add,
    List<String>? remove,
    Key? orgKey,
  }) async {
    emit(state.startLoading(updateOrgUsersLoading));

    try {
      List<Future<OrgUser>> removeFutures = [];
      if (remove != null) {
        for (var userId in remove) {
          removeFutures.add(provider.deleteOrgUser(
            orgId: org.orgId,
            userId: userId,
          ));
        }
        await Future.wait<OrgUser>(removeFutures).onError((error, stackTrace) {
          emit(
            state.addMessage(
              Message.error(
                _localizations.removeOrgUsersError,
              ),
            ),
          );
          return [];
        });
      }

      List<Future<OrgUser>> addFutures = [];
      if (add != null) {
        for (var userId in add) {
          addFutures.add(provider.addOrgUser(
            orgId: org.orgId,
            userId: userId,
          ));
        }
        await Future.wait<OrgUser>(addFutures).onError((error, stackTrace) {
          emit(
            state.addMessage(
              Message.error(
                _localizations.addOrgUsersError,
              ),
            ),
          );
          return [];
        });
      }

      return readOrgData(org);
    } on OrgServiceError catch (_) {
      emit(
        state.addMessage(
          Message.error(
            _localizations.updateOrgError,
          ),
        ),
      );
    } catch (e) {
      _logger.severe('Error updating organization: $e');

      emit(
        state.addMessage(Message.error(
          _localizations.unknownError,
        )),
      );
    } finally {
      emit(state.endLoading(updateOrgUsersLoading));
    }

    return org;
  }

  Organization? getOrg(String orgId) {
    return state.organizations.firstWhere(
      (org) => org.orgId == orgId,
    );
  }

  Map<String, dynamic> getOrgConfig(
    String orgId, {
    required String key,
  }) {
    return getOrg(orgId)?.config[key] ?? {};
  }

  Future<void> updateOrgConfig(
    String orgId, {
    required String key,
    required Map<String, dynamic> config,
  }) async {
    emit(state.startLoading(updateOrgConfigLoading));

    final org = state.organizations.firstWhere(
      (org) => org.orgId == orgId,
    );
    final orgConfig = Map<String, dynamic>.from(org.config);
    orgConfig[key] = config;

    try {
      final orgConfigData = jsonEncode(orgConfig);
      await provider.updateOrg(
        orgId: orgId,
        config: orgConfigData,
      );
      emit(state.copyWith(
        organizations: state.organizations.map((o) {
          if (o.orgId == orgId) {
            return o.copyWith(config: jsonDecode(orgConfigData));
          }
          return o;
        }).toList(),
      ));
    } on OrgServiceError catch (_) {
      emit(
        state.addMessage(
          Message.error(
            _localizations.updateOrgConfigError,
          ),
        ),
      );
    } catch (e) {
      _logger.severe('Error updating organization config: $e');

      emit(
        state.addMessage(Message.error(
          _localizations.unknownError,
        )),
      );
    } finally {
      emit(state.endLoading(updateOrgConfigLoading));
    }
  }

  Future<bool> waitForSubscriptionUpdates(
    Organization org, {
    Duration? timeout,
    //
    // Timer is canceled when all of the following are true:
    //
    // 1. statuses is not null and the org's subscription
    //    status is not in the list
    List<SubscriptionStatus>? statuses,
    // 2. planIdUpdatedTo is not null and the org's
    //    subscription plan is not the same as the planIdUpdatedTo
    String? planIdUpdatedTo,
    // 3. cancelAtPeriodEndSet is not null and the org's
    //    subscription period end is not null
    bool? cancelAtPeriodEndSet,
    //
  }) async {
    _logger.fine(
      'Polling for updates to org(${org.orgId}) == $org.',
    );

    final completer = Completer<bool>();
    final timeoutAt = DateTime.now().add(
      timeout ?? const Duration(seconds: 30),
    );

    // Start a new polling timer
    _subscriptionStatusPollingTimer = Timer.periodic(
      const Duration(seconds: 5),
      (timer) {
        readOrgData(org).then(
          (org) {
            if (DateTime.now().isAfter(timeoutAt)) {
              // Timeout reached
              completer.complete(false);

              _subscriptionStatusPollingTimer = null;
              timer.cancel();
            } else {
              bool cancelTimer = true;

              if (statuses != null) {
                cancelTimer = statuses.contains(org.subscriptionStatus);
              }
              if (cancelTimer && planIdUpdatedTo != null) {
                cancelTimer = org.subscriptionPlan == planIdUpdatedTo;
              }
              if (cancelTimer && cancelAtPeriodEndSet != null) {
                cancelTimer = (cancelAtPeriodEndSet &&
                        org.subscriptionPeriodEnd != null) ||
                    (!cancelAtPeriodEndSet &&
                        org.subscriptionPeriodEnd == null);
              }

              if (cancelTimer) {
                completer.complete(true);

                _subscriptionStatusPollingTimer = null;
                timer.cancel();
              }
            }
          },
        ).onError((error, stackTrace) {
          _logger.severe(
            'Failed to poll for subscription status: $error',
            error,
            stackTrace,
          );
        });
      },
    );
    return completer.future;
  }
}
