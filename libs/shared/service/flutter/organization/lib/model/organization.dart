import 'dart:convert';
import 'package:equatable/equatable.dart';

import 'package:user_space_model/user_space_model.dart';

class Organization extends Equatable {
  static String billingKey = 'billing.id';
  static String subscriptionKey = 'subscription.id';
  static String subscriptionPlanKey = 'subscription.plan';
  static String subscriptionStatusKey = 'subscription.status';
  static String subscriptionPeriodEndKey = 'subscription.periodEnd';

  final String orgId;
  final String name;

  final bool isOwner;
  final bool isAdmin;

  final bool isVerified;

  final Map<String, dynamic> config;

  final List<OrgQuota> quotas;

  final String? billingId;
  final String? subscriptionId;
  final String? subscriptionPlan;
  final SubscriptionStatus? subscriptionStatus;
  final DateTime? subscriptionPeriodEnd;

  bool get valid => orgId.isNotEmpty;

  factory Organization.fromOrgUser(OrgUser orgUser) {
    assert(orgUser.org != null, 'OrgUser must have an org');

    return Organization(
      orgId: orgUser.org!.orgId,
      name: orgUser.org!.orgName,
      isOwner: orgUser.isOwner ?? false,
      isAdmin: orgUser.isAdmin ?? false,
      isVerified: orgUser.org?.verified ?? false,
      config: jsonDecode(orgUser.org?.config ?? '{}'),
      quotas: orgUser.org?.quotas
              ?.map(
                (q) => q!,
              )
              .toList() ??
          [],
      billingId: orgUser.org?.externalRefs
          ?.firstWhere(
            (ref) => ref?.name == Organization.billingKey,
            orElse: () => _nvNull,
          )
          ?.value,
      subscriptionId: orgUser.org?.externalRefs
          ?.firstWhere(
            (ref) => ref?.name == Organization.subscriptionKey,
            orElse: () => _nvNull,
          )
          ?.value,
      subscriptionPlan: orgUser.org?.externalRefs
          ?.firstWhere(
            (ref) => ref?.name == Organization.subscriptionPlanKey,
            orElse: () => _nvNull,
          )
          ?.value,
      subscriptionStatus: SubscriptionStatus.fromString(
        orgUser.org?.externalRefs
            ?.firstWhere(
              (ref) => ref?.name == Organization.subscriptionStatusKey,
              orElse: () => _nvNull,
            )
            ?.value,
      ),
      subscriptionPeriodEnd: _parseSubscriptionPeriodEnd(
        orgUser.org?.externalRefs,
      ),
    );
  }

  factory Organization.invalid() {
    return const Organization(
      orgId: '',
      name: '',
      isOwner: false,
      isAdmin: false,
      isVerified: false,
      config: {},
      quotas: [],
    );
  }

  const Organization({
    required this.orgId,
    required this.name,
    required this.isOwner,
    required this.isAdmin,
    required this.isVerified,
    required this.config,
    required this.quotas,
    this.billingId,
    this.subscriptionId,
    this.subscriptionPlan,
    this.subscriptionStatus,
    this.subscriptionPeriodEnd,
  });

  Organization copyWith({
    String? orgId,
    String? name,
    bool? isOwner,
    bool? isAdmin,
    bool? isVerified,
    Map<String, dynamic>? config,
    List<OrgQuota>? quotas,
    String? billingId,
    String? subscriptionId,
    String? subscriptionPlan,
    SubscriptionStatus? subscriptionStatus,
    DateTime? subscriptionPeriodEnd,
    bool resetSubscriptionPeriodEnd = false,
  }) {
    return Organization(
      orgId: orgId ?? this.orgId,
      name: name ?? this.name,
      isOwner: isOwner ?? this.isOwner,
      isAdmin: isAdmin ?? this.isAdmin,
      isVerified: isVerified ?? this.isVerified,
      config: config ?? this.config,
      quotas: quotas ?? this.quotas,
      billingId: billingId ?? this.billingId,
      subscriptionId: subscriptionId ?? this.subscriptionId,
      subscriptionPlan: subscriptionPlan ?? this.subscriptionPlan,
      subscriptionStatus: subscriptionStatus ?? this.subscriptionStatus,
      subscriptionPeriodEnd: resetSubscriptionPeriodEnd
          ? null
          : subscriptionPeriodEnd ?? this.subscriptionPeriodEnd,
    );
  }

  Organization copyWithOrgData(Org orgData) {
    final subscriptionPeriodEnd = _parseSubscriptionPeriodEnd(
      orgData.externalRefs,
    );
    return copyWith(
      isVerified: orgData.verified ?? false,
      config: jsonDecode(orgData.config ?? '{}'),
      quotas: orgData.quotas
              ?.map(
                (q) => q!,
              )
              .toList() ??
          [],
      billingId: orgData.externalRefs
          ?.firstWhere(
            (ref) => ref?.name == Organization.billingKey,
            orElse: () => _nvNull,
          )
          ?.value,
      subscriptionId: orgData.externalRefs
          ?.firstWhere(
            (ref) => ref?.name == Organization.subscriptionKey,
            orElse: () => _nvNull,
          )
          ?.value,
      subscriptionPlan: orgData.externalRefs
          ?.firstWhere(
            (ref) => ref?.name == Organization.subscriptionPlanKey,
            orElse: () => _nvNull,
          )
          ?.value,
      subscriptionStatus: SubscriptionStatus.fromString(
        orgData.externalRefs
            ?.firstWhere(
              (ref) => ref?.name == Organization.subscriptionStatusKey,
              orElse: () => _nvNull,
            )
            ?.value,
      ),
      subscriptionPeriodEnd: subscriptionPeriodEnd,
      resetSubscriptionPeriodEnd: subscriptionPeriodEnd == null,
    );
  }

  @override
  List<Object?> get props => [
        orgId,
        name,
        isOwner,
        isAdmin,
        isVerified,
        config,
        quotas,
        billingId,
        subscriptionId,
        subscriptionPlan,
        subscriptionStatus,
        subscriptionPeriodEnd,
      ];
}

DateTime? _parseSubscriptionPeriodEnd(List<Nv?>? externalRefs) {
  final value = externalRefs
      ?.firstWhere(
        (ref) => ref?.name == Organization.subscriptionPeriodEndKey,
        orElse: () => _nvNull,
      )
      ?.value;

  if (value != null) {
    return DateTime.fromMillisecondsSinceEpoch(
      int.parse(value) * 1000,
    );
  } else {
    return null;
  }
}

Nv _nvNull = Nv(name: '', value: null);
