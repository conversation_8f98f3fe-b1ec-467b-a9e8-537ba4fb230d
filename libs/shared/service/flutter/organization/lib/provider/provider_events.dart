import 'package:equatable/equatable.dart';

import 'package:app_framework_component/app_framework.dart';
import 'package:user_space_model/models/user_space.dart';

/// The [UserOrgsLoadErrorEvent] is a [ProviderEvent] emitted
/// when there was an error loading the current user's orgs.
final class UserOrgsLoadErrorEvent extends ErrorEventType {
  const UserOrgsLoadErrorEvent(super.error);
}

/// The [UserOrgEventType] is a [ProviderEvent] and is the base
/// class for all events related to a user's org.
sealed class UserOrgEventType extends Equatable implements ProviderEvent {
  final OrgUser orgUser;

  const UserOrgEventType(this.orgUser);

  @override
  List<Object?> get props => [orgUser];

  @override
  String toString() => '$runtimeType: '
      'OrgUser($orgUser)';
}

/// The [OrgEventType] is a [ProviderEvent] and is the base
/// class for all events related to a user's org.
sealed class OrgEventType extends Equatable implements ProviderEvent {
  final Org org;

  const OrgEventType(this.org);

  @override
  List<Object?> get props => [org];

  @override
  String toString() => '$runtimeType: '
      'Org($org)';
}
