import 'dart:async';
import 'dart:convert';
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:logging/logging.dart' as logging;

import 'package:app_framework_component/app_framework.dart';
import 'package:user_space_model/models/user_space.dart';

import 'organization_provider.dart';
import 'exceptions.dart';

class UserSpaceOrgService implements OrgProvider {
  static final logging.Logger _logger = logging.Logger('UserSpaceOrgService');

  // UserSpaceOrgService singleton instance
  static UserSpaceOrgService? _instance;

  // Amplify API category abstraction
  final APICategory _amplifyApi;

  @override
  Stream<ProviderEvent> get providerEventStream => _providerEventStream.stream;
  final _providerEventStream = StreamController<ProviderEvent>.broadcast();

  factory UserSpaceOrgService({
    APICategory? api,
  }) {
    _instance = _instance ?? UserSpaceOrgService._(api: api);
    return _instance!;
  }

  UserSpaceOrgService._({
    APICategory? api,
  }) : _amplifyApi = api ?? Amplify.API;

  @override
  Future<List<OrgUser>> getOrgs() async {
    final operation = _amplifyApi.query<String>(
      request: GraphQLRequest(
        document: '''
          query GetOrgs {
            getUser {
              orgs {
                orgUsers {
                  org {
                    orgID
                    orgName
                    verified
                    config
                    externalRefs {
                      name
                      value
                    }
                    quotas {
                      quotaName
                      description
                      period
                      softLimit
                      limit
                      used
                    }
                  }
                  isOwner
                  isAdmin
                  status
                }
              }
            }
          }
        ''',
      ),
    );

    try {
      final response = await operation.response;
      if (response.hasErrors) {
        final errorPayload = jsonEncode(response.errors);
        _logger.severe(
          'Failed to retrieve orgs for current user: $errorPayload',
        );

        throw UserOrgsLoadError(
          message: 'Failed to retrieve orgs current user is a member of',
        );
      } else {
        final data = jsonDecode(response.data!);

        final orgUsersData = data['getUser']?['orgs']?['orgUsers'];
        if (orgUsersData != null) {
          final orgUsers = (orgUsersData as List)
              .map((orgUserData) => OrgUser.fromJson(orgUserData))
              .toList();

          return orgUsers;
        }
      }
    } catch (e, stackTrace) {
      _logger.severe(
        'Exception when invoking service call to '
        'retrieve orgs for current user: $e',
      );

      throw OrgServiceError(
        message: 'Failed to retrieve orgs current user is a member of',
        innerException: e is Exception ? e : Exception(e),
        innerStackTrace: stackTrace,
      );
    }
    return [];
  }

  @override
  Future<Org> getOrgData(String orgId) async {
    final operation = _amplifyApi.query<String>(
      request: GraphQLRequest(
        document: '''
          query GetOrg(\$orgID: ID!) {
            getOrg(orgID: \$orgID) {
              org {
                orgID
                orgName
                businessRef
                publicKey
                certificate
                verified
                config
                owner {
                  userID
                  userName
                  firstName
                  middleName
                  familyName
                }
                admins {
                  userID
                  userName
                  firstName
                  middleName
                  familyName
                }
                contacts {
                  type
                  description
                  emailAddress
                  phoneNumber
                }
                addresses {
                  type
                  description
                  number
                  street
                  other
                  municipality
                  county
                  province
                  postalCode
                  countryCode
                }
                externalRefs {
                  name
                  value
                }
                quotas {
                  quotaName
                  description
                  period
                  softLimit
                  limit
                  used
                }
                users {
                  orgUsers {
                    user {
                      userID
                      userName
                      firstName
                      middleName
                      familyName
                      emailAddress
                    }
                    isOwner
                    isAdmin
                    status
                  }
                }
              }
            }
          }
        ''',
        variables: {
          'orgID': orgId,
        },
      ),
    );

    final response = await operation.response.onError((e, stackTrace) {
      _logger.severe(
        'Exception when invoking service call to '
        'read org data for org with id "$orgId": $e',
      );

      throw OrgServiceError(
        message: 'Failed to retrieve org data for org with id: $orgId',
        innerException: e is Exception ? e : Exception(e),
        innerStackTrace: stackTrace,
      );
    });

    if (response.hasErrors) {
      final errorPayload = jsonEncode(response.errors);
      _logger.severe('Failed to retrieve org: $errorPayload');

      if (response.errors.any(
        (error) => error.message.contains(
          'Given org was not found',
        ),
      )) {
        throw OrgDoesNotExistError(orgId: orgId);
      } else {
        throw OrgDataReadError(orgId: orgId);
      }
    }

    final data = response.data;
    return Org.fromJson(jsonDecode(data!)['getOrg']['org']);
  }

  @override
  Future<OrgIi> createOrg({
    required String orgName,
    required Key orgKey,
    List<ContactInput>? contacts,
    List<AddressInput>? addresses,
    String? businessRef,
  }) async {
    final operation = _amplifyApi.query<String>(
      request: GraphQLRequest(
        document: '''
          mutation AddOrg(\$orgName: String!, \$orgKey: Key!, \$contacts: [ContactInput], \$addresses: [AddressInput], \$businessRef: String) {
            addOrg(orgName: \$orgName, orgKey: \$orgKey, contacts: \$contacts, addresses: \$addresses, businessRef: \$businessRef) {
              idKey
              orgUser {
                org {
                  orgID
                  orgName
                  verified
                  config
                  quotas {
                    quotaName
                    description
                    period
                    softLimit
                    limit
                    used
                  }
                }
                user {
                  userID
                  userName
                }
                isOwner
                isAdmin
                status
              }
            }
          }
        ''',
        variables: {
          'orgName': orgName,
          'orgKey': orgKey,
          'contacts': contacts,
          'addresses': addresses,
          'businessRef': businessRef,
        },
      ),
    );
    final response = await operation.response.onError((e, stackTrace) {
      _logger.severe(
        'Exception when invoking service call to '
        'create org with name "$orgName": $e',
      );

      throw OrgServiceError(
        message: 'Failed to create org with name: $orgName',
        innerException: e is Exception ? e : Exception(e),
        innerStackTrace: stackTrace,
      );
    });

    if (response.hasErrors) {
      final errorPayload = jsonEncode(response.errors);
      _logger.severe('Failed to create org: $errorPayload');

      throw OrgServiceError(
        message: 'Failed to create org with name: $orgName',
      );
    }

    final data = response.data;
    _logger.finest('Org created: $data');

    return OrgIi.fromJson(jsonDecode(data!)['addOrg']);
  }

  @override
  Future<Org> updateOrg({
    required String orgId,
    Key? orgKey,
    List<ContactInput>? contacts,
    List<AddressInput>? addresses,
    String? config,
  }) async {
    final operation = _amplifyApi.query<String>(
      request: GraphQLRequest(
        document: '''
          mutation UpdateOrg(\$orgID: ID!, \$orgKey: Key, \$contacts: [ContactInput], \$addresses: [AddressInput], \$config: String) {
            updateOrg(orgID: \$orgID, orgKey: \$orgKey, contacts: \$contacts, addresses: \$addresses, config: \$config) {
              orgID
              orgName
              businessRef
              publicKey
              certificate
              verified
              config
              contacts {
                type
                description
                emailAddress
                phoneNumber
              }
              addresses {
                type
                description
                number
                street
                other
                municipality
                county
                province
                postalCode
                countryCode
              }
              quotas {
                quotaName
                description
                period
                softLimit
                limit
                used
              }
            }
          }
        ''',
        variables: {
          'orgID': orgId,
          'orgKey': orgKey,
          'contacts': contacts,
          'addresses': addresses,
          'config': config,
        },
      ),
    );
    final response = await operation.response.onError((e, stackTrace) {
      _logger.severe(
        'Exception when invoking service call to '
        'update org with org id "$orgId": $e',
      );

      throw OrgServiceError(
        message: 'Failed to create org with id: $orgId',
        innerException: e is Exception ? e : Exception(e),
        innerStackTrace: stackTrace,
      );
    });

    if (response.hasErrors) {
      final errorPayload = jsonEncode(response.errors);
      _logger.severe('Failed to update org: $errorPayload');

      if (response.errors.any(
        (error) => error.message.contains(
          'not associated with the given org',
        ),
      )) {
        throw OrgDoesNotExistError(orgId: orgId);
      } else {
        throw OrgDataUpdateError(orgId: orgId);
      }
    }

    final data = response.data;
    _logger.finest('Org updated: $data');

    return Org.fromJson(jsonDecode(data!)['updateOrg']);
  }

  @override
  Future<OrgUser> addOrgUser({
    required String orgId,
    required String userId,
    bool isAdmin = false,
  }) async {
    final operation = _amplifyApi.query<String>(
      request: GraphQLRequest(
        document: '''
          mutation AddOrgUser(\$orgID: ID!, \$userID: ID!, \$isAdmin: Boolean) {
            addOrgUser(orgID: \$orgID, userID: \$userID, isAdmin: \$isAdmin) {
              user {
                userID
                userName
                firstName
                middleName
                familyName
                emailAddress
              }
              org {
                orgID
                orgName
              }
              isOwner
              isAdmin
              status
            }
          }
        ''',
        variables: {
          'orgID': orgId,
          'userID': userId,
          'isAdmin': isAdmin,
        },
      ),
    );
    final response = await operation.response.onError((e, stackTrace) {
      _logger.severe(
        'Exception when invoking service call to '
        'add org user "$userId" to org "$orgId": $e',
      );

      throw OrgServiceError(
        message: 'Failed to add org user "$userId" to org "$orgId"',
        innerException: e is Exception ? e : Exception(e),
        innerStackTrace: stackTrace,
      );
    });

    if (response.hasErrors) {
      final errorPayload = jsonEncode(response.errors);
      _logger.severe('Failed to add org user: $errorPayload');

      if (response.errors.any(
        (error) =>
            error.message.contains(
              'not associated with the given org',
            ) ||
            error.message.contains(
              'does not own org',
            ),
      )) {
        throw OrgDoesNotExistError(orgId: orgId);
      } else if (response.errors.any(
        (error) => error.message.contains(
          'already associated with org',
        ),
      )) {
        throw OrgUserExistsError(orgId: orgId, userId: userId);
      } else {
        throw OrgDataUpdateError(orgId: orgId);
      }
    }

    final data = response.data;
    _logger.finest('Org user added: $data');

    return OrgUser.fromJson(jsonDecode(data!)['addOrgUser']);
  }

  @override
  Future<OrgUser> deleteOrgUser({
    required String orgId,
    required String userId,
  }) async {
    final operation = _amplifyApi.query<String>(
      request: GraphQLRequest(
        document: '''
          mutation DeleteOrgUser(\$orgID: ID!, \$userID: ID!) {
            deleteOrgUser(orgID: \$orgID, userID: \$userID) {
              user {
                userID
                userName
                firstName
                middleName
                familyName
                emailAddress
              }
              org {
                orgID
                orgName
              }
              isOwner
              isAdmin
              status
            }
          }
        ''',
        variables: {
          'orgID': orgId,
          'userID': userId,
        },
      ),
    );
    final response = await operation.response.onError((e, stackTrace) {
      _logger.severe(
        'Exception when invoking service call to '
        'delete org user "$userId" to org "$orgId": $e',
      );

      throw OrgServiceError(
        message: 'Failed to delete org user "$userId" to org "$orgId"',
        innerException: e is Exception ? e : Exception(e),
        innerStackTrace: stackTrace,
      );
    });

    if (response.hasErrors) {
      final errorPayload = jsonEncode(response.errors);
      _logger.severe('Failed to delete org user: $errorPayload');

      if (response.errors.any(
        (error) =>
            error.message.contains(
              'not associated with the given org',
            ) ||
            error.message.contains(
              'can only delete org users from orgs he/she owns',
            ),
      )) {
        throw OrgDoesNotExistError(orgId: orgId);
      } else {
        throw OrgDataUpdateError(orgId: orgId);
      }
    }

    final data = response.data;
    _logger.finest('Org user delete: $data');

    return OrgUser.fromJson(jsonDecode(data!)['deleteOrgUser']);
  }

  @override
  Future<void> deleteOrg(String orgId) async {
    final operation = _amplifyApi.query<String>(
      request: GraphQLRequest(
        document: '''
          mutation DeleteOrg(\$orgID: ID!) {
            deleteOrg(orgID: \$orgID)
          }
        ''',
        variables: {
          'orgID': orgId,
        },
      ),
    );
    final response = await operation.response.onError((e, stackTrace) {
      _logger.severe(
        'Exception when invoking service call to '
        'delete org with org id "$orgId": $e',
      );

      throw OrgServiceError(
        message: 'Failed to delete org with id: $orgId',
        innerException: e is Exception ? e : Exception(e),
        innerStackTrace: stackTrace,
      );
    });

    if (response.hasErrors) {
      final errorPayload = jsonEncode(response.errors);
      _logger.severe('Failed to delete org: $errorPayload');

      if (response.errors.any(
        (error) => error.message.contains(
          'not associated with the given org',
        ),
      )) {
        throw OrgDoesNotExistError(orgId: orgId);
      } else {
        throw OrgDeleteError(orgId: orgId);
      }
    }

    final data = response.data;
    _logger.finest('Org deleted: $data');
  }

  @override
  Future<void> initialize() async {
    // NoOp
  }

  @override
  Future<void> dispose() async {
    await _providerEventStream.close();
  }
}
