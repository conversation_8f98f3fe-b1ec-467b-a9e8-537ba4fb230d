import 'package:flutter/foundation.dart';

import 'package:utilities_ab/error/app_exception.dart';

class OrgServiceError extends AppException {
  OrgServiceError({
    required super.message,
    super.innerException,
    super.innerStackTrace,
    bool log = kDebugMode,
  }) : super(
          stackTrace: StackTrace.current,
        );
}

class UserOrgsLoadError extends AppException {
  UserOrgsLoadError({
    required super.message,
    bool log = kDebugMode,
  }) : super(
          stackTrace: StackTrace.current,
        );
}

class OrgDoesNotExistError extends AppException {
  OrgDoesNotExistError({
    required String orgId,
    bool log = kDebugMode,
  }) : super(
          message: 'Organization with id "$orgId" does not exist',
          stackTrace: StackTrace.current,
        );
}

class OrgUserExistsError extends AppException {
  OrgUserExistsError({
    required String orgId,
    required String userId,
    bool log = kDebugMode,
  }) : super(
          message: 'Organization with id "$orgId" '
              'already has a user with id "$userId"',
          stackTrace: StackTrace.current,
        );
}

class OrgDataReadError extends AppException {
  OrgDataReadError({
    required String orgId,
    bool log = kDebugMode,
  }) : super(
          message: 'Failed to delete org with id: $orgId',
          stackTrace: StackTrace.current,
        );
}

class OrgDataUpdateError extends AppException {
  OrgDataUpdateError({
    required String orgId,
    bool log = kDebugMode,
  }) : super(
          message: 'Failed to update org with id: $orgId',
          stackTrace: StackTrace.current,
        );
}

class OrgDeleteError extends AppException {
  OrgDeleteError({
    required String orgId,
    bool log = kDebugMode,
  }) : super(
          message: 'Failed to delete org with id: $orgId',
          stackTrace: StackTrace.current,
        );
}
