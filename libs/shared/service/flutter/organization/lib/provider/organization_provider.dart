import 'package:app_framework_component/app_framework.dart';
import 'package:user_space_model/user_space_model.dart';

abstract interface class OrgProvider implements RepositoryProvider {
  // User-Space organization service

  // Queries

  /// Retrieves all the orgs the current user
  /// is a part of.
  Future<List<OrgUser>> getOrgs();

  /// Retrieves the org details for the given
  /// org id
  Future<Org> getOrgData(String orgId);

  // Mutations

  /// Create a new organization with the given
  /// information. Current logged in user will
  /// be added as the owner and admin of the
  /// organization.
  Future<OrgIi> createOrg({
    required String orgName,
    required Key orgKey,
    List<ContactInput>? contacts,
    List<AddressInput>? addresses,
    String? businessRef,
  });

  /// Update the org with the given org id.
  /// The org name and business reference
  /// cannot be updated as they are immutable
  /// once an org has been created and
  /// verified.
  Future<Org> updateOrg({
    required String orgId,
    Key? orgKey,
    List<ContactInput>? contacts,
    List<AddressInput>? addresses,
    String? config,
  });

  /// Add a user to the org with the given
  /// org id. The user will be added as a
  /// member of the org.
  Future<OrgUser> addOrgUser({
    required String orgId,
    required String userId,
    bool isAdmin = false,
  });

  /// Remove a user from the org with the
  /// given org id.
  Future<OrgUser> deleteOrgUser({
    required String orgId,
    required String userId,
  });

  /// Deletes the the org with the given
  /// org id
  Future<void> deleteOrg(String orgId);
}
