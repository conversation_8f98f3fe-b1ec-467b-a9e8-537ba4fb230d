// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'organization_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class OrganizationLocalizationsEn extends OrganizationLocalizations {
  OrganizationLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get loadUserOrgsError =>
      'Failed to load organizations user is a member of. Please contact support if this error persists.';

  @override
  String orgNotFoundError(String orgName) {
    return 'Organization with name \"$orgName\" was not found.';
  }

  @override
  String get readOrgDataError =>
      'Error loading organization details. Please contact support if this error persists.';

  @override
  String createOrgSuccess(Object orgName) {
    return 'Organization named \"$orgName\" was created. You will be able to manage it once verified.';
  }

  @override
  String get createOrgError =>
      'Error creating new organization. Please contact support if this error persists.';

  @override
  String get updateOrgError =>
      'Error updating organization. Please contact support if this error persists.';

  @override
  String get addOrgUsersError =>
      'Error adding users to the organization. Please contact support if this error persists.';

  @override
  String get removeOrgUsersError =>
      'Error removing users from the organization. Please contact support if this error persists.';

  @override
  String get updateOrgConfigError =>
      'Error updating organization config. Please contact support if this error persists.';

  @override
  String get unknownError =>
      'An unknown error occurred. Please contact support.';
}
