{"@@locale": "en", "loadUserOrgsError": "Failed to load organizations user is a member of. Please contact support if this error persists.", "orgNotFoundError": "Organization with name \"{orgName}\" was not found.", "@orgNotFoundError": {"placeholders": {"orgName": {"type": "String"}}}, "readOrgDataError": "Error loading organization details. Please contact support if this error persists.", "createOrgSuccess": "Organization named \"{orgName}\" was created. You will be able to manage it once verified.", "createOrgError": "Error creating new organization. Please contact support if this error persists.", "updateOrgError": "Error updating organization. Please contact support if this error persists.", "addOrgUsersError": "Error adding users to the organization. Please contact support if this error persists.", "removeOrgUsersError": "Error removing users from the organization. Please contact support if this error persists.", "updateOrgConfigError": "Error updating organization config. Please contact support if this error persists.", "unknownError": "An unknown error occurred. Please contact support."}