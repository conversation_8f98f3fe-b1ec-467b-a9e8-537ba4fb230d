{"scripts": {"generate-nix": "rm -f lib/models/user_space.* && graphql-codegen && sed -i 's/const //g' lib/models/user_space.dart && dart run build_runner build -d", "generate-macos": "rm -f lib/models/user_space.* && graphql-codegen && sed -i '' 's/const //g' lib/models/user_space.dart && dart run build_runner build -d"}, "dependencies": {"graphql": "^16.10.0"}, "devDependencies": {"@graphql-codegen/cli": "^5.0.7", "@graphql-codegen/flutter-freezed": "^4.0.1", "typescript": "^5.8.2"}}