#
# User-Space Model API
#
schema {
  query: Query
  mutation: Mutation
  subscription: Subscription
}

# Query Root
type Query {
  mycsCloudProps: MyCSCloudProps

  # Returns a list of users matching
  # the given filter
  userSearch(filter: UserSearchFilterInput!, limit: Int): [UserRef]!

  # Authenticates and authorizes the
  # current logged in user on the device
  # identified by the given key. The
  # response returns the user's access
  # type on the device and if the user
  # has admin access the full device
  # information will be returned.
  authDevice(idKey: String!): DeviceAuth

  # Return the logged in user
  getUser: User
  # Gets a device associated with the
  # logged in user
  getDevice(deviceID: ID!): DeviceUser
  # Gets an org associated with
  # the logged in user
  getOrg(orgID: ID!): OrgUser
  # Gets a space associated with the
  # logged in user
  getSpace(spaceID: ID!): SpaceUser
  # Gets an app associated with the
  # logged in user
  getApp(appID: ID!): AppUser

  # Gets all device access requests for
  # devices owned by the logged in User
  getDeviceAccessRequests(deviceID: ID!): [DeviceUser]
  # Gets all space invitations recieved
  # by the logged in User
  getSpaceInvitations: [SpaceUser]
}

# Mutation Root
type Mutation {
  # Update the logged in user's key
  updateUserKey(userKey: Key!): User
  # Update the logged in user's universal config
  updateUserConfig(universalConfig: String!, asOf: String!): String!

  # Add a new device for the logged in user
  addDevice(deviceName: String!, deviceInfo: DeviceInfo!, deviceKey: Key!): DeviceII
  # Add logged in user to the given device
  addDeviceUser(deviceID: ID!, userID: ID): DeviceUser
  # Activate a user's access to a device
  activateDeviceUser(deviceID: ID!, userID: ID!): DeviceUser

  # Remove the logged in user from the device. If
  # the logged in user is the device owner then
  # he/she can remove the given user from the
  # device.
  deleteDeviceUser(deviceID: ID!, userID: ID): DeviceUser
  # Delete an owned device. Returns userIDs of
  # all associated users deleted as a result.
  deleteDevice(deviceID: ID!): [ID]

  # Update device
  updateDevice(deviceID: ID!, deviceInfo: DeviceInfo!, deviceKey: Key, settings: String): Device

  # Sets user's config for accessing a space with a particular device
  setDeviceUserSpaceConfig(userID: ID, deviceID: ID!, spaceID: ID!, config: UserAccessConfig!): DeviceUserSpaceConfig
  deleteDeviceUserSpaceConfig(userID: ID, deviceID: ID!, spaceID: ID!): DeviceUserSpaceConfig

  # Add new org for the logged in user
  addOrg(orgName: String!, orgKey: Key!, contacts: [ContactInput], addresses: [AddressInput], businessRef: String): OrgII
  # Add logged in user to the given device
  addOrgUser(orgID: ID!, userID: ID!, isAdmin: Boolean): OrgUser
  # Activates a org user in the
  # org owned by the logged in user
  activateOrgUser(orgID: ID!, userID: ID): OrgUser
  # Deactivates a org user from the
  # org owned  by the logged in user
  deactivateOrgUser(orgID: ID!, userID: ID!): OrgUser
  # Delete an org user from the
  # org owned by the logged in user
  deleteOrgUser(orgID: ID!, userID: ID): OrgUser
  # Deletes an owned org. Returns
  # IDs of associated users deleted.
  deleteOrg(orgID: ID!): [ID]

  # Update org
  updateOrg(orgID: ID!, orgKey: Key, contacts: [ContactInput], addresses: [AddressInput], config: String): Org
  # Update org user
  updateOrgUser(orgID: ID!, userID: ID, isAdmin: Boolean): OrgUser

  # Claim org quota
  claimOrgQuota(orgID: ID!, quotaName: String!, value: Int!, commit: Boolean): String
    @aws_cognito_user_pools @aws_iam
  # Commit org quota
  commitOrgQuota(orgID: ID!, quotaName: String!, claimID: String!): Boolean
    @aws_cognito_user_pools @aws_iam
  # Return org quota
  returnOrgQuota(orgID: ID!, quotaName: String!, claimID: String!): Boolean
    @aws_cognito_user_pools @aws_iam

  # Add new space for the logged in user
  addSpace(spaceName: String!, spaceKey: Key!, cookbook: String!, recipe: String!, iaas: String!, region: String!, isEgressNode: Boolean!, orgID: ID): SpaceII
  # Invite a user to connect and use space services
  addSpaceUser(spaceID: ID!, userID: ID!, isAdmin: Boolean, canUseSpaceForEgress: Boolean, enableSiteBlocking: Boolean): SpaceUser
  # Activates a space user in the space owned
  # by the logged in user
  activateSpaceUser(spaceID: ID!, userID: ID!): SpaceUser
  # Deactivates a space user from the space owned
  # by the logged in user
  deactivateSpaceUser(spaceID: ID!, userID: ID!): SpaceUser
  # Delete a space user from the space owned by
  # the logged in user
  deleteSpaceUser(spaceID: ID!, userID: ID): SpaceUser
  # Deletes an owned space. Returns IDs of
  # all associated users deleted.
  deleteSpace(spaceID: ID!): [ID]

  # Accept the invitation for the logged in user
  # to connect and use space services
  acceptSpaceUserInvitation(spaceID: ID!): SpaceUser
  # Deactivates the current logged in user's
  # access to the given space
  leaveSpaceUser(spaceID: ID!): SpaceUser

  # Update space
  updateSpace(spaceID: ID!, spaceKey: Key, version: String, settings: String): Space
  # Update space user
  updateSpaceUser(spaceID: ID!, userID: ID, isAdmin: Boolean, canUseSpaceForEgress: Boolean, enableSiteBlocking: Boolean): SpaceUser

  # Add new app for the logged in user
  addApp(appName: String!, appKey: Key!, cookbook: String!, recipe: String!, iaas: String!, region: String!, spaceID: ID!): AppII
  # Add app user
  addAppUser(appID: ID!, userID: ID!): AppUser
  # Delete app user
  deleteAppUser(appID: ID!, userID: ID): AppUser
  # Delete app
  deleteApp(appID: ID!): [ID]

  # Asynchronously pushes a data payload of a
  # given type to the platform. The only response
  # expected will be a success of fail
  publishData(data: [PublishDataInput!]!): [PublishResult]

  # Mutations to data entities attached to
  # subscriptions that will push changes to
  # clients with active subscriptions to
  # these entitites.
  pushUsersUpdate(data: String!): UserUpdate
    @aws_iam
  pushDevicesUpdate(data: String!): DeviceUpdate
    @aws_iam
  pushDeviceUsersUpdate(data: String!): DeviceUserUpdate
    @aws_iam
  pushSpacesUpdate(data: String!): SpaceUpdate
    @aws_iam
  pushSpaceUsersUpdate(data: String!): SpaceUserUpdate
    @aws_iam
  pushAppsUpdate(data: String!): AppUpdate
    @aws_iam
  pushAppUsersUpdate(data: String!): AppUserUpdate
    @aws_iam

  # Subscription keep alive call
  touchSubscriptions(subs: [String!]): AWSTimestamp
}

type Subscription {
  userUpdates(userID: ID!): UserUpdate
    @aws_subscribe(mutations: ["pushUsersUpdate"])

  deviceUpdates(deviceID: ID!): DeviceUpdate
    @aws_subscribe(mutations: ["pushDevicesUpdate"])

  deviceUserUpdates(deviceID: ID!, userID: ID!): DeviceUserUpdate
    @aws_subscribe(mutations: ["pushDeviceUsersUpdate"])

  spaceUpdates(spaceID: ID!): SpaceUpdate
    @aws_subscribe(mutations: ["pushSpacesUpdate"])

  spaceUserUpdates(spaceID: ID!, userID: ID!): SpaceUserUpdate
    @aws_subscribe(mutations: ["pushSpaceUsersUpdate"])

  appUpdates(appID: ID!): AppUpdate
    @aws_subscribe(mutations: ["pushAppsUpdate"])

  appUserUpdates(appID: ID!, userID: ID!): AppUserUpdate
    @aws_subscribe(mutations: ["pushAppUsersUpdate"])
}

# MyCS User Space cloud global properties
type MyCSCloudProps {
  publicKeyID: String
  publicKey: String
}

# A user within the My Cloud Space eco-system can
# create and administer spaces they own as well as
# invite other users to securely share resources
# within their space.
type User @aws_cognito_user_pools @aws_iam {
  userID: ID!
  userName: String

  firstName: String
  middleName: String
  familyName: String
  preferredName: String

  emailAddress: AWSEmail
  mobilePhone: String
  confirmed: Boolean

  # External references
  externalRefs: [Nv]

  publicKey: String
  certificate: String

  devices(limit: Int, next: CursorInput): DeviceUsersConnection
  orgs(limit: Int, next: CursorInput): OrgUsersConnection
  spaces(limit: Int, next: CursorInput): SpaceUsersConnection
  apps(limit: Int, next: CursorInput): AppUsersConnection

  # A user's universal config is an encrypted
  # document containing metadata of all spaces the
  # user owns.
  universalConfig: String
}

# A user reference type returns returns a
# user entry as the response to a user search
# when looking up users to connect to spaces
# and apps of the current logged in user or
# used reference to a user
type UserRef {
  userID: ID!
  userName: String
  firstName: String
  middleName: String
  familyName: String
}

enum UserAccessStatus {
  pending,
  active,
  inactive
}

# The device a user has configured to be able to
# connect to personal and shared spaces.
type Device @aws_cognito_user_pools @aws_iam {
  deviceID: ID!
  deviceName: String

  owner: UserRef

  # device info
  deviceType: String
  clientVersion: String

  publicKey: String
  certificate: String

  # common configuration associated
  # with the device such as default
  # settings for new device users
  settings: String

  # ID of managing device if this
  # is a managed device. '-' if not.
  managedBy: String
  # managed devices
  managedDevices: [Device]

  users(limit: Int, next: CursorInput): DeviceUsersConnection
}

input DeviceInfo {
  deviceType: String!
  clientVersion: String!

  # ID of device that manages this
  # device
  managedBy: String
}

type DeviceUsersConnection {
  pageInfo: PageInfo
  edges: [DeviceUsersEdge]
  totalCount: Int
  deviceUsers: [DeviceUser]
}
type DeviceUsersEdge {
  node: DeviceUser!
}

# This entity associates a particular device with
# a user. More than one device may be associated
# with a user and a device may be used by multiple
# users.
type DeviceUser @aws_cognito_user_pools @aws_iam {
  device: Device
  user: User

  isOwner: Boolean
  status: UserAccessStatus

  spaceConfigs: [DeviceUserSpaceConfig]

  bytesUploaded: Long
  bytesDownloaded: Long

  lastAccessTime: AWSTimestamp
  lastConnectSpace: Space
}

# Configuration for a device user to that has access to
# a particular space, such as the wireguard configuration
# need to connect via a wireguard client.
type DeviceUserSpaceConfig {
  space: Space

  viewed: Boolean

  wgConfigName: String
  wgConfig: String

  wgConfigExpireAt: AWSTimestamp
  wgInactivityExpireAt: AWSTimestamp

  wgInactivityTimeout: Int
}

input UserAccessConfig {
  wgConfigName: String
  wgConfig: String

  # whether the access config
  # has been viewed by the user
  viewed: Boolean

  # Timeouts are in hours
  wgExpirationTimeout: Int
  wgInactivityTimeout: Int
}

# An org is an organizing entity
# that consists of one or users that is
# grouped into a logical body such as a
# company. Each org can have one or
# more spaces that represent a physical access
# boundary, such as a division or office.
type Org @aws_cognito_user_pools @aws_iam {
  orgID: ID!
  orgName: String!

  owner: UserRef
  admins: [UserRef]

  businessRef: String

  publicKey: String
  certificate: String

  contacts: [Contact]
  addresses: [Address]

  config: String

  quotas: [OrgQuota]

  verified: Boolean

  # External references
  externalRefs: [Nv]

  spaces(limit: Int, next: CursorInput): OrgSpacesConnection
  users(limit: Int, next: CursorInput): OrgUsersConnection
}

type Address {
  type: AddressType!
  description: String

  number: String
  street: String
  other: String

  municipality: String
  county: String
  province: String
  postalCode: String
  countryCode: String
}

input AddressInput {
  type: AddressType!
  description: String

  number: String
  street: String
  other: String

  municipality: String
  county: String
  province: String
  postalCode: String
  countryCode: String
}

enum AddressType {
  main,
  branch,
  billing
}

type Contact {
  type: ContactType!
  description: String

  phoneNumber: String
  emailAddress: String
}

input ContactInput {
  type: ContactType!
  description: String

  phoneNumber: String
  emailAddress: String
}

enum ContactType {
  email,
  tollFree,
  voiceAndMessaging,
  voiceOnly,
  messagingOnly,
  fax
}

type OrgQuota {
  quotaName: String!
  description: String

  period: QuotaPeriod!
  softLimit: Boolean!

  limit: Int!
  used: Int!
}

enum QuotaPeriod {
  none,
  daily,
  weekly,
  monthly,
  yearly
}

type OrgUsersConnection {
  pageInfo: PageInfo
  edges: [OrgUsersEdge]
  totalCount: Int
  orgUsers: [OrgUser]
}
type OrgUsersEdge {
  node: OrgUser!
}

type OrgSpacesConnection {
  pageInfo: PageInfo
  edges: [OrgSpacesEdge]
  totalCount: Int
  orgSpaces: [Space]
}
type OrgSpacesEdge {
  node: Space!
}

# Associates a user with an org.
# An org has at least one user
# associated with it, who will be the
# org owner and admin. There can
# only be one owner.
type OrgUser @aws_cognito_user_pools @aws_iam {
  user: User
  org: Org

  isOwner: Boolean
  isAdmin: Boolean

  status: UserAccessStatus

  accessList(limit: Int, next: CursorInput): SpaceUsersConnection
}

# A space within the My Cloud Space eco-system is
# a virtual private network in the cloud that is
# secured behind a VPN access point.
type Space @aws_cognito_user_pools @aws_iam {
  spaceID: ID!
  spaceName: String

  owner: UserRef
  admins: [UserRef]

  org: Org

  cookbook: String
  recipe: String
  iaas: String
  region: String
  version: String

  publicKey: String
  certificate: String

  isEgressNode: Boolean

  # common configuration associated
  # with the space such as default
  # settings for new space users
  settings: String

  # space node
  ipAddress: String
  fqdn: String
  port: Int
  domainName: String
  vpnType: String
  localCARoot: String

  # space mesh network
  meshNetworkType: IPType
  meshNetworkBitmask: Int

  apps(limit: Int, next: CursorInput): SpaceAppsConnection
  users(limit: Int, next: CursorInput): SpaceUsersConnection

  status: SpaceStatus
  lastSeen: AWSTimestamp
}

enum SpaceStatus {
  undeployed,
  running,
  shutdown,
  pending,
  unknown
}

enum IPType {
  IPv4,
  IPv6
}

type SpaceUsersConnection {
  pageInfo: PageInfo
  edges: [SpaceUsersEdge]
  totalCount: Int
  spaceUsers: [SpaceUser]
}
type SpaceUsersEdge {
  node: SpaceUser!
}

type SpaceAppsConnection {
  pageInfo: PageInfo
  edges: [SpaceAppsEdge]
  totalCount: Int
  spaceApps: [App]
}
type SpaceAppsEdge {
  node: App!
}

# Associates a user with a space. A space will have
# at least one user associated with it, which will
# be the space owner and admin. A space cannot have
# more than one owner.
type SpaceUser @aws_cognito_user_pools @aws_iam {
  space: Space
  user: User

  isOwner: Boolean
  isAdmin: Boolean
  # User's that are niether owners or admin can
  # connect to the space and access only apps
  # they are allowed to access. If this flag
  # is set then they can also use the space
  # as the egress node for internet access.
  canUseSpaceForEgress: Boolean
  # If this flag is set when the user connects
  # the connection will be configured to block
  # sites that server ads as well as adult
  # content.
  enableSiteBlocking: Boolean

  status: UserAccessStatus

  bytesUploaded: Long
  bytesDownloaded: Long

  accessList(limit: Int, next: CursorInput): AppUsersConnection

  lastConnectTime: AWSTimestamp
  lastConnectDevice: Device
}

# An App is a cloud app or service that is deployed
# to a space's virtual private network.
type App @aws_cognito_user_pools @aws_iam  {
  appID: ID!
  appName: String

  cookbook: String
  recipe: String
  iaas: String
  region: String
  version: String

  publicKey: String
  certificate: String

  # app node
  description: String
  domainName: String
  port: Int

  status: AppStatus
  lastSeen: AWSTimestamp

  space: Space
  users(limit: Int, next: CursorInput): AppUsersConnection
}

enum AppStatus {
  undeployed,
  running,
  shutdown,
  pending,
  unknown
}

type AppUsersConnection {
  pageInfo: PageInfo
  edges: [AppUsersEdge]
  totalCount: Int
  appUsers: [AppUser]
}
type AppUsersEdge {
  node: AppUser!
}

# Access to an App within a space can be granted on
# a per user basis. Admin users of a space has access
# to all apps within the space.
type AppUser @aws_cognito_user_pools @aws_iam {
  app: App
  user: User

  isOwner: Boolean

  lastAccessedTime: AWSTimestamp
}

# Generic types
type Nv {
  name: String!
  value: String
}

#
# Data publishing types
#

# Push data message
input PublishDataInput {
  type: PublishDataType!
  compressed: Boolean!
  payload: String!
}

type PublishResult {
  success: Boolean!
  error: String
}

# Async push data type
enum PublishDataType {
  event
}

#
# Subscription Input types
#

input SubInfoInput{
  name: String!
  args: [String!]!
}

#
# Subscription Output types
#

type UserUpdate @aws_cognito_user_pools @aws_iam {
  userID: ID!
  numDevices: Int
  numSpaces: Int
  numApps: Int

  user: User!
}

type DeviceUpdate @aws_cognito_user_pools @aws_iam {
  deviceID: ID!
  numUsers: Int

  device: Device!
}

type DeviceUserUpdate @aws_cognito_user_pools @aws_iam {
  deviceID: ID!
  userID: ID!

  deviceUser: DeviceUser!
}

type SpaceUpdate @aws_cognito_user_pools @aws_iam {
  spaceID: ID!
  numUsers: Int
  numApps: Int

  space: Space!
}

type SpaceUserUpdate @aws_cognito_user_pools @aws_iam {
  spaceID: ID!
  userID: ID!

  spaceUser: SpaceUser!
}

type AppUpdate @aws_cognito_user_pools @aws_iam {
  appID: ID!
  numUsers: Int

  app: App!
}

type AppUserUpdate @aws_cognito_user_pools @aws_iam {
  appID: ID!
  userID: ID!

  appUser: AppUser!
}

#
# Output types
#

# Device identification information
type DeviceII {
  idKey: String!
  deviceUser: DeviceUser!
}

# Device authorization response
type DeviceAuth {
  accessType: AccessType!
  device: Device
}

enum AccessType {
  unauthorized,
  pending,
  guest,
  admin
}

# Org identification information
type OrgII {
  idKey: String!
  orgUser: OrgUser!
}

# Space identification information
type SpaceII {
  idKey: String!
  spaceUser: SpaceUser!
}

# App identification information
type AppII {
  idKey: String!
  app: App!
}

#
# Key input types
#

input Key {
  publicKey: String!

  # key timestamp
  keyTimestamp: String

  # certificate request used to
  # request a signed certificate
  # for the key owner
  certificateRequest: String
}

#
# Filter Inputs
#

input UserSearchFilterInput {
  userName: String
  emailAddress: String
}

#
# Filter input templates that can be transformed in VTL via
# [query|scan]: $util.transform.toDynamoDBFilterExpression($ctx.args.filter)
#

input TableIDFilterInput {
	ne: ID
	eq: ID
	le: ID
	lt: ID
	ge: ID
	gt: ID
	contains: ID
	notContains: ID
	between: [ID]
	beginsWith: ID
}

input TableStringFilterInput {
	ne: String
	eq: String
	le: String
	lt: String
	ge: String
	gt: String
	contains: String
	notContains: String
	between: [String]
	beginsWith: String
}

input TableIntFilterInput {
	ne: Int
	eq: Int
	le: Int
	lt: Int
	ge: Int
	gt: Int
	contains: Int
	notContains: Int
	between: [Int]
}

input TableFloatFilterInput {
	ne: Float
	eq: Float
	le: Float
	lt: Float
	ge: Float
	gt: Float
	contains: Float
	notContains: Float
	between: [Float]
}

input TableBooleanFilterInput {
	ne: Boolean
	eq: Boolean
}

#
# Types used for pagination output and input
#

# This type provides information about pagination in a
# relay connection.
type PageInfo {
  # When paginating forwards, are there more items?
  hasNextPage: Boolean!
  # When paginating backwards, are there more items?
  hasPreviousPage: Boolean!
  # Cursor used for pagination
  cursor: Cursor
}

# Cursor to be used for pagination. Simply pass the
# cursor back to the API call to paginate forward.
type Cursor {
  # The next page token index. To move to the next
  # page, cursor does not need to be updated. To move
  # to the prev page simply decrement the index by 2.
  index: Int!
  # The token to use to retrieve the next page for a
  # given index
  tokens: [String]!
}

# Page Cursor input has the same structure Cursor as
# the cursor returned from a multi-page request. By
# default the same returned value if passed as input
# will return the next page of results.
input CursorInput {
  index: Int!
  tokens: [String]!
}

# AWS Scalars
scalar AWSEmail
scalar AWSTimestamp
scalar AWSIPAddress
scalar Long
