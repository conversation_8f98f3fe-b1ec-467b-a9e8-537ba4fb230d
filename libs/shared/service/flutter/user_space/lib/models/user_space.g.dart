// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_space.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AddressImpl _$$AddressImplFromJson(Map<String, dynamic> json) =>
    _$AddressImpl(
      countryCode: json['countryCode'] as String?,
      county: json['county'] as String?,
      description: json['description'] as String?,
      municipality: json['municipality'] as String?,
      number: json['number'] as String?,
      other: json['other'] as String?,
      postalCode: json['postalCode'] as String?,
      province: json['province'] as String?,
      street: json['street'] as String?,
      type: $enumDecode(_$AddressTypeEnumMap, json['type']),
    );

Map<String, dynamic> _$$AddressImplToJson(_$AddressImpl instance) =>
    <String, dynamic>{
      'countryCode': instance.countryCode,
      'county': instance.county,
      'description': instance.description,
      'municipality': instance.municipality,
      'number': instance.number,
      'other': instance.other,
      'postalCode': instance.postalCode,
      'province': instance.province,
      'street': instance.street,
      'type': _$AddressTypeEnumMap[instance.type]!,
    };

const _$AddressTypeEnumMap = {
  AddressType.billing: 'billing',
  AddressType.branch: 'branch',
  AddressType.main: 'main',
};

_$AddressInputImpl _$$AddressInputImplFromJson(Map<String, dynamic> json) =>
    _$AddressInputImpl(
      countryCode: json['countryCode'] as String?,
      county: json['county'] as String?,
      description: json['description'] as String?,
      municipality: json['municipality'] as String?,
      number: json['number'] as String?,
      other: json['other'] as String?,
      postalCode: json['postalCode'] as String?,
      province: json['province'] as String?,
      street: json['street'] as String?,
      type: $enumDecode(_$AddressTypeEnumMap, json['type']),
    );

Map<String, dynamic> _$$AddressInputImplToJson(_$AddressInputImpl instance) =>
    <String, dynamic>{
      'countryCode': instance.countryCode,
      'county': instance.county,
      'description': instance.description,
      'municipality': instance.municipality,
      'number': instance.number,
      'other': instance.other,
      'postalCode': instance.postalCode,
      'province': instance.province,
      'street': instance.street,
      'type': _$AddressTypeEnumMap[instance.type]!,
    };

_$AppImpl _$$AppImplFromJson(Map<String, dynamic> json) => _$AppImpl(
      appId: json['appID'] as String,
      appName: json['appName'] as String?,
      certificate: json['certificate'] as String?,
      cookbook: json['cookbook'] as String?,
      description: json['description'] as String?,
      domainName: json['domainName'] as String?,
      iaas: json['iaas'] as String?,
      lastSeen: (json['lastSeen'] as num?)?.toInt(),
      port: (json['port'] as num?)?.toInt(),
      publicKey: json['publicKey'] as String?,
      recipe: json['recipe'] as String?,
      region: json['region'] as String?,
      space: json['space'] == null
          ? null
          : Space.fromJson(json['space'] as Map<String, dynamic>),
      status: $enumDecodeNullable(_$AppStatusEnumMap, json['status']),
      users: json['users'] == null
          ? null
          : AppUsersConnection.fromJson(json['users'] as Map<String, dynamic>),
      version: json['version'] as String?,
    );

Map<String, dynamic> _$$AppImplToJson(_$AppImpl instance) => <String, dynamic>{
      'appID': instance.appId,
      'appName': instance.appName,
      'certificate': instance.certificate,
      'cookbook': instance.cookbook,
      'description': instance.description,
      'domainName': instance.domainName,
      'iaas': instance.iaas,
      'lastSeen': instance.lastSeen,
      'port': instance.port,
      'publicKey': instance.publicKey,
      'recipe': instance.recipe,
      'region': instance.region,
      'space': instance.space,
      'status': _$AppStatusEnumMap[instance.status],
      'users': instance.users,
      'version': instance.version,
    };

const _$AppStatusEnumMap = {
  AppStatus.pending: 'pending',
  AppStatus.running: 'running',
  AppStatus.shutdown: 'shutdown',
  AppStatus.undeployed: 'undeployed',
  AppStatus.unknown: 'unknown',
};

_$AppIiImpl _$$AppIiImplFromJson(Map<String, dynamic> json) => _$AppIiImpl(
      app: App.fromJson(json['app'] as Map<String, dynamic>),
      idKey: json['idKey'] as String,
    );

Map<String, dynamic> _$$AppIiImplToJson(_$AppIiImpl instance) =>
    <String, dynamic>{
      'app': instance.app,
      'idKey': instance.idKey,
    };

_$AppUpdateImpl _$$AppUpdateImplFromJson(Map<String, dynamic> json) =>
    _$AppUpdateImpl(
      app: App.fromJson(json['app'] as Map<String, dynamic>),
      appId: json['appID'] as String,
      numUsers: (json['numUsers'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$AppUpdateImplToJson(_$AppUpdateImpl instance) =>
    <String, dynamic>{
      'app': instance.app,
      'appID': instance.appId,
      'numUsers': instance.numUsers,
    };

_$AppUserImpl _$$AppUserImplFromJson(Map<String, dynamic> json) =>
    _$AppUserImpl(
      app: json['app'] == null
          ? null
          : App.fromJson(json['app'] as Map<String, dynamic>),
      isOwner: json['isOwner'] as bool?,
      lastAccessedTime: (json['lastAccessedTime'] as num?)?.toInt(),
      user: json['user'] == null
          ? null
          : User.fromJson(json['user'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$AppUserImplToJson(_$AppUserImpl instance) =>
    <String, dynamic>{
      'app': instance.app,
      'isOwner': instance.isOwner,
      'lastAccessedTime': instance.lastAccessedTime,
      'user': instance.user,
    };

_$AppUserUpdateImpl _$$AppUserUpdateImplFromJson(Map<String, dynamic> json) =>
    _$AppUserUpdateImpl(
      appId: json['appID'] as String,
      appUser: AppUser.fromJson(json['appUser'] as Map<String, dynamic>),
      userId: json['userID'] as String,
    );

Map<String, dynamic> _$$AppUserUpdateImplToJson(_$AppUserUpdateImpl instance) =>
    <String, dynamic>{
      'appID': instance.appId,
      'appUser': instance.appUser,
      'userID': instance.userId,
    };

_$AppUsersConnectionImpl _$$AppUsersConnectionImplFromJson(
        Map<String, dynamic> json) =>
    _$AppUsersConnectionImpl(
      appUsers: (json['appUsers'] as List<dynamic>?)
          ?.map((e) =>
              e == null ? null : AppUser.fromJson(e as Map<String, dynamic>))
          .toList(),
      edges: (json['edges'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : AppUsersEdge.fromJson(e as Map<String, dynamic>))
          .toList(),
      pageInfo: json['pageInfo'] == null
          ? null
          : PageInfo.fromJson(json['pageInfo'] as Map<String, dynamic>),
      totalCount: (json['totalCount'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$AppUsersConnectionImplToJson(
        _$AppUsersConnectionImpl instance) =>
    <String, dynamic>{
      'appUsers': instance.appUsers,
      'edges': instance.edges,
      'pageInfo': instance.pageInfo,
      'totalCount': instance.totalCount,
    };

_$AppUsersEdgeImpl _$$AppUsersEdgeImplFromJson(Map<String, dynamic> json) =>
    _$AppUsersEdgeImpl(
      node: AppUser.fromJson(json['node'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$AppUsersEdgeImplToJson(_$AppUsersEdgeImpl instance) =>
    <String, dynamic>{
      'node': instance.node,
    };

_$ContactImpl _$$ContactImplFromJson(Map<String, dynamic> json) =>
    _$ContactImpl(
      description: json['description'] as String?,
      emailAddress: json['emailAddress'] as String?,
      phoneNumber: json['phoneNumber'] as String?,
      type: $enumDecode(_$ContactTypeEnumMap, json['type']),
    );

Map<String, dynamic> _$$ContactImplToJson(_$ContactImpl instance) =>
    <String, dynamic>{
      'description': instance.description,
      'emailAddress': instance.emailAddress,
      'phoneNumber': instance.phoneNumber,
      'type': _$ContactTypeEnumMap[instance.type]!,
    };

const _$ContactTypeEnumMap = {
  ContactType.email: 'email',
  ContactType.fax: 'fax',
  ContactType.messagingOnly: 'messagingOnly',
  ContactType.tollFree: 'tollFree',
  ContactType.voiceAndMessaging: 'voiceAndMessaging',
  ContactType.voiceOnly: 'voiceOnly',
};

_$ContactInputImpl _$$ContactInputImplFromJson(Map<String, dynamic> json) =>
    _$ContactInputImpl(
      description: json['description'] as String?,
      emailAddress: json['emailAddress'] as String?,
      phoneNumber: json['phoneNumber'] as String?,
      type: $enumDecode(_$ContactTypeEnumMap, json['type']),
    );

Map<String, dynamic> _$$ContactInputImplToJson(_$ContactInputImpl instance) =>
    <String, dynamic>{
      'description': instance.description,
      'emailAddress': instance.emailAddress,
      'phoneNumber': instance.phoneNumber,
      'type': _$ContactTypeEnumMap[instance.type]!,
    };

_$CursorImpl _$$CursorImplFromJson(Map<String, dynamic> json) => _$CursorImpl(
      index: (json['index'] as num).toInt(),
      tokens:
          (json['tokens'] as List<dynamic>).map((e) => e as String?).toList(),
    );

Map<String, dynamic> _$$CursorImplToJson(_$CursorImpl instance) =>
    <String, dynamic>{
      'index': instance.index,
      'tokens': instance.tokens,
    };

_$CursorInputImpl _$$CursorInputImplFromJson(Map<String, dynamic> json) =>
    _$CursorInputImpl(
      index: (json['index'] as num).toInt(),
      tokens:
          (json['tokens'] as List<dynamic>).map((e) => e as String?).toList(),
    );

Map<String, dynamic> _$$CursorInputImplToJson(_$CursorInputImpl instance) =>
    <String, dynamic>{
      'index': instance.index,
      'tokens': instance.tokens,
    };

_$DeviceImpl _$$DeviceImplFromJson(Map<String, dynamic> json) => _$DeviceImpl(
      certificate: json['certificate'] as String?,
      clientVersion: json['clientVersion'] as String?,
      deviceId: json['deviceID'] as String,
      deviceName: json['deviceName'] as String?,
      deviceType: json['deviceType'] as String?,
      managedBy: json['managedBy'] as String?,
      managedDevices: (json['managedDevices'] as List<dynamic>?)
          ?.map((e) =>
              e == null ? null : Device.fromJson(e as Map<String, dynamic>))
          .toList(),
      owner: json['owner'] == null
          ? null
          : UserRef.fromJson(json['owner'] as Map<String, dynamic>),
      publicKey: json['publicKey'] as String?,
      settings: json['settings'] as String?,
      users: json['users'] == null
          ? null
          : DeviceUsersConnection.fromJson(
              json['users'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$DeviceImplToJson(_$DeviceImpl instance) =>
    <String, dynamic>{
      'certificate': instance.certificate,
      'clientVersion': instance.clientVersion,
      'deviceID': instance.deviceId,
      'deviceName': instance.deviceName,
      'deviceType': instance.deviceType,
      'managedBy': instance.managedBy,
      'managedDevices': instance.managedDevices,
      'owner': instance.owner,
      'publicKey': instance.publicKey,
      'settings': instance.settings,
      'users': instance.users,
    };

_$DeviceAuthImpl _$$DeviceAuthImplFromJson(Map<String, dynamic> json) =>
    _$DeviceAuthImpl(
      accessType: $enumDecode(_$AccessTypeEnumMap, json['accessType']),
      device: json['device'] == null
          ? null
          : Device.fromJson(json['device'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$DeviceAuthImplToJson(_$DeviceAuthImpl instance) =>
    <String, dynamic>{
      'accessType': _$AccessTypeEnumMap[instance.accessType]!,
      'device': instance.device,
    };

const _$AccessTypeEnumMap = {
  AccessType.admin: 'admin',
  AccessType.guest: 'guest',
  AccessType.pending: 'pending',
  AccessType.unauthorized: 'unauthorized',
};

_$DeviceIiImpl _$$DeviceIiImplFromJson(Map<String, dynamic> json) =>
    _$DeviceIiImpl(
      deviceUser:
          DeviceUser.fromJson(json['deviceUser'] as Map<String, dynamic>),
      idKey: json['idKey'] as String,
    );

Map<String, dynamic> _$$DeviceIiImplToJson(_$DeviceIiImpl instance) =>
    <String, dynamic>{
      'deviceUser': instance.deviceUser,
      'idKey': instance.idKey,
    };

_$DeviceInfoImpl _$$DeviceInfoImplFromJson(Map<String, dynamic> json) =>
    _$DeviceInfoImpl(
      clientVersion: json['clientVersion'] as String,
      deviceType: json['deviceType'] as String,
      managedBy: json['managedBy'] as String?,
    );

Map<String, dynamic> _$$DeviceInfoImplToJson(_$DeviceInfoImpl instance) =>
    <String, dynamic>{
      'clientVersion': instance.clientVersion,
      'deviceType': instance.deviceType,
      'managedBy': instance.managedBy,
    };

_$DeviceUpdateImpl _$$DeviceUpdateImplFromJson(Map<String, dynamic> json) =>
    _$DeviceUpdateImpl(
      device: Device.fromJson(json['device'] as Map<String, dynamic>),
      deviceId: json['deviceID'] as String,
      numUsers: (json['numUsers'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$DeviceUpdateImplToJson(_$DeviceUpdateImpl instance) =>
    <String, dynamic>{
      'device': instance.device,
      'deviceID': instance.deviceId,
      'numUsers': instance.numUsers,
    };

_$DeviceUserImpl _$$DeviceUserImplFromJson(Map<String, dynamic> json) =>
    _$DeviceUserImpl(
      bytesDownloaded: (json['bytesDownloaded'] as num?)?.toInt(),
      bytesUploaded: (json['bytesUploaded'] as num?)?.toInt(),
      device: json['device'] == null
          ? null
          : Device.fromJson(json['device'] as Map<String, dynamic>),
      isOwner: json['isOwner'] as bool?,
      lastAccessTime: (json['lastAccessTime'] as num?)?.toInt(),
      lastConnectSpace: json['lastConnectSpace'] == null
          ? null
          : Space.fromJson(json['lastConnectSpace'] as Map<String, dynamic>),
      spaceConfigs: (json['spaceConfigs'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : DeviceUserSpaceConfig.fromJson(e as Map<String, dynamic>))
          .toList(),
      status: $enumDecodeNullable(_$UserAccessStatusEnumMap, json['status']),
      user: json['user'] == null
          ? null
          : User.fromJson(json['user'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$DeviceUserImplToJson(_$DeviceUserImpl instance) =>
    <String, dynamic>{
      'bytesDownloaded': instance.bytesDownloaded,
      'bytesUploaded': instance.bytesUploaded,
      'device': instance.device,
      'isOwner': instance.isOwner,
      'lastAccessTime': instance.lastAccessTime,
      'lastConnectSpace': instance.lastConnectSpace,
      'spaceConfigs': instance.spaceConfigs,
      'status': _$UserAccessStatusEnumMap[instance.status],
      'user': instance.user,
    };

const _$UserAccessStatusEnumMap = {
  UserAccessStatus.active: 'active',
  UserAccessStatus.inactive: 'inactive',
  UserAccessStatus.pending: 'pending',
};

_$DeviceUserSpaceConfigImpl _$$DeviceUserSpaceConfigImplFromJson(
        Map<String, dynamic> json) =>
    _$DeviceUserSpaceConfigImpl(
      space: json['space'] == null
          ? null
          : Space.fromJson(json['space'] as Map<String, dynamic>),
      viewed: json['viewed'] as bool?,
      wgConfig: json['wgConfig'] as String?,
      wgConfigExpireAt: (json['wgConfigExpireAt'] as num?)?.toInt(),
      wgConfigName: json['wgConfigName'] as String?,
      wgInactivityExpireAt: (json['wgInactivityExpireAt'] as num?)?.toInt(),
      wgInactivityTimeout: (json['wgInactivityTimeout'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$DeviceUserSpaceConfigImplToJson(
        _$DeviceUserSpaceConfigImpl instance) =>
    <String, dynamic>{
      'space': instance.space,
      'viewed': instance.viewed,
      'wgConfig': instance.wgConfig,
      'wgConfigExpireAt': instance.wgConfigExpireAt,
      'wgConfigName': instance.wgConfigName,
      'wgInactivityExpireAt': instance.wgInactivityExpireAt,
      'wgInactivityTimeout': instance.wgInactivityTimeout,
    };

_$DeviceUserUpdateImpl _$$DeviceUserUpdateImplFromJson(
        Map<String, dynamic> json) =>
    _$DeviceUserUpdateImpl(
      deviceId: json['deviceID'] as String,
      deviceUser:
          DeviceUser.fromJson(json['deviceUser'] as Map<String, dynamic>),
      userId: json['userID'] as String,
    );

Map<String, dynamic> _$$DeviceUserUpdateImplToJson(
        _$DeviceUserUpdateImpl instance) =>
    <String, dynamic>{
      'deviceID': instance.deviceId,
      'deviceUser': instance.deviceUser,
      'userID': instance.userId,
    };

_$DeviceUsersConnectionImpl _$$DeviceUsersConnectionImplFromJson(
        Map<String, dynamic> json) =>
    _$DeviceUsersConnectionImpl(
      deviceUsers: (json['deviceUsers'] as List<dynamic>?)
          ?.map((e) =>
              e == null ? null : DeviceUser.fromJson(e as Map<String, dynamic>))
          .toList(),
      edges: (json['edges'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : DeviceUsersEdge.fromJson(e as Map<String, dynamic>))
          .toList(),
      pageInfo: json['pageInfo'] == null
          ? null
          : PageInfo.fromJson(json['pageInfo'] as Map<String, dynamic>),
      totalCount: (json['totalCount'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$DeviceUsersConnectionImplToJson(
        _$DeviceUsersConnectionImpl instance) =>
    <String, dynamic>{
      'deviceUsers': instance.deviceUsers,
      'edges': instance.edges,
      'pageInfo': instance.pageInfo,
      'totalCount': instance.totalCount,
    };

_$DeviceUsersEdgeImpl _$$DeviceUsersEdgeImplFromJson(
        Map<String, dynamic> json) =>
    _$DeviceUsersEdgeImpl(
      node: DeviceUser.fromJson(json['node'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$DeviceUsersEdgeImplToJson(
        _$DeviceUsersEdgeImpl instance) =>
    <String, dynamic>{
      'node': instance.node,
    };

_$KeyImpl _$$KeyImplFromJson(Map<String, dynamic> json) => _$KeyImpl(
      certificateRequest: json['certificateRequest'] as String?,
      keyTimestamp: json['keyTimestamp'] as String?,
      publicKey: json['publicKey'] as String,
    );

Map<String, dynamic> _$$KeyImplToJson(_$KeyImpl instance) => <String, dynamic>{
      'certificateRequest': instance.certificateRequest,
      'keyTimestamp': instance.keyTimestamp,
      'publicKey': instance.publicKey,
    };

_$MyCsCloudPropsImpl _$$MyCsCloudPropsImplFromJson(Map<String, dynamic> json) =>
    _$MyCsCloudPropsImpl(
      publicKey: json['publicKey'] as String?,
      publicKeyId: json['publicKeyID'] as String?,
    );

Map<String, dynamic> _$$MyCsCloudPropsImplToJson(
        _$MyCsCloudPropsImpl instance) =>
    <String, dynamic>{
      'publicKey': instance.publicKey,
      'publicKeyID': instance.publicKeyId,
    };

_$NvImpl _$$NvImplFromJson(Map<String, dynamic> json) => _$NvImpl(
      name: json['name'] as String,
      value: json['value'] as String?,
    );

Map<String, dynamic> _$$NvImplToJson(_$NvImpl instance) => <String, dynamic>{
      'name': instance.name,
      'value': instance.value,
    };

_$OrgImpl _$$OrgImplFromJson(Map<String, dynamic> json) => _$OrgImpl(
      addresses: (json['addresses'] as List<dynamic>?)
          ?.map((e) =>
              e == null ? null : Address.fromJson(e as Map<String, dynamic>))
          .toList(),
      admins: (json['admins'] as List<dynamic>?)
          ?.map((e) =>
              e == null ? null : UserRef.fromJson(e as Map<String, dynamic>))
          .toList(),
      businessRef: json['businessRef'] as String?,
      certificate: json['certificate'] as String?,
      config: json['config'] as String?,
      contacts: (json['contacts'] as List<dynamic>?)
          ?.map((e) =>
              e == null ? null : Contact.fromJson(e as Map<String, dynamic>))
          .toList(),
      externalRefs: (json['externalRefs'] as List<dynamic>?)
          ?.map(
              (e) => e == null ? null : Nv.fromJson(e as Map<String, dynamic>))
          .toList(),
      orgId: json['orgID'] as String,
      orgName: json['orgName'] as String,
      owner: json['owner'] == null
          ? null
          : UserRef.fromJson(json['owner'] as Map<String, dynamic>),
      publicKey: json['publicKey'] as String?,
      quotas: (json['quotas'] as List<dynamic>?)
          ?.map((e) =>
              e == null ? null : OrgQuota.fromJson(e as Map<String, dynamic>))
          .toList(),
      spaces: json['spaces'] == null
          ? null
          : OrgSpacesConnection.fromJson(
              json['spaces'] as Map<String, dynamic>),
      users: json['users'] == null
          ? null
          : OrgUsersConnection.fromJson(json['users'] as Map<String, dynamic>),
      verified: json['verified'] as bool?,
    );

Map<String, dynamic> _$$OrgImplToJson(_$OrgImpl instance) => <String, dynamic>{
      'addresses': instance.addresses,
      'admins': instance.admins,
      'businessRef': instance.businessRef,
      'certificate': instance.certificate,
      'config': instance.config,
      'contacts': instance.contacts,
      'externalRefs': instance.externalRefs,
      'orgID': instance.orgId,
      'orgName': instance.orgName,
      'owner': instance.owner,
      'publicKey': instance.publicKey,
      'quotas': instance.quotas,
      'spaces': instance.spaces,
      'users': instance.users,
      'verified': instance.verified,
    };

_$OrgIiImpl _$$OrgIiImplFromJson(Map<String, dynamic> json) => _$OrgIiImpl(
      idKey: json['idKey'] as String,
      orgUser: OrgUser.fromJson(json['orgUser'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$OrgIiImplToJson(_$OrgIiImpl instance) =>
    <String, dynamic>{
      'idKey': instance.idKey,
      'orgUser': instance.orgUser,
    };

_$OrgQuotaImpl _$$OrgQuotaImplFromJson(Map<String, dynamic> json) =>
    _$OrgQuotaImpl(
      description: json['description'] as String?,
      limit: (json['limit'] as num).toInt(),
      period: $enumDecode(_$QuotaPeriodEnumMap, json['period']),
      quotaName: json['quotaName'] as String,
      softLimit: json['softLimit'] as bool,
      used: (json['used'] as num).toInt(),
    );

Map<String, dynamic> _$$OrgQuotaImplToJson(_$OrgQuotaImpl instance) =>
    <String, dynamic>{
      'description': instance.description,
      'limit': instance.limit,
      'period': _$QuotaPeriodEnumMap[instance.period]!,
      'quotaName': instance.quotaName,
      'softLimit': instance.softLimit,
      'used': instance.used,
    };

const _$QuotaPeriodEnumMap = {
  QuotaPeriod.daily: 'daily',
  QuotaPeriod.monthly: 'monthly',
  QuotaPeriod.none: 'none',
  QuotaPeriod.weekly: 'weekly',
  QuotaPeriod.yearly: 'yearly',
};

_$OrgSpacesConnectionImpl _$$OrgSpacesConnectionImplFromJson(
        Map<String, dynamic> json) =>
    _$OrgSpacesConnectionImpl(
      edges: (json['edges'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : OrgSpacesEdge.fromJson(e as Map<String, dynamic>))
          .toList(),
      orgSpaces: (json['orgSpaces'] as List<dynamic>?)
          ?.map((e) =>
              e == null ? null : Space.fromJson(e as Map<String, dynamic>))
          .toList(),
      pageInfo: json['pageInfo'] == null
          ? null
          : PageInfo.fromJson(json['pageInfo'] as Map<String, dynamic>),
      totalCount: (json['totalCount'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$OrgSpacesConnectionImplToJson(
        _$OrgSpacesConnectionImpl instance) =>
    <String, dynamic>{
      'edges': instance.edges,
      'orgSpaces': instance.orgSpaces,
      'pageInfo': instance.pageInfo,
      'totalCount': instance.totalCount,
    };

_$OrgSpacesEdgeImpl _$$OrgSpacesEdgeImplFromJson(Map<String, dynamic> json) =>
    _$OrgSpacesEdgeImpl(
      node: Space.fromJson(json['node'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$OrgSpacesEdgeImplToJson(_$OrgSpacesEdgeImpl instance) =>
    <String, dynamic>{
      'node': instance.node,
    };

_$OrgUserImpl _$$OrgUserImplFromJson(Map<String, dynamic> json) =>
    _$OrgUserImpl(
      accessList: json['accessList'] == null
          ? null
          : SpaceUsersConnection.fromJson(
              json['accessList'] as Map<String, dynamic>),
      isAdmin: json['isAdmin'] as bool?,
      isOwner: json['isOwner'] as bool?,
      org: json['org'] == null
          ? null
          : Org.fromJson(json['org'] as Map<String, dynamic>),
      status: $enumDecodeNullable(_$UserAccessStatusEnumMap, json['status']),
      user: json['user'] == null
          ? null
          : User.fromJson(json['user'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$OrgUserImplToJson(_$OrgUserImpl instance) =>
    <String, dynamic>{
      'accessList': instance.accessList,
      'isAdmin': instance.isAdmin,
      'isOwner': instance.isOwner,
      'org': instance.org,
      'status': _$UserAccessStatusEnumMap[instance.status],
      'user': instance.user,
    };

_$OrgUsersConnectionImpl _$$OrgUsersConnectionImplFromJson(
        Map<String, dynamic> json) =>
    _$OrgUsersConnectionImpl(
      edges: (json['edges'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : OrgUsersEdge.fromJson(e as Map<String, dynamic>))
          .toList(),
      orgUsers: (json['orgUsers'] as List<dynamic>?)
          ?.map((e) =>
              e == null ? null : OrgUser.fromJson(e as Map<String, dynamic>))
          .toList(),
      pageInfo: json['pageInfo'] == null
          ? null
          : PageInfo.fromJson(json['pageInfo'] as Map<String, dynamic>),
      totalCount: (json['totalCount'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$OrgUsersConnectionImplToJson(
        _$OrgUsersConnectionImpl instance) =>
    <String, dynamic>{
      'edges': instance.edges,
      'orgUsers': instance.orgUsers,
      'pageInfo': instance.pageInfo,
      'totalCount': instance.totalCount,
    };

_$OrgUsersEdgeImpl _$$OrgUsersEdgeImplFromJson(Map<String, dynamic> json) =>
    _$OrgUsersEdgeImpl(
      node: OrgUser.fromJson(json['node'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$OrgUsersEdgeImplToJson(_$OrgUsersEdgeImpl instance) =>
    <String, dynamic>{
      'node': instance.node,
    };

_$PageInfoImpl _$$PageInfoImplFromJson(Map<String, dynamic> json) =>
    _$PageInfoImpl(
      cursor: json['cursor'] == null
          ? null
          : Cursor.fromJson(json['cursor'] as Map<String, dynamic>),
      hasNextPage: json['hasNextPage'] as bool,
      hasPreviousPage: json['hasPreviousPage'] as bool,
    );

Map<String, dynamic> _$$PageInfoImplToJson(_$PageInfoImpl instance) =>
    <String, dynamic>{
      'cursor': instance.cursor,
      'hasNextPage': instance.hasNextPage,
      'hasPreviousPage': instance.hasPreviousPage,
    };

_$PublishDataInputImpl _$$PublishDataInputImplFromJson(
        Map<String, dynamic> json) =>
    _$PublishDataInputImpl(
      compressed: json['compressed'] as bool,
      payload: json['payload'] as String,
      type: $enumDecode(_$PublishDataTypeEnumMap, json['type']),
    );

Map<String, dynamic> _$$PublishDataInputImplToJson(
        _$PublishDataInputImpl instance) =>
    <String, dynamic>{
      'compressed': instance.compressed,
      'payload': instance.payload,
      'type': _$PublishDataTypeEnumMap[instance.type]!,
    };

const _$PublishDataTypeEnumMap = {
  PublishDataType.event: 'event',
};

_$PublishResultImpl _$$PublishResultImplFromJson(Map<String, dynamic> json) =>
    _$PublishResultImpl(
      error: json['error'] as String?,
      success: json['success'] as bool,
    );

Map<String, dynamic> _$$PublishResultImplToJson(_$PublishResultImpl instance) =>
    <String, dynamic>{
      'error': instance.error,
      'success': instance.success,
    };

_$SpaceImpl _$$SpaceImplFromJson(Map<String, dynamic> json) => _$SpaceImpl(
      admins: (json['admins'] as List<dynamic>?)
          ?.map((e) =>
              e == null ? null : UserRef.fromJson(e as Map<String, dynamic>))
          .toList(),
      apps: json['apps'] == null
          ? null
          : SpaceAppsConnection.fromJson(json['apps'] as Map<String, dynamic>),
      certificate: json['certificate'] as String?,
      cookbook: json['cookbook'] as String?,
      domainName: json['domainName'] as String?,
      fqdn: json['fqdn'] as String?,
      iaas: json['iaas'] as String?,
      ipAddress: json['ipAddress'] as String?,
      isEgressNode: json['isEgressNode'] as bool?,
      lastSeen: (json['lastSeen'] as num?)?.toInt(),
      localCaRoot: json['localCARoot'] as String?,
      meshNetworkBitmask: (json['meshNetworkBitmask'] as num?)?.toInt(),
      meshNetworkType:
          $enumDecodeNullable(_$IPTypeEnumMap, json['meshNetworkType']),
      org: json['org'] == null
          ? null
          : Org.fromJson(json['org'] as Map<String, dynamic>),
      owner: json['owner'] == null
          ? null
          : UserRef.fromJson(json['owner'] as Map<String, dynamic>),
      port: (json['port'] as num?)?.toInt(),
      publicKey: json['publicKey'] as String?,
      recipe: json['recipe'] as String?,
      region: json['region'] as String?,
      settings: json['settings'] as String?,
      spaceId: json['spaceID'] as String,
      spaceName: json['spaceName'] as String?,
      status: $enumDecodeNullable(_$SpaceStatusEnumMap, json['status']),
      users: json['users'] == null
          ? null
          : SpaceUsersConnection.fromJson(
              json['users'] as Map<String, dynamic>),
      version: json['version'] as String?,
      vpnType: json['vpnType'] as String?,
    );

Map<String, dynamic> _$$SpaceImplToJson(_$SpaceImpl instance) =>
    <String, dynamic>{
      'admins': instance.admins,
      'apps': instance.apps,
      'certificate': instance.certificate,
      'cookbook': instance.cookbook,
      'domainName': instance.domainName,
      'fqdn': instance.fqdn,
      'iaas': instance.iaas,
      'ipAddress': instance.ipAddress,
      'isEgressNode': instance.isEgressNode,
      'lastSeen': instance.lastSeen,
      'localCARoot': instance.localCaRoot,
      'meshNetworkBitmask': instance.meshNetworkBitmask,
      'meshNetworkType': _$IPTypeEnumMap[instance.meshNetworkType],
      'org': instance.org,
      'owner': instance.owner,
      'port': instance.port,
      'publicKey': instance.publicKey,
      'recipe': instance.recipe,
      'region': instance.region,
      'settings': instance.settings,
      'spaceID': instance.spaceId,
      'spaceName': instance.spaceName,
      'status': _$SpaceStatusEnumMap[instance.status],
      'users': instance.users,
      'version': instance.version,
      'vpnType': instance.vpnType,
    };

const _$IPTypeEnumMap = {
  IPType.ipv4: 'IPv4',
  IPType.ipv6: 'IPv6',
};

const _$SpaceStatusEnumMap = {
  SpaceStatus.pending: 'pending',
  SpaceStatus.running: 'running',
  SpaceStatus.shutdown: 'shutdown',
  SpaceStatus.undeployed: 'undeployed',
  SpaceStatus.unknown: 'unknown',
};

_$SpaceAppsConnectionImpl _$$SpaceAppsConnectionImplFromJson(
        Map<String, dynamic> json) =>
    _$SpaceAppsConnectionImpl(
      edges: (json['edges'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : SpaceAppsEdge.fromJson(e as Map<String, dynamic>))
          .toList(),
      pageInfo: json['pageInfo'] == null
          ? null
          : PageInfo.fromJson(json['pageInfo'] as Map<String, dynamic>),
      spaceApps: (json['spaceApps'] as List<dynamic>?)
          ?.map(
              (e) => e == null ? null : App.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalCount: (json['totalCount'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$SpaceAppsConnectionImplToJson(
        _$SpaceAppsConnectionImpl instance) =>
    <String, dynamic>{
      'edges': instance.edges,
      'pageInfo': instance.pageInfo,
      'spaceApps': instance.spaceApps,
      'totalCount': instance.totalCount,
    };

_$SpaceAppsEdgeImpl _$$SpaceAppsEdgeImplFromJson(Map<String, dynamic> json) =>
    _$SpaceAppsEdgeImpl(
      node: App.fromJson(json['node'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$SpaceAppsEdgeImplToJson(_$SpaceAppsEdgeImpl instance) =>
    <String, dynamic>{
      'node': instance.node,
    };

_$SpaceIiImpl _$$SpaceIiImplFromJson(Map<String, dynamic> json) =>
    _$SpaceIiImpl(
      idKey: json['idKey'] as String,
      spaceUser: SpaceUser.fromJson(json['spaceUser'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$SpaceIiImplToJson(_$SpaceIiImpl instance) =>
    <String, dynamic>{
      'idKey': instance.idKey,
      'spaceUser': instance.spaceUser,
    };

_$SpaceUpdateImpl _$$SpaceUpdateImplFromJson(Map<String, dynamic> json) =>
    _$SpaceUpdateImpl(
      numApps: (json['numApps'] as num?)?.toInt(),
      numUsers: (json['numUsers'] as num?)?.toInt(),
      space: Space.fromJson(json['space'] as Map<String, dynamic>),
      spaceId: json['spaceID'] as String,
    );

Map<String, dynamic> _$$SpaceUpdateImplToJson(_$SpaceUpdateImpl instance) =>
    <String, dynamic>{
      'numApps': instance.numApps,
      'numUsers': instance.numUsers,
      'space': instance.space,
      'spaceID': instance.spaceId,
    };

_$SpaceUserImpl _$$SpaceUserImplFromJson(Map<String, dynamic> json) =>
    _$SpaceUserImpl(
      accessList: json['accessList'] == null
          ? null
          : AppUsersConnection.fromJson(
              json['accessList'] as Map<String, dynamic>),
      bytesDownloaded: (json['bytesDownloaded'] as num?)?.toInt(),
      bytesUploaded: (json['bytesUploaded'] as num?)?.toInt(),
      canUseSpaceForEgress: json['canUseSpaceForEgress'] as bool?,
      enableSiteBlocking: json['enableSiteBlocking'] as bool?,
      isAdmin: json['isAdmin'] as bool?,
      isOwner: json['isOwner'] as bool?,
      lastConnectDevice: json['lastConnectDevice'] == null
          ? null
          : Device.fromJson(json['lastConnectDevice'] as Map<String, dynamic>),
      lastConnectTime: (json['lastConnectTime'] as num?)?.toInt(),
      space: json['space'] == null
          ? null
          : Space.fromJson(json['space'] as Map<String, dynamic>),
      status: $enumDecodeNullable(_$UserAccessStatusEnumMap, json['status']),
      user: json['user'] == null
          ? null
          : User.fromJson(json['user'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$SpaceUserImplToJson(_$SpaceUserImpl instance) =>
    <String, dynamic>{
      'accessList': instance.accessList,
      'bytesDownloaded': instance.bytesDownloaded,
      'bytesUploaded': instance.bytesUploaded,
      'canUseSpaceForEgress': instance.canUseSpaceForEgress,
      'enableSiteBlocking': instance.enableSiteBlocking,
      'isAdmin': instance.isAdmin,
      'isOwner': instance.isOwner,
      'lastConnectDevice': instance.lastConnectDevice,
      'lastConnectTime': instance.lastConnectTime,
      'space': instance.space,
      'status': _$UserAccessStatusEnumMap[instance.status],
      'user': instance.user,
    };

_$SpaceUserUpdateImpl _$$SpaceUserUpdateImplFromJson(
        Map<String, dynamic> json) =>
    _$SpaceUserUpdateImpl(
      spaceId: json['spaceID'] as String,
      spaceUser: SpaceUser.fromJson(json['spaceUser'] as Map<String, dynamic>),
      userId: json['userID'] as String,
    );

Map<String, dynamic> _$$SpaceUserUpdateImplToJson(
        _$SpaceUserUpdateImpl instance) =>
    <String, dynamic>{
      'spaceID': instance.spaceId,
      'spaceUser': instance.spaceUser,
      'userID': instance.userId,
    };

_$SpaceUsersConnectionImpl _$$SpaceUsersConnectionImplFromJson(
        Map<String, dynamic> json) =>
    _$SpaceUsersConnectionImpl(
      edges: (json['edges'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : SpaceUsersEdge.fromJson(e as Map<String, dynamic>))
          .toList(),
      pageInfo: json['pageInfo'] == null
          ? null
          : PageInfo.fromJson(json['pageInfo'] as Map<String, dynamic>),
      spaceUsers: (json['spaceUsers'] as List<dynamic>?)
          ?.map((e) =>
              e == null ? null : SpaceUser.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalCount: (json['totalCount'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$SpaceUsersConnectionImplToJson(
        _$SpaceUsersConnectionImpl instance) =>
    <String, dynamic>{
      'edges': instance.edges,
      'pageInfo': instance.pageInfo,
      'spaceUsers': instance.spaceUsers,
      'totalCount': instance.totalCount,
    };

_$SpaceUsersEdgeImpl _$$SpaceUsersEdgeImplFromJson(Map<String, dynamic> json) =>
    _$SpaceUsersEdgeImpl(
      node: SpaceUser.fromJson(json['node'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$SpaceUsersEdgeImplToJson(
        _$SpaceUsersEdgeImpl instance) =>
    <String, dynamic>{
      'node': instance.node,
    };

_$SubInfoInputImpl _$$SubInfoInputImplFromJson(Map<String, dynamic> json) =>
    _$SubInfoInputImpl(
      args: (json['args'] as List<dynamic>).map((e) => e as String).toList(),
      name: json['name'] as String,
    );

Map<String, dynamic> _$$SubInfoInputImplToJson(_$SubInfoInputImpl instance) =>
    <String, dynamic>{
      'args': instance.args,
      'name': instance.name,
    };

_$TableBooleanFilterInputImpl _$$TableBooleanFilterInputImplFromJson(
        Map<String, dynamic> json) =>
    _$TableBooleanFilterInputImpl(
      eq: json['eq'] as bool?,
      ne: json['ne'] as bool?,
    );

Map<String, dynamic> _$$TableBooleanFilterInputImplToJson(
        _$TableBooleanFilterInputImpl instance) =>
    <String, dynamic>{
      'eq': instance.eq,
      'ne': instance.ne,
    };

_$TableFloatFilterInputImpl _$$TableFloatFilterInputImplFromJson(
        Map<String, dynamic> json) =>
    _$TableFloatFilterInputImpl(
      between: (json['between'] as List<dynamic>?)
          ?.map((e) => (e as num?)?.toDouble())
          .toList(),
      contains: (json['contains'] as num?)?.toDouble(),
      eq: (json['eq'] as num?)?.toDouble(),
      ge: (json['ge'] as num?)?.toDouble(),
      gt: (json['gt'] as num?)?.toDouble(),
      le: (json['le'] as num?)?.toDouble(),
      lt: (json['lt'] as num?)?.toDouble(),
      ne: (json['ne'] as num?)?.toDouble(),
      notContains: (json['notContains'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$TableFloatFilterInputImplToJson(
        _$TableFloatFilterInputImpl instance) =>
    <String, dynamic>{
      'between': instance.between,
      'contains': instance.contains,
      'eq': instance.eq,
      'ge': instance.ge,
      'gt': instance.gt,
      'le': instance.le,
      'lt': instance.lt,
      'ne': instance.ne,
      'notContains': instance.notContains,
    };

_$TableIdFilterInputImpl _$$TableIdFilterInputImplFromJson(
        Map<String, dynamic> json) =>
    _$TableIdFilterInputImpl(
      beginsWith: json['beginsWith'] as String?,
      between: (json['between'] as List<dynamic>?)
          ?.map((e) => e as String?)
          .toList(),
      contains: json['contains'] as String?,
      eq: json['eq'] as String?,
      ge: json['ge'] as String?,
      gt: json['gt'] as String?,
      le: json['le'] as String?,
      lt: json['lt'] as String?,
      ne: json['ne'] as String?,
      notContains: json['notContains'] as String?,
    );

Map<String, dynamic> _$$TableIdFilterInputImplToJson(
        _$TableIdFilterInputImpl instance) =>
    <String, dynamic>{
      'beginsWith': instance.beginsWith,
      'between': instance.between,
      'contains': instance.contains,
      'eq': instance.eq,
      'ge': instance.ge,
      'gt': instance.gt,
      'le': instance.le,
      'lt': instance.lt,
      'ne': instance.ne,
      'notContains': instance.notContains,
    };

_$TableIntFilterInputImpl _$$TableIntFilterInputImplFromJson(
        Map<String, dynamic> json) =>
    _$TableIntFilterInputImpl(
      between: (json['between'] as List<dynamic>?)
          ?.map((e) => (e as num?)?.toInt())
          .toList(),
      contains: (json['contains'] as num?)?.toInt(),
      eq: (json['eq'] as num?)?.toInt(),
      ge: (json['ge'] as num?)?.toInt(),
      gt: (json['gt'] as num?)?.toInt(),
      le: (json['le'] as num?)?.toInt(),
      lt: (json['lt'] as num?)?.toInt(),
      ne: (json['ne'] as num?)?.toInt(),
      notContains: (json['notContains'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$TableIntFilterInputImplToJson(
        _$TableIntFilterInputImpl instance) =>
    <String, dynamic>{
      'between': instance.between,
      'contains': instance.contains,
      'eq': instance.eq,
      'ge': instance.ge,
      'gt': instance.gt,
      'le': instance.le,
      'lt': instance.lt,
      'ne': instance.ne,
      'notContains': instance.notContains,
    };

_$TableStringFilterInputImpl _$$TableStringFilterInputImplFromJson(
        Map<String, dynamic> json) =>
    _$TableStringFilterInputImpl(
      beginsWith: json['beginsWith'] as String?,
      between: (json['between'] as List<dynamic>?)
          ?.map((e) => e as String?)
          .toList(),
      contains: json['contains'] as String?,
      eq: json['eq'] as String?,
      ge: json['ge'] as String?,
      gt: json['gt'] as String?,
      le: json['le'] as String?,
      lt: json['lt'] as String?,
      ne: json['ne'] as String?,
      notContains: json['notContains'] as String?,
    );

Map<String, dynamic> _$$TableStringFilterInputImplToJson(
        _$TableStringFilterInputImpl instance) =>
    <String, dynamic>{
      'beginsWith': instance.beginsWith,
      'between': instance.between,
      'contains': instance.contains,
      'eq': instance.eq,
      'ge': instance.ge,
      'gt': instance.gt,
      'le': instance.le,
      'lt': instance.lt,
      'ne': instance.ne,
      'notContains': instance.notContains,
    };

_$UserImpl _$$UserImplFromJson(Map<String, dynamic> json) => _$UserImpl(
      apps: json['apps'] == null
          ? null
          : AppUsersConnection.fromJson(json['apps'] as Map<String, dynamic>),
      certificate: json['certificate'] as String?,
      confirmed: json['confirmed'] as bool?,
      devices: json['devices'] == null
          ? null
          : DeviceUsersConnection.fromJson(
              json['devices'] as Map<String, dynamic>),
      emailAddress: json['emailAddress'] as String?,
      externalRefs: (json['externalRefs'] as List<dynamic>?)
          ?.map(
              (e) => e == null ? null : Nv.fromJson(e as Map<String, dynamic>))
          .toList(),
      familyName: json['familyName'] as String?,
      firstName: json['firstName'] as String?,
      middleName: json['middleName'] as String?,
      mobilePhone: json['mobilePhone'] as String?,
      orgs: json['orgs'] == null
          ? null
          : OrgUsersConnection.fromJson(json['orgs'] as Map<String, dynamic>),
      preferredName: json['preferredName'] as String?,
      publicKey: json['publicKey'] as String?,
      spaces: json['spaces'] == null
          ? null
          : SpaceUsersConnection.fromJson(
              json['spaces'] as Map<String, dynamic>),
      universalConfig: json['universalConfig'] as String?,
      userId: json['userID'] as String,
      userName: json['userName'] as String?,
    );

Map<String, dynamic> _$$UserImplToJson(_$UserImpl instance) =>
    <String, dynamic>{
      'apps': instance.apps,
      'certificate': instance.certificate,
      'confirmed': instance.confirmed,
      'devices': instance.devices,
      'emailAddress': instance.emailAddress,
      'externalRefs': instance.externalRefs,
      'familyName': instance.familyName,
      'firstName': instance.firstName,
      'middleName': instance.middleName,
      'mobilePhone': instance.mobilePhone,
      'orgs': instance.orgs,
      'preferredName': instance.preferredName,
      'publicKey': instance.publicKey,
      'spaces': instance.spaces,
      'universalConfig': instance.universalConfig,
      'userID': instance.userId,
      'userName': instance.userName,
    };

_$UserAccessConfigImpl _$$UserAccessConfigImplFromJson(
        Map<String, dynamic> json) =>
    _$UserAccessConfigImpl(
      viewed: json['viewed'] as bool?,
      wgConfig: json['wgConfig'] as String?,
      wgConfigName: json['wgConfigName'] as String?,
      wgExpirationTimeout: (json['wgExpirationTimeout'] as num?)?.toInt(),
      wgInactivityTimeout: (json['wgInactivityTimeout'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$UserAccessConfigImplToJson(
        _$UserAccessConfigImpl instance) =>
    <String, dynamic>{
      'viewed': instance.viewed,
      'wgConfig': instance.wgConfig,
      'wgConfigName': instance.wgConfigName,
      'wgExpirationTimeout': instance.wgExpirationTimeout,
      'wgInactivityTimeout': instance.wgInactivityTimeout,
    };

_$UserRefImpl _$$UserRefImplFromJson(Map<String, dynamic> json) =>
    _$UserRefImpl(
      familyName: json['familyName'] as String?,
      firstName: json['firstName'] as String?,
      middleName: json['middleName'] as String?,
      userId: json['userID'] as String,
      userName: json['userName'] as String?,
    );

Map<String, dynamic> _$$UserRefImplToJson(_$UserRefImpl instance) =>
    <String, dynamic>{
      'familyName': instance.familyName,
      'firstName': instance.firstName,
      'middleName': instance.middleName,
      'userID': instance.userId,
      'userName': instance.userName,
    };

_$UserSearchFilterInputImpl _$$UserSearchFilterInputImplFromJson(
        Map<String, dynamic> json) =>
    _$UserSearchFilterInputImpl(
      emailAddress: json['emailAddress'] as String?,
      userName: json['userName'] as String?,
    );

Map<String, dynamic> _$$UserSearchFilterInputImplToJson(
        _$UserSearchFilterInputImpl instance) =>
    <String, dynamic>{
      'emailAddress': instance.emailAddress,
      'userName': instance.userName,
    };

_$UserUpdateImpl _$$UserUpdateImplFromJson(Map<String, dynamic> json) =>
    _$UserUpdateImpl(
      numApps: (json['numApps'] as num?)?.toInt(),
      numDevices: (json['numDevices'] as num?)?.toInt(),
      numSpaces: (json['numSpaces'] as num?)?.toInt(),
      user: User.fromJson(json['user'] as Map<String, dynamic>),
      userId: json['userID'] as String,
    );

Map<String, dynamic> _$$UserUpdateImplToJson(_$UserUpdateImpl instance) =>
    <String, dynamic>{
      'numApps': instance.numApps,
      'numDevices': instance.numDevices,
      'numSpaces': instance.numSpaces,
      'user': instance.user,
      'userID': instance.userId,
    };
