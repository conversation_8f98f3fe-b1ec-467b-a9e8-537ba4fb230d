/// Plan subscription status
enum SubscriptionStatus {
  unknown,
  active,
  pastDue,
  canceled,
  trialing,
  suspended;

  static SubscriptionStatus fromString(String? status) {
    return SubscriptionStatus.values.firstWhere(
      (e) => e.name == status,
      orElse: () => SubscriptionStatus.unknown,
    );
  }

  @override
  String toString() {
    if (this == SubscriptionStatus.pastDue) {
      return 'past due';
    }
    return name;
  }
}
