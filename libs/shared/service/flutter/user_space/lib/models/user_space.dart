import './scalar_types.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';

part 'user_space.freezed.dart';
part 'user_space.g.dart';

enum AccessType {
  admin,
  guest,
  pending,
  unauthorized,
}

@freezed
class Address with _$Address {
  Address._();

  factory Address({
    String? countryCode,
    String? county,
    String? description,
    String? municipality,
    String? number,
    String? other,
    String? postalCode,
    String? province,
    String? street,
    required AddressType type,
  }) = _Address;

  factory Address.fromJson(Map<String, dynamic> json) => _$AddressFromJson(json);
}

@unfreezed
class AddressInput with _$AddressInput {
  AddressInput._();

  factory AddressInput({
    String? countryCode,
    String? county,
    String? description,
    String? municipality,
    String? number,
    String? other,
    String? postalCode,
    String? province,
    String? street,
    required AddressType type,
  }) = _AddressInput;

  factory AddressInput.fromJson(Map<String, dynamic> json) => _$AddressInputFromJson(json);
}

enum AddressType {
  billing,
  branch,
  main,
}

@freezed
class App with _$App {
  App._();

  factory App({
    @JsonKey(name: 'appID')
    required String appId,
    String? appName,
    String? certificate,
    String? cookbook,
    String? description,
    String? domainName,
    String? iaas,
    AWSTimestamp? lastSeen,
    int? port,
    String? publicKey,
    String? recipe,
    String? region,
    Space? space,
    AppStatus? status,
    AppUsersConnection? users,
    String? version,
  }) = _App;

  factory App.fromJson(Map<String, dynamic> json) => _$AppFromJson(json);
}

@freezed
class AppIi with _$AppIi {
  AppIi._();

  factory AppIi({
    required App app,
    required String idKey,
  }) = _AppIi;

  factory AppIi.fromJson(Map<String, dynamic> json) => _$AppIiFromJson(json);
}

enum AppStatus {
  pending,
  running,
  shutdown,
  undeployed,
  unknown,
}

@freezed
class AppUpdate with _$AppUpdate {
  AppUpdate._();

  factory AppUpdate({
    required App app,
    @JsonKey(name: 'appID')
    required String appId,
    int? numUsers,
  }) = _AppUpdate;

  factory AppUpdate.fromJson(Map<String, dynamic> json) => _$AppUpdateFromJson(json);
}

@freezed
class AppUser with _$AppUser {
  AppUser._();

  factory AppUser({
    App? app,
    bool? isOwner,
    AWSTimestamp? lastAccessedTime,
    User? user,
  }) = _AppUser;

  factory AppUser.fromJson(Map<String, dynamic> json) => _$AppUserFromJson(json);
}

@freezed
class AppUserUpdate with _$AppUserUpdate {
  AppUserUpdate._();

  factory AppUserUpdate({
    @JsonKey(name: 'appID')
    required String appId,
    required AppUser appUser,
    @JsonKey(name: 'userID')
    required String userId,
  }) = _AppUserUpdate;

  factory AppUserUpdate.fromJson(Map<String, dynamic> json) => _$AppUserUpdateFromJson(json);
}

@freezed
class AppUsersConnection with _$AppUsersConnection {
  AppUsersConnection._();

  factory AppUsersConnection({
    List<AppUser?>? appUsers,
    List<AppUsersEdge?>? edges,
    PageInfo? pageInfo,
    int? totalCount,
  }) = _AppUsersConnection;

  factory AppUsersConnection.fromJson(Map<String, dynamic> json) => _$AppUsersConnectionFromJson(json);
}

@freezed
class AppUsersEdge with _$AppUsersEdge {
  AppUsersEdge._();

  factory AppUsersEdge({
    required AppUser node,
  }) = _AppUsersEdge;

  factory AppUsersEdge.fromJson(Map<String, dynamic> json) => _$AppUsersEdgeFromJson(json);
}

@freezed
class Contact with _$Contact {
  Contact._();

  factory Contact({
    String? description,
    String? emailAddress,
    String? phoneNumber,
    required ContactType type,
  }) = _Contact;

  factory Contact.fromJson(Map<String, dynamic> json) => _$ContactFromJson(json);
}

@unfreezed
class ContactInput with _$ContactInput {
  ContactInput._();

  factory ContactInput({
    String? description,
    String? emailAddress,
    String? phoneNumber,
    required ContactType type,
  }) = _ContactInput;

  factory ContactInput.fromJson(Map<String, dynamic> json) => _$ContactInputFromJson(json);
}

enum ContactType {
  email,
  fax,
  messagingOnly,
  tollFree,
  voiceAndMessaging,
  voiceOnly,
}

@freezed
class Cursor with _$Cursor {
  Cursor._();

  factory Cursor({
    required int index,
    required List<String?> tokens,
  }) = _Cursor;

  factory Cursor.fromJson(Map<String, dynamic> json) => _$CursorFromJson(json);
}

@unfreezed
class CursorInput with _$CursorInput {
  CursorInput._();

  factory CursorInput({
    required int index,
    required List<String?> tokens,
  }) = _CursorInput;

  factory CursorInput.fromJson(Map<String, dynamic> json) => _$CursorInputFromJson(json);
}

@freezed
class Device with _$Device {
  Device._();

  factory Device({
    String? certificate,
    String? clientVersion,
    @JsonKey(name: 'deviceID')
    required String deviceId,
    String? deviceName,
    String? deviceType,
    String? managedBy,
    List<Device?>? managedDevices,
    UserRef? owner,
    String? publicKey,
    String? settings,
    DeviceUsersConnection? users,
  }) = _Device;

  factory Device.fromJson(Map<String, dynamic> json) => _$DeviceFromJson(json);
}

@freezed
class DeviceAuth with _$DeviceAuth {
  DeviceAuth._();

  factory DeviceAuth({
    required AccessType accessType,
    Device? device,
  }) = _DeviceAuth;

  factory DeviceAuth.fromJson(Map<String, dynamic> json) => _$DeviceAuthFromJson(json);
}

@freezed
class DeviceIi with _$DeviceIi {
  DeviceIi._();

  factory DeviceIi({
    required DeviceUser deviceUser,
    required String idKey,
  }) = _DeviceIi;

  factory DeviceIi.fromJson(Map<String, dynamic> json) => _$DeviceIiFromJson(json);
}

@unfreezed
class DeviceInfo with _$DeviceInfo {
  DeviceInfo._();

  factory DeviceInfo({
    required String clientVersion,
    required String deviceType,
    String? managedBy,
  }) = _DeviceInfo;

  factory DeviceInfo.fromJson(Map<String, dynamic> json) => _$DeviceInfoFromJson(json);
}

@freezed
class DeviceUpdate with _$DeviceUpdate {
  DeviceUpdate._();

  factory DeviceUpdate({
    required Device device,
    @JsonKey(name: 'deviceID')
    required String deviceId,
    int? numUsers,
  }) = _DeviceUpdate;

  factory DeviceUpdate.fromJson(Map<String, dynamic> json) => _$DeviceUpdateFromJson(json);
}

@freezed
class DeviceUser with _$DeviceUser {
  DeviceUser._();

  factory DeviceUser({
    Long? bytesDownloaded,
    Long? bytesUploaded,
    Device? device,
    bool? isOwner,
    AWSTimestamp? lastAccessTime,
    Space? lastConnectSpace,
    List<DeviceUserSpaceConfig?>? spaceConfigs,
    UserAccessStatus? status,
    User? user,
  }) = _DeviceUser;

  factory DeviceUser.fromJson(Map<String, dynamic> json) => _$DeviceUserFromJson(json);
}

@freezed
class DeviceUserSpaceConfig with _$DeviceUserSpaceConfig {
  DeviceUserSpaceConfig._();

  factory DeviceUserSpaceConfig({
    Space? space,
    bool? viewed,
    String? wgConfig,
    AWSTimestamp? wgConfigExpireAt,
    String? wgConfigName,
    AWSTimestamp? wgInactivityExpireAt,
    int? wgInactivityTimeout,
  }) = _DeviceUserSpaceConfig;

  factory DeviceUserSpaceConfig.fromJson(Map<String, dynamic> json) => _$DeviceUserSpaceConfigFromJson(json);
}

@freezed
class DeviceUserUpdate with _$DeviceUserUpdate {
  DeviceUserUpdate._();

  factory DeviceUserUpdate({
    @JsonKey(name: 'deviceID')
    required String deviceId,
    required DeviceUser deviceUser,
    @JsonKey(name: 'userID')
    required String userId,
  }) = _DeviceUserUpdate;

  factory DeviceUserUpdate.fromJson(Map<String, dynamic> json) => _$DeviceUserUpdateFromJson(json);
}

@freezed
class DeviceUsersConnection with _$DeviceUsersConnection {
  DeviceUsersConnection._();

  factory DeviceUsersConnection({
    List<DeviceUser?>? deviceUsers,
    List<DeviceUsersEdge?>? edges,
    PageInfo? pageInfo,
    int? totalCount,
  }) = _DeviceUsersConnection;

  factory DeviceUsersConnection.fromJson(Map<String, dynamic> json) => _$DeviceUsersConnectionFromJson(json);
}

@freezed
class DeviceUsersEdge with _$DeviceUsersEdge {
  DeviceUsersEdge._();

  factory DeviceUsersEdge({
    required DeviceUser node,
  }) = _DeviceUsersEdge;

  factory DeviceUsersEdge.fromJson(Map<String, dynamic> json) => _$DeviceUsersEdgeFromJson(json);
}

enum IpType {
  @JsonKey(name: 'IPv4')
  iPv4,
  @JsonKey(name: 'IPv6')
  iPv6,
}

@unfreezed
class Key with _$Key {
  Key._();

  factory Key({
    String? certificateRequest,
    String? keyTimestamp,
    required String publicKey,
  }) = _Key;

  factory Key.fromJson(Map<String, dynamic> json) => _$KeyFromJson(json);
}

@freezed
class MyCsCloudProps with _$MyCsCloudProps {
  MyCsCloudProps._();

  factory MyCsCloudProps({
    String? publicKey,
    @JsonKey(name: 'publicKeyID')
    String? publicKeyId,
  }) = _MyCsCloudProps;

  factory MyCsCloudProps.fromJson(Map<String, dynamic> json) => _$MyCsCloudPropsFromJson(json);
}

@freezed
class Nv with _$Nv {
  Nv._();

  factory Nv({
    required String name,
    String? value,
  }) = _Nv;

  factory Nv.fromJson(Map<String, dynamic> json) => _$NvFromJson(json);
}

@freezed
class Org with _$Org {
  Org._();

  factory Org({
    List<Address?>? addresses,
    List<UserRef?>? admins,
    String? businessRef,
    String? certificate,
    String? config,
    List<Contact?>? contacts,
    List<Nv?>? externalRefs,
    @JsonKey(name: 'orgID')
    required String orgId,
    required String orgName,
    UserRef? owner,
    String? publicKey,
    List<OrgQuota?>? quotas,
    OrgSpacesConnection? spaces,
    OrgUsersConnection? users,
    bool? verified,
  }) = _Org;

  factory Org.fromJson(Map<String, dynamic> json) => _$OrgFromJson(json);
}

@freezed
class OrgIi with _$OrgIi {
  OrgIi._();

  factory OrgIi({
    required String idKey,
    required OrgUser orgUser,
  }) = _OrgIi;

  factory OrgIi.fromJson(Map<String, dynamic> json) => _$OrgIiFromJson(json);
}

@freezed
class OrgQuota with _$OrgQuota {
  OrgQuota._();

  factory OrgQuota({
    String? description,
    required int limit,
    required QuotaPeriod period,
    required String quotaName,
    required bool softLimit,
    required int used,
  }) = _OrgQuota;

  factory OrgQuota.fromJson(Map<String, dynamic> json) => _$OrgQuotaFromJson(json);
}

@freezed
class OrgSpacesConnection with _$OrgSpacesConnection {
  OrgSpacesConnection._();

  factory OrgSpacesConnection({
    List<OrgSpacesEdge?>? edges,
    List<Space?>? orgSpaces,
    PageInfo? pageInfo,
    int? totalCount,
  }) = _OrgSpacesConnection;

  factory OrgSpacesConnection.fromJson(Map<String, dynamic> json) => _$OrgSpacesConnectionFromJson(json);
}

@freezed
class OrgSpacesEdge with _$OrgSpacesEdge {
  OrgSpacesEdge._();

  factory OrgSpacesEdge({
    required Space node,
  }) = _OrgSpacesEdge;

  factory OrgSpacesEdge.fromJson(Map<String, dynamic> json) => _$OrgSpacesEdgeFromJson(json);
}

@freezed
class OrgUser with _$OrgUser {
  OrgUser._();

  factory OrgUser({
    SpaceUsersConnection? accessList,
    bool? isAdmin,
    bool? isOwner,
    Org? org,
    UserAccessStatus? status,
    User? user,
  }) = _OrgUser;

  factory OrgUser.fromJson(Map<String, dynamic> json) => _$OrgUserFromJson(json);
}

@freezed
class OrgUsersConnection with _$OrgUsersConnection {
  OrgUsersConnection._();

  factory OrgUsersConnection({
    List<OrgUsersEdge?>? edges,
    List<OrgUser?>? orgUsers,
    PageInfo? pageInfo,
    int? totalCount,
  }) = _OrgUsersConnection;

  factory OrgUsersConnection.fromJson(Map<String, dynamic> json) => _$OrgUsersConnectionFromJson(json);
}

@freezed
class OrgUsersEdge with _$OrgUsersEdge {
  OrgUsersEdge._();

  factory OrgUsersEdge({
    required OrgUser node,
  }) = _OrgUsersEdge;

  factory OrgUsersEdge.fromJson(Map<String, dynamic> json) => _$OrgUsersEdgeFromJson(json);
}

@freezed
class PageInfo with _$PageInfo {
  PageInfo._();

  factory PageInfo({
    Cursor? cursor,
    required bool hasNextPage,
    required bool hasPreviousPage,
  }) = _PageInfo;

  factory PageInfo.fromJson(Map<String, dynamic> json) => _$PageInfoFromJson(json);
}

@unfreezed
class PublishDataInput with _$PublishDataInput {
  PublishDataInput._();

  factory PublishDataInput({
    required bool compressed,
    required String payload,
    required PublishDataType type,
  }) = _PublishDataInput;

  factory PublishDataInput.fromJson(Map<String, dynamic> json) => _$PublishDataInputFromJson(json);
}

enum PublishDataType {
  event,
}

@freezed
class PublishResult with _$PublishResult {
  PublishResult._();

  factory PublishResult({
    String? error,
    required bool success,
  }) = _PublishResult;

  factory PublishResult.fromJson(Map<String, dynamic> json) => _$PublishResultFromJson(json);
}

enum QuotaPeriod {
  daily,
  monthly,
  none,
  weekly,
  yearly,
}

@freezed
class Space with _$Space {
  Space._();

  factory Space({
    List<UserRef?>? admins,
    SpaceAppsConnection? apps,
    String? certificate,
    String? cookbook,
    String? domainName,
    String? fqdn,
    String? iaas,
    String? ipAddress,
    bool? isEgressNode,
    AWSTimestamp? lastSeen,
    @JsonKey(name: 'localCARoot')
    String? localCaRoot,
    int? meshNetworkBitmask,
    IPType? meshNetworkType,
    Org? org,
    UserRef? owner,
    int? port,
    String? publicKey,
    String? recipe,
    String? region,
    String? settings,
    @JsonKey(name: 'spaceID')
    required String spaceId,
    String? spaceName,
    SpaceStatus? status,
    SpaceUsersConnection? users,
    String? version,
    String? vpnType,
  }) = _Space;

  factory Space.fromJson(Map<String, dynamic> json) => _$SpaceFromJson(json);
}

@freezed
class SpaceAppsConnection with _$SpaceAppsConnection {
  SpaceAppsConnection._();

  factory SpaceAppsConnection({
    List<SpaceAppsEdge?>? edges,
    PageInfo? pageInfo,
    List<App?>? spaceApps,
    int? totalCount,
  }) = _SpaceAppsConnection;

  factory SpaceAppsConnection.fromJson(Map<String, dynamic> json) => _$SpaceAppsConnectionFromJson(json);
}

@freezed
class SpaceAppsEdge with _$SpaceAppsEdge {
  SpaceAppsEdge._();

  factory SpaceAppsEdge({
    required App node,
  }) = _SpaceAppsEdge;

  factory SpaceAppsEdge.fromJson(Map<String, dynamic> json) => _$SpaceAppsEdgeFromJson(json);
}

@freezed
class SpaceIi with _$SpaceIi {
  SpaceIi._();

  factory SpaceIi({
    required String idKey,
    required SpaceUser spaceUser,
  }) = _SpaceIi;

  factory SpaceIi.fromJson(Map<String, dynamic> json) => _$SpaceIiFromJson(json);
}

enum SpaceStatus {
  pending,
  running,
  shutdown,
  undeployed,
  unknown,
}

@freezed
class SpaceUpdate with _$SpaceUpdate {
  SpaceUpdate._();

  factory SpaceUpdate({
    int? numApps,
    int? numUsers,
    required Space space,
    @JsonKey(name: 'spaceID')
    required String spaceId,
  }) = _SpaceUpdate;

  factory SpaceUpdate.fromJson(Map<String, dynamic> json) => _$SpaceUpdateFromJson(json);
}

@freezed
class SpaceUser with _$SpaceUser {
  SpaceUser._();

  factory SpaceUser({
    AppUsersConnection? accessList,
    Long? bytesDownloaded,
    Long? bytesUploaded,
    bool? canUseSpaceForEgress,
    bool? enableSiteBlocking,
    bool? isAdmin,
    bool? isOwner,
    Device? lastConnectDevice,
    AWSTimestamp? lastConnectTime,
    Space? space,
    UserAccessStatus? status,
    User? user,
  }) = _SpaceUser;

  factory SpaceUser.fromJson(Map<String, dynamic> json) => _$SpaceUserFromJson(json);
}

@freezed
class SpaceUserUpdate with _$SpaceUserUpdate {
  SpaceUserUpdate._();

  factory SpaceUserUpdate({
    @JsonKey(name: 'spaceID')
    required String spaceId,
    required SpaceUser spaceUser,
    @JsonKey(name: 'userID')
    required String userId,
  }) = _SpaceUserUpdate;

  factory SpaceUserUpdate.fromJson(Map<String, dynamic> json) => _$SpaceUserUpdateFromJson(json);
}

@freezed
class SpaceUsersConnection with _$SpaceUsersConnection {
  SpaceUsersConnection._();

  factory SpaceUsersConnection({
    List<SpaceUsersEdge?>? edges,
    PageInfo? pageInfo,
    List<SpaceUser?>? spaceUsers,
    int? totalCount,
  }) = _SpaceUsersConnection;

  factory SpaceUsersConnection.fromJson(Map<String, dynamic> json) => _$SpaceUsersConnectionFromJson(json);
}

@freezed
class SpaceUsersEdge with _$SpaceUsersEdge {
  SpaceUsersEdge._();

  factory SpaceUsersEdge({
    required SpaceUser node,
  }) = _SpaceUsersEdge;

  factory SpaceUsersEdge.fromJson(Map<String, dynamic> json) => _$SpaceUsersEdgeFromJson(json);
}

@unfreezed
class SubInfoInput with _$SubInfoInput {
  SubInfoInput._();

  factory SubInfoInput({
    required List<String> args,
    required String name,
  }) = _SubInfoInput;

  factory SubInfoInput.fromJson(Map<String, dynamic> json) => _$SubInfoInputFromJson(json);
}

@unfreezed
class TableBooleanFilterInput with _$TableBooleanFilterInput {
  TableBooleanFilterInput._();

  factory TableBooleanFilterInput({
    bool? eq,
    bool? ne,
  }) = _TableBooleanFilterInput;

  factory TableBooleanFilterInput.fromJson(Map<String, dynamic> json) => _$TableBooleanFilterInputFromJson(json);
}

@unfreezed
class TableFloatFilterInput with _$TableFloatFilterInput {
  TableFloatFilterInput._();

  factory TableFloatFilterInput({
    List<double?>? between,
    double? contains,
    double? eq,
    double? ge,
    double? gt,
    double? le,
    double? lt,
    double? ne,
    double? notContains,
  }) = _TableFloatFilterInput;

  factory TableFloatFilterInput.fromJson(Map<String, dynamic> json) => _$TableFloatFilterInputFromJson(json);
}

@unfreezed
class TableIdFilterInput with _$TableIdFilterInput {
  TableIdFilterInput._();

  factory TableIdFilterInput({
    String? beginsWith,
    List<String?>? between,
    String? contains,
    String? eq,
    String? ge,
    String? gt,
    String? le,
    String? lt,
    String? ne,
    String? notContains,
  }) = _TableIdFilterInput;

  factory TableIdFilterInput.fromJson(Map<String, dynamic> json) => _$TableIdFilterInputFromJson(json);
}

@unfreezed
class TableIntFilterInput with _$TableIntFilterInput {
  TableIntFilterInput._();

  factory TableIntFilterInput({
    List<int?>? between,
    int? contains,
    int? eq,
    int? ge,
    int? gt,
    int? le,
    int? lt,
    int? ne,
    int? notContains,
  }) = _TableIntFilterInput;

  factory TableIntFilterInput.fromJson(Map<String, dynamic> json) => _$TableIntFilterInputFromJson(json);
}

@unfreezed
class TableStringFilterInput with _$TableStringFilterInput {
  TableStringFilterInput._();

  factory TableStringFilterInput({
    String? beginsWith,
    List<String?>? between,
    String? contains,
    String? eq,
    String? ge,
    String? gt,
    String? le,
    String? lt,
    String? ne,
    String? notContains,
  }) = _TableStringFilterInput;

  factory TableStringFilterInput.fromJson(Map<String, dynamic> json) => _$TableStringFilterInputFromJson(json);
}

@freezed
class User with _$User {
  User._();

  factory User({
    AppUsersConnection? apps,
    String? certificate,
    bool? confirmed,
    DeviceUsersConnection? devices,
    AWSEmail? emailAddress,
    List<Nv?>? externalRefs,
    String? familyName,
    String? firstName,
    String? middleName,
    String? mobilePhone,
    OrgUsersConnection? orgs,
    String? preferredName,
    String? publicKey,
    SpaceUsersConnection? spaces,
    String? universalConfig,
    @JsonKey(name: 'userID')
    required String userId,
    String? userName,
  }) = _User;

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
}

@unfreezed
class UserAccessConfig with _$UserAccessConfig {
  UserAccessConfig._();

  factory UserAccessConfig({
    bool? viewed,
    String? wgConfig,
    String? wgConfigName,
    int? wgExpirationTimeout,
    int? wgInactivityTimeout,
  }) = _UserAccessConfig;

  factory UserAccessConfig.fromJson(Map<String, dynamic> json) => _$UserAccessConfigFromJson(json);
}

enum UserAccessStatus {
  active,
  inactive,
  pending,
}

@freezed
class UserRef with _$UserRef {
  UserRef._();

  factory UserRef({
    String? familyName,
    String? firstName,
    String? middleName,
    @JsonKey(name: 'userID')
    required String userId,
    String? userName,
  }) = _UserRef;

  factory UserRef.fromJson(Map<String, dynamic> json) => _$UserRefFromJson(json);
}

@unfreezed
class UserSearchFilterInput with _$UserSearchFilterInput {
  UserSearchFilterInput._();

  factory UserSearchFilterInput({
    String? emailAddress,
    String? userName,
  }) = _UserSearchFilterInput;

  factory UserSearchFilterInput.fromJson(Map<String, dynamic> json) => _$UserSearchFilterInputFromJson(json);
}

@freezed
class UserUpdate with _$UserUpdate {
  UserUpdate._();

  factory UserUpdate({
    int? numApps,
    int? numDevices,
    int? numSpaces,
    required User user,
    @JsonKey(name: 'userID')
    required String userId,
  }) = _UserUpdate;

  factory UserUpdate.fromJson(Map<String, dynamic> json) => _$UserUpdateFromJson(json);
}