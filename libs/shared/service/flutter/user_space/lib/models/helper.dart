import 'package:user_space_model/user_space_model.dart';

/// User space model helper functions

extension UserExtension on User {
  /// Returns the user's full name
  String? get fullName {
    final fullNameBuilder = StringBuffer();
    if (firstName != null && firstName!.isNotEmpty) {
      fullNameBuilder.write(firstName);
    }
    if (middleName != null && middleName!.isNotEmpty) {
      fullNameBuilder.write(' ');
      fullNameBuilder.write(middleName);
      if (middleName!.length == 1) {
        fullNameBuilder.write('.');
      }
    }
    if (familyName != null && familyName!.isNotEmpty) {
      fullNameBuilder.write(' ');
      fullNameBuilder.write(familyName);
    }
    final fullName = fullNameBuilder.toString().trim();
    return fullName.isNotEmpty ? fullName : null;
  }
}
