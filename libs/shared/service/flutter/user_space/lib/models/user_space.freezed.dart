// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_space.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Address _$AddressFromJson(Map<String, dynamic> json) {
  return _Address.fromJson(json);
}

/// @nodoc
mixin _$Address {
  String? get countryCode => throw _privateConstructorUsedError;
  String? get county => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get municipality => throw _privateConstructorUsedError;
  String? get number => throw _privateConstructorUsedError;
  String? get other => throw _privateConstructorUsedError;
  String? get postalCode => throw _privateConstructorUsedError;
  String? get province => throw _privateConstructorUsedError;
  String? get street => throw _privateConstructorUsedError;
  AddressType get type => throw _privateConstructorUsedError;

  /// Serializes this Address to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Address
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AddressCopyWith<Address> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AddressCopyWith<$Res> {
  factory $AddressCopyWith(Address value, $Res Function(Address) then) =
      _$AddressCopyWithImpl<$Res, Address>;
  @useResult
  $Res call(
      {String? countryCode,
      String? county,
      String? description,
      String? municipality,
      String? number,
      String? other,
      String? postalCode,
      String? province,
      String? street,
      AddressType type});
}

/// @nodoc
class _$AddressCopyWithImpl<$Res, $Val extends Address>
    implements $AddressCopyWith<$Res> {
  _$AddressCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Address
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? countryCode = freezed,
    Object? county = freezed,
    Object? description = freezed,
    Object? municipality = freezed,
    Object? number = freezed,
    Object? other = freezed,
    Object? postalCode = freezed,
    Object? province = freezed,
    Object? street = freezed,
    Object? type = null,
  }) {
    return _then(_value.copyWith(
      countryCode: freezed == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String?,
      county: freezed == county
          ? _value.county
          : county // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      municipality: freezed == municipality
          ? _value.municipality
          : municipality // ignore: cast_nullable_to_non_nullable
              as String?,
      number: freezed == number
          ? _value.number
          : number // ignore: cast_nullable_to_non_nullable
              as String?,
      other: freezed == other
          ? _value.other
          : other // ignore: cast_nullable_to_non_nullable
              as String?,
      postalCode: freezed == postalCode
          ? _value.postalCode
          : postalCode // ignore: cast_nullable_to_non_nullable
              as String?,
      province: freezed == province
          ? _value.province
          : province // ignore: cast_nullable_to_non_nullable
              as String?,
      street: freezed == street
          ? _value.street
          : street // ignore: cast_nullable_to_non_nullable
              as String?,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as AddressType,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AddressImplCopyWith<$Res> implements $AddressCopyWith<$Res> {
  factory _$$AddressImplCopyWith(
          _$AddressImpl value, $Res Function(_$AddressImpl) then) =
      __$$AddressImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? countryCode,
      String? county,
      String? description,
      String? municipality,
      String? number,
      String? other,
      String? postalCode,
      String? province,
      String? street,
      AddressType type});
}

/// @nodoc
class __$$AddressImplCopyWithImpl<$Res>
    extends _$AddressCopyWithImpl<$Res, _$AddressImpl>
    implements _$$AddressImplCopyWith<$Res> {
  __$$AddressImplCopyWithImpl(
      _$AddressImpl _value, $Res Function(_$AddressImpl) _then)
      : super(_value, _then);

  /// Create a copy of Address
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? countryCode = freezed,
    Object? county = freezed,
    Object? description = freezed,
    Object? municipality = freezed,
    Object? number = freezed,
    Object? other = freezed,
    Object? postalCode = freezed,
    Object? province = freezed,
    Object? street = freezed,
    Object? type = null,
  }) {
    return _then(_$AddressImpl(
      countryCode: freezed == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String?,
      county: freezed == county
          ? _value.county
          : county // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      municipality: freezed == municipality
          ? _value.municipality
          : municipality // ignore: cast_nullable_to_non_nullable
              as String?,
      number: freezed == number
          ? _value.number
          : number // ignore: cast_nullable_to_non_nullable
              as String?,
      other: freezed == other
          ? _value.other
          : other // ignore: cast_nullable_to_non_nullable
              as String?,
      postalCode: freezed == postalCode
          ? _value.postalCode
          : postalCode // ignore: cast_nullable_to_non_nullable
              as String?,
      province: freezed == province
          ? _value.province
          : province // ignore: cast_nullable_to_non_nullable
              as String?,
      street: freezed == street
          ? _value.street
          : street // ignore: cast_nullable_to_non_nullable
              as String?,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as AddressType,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AddressImpl extends _Address with DiagnosticableTreeMixin {
  _$AddressImpl(
      {this.countryCode,
      this.county,
      this.description,
      this.municipality,
      this.number,
      this.other,
      this.postalCode,
      this.province,
      this.street,
      required this.type})
      : super._();

  factory _$AddressImpl.fromJson(Map<String, dynamic> json) =>
      _$$AddressImplFromJson(json);

  @override
  final String? countryCode;
  @override
  final String? county;
  @override
  final String? description;
  @override
  final String? municipality;
  @override
  final String? number;
  @override
  final String? other;
  @override
  final String? postalCode;
  @override
  final String? province;
  @override
  final String? street;
  @override
  final AddressType type;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'Address(countryCode: $countryCode, county: $county, description: $description, municipality: $municipality, number: $number, other: $other, postalCode: $postalCode, province: $province, street: $street, type: $type)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'Address'))
      ..add(DiagnosticsProperty('countryCode', countryCode))
      ..add(DiagnosticsProperty('county', county))
      ..add(DiagnosticsProperty('description', description))
      ..add(DiagnosticsProperty('municipality', municipality))
      ..add(DiagnosticsProperty('number', number))
      ..add(DiagnosticsProperty('other', other))
      ..add(DiagnosticsProperty('postalCode', postalCode))
      ..add(DiagnosticsProperty('province', province))
      ..add(DiagnosticsProperty('street', street))
      ..add(DiagnosticsProperty('type', type));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddressImpl &&
            (identical(other.countryCode, countryCode) ||
                other.countryCode == countryCode) &&
            (identical(other.county, county) || other.county == county) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.municipality, municipality) ||
                other.municipality == municipality) &&
            (identical(other.number, number) || other.number == number) &&
            (identical(other.other, this.other) || other.other == this.other) &&
            (identical(other.postalCode, postalCode) ||
                other.postalCode == postalCode) &&
            (identical(other.province, province) ||
                other.province == province) &&
            (identical(other.street, street) || other.street == street) &&
            (identical(other.type, type) || other.type == type));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, countryCode, county, description,
      municipality, number, other, postalCode, province, street, type);

  /// Create a copy of Address
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddressImplCopyWith<_$AddressImpl> get copyWith =>
      __$$AddressImplCopyWithImpl<_$AddressImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AddressImplToJson(
      this,
    );
  }
}

abstract class _Address extends Address {
  factory _Address(
      {final String? countryCode,
      final String? county,
      final String? description,
      final String? municipality,
      final String? number,
      final String? other,
      final String? postalCode,
      final String? province,
      final String? street,
      required final AddressType type}) = _$AddressImpl;
  _Address._() : super._();

  factory _Address.fromJson(Map<String, dynamic> json) = _$AddressImpl.fromJson;

  @override
  String? get countryCode;
  @override
  String? get county;
  @override
  String? get description;
  @override
  String? get municipality;
  @override
  String? get number;
  @override
  String? get other;
  @override
  String? get postalCode;
  @override
  String? get province;
  @override
  String? get street;
  @override
  AddressType get type;

  /// Create a copy of Address
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddressImplCopyWith<_$AddressImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AddressInput _$AddressInputFromJson(Map<String, dynamic> json) {
  return _AddressInput.fromJson(json);
}

/// @nodoc
mixin _$AddressInput {
  String? get countryCode => throw _privateConstructorUsedError;
  set countryCode(String? value) => throw _privateConstructorUsedError;
  String? get county => throw _privateConstructorUsedError;
  set county(String? value) => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  set description(String? value) => throw _privateConstructorUsedError;
  String? get municipality => throw _privateConstructorUsedError;
  set municipality(String? value) => throw _privateConstructorUsedError;
  String? get number => throw _privateConstructorUsedError;
  set number(String? value) => throw _privateConstructorUsedError;
  String? get other => throw _privateConstructorUsedError;
  set other(String? value) => throw _privateConstructorUsedError;
  String? get postalCode => throw _privateConstructorUsedError;
  set postalCode(String? value) => throw _privateConstructorUsedError;
  String? get province => throw _privateConstructorUsedError;
  set province(String? value) => throw _privateConstructorUsedError;
  String? get street => throw _privateConstructorUsedError;
  set street(String? value) => throw _privateConstructorUsedError;
  AddressType get type => throw _privateConstructorUsedError;
  set type(AddressType value) => throw _privateConstructorUsedError;

  /// Serializes this AddressInput to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AddressInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AddressInputCopyWith<AddressInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AddressInputCopyWith<$Res> {
  factory $AddressInputCopyWith(
          AddressInput value, $Res Function(AddressInput) then) =
      _$AddressInputCopyWithImpl<$Res, AddressInput>;
  @useResult
  $Res call(
      {String? countryCode,
      String? county,
      String? description,
      String? municipality,
      String? number,
      String? other,
      String? postalCode,
      String? province,
      String? street,
      AddressType type});
}

/// @nodoc
class _$AddressInputCopyWithImpl<$Res, $Val extends AddressInput>
    implements $AddressInputCopyWith<$Res> {
  _$AddressInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AddressInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? countryCode = freezed,
    Object? county = freezed,
    Object? description = freezed,
    Object? municipality = freezed,
    Object? number = freezed,
    Object? other = freezed,
    Object? postalCode = freezed,
    Object? province = freezed,
    Object? street = freezed,
    Object? type = null,
  }) {
    return _then(_value.copyWith(
      countryCode: freezed == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String?,
      county: freezed == county
          ? _value.county
          : county // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      municipality: freezed == municipality
          ? _value.municipality
          : municipality // ignore: cast_nullable_to_non_nullable
              as String?,
      number: freezed == number
          ? _value.number
          : number // ignore: cast_nullable_to_non_nullable
              as String?,
      other: freezed == other
          ? _value.other
          : other // ignore: cast_nullable_to_non_nullable
              as String?,
      postalCode: freezed == postalCode
          ? _value.postalCode
          : postalCode // ignore: cast_nullable_to_non_nullable
              as String?,
      province: freezed == province
          ? _value.province
          : province // ignore: cast_nullable_to_non_nullable
              as String?,
      street: freezed == street
          ? _value.street
          : street // ignore: cast_nullable_to_non_nullable
              as String?,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as AddressType,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AddressInputImplCopyWith<$Res>
    implements $AddressInputCopyWith<$Res> {
  factory _$$AddressInputImplCopyWith(
          _$AddressInputImpl value, $Res Function(_$AddressInputImpl) then) =
      __$$AddressInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? countryCode,
      String? county,
      String? description,
      String? municipality,
      String? number,
      String? other,
      String? postalCode,
      String? province,
      String? street,
      AddressType type});
}

/// @nodoc
class __$$AddressInputImplCopyWithImpl<$Res>
    extends _$AddressInputCopyWithImpl<$Res, _$AddressInputImpl>
    implements _$$AddressInputImplCopyWith<$Res> {
  __$$AddressInputImplCopyWithImpl(
      _$AddressInputImpl _value, $Res Function(_$AddressInputImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddressInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? countryCode = freezed,
    Object? county = freezed,
    Object? description = freezed,
    Object? municipality = freezed,
    Object? number = freezed,
    Object? other = freezed,
    Object? postalCode = freezed,
    Object? province = freezed,
    Object? street = freezed,
    Object? type = null,
  }) {
    return _then(_$AddressInputImpl(
      countryCode: freezed == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String?,
      county: freezed == county
          ? _value.county
          : county // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      municipality: freezed == municipality
          ? _value.municipality
          : municipality // ignore: cast_nullable_to_non_nullable
              as String?,
      number: freezed == number
          ? _value.number
          : number // ignore: cast_nullable_to_non_nullable
              as String?,
      other: freezed == other
          ? _value.other
          : other // ignore: cast_nullable_to_non_nullable
              as String?,
      postalCode: freezed == postalCode
          ? _value.postalCode
          : postalCode // ignore: cast_nullable_to_non_nullable
              as String?,
      province: freezed == province
          ? _value.province
          : province // ignore: cast_nullable_to_non_nullable
              as String?,
      street: freezed == street
          ? _value.street
          : street // ignore: cast_nullable_to_non_nullable
              as String?,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as AddressType,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AddressInputImpl extends _AddressInput with DiagnosticableTreeMixin {
  _$AddressInputImpl(
      {this.countryCode,
      this.county,
      this.description,
      this.municipality,
      this.number,
      this.other,
      this.postalCode,
      this.province,
      this.street,
      required this.type})
      : super._();

  factory _$AddressInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$AddressInputImplFromJson(json);

  @override
  String? countryCode;
  @override
  String? county;
  @override
  String? description;
  @override
  String? municipality;
  @override
  String? number;
  @override
  String? other;
  @override
  String? postalCode;
  @override
  String? province;
  @override
  String? street;
  @override
  AddressType type;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AddressInput(countryCode: $countryCode, county: $county, description: $description, municipality: $municipality, number: $number, other: $other, postalCode: $postalCode, province: $province, street: $street, type: $type)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AddressInput'))
      ..add(DiagnosticsProperty('countryCode', countryCode))
      ..add(DiagnosticsProperty('county', county))
      ..add(DiagnosticsProperty('description', description))
      ..add(DiagnosticsProperty('municipality', municipality))
      ..add(DiagnosticsProperty('number', number))
      ..add(DiagnosticsProperty('other', other))
      ..add(DiagnosticsProperty('postalCode', postalCode))
      ..add(DiagnosticsProperty('province', province))
      ..add(DiagnosticsProperty('street', street))
      ..add(DiagnosticsProperty('type', type));
  }

  /// Create a copy of AddressInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddressInputImplCopyWith<_$AddressInputImpl> get copyWith =>
      __$$AddressInputImplCopyWithImpl<_$AddressInputImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AddressInputImplToJson(
      this,
    );
  }
}

abstract class _AddressInput extends AddressInput {
  factory _AddressInput(
      {String? countryCode,
      String? county,
      String? description,
      String? municipality,
      String? number,
      String? other,
      String? postalCode,
      String? province,
      String? street,
      required AddressType type}) = _$AddressInputImpl;
  _AddressInput._() : super._();

  factory _AddressInput.fromJson(Map<String, dynamic> json) =
      _$AddressInputImpl.fromJson;

  @override
  String? get countryCode;
  set countryCode(String? value);
  @override
  String? get county;
  set county(String? value);
  @override
  String? get description;
  set description(String? value);
  @override
  String? get municipality;
  set municipality(String? value);
  @override
  String? get number;
  set number(String? value);
  @override
  String? get other;
  set other(String? value);
  @override
  String? get postalCode;
  set postalCode(String? value);
  @override
  String? get province;
  set province(String? value);
  @override
  String? get street;
  set street(String? value);
  @override
  AddressType get type;
  set type(AddressType value);

  /// Create a copy of AddressInput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddressInputImplCopyWith<_$AddressInputImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

App _$AppFromJson(Map<String, dynamic> json) {
  return _App.fromJson(json);
}

/// @nodoc
mixin _$App {
  @JsonKey(name: 'appID')
  String get appId => throw _privateConstructorUsedError;
  String? get appName => throw _privateConstructorUsedError;
  String? get certificate => throw _privateConstructorUsedError;
  String? get cookbook => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get domainName => throw _privateConstructorUsedError;
  String? get iaas => throw _privateConstructorUsedError;
  int? get lastSeen => throw _privateConstructorUsedError;
  int? get port => throw _privateConstructorUsedError;
  String? get publicKey => throw _privateConstructorUsedError;
  String? get recipe => throw _privateConstructorUsedError;
  String? get region => throw _privateConstructorUsedError;
  Space? get space => throw _privateConstructorUsedError;
  AppStatus? get status => throw _privateConstructorUsedError;
  AppUsersConnection? get users => throw _privateConstructorUsedError;
  String? get version => throw _privateConstructorUsedError;

  /// Serializes this App to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of App
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AppCopyWith<App> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppCopyWith<$Res> {
  factory $AppCopyWith(App value, $Res Function(App) then) =
      _$AppCopyWithImpl<$Res, App>;
  @useResult
  $Res call(
      {@JsonKey(name: 'appID') String appId,
      String? appName,
      String? certificate,
      String? cookbook,
      String? description,
      String? domainName,
      String? iaas,
      int? lastSeen,
      int? port,
      String? publicKey,
      String? recipe,
      String? region,
      Space? space,
      AppStatus? status,
      AppUsersConnection? users,
      String? version});

  $SpaceCopyWith<$Res>? get space;
  $AppUsersConnectionCopyWith<$Res>? get users;
}

/// @nodoc
class _$AppCopyWithImpl<$Res, $Val extends App> implements $AppCopyWith<$Res> {
  _$AppCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of App
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? appId = null,
    Object? appName = freezed,
    Object? certificate = freezed,
    Object? cookbook = freezed,
    Object? description = freezed,
    Object? domainName = freezed,
    Object? iaas = freezed,
    Object? lastSeen = freezed,
    Object? port = freezed,
    Object? publicKey = freezed,
    Object? recipe = freezed,
    Object? region = freezed,
    Object? space = freezed,
    Object? status = freezed,
    Object? users = freezed,
    Object? version = freezed,
  }) {
    return _then(_value.copyWith(
      appId: null == appId
          ? _value.appId
          : appId // ignore: cast_nullable_to_non_nullable
              as String,
      appName: freezed == appName
          ? _value.appName
          : appName // ignore: cast_nullable_to_non_nullable
              as String?,
      certificate: freezed == certificate
          ? _value.certificate
          : certificate // ignore: cast_nullable_to_non_nullable
              as String?,
      cookbook: freezed == cookbook
          ? _value.cookbook
          : cookbook // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      domainName: freezed == domainName
          ? _value.domainName
          : domainName // ignore: cast_nullable_to_non_nullable
              as String?,
      iaas: freezed == iaas
          ? _value.iaas
          : iaas // ignore: cast_nullable_to_non_nullable
              as String?,
      lastSeen: freezed == lastSeen
          ? _value.lastSeen
          : lastSeen // ignore: cast_nullable_to_non_nullable
              as int?,
      port: freezed == port
          ? _value.port
          : port // ignore: cast_nullable_to_non_nullable
              as int?,
      publicKey: freezed == publicKey
          ? _value.publicKey
          : publicKey // ignore: cast_nullable_to_non_nullable
              as String?,
      recipe: freezed == recipe
          ? _value.recipe
          : recipe // ignore: cast_nullable_to_non_nullable
              as String?,
      region: freezed == region
          ? _value.region
          : region // ignore: cast_nullable_to_non_nullable
              as String?,
      space: freezed == space
          ? _value.space
          : space // ignore: cast_nullable_to_non_nullable
              as Space?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as AppStatus?,
      users: freezed == users
          ? _value.users
          : users // ignore: cast_nullable_to_non_nullable
              as AppUsersConnection?,
      version: freezed == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of App
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SpaceCopyWith<$Res>? get space {
    if (_value.space == null) {
      return null;
    }

    return $SpaceCopyWith<$Res>(_value.space!, (value) {
      return _then(_value.copyWith(space: value) as $Val);
    });
  }

  /// Create a copy of App
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppUsersConnectionCopyWith<$Res>? get users {
    if (_value.users == null) {
      return null;
    }

    return $AppUsersConnectionCopyWith<$Res>(_value.users!, (value) {
      return _then(_value.copyWith(users: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AppImplCopyWith<$Res> implements $AppCopyWith<$Res> {
  factory _$$AppImplCopyWith(_$AppImpl value, $Res Function(_$AppImpl) then) =
      __$$AppImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'appID') String appId,
      String? appName,
      String? certificate,
      String? cookbook,
      String? description,
      String? domainName,
      String? iaas,
      int? lastSeen,
      int? port,
      String? publicKey,
      String? recipe,
      String? region,
      Space? space,
      AppStatus? status,
      AppUsersConnection? users,
      String? version});

  @override
  $SpaceCopyWith<$Res>? get space;
  @override
  $AppUsersConnectionCopyWith<$Res>? get users;
}

/// @nodoc
class __$$AppImplCopyWithImpl<$Res> extends _$AppCopyWithImpl<$Res, _$AppImpl>
    implements _$$AppImplCopyWith<$Res> {
  __$$AppImplCopyWithImpl(_$AppImpl _value, $Res Function(_$AppImpl) _then)
      : super(_value, _then);

  /// Create a copy of App
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? appId = null,
    Object? appName = freezed,
    Object? certificate = freezed,
    Object? cookbook = freezed,
    Object? description = freezed,
    Object? domainName = freezed,
    Object? iaas = freezed,
    Object? lastSeen = freezed,
    Object? port = freezed,
    Object? publicKey = freezed,
    Object? recipe = freezed,
    Object? region = freezed,
    Object? space = freezed,
    Object? status = freezed,
    Object? users = freezed,
    Object? version = freezed,
  }) {
    return _then(_$AppImpl(
      appId: null == appId
          ? _value.appId
          : appId // ignore: cast_nullable_to_non_nullable
              as String,
      appName: freezed == appName
          ? _value.appName
          : appName // ignore: cast_nullable_to_non_nullable
              as String?,
      certificate: freezed == certificate
          ? _value.certificate
          : certificate // ignore: cast_nullable_to_non_nullable
              as String?,
      cookbook: freezed == cookbook
          ? _value.cookbook
          : cookbook // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      domainName: freezed == domainName
          ? _value.domainName
          : domainName // ignore: cast_nullable_to_non_nullable
              as String?,
      iaas: freezed == iaas
          ? _value.iaas
          : iaas // ignore: cast_nullable_to_non_nullable
              as String?,
      lastSeen: freezed == lastSeen
          ? _value.lastSeen
          : lastSeen // ignore: cast_nullable_to_non_nullable
              as int?,
      port: freezed == port
          ? _value.port
          : port // ignore: cast_nullable_to_non_nullable
              as int?,
      publicKey: freezed == publicKey
          ? _value.publicKey
          : publicKey // ignore: cast_nullable_to_non_nullable
              as String?,
      recipe: freezed == recipe
          ? _value.recipe
          : recipe // ignore: cast_nullable_to_non_nullable
              as String?,
      region: freezed == region
          ? _value.region
          : region // ignore: cast_nullable_to_non_nullable
              as String?,
      space: freezed == space
          ? _value.space
          : space // ignore: cast_nullable_to_non_nullable
              as Space?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as AppStatus?,
      users: freezed == users
          ? _value.users
          : users // ignore: cast_nullable_to_non_nullable
              as AppUsersConnection?,
      version: freezed == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AppImpl extends _App with DiagnosticableTreeMixin {
  _$AppImpl(
      {@JsonKey(name: 'appID') required this.appId,
      this.appName,
      this.certificate,
      this.cookbook,
      this.description,
      this.domainName,
      this.iaas,
      this.lastSeen,
      this.port,
      this.publicKey,
      this.recipe,
      this.region,
      this.space,
      this.status,
      this.users,
      this.version})
      : super._();

  factory _$AppImpl.fromJson(Map<String, dynamic> json) =>
      _$$AppImplFromJson(json);

  @override
  @JsonKey(name: 'appID')
  final String appId;
  @override
  final String? appName;
  @override
  final String? certificate;
  @override
  final String? cookbook;
  @override
  final String? description;
  @override
  final String? domainName;
  @override
  final String? iaas;
  @override
  final int? lastSeen;
  @override
  final int? port;
  @override
  final String? publicKey;
  @override
  final String? recipe;
  @override
  final String? region;
  @override
  final Space? space;
  @override
  final AppStatus? status;
  @override
  final AppUsersConnection? users;
  @override
  final String? version;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'App(appId: $appId, appName: $appName, certificate: $certificate, cookbook: $cookbook, description: $description, domainName: $domainName, iaas: $iaas, lastSeen: $lastSeen, port: $port, publicKey: $publicKey, recipe: $recipe, region: $region, space: $space, status: $status, users: $users, version: $version)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'App'))
      ..add(DiagnosticsProperty('appId', appId))
      ..add(DiagnosticsProperty('appName', appName))
      ..add(DiagnosticsProperty('certificate', certificate))
      ..add(DiagnosticsProperty('cookbook', cookbook))
      ..add(DiagnosticsProperty('description', description))
      ..add(DiagnosticsProperty('domainName', domainName))
      ..add(DiagnosticsProperty('iaas', iaas))
      ..add(DiagnosticsProperty('lastSeen', lastSeen))
      ..add(DiagnosticsProperty('port', port))
      ..add(DiagnosticsProperty('publicKey', publicKey))
      ..add(DiagnosticsProperty('recipe', recipe))
      ..add(DiagnosticsProperty('region', region))
      ..add(DiagnosticsProperty('space', space))
      ..add(DiagnosticsProperty('status', status))
      ..add(DiagnosticsProperty('users', users))
      ..add(DiagnosticsProperty('version', version));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppImpl &&
            (identical(other.appId, appId) || other.appId == appId) &&
            (identical(other.appName, appName) || other.appName == appName) &&
            (identical(other.certificate, certificate) ||
                other.certificate == certificate) &&
            (identical(other.cookbook, cookbook) ||
                other.cookbook == cookbook) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.domainName, domainName) ||
                other.domainName == domainName) &&
            (identical(other.iaas, iaas) || other.iaas == iaas) &&
            (identical(other.lastSeen, lastSeen) ||
                other.lastSeen == lastSeen) &&
            (identical(other.port, port) || other.port == port) &&
            (identical(other.publicKey, publicKey) ||
                other.publicKey == publicKey) &&
            (identical(other.recipe, recipe) || other.recipe == recipe) &&
            (identical(other.region, region) || other.region == region) &&
            (identical(other.space, space) || other.space == space) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.users, users) || other.users == users) &&
            (identical(other.version, version) || other.version == version));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      appId,
      appName,
      certificate,
      cookbook,
      description,
      domainName,
      iaas,
      lastSeen,
      port,
      publicKey,
      recipe,
      region,
      space,
      status,
      users,
      version);

  /// Create a copy of App
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AppImplCopyWith<_$AppImpl> get copyWith =>
      __$$AppImplCopyWithImpl<_$AppImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AppImplToJson(
      this,
    );
  }
}

abstract class _App extends App {
  factory _App(
      {@JsonKey(name: 'appID') required final String appId,
      final String? appName,
      final String? certificate,
      final String? cookbook,
      final String? description,
      final String? domainName,
      final String? iaas,
      final int? lastSeen,
      final int? port,
      final String? publicKey,
      final String? recipe,
      final String? region,
      final Space? space,
      final AppStatus? status,
      final AppUsersConnection? users,
      final String? version}) = _$AppImpl;
  _App._() : super._();

  factory _App.fromJson(Map<String, dynamic> json) = _$AppImpl.fromJson;

  @override
  @JsonKey(name: 'appID')
  String get appId;
  @override
  String? get appName;
  @override
  String? get certificate;
  @override
  String? get cookbook;
  @override
  String? get description;
  @override
  String? get domainName;
  @override
  String? get iaas;
  @override
  int? get lastSeen;
  @override
  int? get port;
  @override
  String? get publicKey;
  @override
  String? get recipe;
  @override
  String? get region;
  @override
  Space? get space;
  @override
  AppStatus? get status;
  @override
  AppUsersConnection? get users;
  @override
  String? get version;

  /// Create a copy of App
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AppImplCopyWith<_$AppImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AppIi _$AppIiFromJson(Map<String, dynamic> json) {
  return _AppIi.fromJson(json);
}

/// @nodoc
mixin _$AppIi {
  App get app => throw _privateConstructorUsedError;
  String get idKey => throw _privateConstructorUsedError;

  /// Serializes this AppIi to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AppIi
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AppIiCopyWith<AppIi> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppIiCopyWith<$Res> {
  factory $AppIiCopyWith(AppIi value, $Res Function(AppIi) then) =
      _$AppIiCopyWithImpl<$Res, AppIi>;
  @useResult
  $Res call({App app, String idKey});

  $AppCopyWith<$Res> get app;
}

/// @nodoc
class _$AppIiCopyWithImpl<$Res, $Val extends AppIi>
    implements $AppIiCopyWith<$Res> {
  _$AppIiCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AppIi
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? app = null,
    Object? idKey = null,
  }) {
    return _then(_value.copyWith(
      app: null == app
          ? _value.app
          : app // ignore: cast_nullable_to_non_nullable
              as App,
      idKey: null == idKey
          ? _value.idKey
          : idKey // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }

  /// Create a copy of AppIi
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppCopyWith<$Res> get app {
    return $AppCopyWith<$Res>(_value.app, (value) {
      return _then(_value.copyWith(app: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AppIiImplCopyWith<$Res> implements $AppIiCopyWith<$Res> {
  factory _$$AppIiImplCopyWith(
          _$AppIiImpl value, $Res Function(_$AppIiImpl) then) =
      __$$AppIiImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({App app, String idKey});

  @override
  $AppCopyWith<$Res> get app;
}

/// @nodoc
class __$$AppIiImplCopyWithImpl<$Res>
    extends _$AppIiCopyWithImpl<$Res, _$AppIiImpl>
    implements _$$AppIiImplCopyWith<$Res> {
  __$$AppIiImplCopyWithImpl(
      _$AppIiImpl _value, $Res Function(_$AppIiImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppIi
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? app = null,
    Object? idKey = null,
  }) {
    return _then(_$AppIiImpl(
      app: null == app
          ? _value.app
          : app // ignore: cast_nullable_to_non_nullable
              as App,
      idKey: null == idKey
          ? _value.idKey
          : idKey // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AppIiImpl extends _AppIi with DiagnosticableTreeMixin {
  _$AppIiImpl({required this.app, required this.idKey}) : super._();

  factory _$AppIiImpl.fromJson(Map<String, dynamic> json) =>
      _$$AppIiImplFromJson(json);

  @override
  final App app;
  @override
  final String idKey;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AppIi(app: $app, idKey: $idKey)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AppIi'))
      ..add(DiagnosticsProperty('app', app))
      ..add(DiagnosticsProperty('idKey', idKey));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppIiImpl &&
            (identical(other.app, app) || other.app == app) &&
            (identical(other.idKey, idKey) || other.idKey == idKey));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, app, idKey);

  /// Create a copy of AppIi
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AppIiImplCopyWith<_$AppIiImpl> get copyWith =>
      __$$AppIiImplCopyWithImpl<_$AppIiImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AppIiImplToJson(
      this,
    );
  }
}

abstract class _AppIi extends AppIi {
  factory _AppIi({required final App app, required final String idKey}) =
      _$AppIiImpl;
  _AppIi._() : super._();

  factory _AppIi.fromJson(Map<String, dynamic> json) = _$AppIiImpl.fromJson;

  @override
  App get app;
  @override
  String get idKey;

  /// Create a copy of AppIi
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AppIiImplCopyWith<_$AppIiImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AppUpdate _$AppUpdateFromJson(Map<String, dynamic> json) {
  return _AppUpdate.fromJson(json);
}

/// @nodoc
mixin _$AppUpdate {
  App get app => throw _privateConstructorUsedError;
  @JsonKey(name: 'appID')
  String get appId => throw _privateConstructorUsedError;
  int? get numUsers => throw _privateConstructorUsedError;

  /// Serializes this AppUpdate to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AppUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AppUpdateCopyWith<AppUpdate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppUpdateCopyWith<$Res> {
  factory $AppUpdateCopyWith(AppUpdate value, $Res Function(AppUpdate) then) =
      _$AppUpdateCopyWithImpl<$Res, AppUpdate>;
  @useResult
  $Res call({App app, @JsonKey(name: 'appID') String appId, int? numUsers});

  $AppCopyWith<$Res> get app;
}

/// @nodoc
class _$AppUpdateCopyWithImpl<$Res, $Val extends AppUpdate>
    implements $AppUpdateCopyWith<$Res> {
  _$AppUpdateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AppUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? app = null,
    Object? appId = null,
    Object? numUsers = freezed,
  }) {
    return _then(_value.copyWith(
      app: null == app
          ? _value.app
          : app // ignore: cast_nullable_to_non_nullable
              as App,
      appId: null == appId
          ? _value.appId
          : appId // ignore: cast_nullable_to_non_nullable
              as String,
      numUsers: freezed == numUsers
          ? _value.numUsers
          : numUsers // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }

  /// Create a copy of AppUpdate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppCopyWith<$Res> get app {
    return $AppCopyWith<$Res>(_value.app, (value) {
      return _then(_value.copyWith(app: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AppUpdateImplCopyWith<$Res>
    implements $AppUpdateCopyWith<$Res> {
  factory _$$AppUpdateImplCopyWith(
          _$AppUpdateImpl value, $Res Function(_$AppUpdateImpl) then) =
      __$$AppUpdateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({App app, @JsonKey(name: 'appID') String appId, int? numUsers});

  @override
  $AppCopyWith<$Res> get app;
}

/// @nodoc
class __$$AppUpdateImplCopyWithImpl<$Res>
    extends _$AppUpdateCopyWithImpl<$Res, _$AppUpdateImpl>
    implements _$$AppUpdateImplCopyWith<$Res> {
  __$$AppUpdateImplCopyWithImpl(
      _$AppUpdateImpl _value, $Res Function(_$AppUpdateImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? app = null,
    Object? appId = null,
    Object? numUsers = freezed,
  }) {
    return _then(_$AppUpdateImpl(
      app: null == app
          ? _value.app
          : app // ignore: cast_nullable_to_non_nullable
              as App,
      appId: null == appId
          ? _value.appId
          : appId // ignore: cast_nullable_to_non_nullable
              as String,
      numUsers: freezed == numUsers
          ? _value.numUsers
          : numUsers // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AppUpdateImpl extends _AppUpdate with DiagnosticableTreeMixin {
  _$AppUpdateImpl(
      {required this.app,
      @JsonKey(name: 'appID') required this.appId,
      this.numUsers})
      : super._();

  factory _$AppUpdateImpl.fromJson(Map<String, dynamic> json) =>
      _$$AppUpdateImplFromJson(json);

  @override
  final App app;
  @override
  @JsonKey(name: 'appID')
  final String appId;
  @override
  final int? numUsers;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AppUpdate(app: $app, appId: $appId, numUsers: $numUsers)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AppUpdate'))
      ..add(DiagnosticsProperty('app', app))
      ..add(DiagnosticsProperty('appId', appId))
      ..add(DiagnosticsProperty('numUsers', numUsers));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppUpdateImpl &&
            (identical(other.app, app) || other.app == app) &&
            (identical(other.appId, appId) || other.appId == appId) &&
            (identical(other.numUsers, numUsers) ||
                other.numUsers == numUsers));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, app, appId, numUsers);

  /// Create a copy of AppUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AppUpdateImplCopyWith<_$AppUpdateImpl> get copyWith =>
      __$$AppUpdateImplCopyWithImpl<_$AppUpdateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AppUpdateImplToJson(
      this,
    );
  }
}

abstract class _AppUpdate extends AppUpdate {
  factory _AppUpdate(
      {required final App app,
      @JsonKey(name: 'appID') required final String appId,
      final int? numUsers}) = _$AppUpdateImpl;
  _AppUpdate._() : super._();

  factory _AppUpdate.fromJson(Map<String, dynamic> json) =
      _$AppUpdateImpl.fromJson;

  @override
  App get app;
  @override
  @JsonKey(name: 'appID')
  String get appId;
  @override
  int? get numUsers;

  /// Create a copy of AppUpdate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AppUpdateImplCopyWith<_$AppUpdateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AppUser _$AppUserFromJson(Map<String, dynamic> json) {
  return _AppUser.fromJson(json);
}

/// @nodoc
mixin _$AppUser {
  App? get app => throw _privateConstructorUsedError;
  bool? get isOwner => throw _privateConstructorUsedError;
  int? get lastAccessedTime => throw _privateConstructorUsedError;
  User? get user => throw _privateConstructorUsedError;

  /// Serializes this AppUser to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AppUser
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AppUserCopyWith<AppUser> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppUserCopyWith<$Res> {
  factory $AppUserCopyWith(AppUser value, $Res Function(AppUser) then) =
      _$AppUserCopyWithImpl<$Res, AppUser>;
  @useResult
  $Res call({App? app, bool? isOwner, int? lastAccessedTime, User? user});

  $AppCopyWith<$Res>? get app;
  $UserCopyWith<$Res>? get user;
}

/// @nodoc
class _$AppUserCopyWithImpl<$Res, $Val extends AppUser>
    implements $AppUserCopyWith<$Res> {
  _$AppUserCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AppUser
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? app = freezed,
    Object? isOwner = freezed,
    Object? lastAccessedTime = freezed,
    Object? user = freezed,
  }) {
    return _then(_value.copyWith(
      app: freezed == app
          ? _value.app
          : app // ignore: cast_nullable_to_non_nullable
              as App?,
      isOwner: freezed == isOwner
          ? _value.isOwner
          : isOwner // ignore: cast_nullable_to_non_nullable
              as bool?,
      lastAccessedTime: freezed == lastAccessedTime
          ? _value.lastAccessedTime
          : lastAccessedTime // ignore: cast_nullable_to_non_nullable
              as int?,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User?,
    ) as $Val);
  }

  /// Create a copy of AppUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppCopyWith<$Res>? get app {
    if (_value.app == null) {
      return null;
    }

    return $AppCopyWith<$Res>(_value.app!, (value) {
      return _then(_value.copyWith(app: value) as $Val);
    });
  }

  /// Create a copy of AppUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserCopyWith<$Res>? get user {
    if (_value.user == null) {
      return null;
    }

    return $UserCopyWith<$Res>(_value.user!, (value) {
      return _then(_value.copyWith(user: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AppUserImplCopyWith<$Res> implements $AppUserCopyWith<$Res> {
  factory _$$AppUserImplCopyWith(
          _$AppUserImpl value, $Res Function(_$AppUserImpl) then) =
      __$$AppUserImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({App? app, bool? isOwner, int? lastAccessedTime, User? user});

  @override
  $AppCopyWith<$Res>? get app;
  @override
  $UserCopyWith<$Res>? get user;
}

/// @nodoc
class __$$AppUserImplCopyWithImpl<$Res>
    extends _$AppUserCopyWithImpl<$Res, _$AppUserImpl>
    implements _$$AppUserImplCopyWith<$Res> {
  __$$AppUserImplCopyWithImpl(
      _$AppUserImpl _value, $Res Function(_$AppUserImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppUser
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? app = freezed,
    Object? isOwner = freezed,
    Object? lastAccessedTime = freezed,
    Object? user = freezed,
  }) {
    return _then(_$AppUserImpl(
      app: freezed == app
          ? _value.app
          : app // ignore: cast_nullable_to_non_nullable
              as App?,
      isOwner: freezed == isOwner
          ? _value.isOwner
          : isOwner // ignore: cast_nullable_to_non_nullable
              as bool?,
      lastAccessedTime: freezed == lastAccessedTime
          ? _value.lastAccessedTime
          : lastAccessedTime // ignore: cast_nullable_to_non_nullable
              as int?,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AppUserImpl extends _AppUser with DiagnosticableTreeMixin {
  _$AppUserImpl({this.app, this.isOwner, this.lastAccessedTime, this.user})
      : super._();

  factory _$AppUserImpl.fromJson(Map<String, dynamic> json) =>
      _$$AppUserImplFromJson(json);

  @override
  final App? app;
  @override
  final bool? isOwner;
  @override
  final int? lastAccessedTime;
  @override
  final User? user;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AppUser(app: $app, isOwner: $isOwner, lastAccessedTime: $lastAccessedTime, user: $user)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AppUser'))
      ..add(DiagnosticsProperty('app', app))
      ..add(DiagnosticsProperty('isOwner', isOwner))
      ..add(DiagnosticsProperty('lastAccessedTime', lastAccessedTime))
      ..add(DiagnosticsProperty('user', user));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppUserImpl &&
            (identical(other.app, app) || other.app == app) &&
            (identical(other.isOwner, isOwner) || other.isOwner == isOwner) &&
            (identical(other.lastAccessedTime, lastAccessedTime) ||
                other.lastAccessedTime == lastAccessedTime) &&
            (identical(other.user, user) || other.user == user));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, app, isOwner, lastAccessedTime, user);

  /// Create a copy of AppUser
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AppUserImplCopyWith<_$AppUserImpl> get copyWith =>
      __$$AppUserImplCopyWithImpl<_$AppUserImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AppUserImplToJson(
      this,
    );
  }
}

abstract class _AppUser extends AppUser {
  factory _AppUser(
      {final App? app,
      final bool? isOwner,
      final int? lastAccessedTime,
      final User? user}) = _$AppUserImpl;
  _AppUser._() : super._();

  factory _AppUser.fromJson(Map<String, dynamic> json) = _$AppUserImpl.fromJson;

  @override
  App? get app;
  @override
  bool? get isOwner;
  @override
  int? get lastAccessedTime;
  @override
  User? get user;

  /// Create a copy of AppUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AppUserImplCopyWith<_$AppUserImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AppUserUpdate _$AppUserUpdateFromJson(Map<String, dynamic> json) {
  return _AppUserUpdate.fromJson(json);
}

/// @nodoc
mixin _$AppUserUpdate {
  @JsonKey(name: 'appID')
  String get appId => throw _privateConstructorUsedError;
  AppUser get appUser => throw _privateConstructorUsedError;
  @JsonKey(name: 'userID')
  String get userId => throw _privateConstructorUsedError;

  /// Serializes this AppUserUpdate to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AppUserUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AppUserUpdateCopyWith<AppUserUpdate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppUserUpdateCopyWith<$Res> {
  factory $AppUserUpdateCopyWith(
          AppUserUpdate value, $Res Function(AppUserUpdate) then) =
      _$AppUserUpdateCopyWithImpl<$Res, AppUserUpdate>;
  @useResult
  $Res call(
      {@JsonKey(name: 'appID') String appId,
      AppUser appUser,
      @JsonKey(name: 'userID') String userId});

  $AppUserCopyWith<$Res> get appUser;
}

/// @nodoc
class _$AppUserUpdateCopyWithImpl<$Res, $Val extends AppUserUpdate>
    implements $AppUserUpdateCopyWith<$Res> {
  _$AppUserUpdateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AppUserUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? appId = null,
    Object? appUser = null,
    Object? userId = null,
  }) {
    return _then(_value.copyWith(
      appId: null == appId
          ? _value.appId
          : appId // ignore: cast_nullable_to_non_nullable
              as String,
      appUser: null == appUser
          ? _value.appUser
          : appUser // ignore: cast_nullable_to_non_nullable
              as AppUser,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }

  /// Create a copy of AppUserUpdate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppUserCopyWith<$Res> get appUser {
    return $AppUserCopyWith<$Res>(_value.appUser, (value) {
      return _then(_value.copyWith(appUser: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AppUserUpdateImplCopyWith<$Res>
    implements $AppUserUpdateCopyWith<$Res> {
  factory _$$AppUserUpdateImplCopyWith(
          _$AppUserUpdateImpl value, $Res Function(_$AppUserUpdateImpl) then) =
      __$$AppUserUpdateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'appID') String appId,
      AppUser appUser,
      @JsonKey(name: 'userID') String userId});

  @override
  $AppUserCopyWith<$Res> get appUser;
}

/// @nodoc
class __$$AppUserUpdateImplCopyWithImpl<$Res>
    extends _$AppUserUpdateCopyWithImpl<$Res, _$AppUserUpdateImpl>
    implements _$$AppUserUpdateImplCopyWith<$Res> {
  __$$AppUserUpdateImplCopyWithImpl(
      _$AppUserUpdateImpl _value, $Res Function(_$AppUserUpdateImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppUserUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? appId = null,
    Object? appUser = null,
    Object? userId = null,
  }) {
    return _then(_$AppUserUpdateImpl(
      appId: null == appId
          ? _value.appId
          : appId // ignore: cast_nullable_to_non_nullable
              as String,
      appUser: null == appUser
          ? _value.appUser
          : appUser // ignore: cast_nullable_to_non_nullable
              as AppUser,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AppUserUpdateImpl extends _AppUserUpdate with DiagnosticableTreeMixin {
  _$AppUserUpdateImpl(
      {@JsonKey(name: 'appID') required this.appId,
      required this.appUser,
      @JsonKey(name: 'userID') required this.userId})
      : super._();

  factory _$AppUserUpdateImpl.fromJson(Map<String, dynamic> json) =>
      _$$AppUserUpdateImplFromJson(json);

  @override
  @JsonKey(name: 'appID')
  final String appId;
  @override
  final AppUser appUser;
  @override
  @JsonKey(name: 'userID')
  final String userId;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AppUserUpdate(appId: $appId, appUser: $appUser, userId: $userId)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AppUserUpdate'))
      ..add(DiagnosticsProperty('appId', appId))
      ..add(DiagnosticsProperty('appUser', appUser))
      ..add(DiagnosticsProperty('userId', userId));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppUserUpdateImpl &&
            (identical(other.appId, appId) || other.appId == appId) &&
            (identical(other.appUser, appUser) || other.appUser == appUser) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, appId, appUser, userId);

  /// Create a copy of AppUserUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AppUserUpdateImplCopyWith<_$AppUserUpdateImpl> get copyWith =>
      __$$AppUserUpdateImplCopyWithImpl<_$AppUserUpdateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AppUserUpdateImplToJson(
      this,
    );
  }
}

abstract class _AppUserUpdate extends AppUserUpdate {
  factory _AppUserUpdate(
          {@JsonKey(name: 'appID') required final String appId,
          required final AppUser appUser,
          @JsonKey(name: 'userID') required final String userId}) =
      _$AppUserUpdateImpl;
  _AppUserUpdate._() : super._();

  factory _AppUserUpdate.fromJson(Map<String, dynamic> json) =
      _$AppUserUpdateImpl.fromJson;

  @override
  @JsonKey(name: 'appID')
  String get appId;
  @override
  AppUser get appUser;
  @override
  @JsonKey(name: 'userID')
  String get userId;

  /// Create a copy of AppUserUpdate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AppUserUpdateImplCopyWith<_$AppUserUpdateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AppUsersConnection _$AppUsersConnectionFromJson(Map<String, dynamic> json) {
  return _AppUsersConnection.fromJson(json);
}

/// @nodoc
mixin _$AppUsersConnection {
  List<AppUser?>? get appUsers => throw _privateConstructorUsedError;
  List<AppUsersEdge?>? get edges => throw _privateConstructorUsedError;
  PageInfo? get pageInfo => throw _privateConstructorUsedError;
  int? get totalCount => throw _privateConstructorUsedError;

  /// Serializes this AppUsersConnection to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AppUsersConnection
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AppUsersConnectionCopyWith<AppUsersConnection> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppUsersConnectionCopyWith<$Res> {
  factory $AppUsersConnectionCopyWith(
          AppUsersConnection value, $Res Function(AppUsersConnection) then) =
      _$AppUsersConnectionCopyWithImpl<$Res, AppUsersConnection>;
  @useResult
  $Res call(
      {List<AppUser?>? appUsers,
      List<AppUsersEdge?>? edges,
      PageInfo? pageInfo,
      int? totalCount});

  $PageInfoCopyWith<$Res>? get pageInfo;
}

/// @nodoc
class _$AppUsersConnectionCopyWithImpl<$Res, $Val extends AppUsersConnection>
    implements $AppUsersConnectionCopyWith<$Res> {
  _$AppUsersConnectionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AppUsersConnection
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? appUsers = freezed,
    Object? edges = freezed,
    Object? pageInfo = freezed,
    Object? totalCount = freezed,
  }) {
    return _then(_value.copyWith(
      appUsers: freezed == appUsers
          ? _value.appUsers
          : appUsers // ignore: cast_nullable_to_non_nullable
              as List<AppUser?>?,
      edges: freezed == edges
          ? _value.edges
          : edges // ignore: cast_nullable_to_non_nullable
              as List<AppUsersEdge?>?,
      pageInfo: freezed == pageInfo
          ? _value.pageInfo
          : pageInfo // ignore: cast_nullable_to_non_nullable
              as PageInfo?,
      totalCount: freezed == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }

  /// Create a copy of AppUsersConnection
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PageInfoCopyWith<$Res>? get pageInfo {
    if (_value.pageInfo == null) {
      return null;
    }

    return $PageInfoCopyWith<$Res>(_value.pageInfo!, (value) {
      return _then(_value.copyWith(pageInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AppUsersConnectionImplCopyWith<$Res>
    implements $AppUsersConnectionCopyWith<$Res> {
  factory _$$AppUsersConnectionImplCopyWith(_$AppUsersConnectionImpl value,
          $Res Function(_$AppUsersConnectionImpl) then) =
      __$$AppUsersConnectionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<AppUser?>? appUsers,
      List<AppUsersEdge?>? edges,
      PageInfo? pageInfo,
      int? totalCount});

  @override
  $PageInfoCopyWith<$Res>? get pageInfo;
}

/// @nodoc
class __$$AppUsersConnectionImplCopyWithImpl<$Res>
    extends _$AppUsersConnectionCopyWithImpl<$Res, _$AppUsersConnectionImpl>
    implements _$$AppUsersConnectionImplCopyWith<$Res> {
  __$$AppUsersConnectionImplCopyWithImpl(_$AppUsersConnectionImpl _value,
      $Res Function(_$AppUsersConnectionImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppUsersConnection
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? appUsers = freezed,
    Object? edges = freezed,
    Object? pageInfo = freezed,
    Object? totalCount = freezed,
  }) {
    return _then(_$AppUsersConnectionImpl(
      appUsers: freezed == appUsers
          ? _value._appUsers
          : appUsers // ignore: cast_nullable_to_non_nullable
              as List<AppUser?>?,
      edges: freezed == edges
          ? _value._edges
          : edges // ignore: cast_nullable_to_non_nullable
              as List<AppUsersEdge?>?,
      pageInfo: freezed == pageInfo
          ? _value.pageInfo
          : pageInfo // ignore: cast_nullable_to_non_nullable
              as PageInfo?,
      totalCount: freezed == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AppUsersConnectionImpl extends _AppUsersConnection
    with DiagnosticableTreeMixin {
  _$AppUsersConnectionImpl(
      {final List<AppUser?>? appUsers,
      final List<AppUsersEdge?>? edges,
      this.pageInfo,
      this.totalCount})
      : _appUsers = appUsers,
        _edges = edges,
        super._();

  factory _$AppUsersConnectionImpl.fromJson(Map<String, dynamic> json) =>
      _$$AppUsersConnectionImplFromJson(json);

  final List<AppUser?>? _appUsers;
  @override
  List<AppUser?>? get appUsers {
    final value = _appUsers;
    if (value == null) return null;
    if (_appUsers is EqualUnmodifiableListView) return _appUsers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<AppUsersEdge?>? _edges;
  @override
  List<AppUsersEdge?>? get edges {
    final value = _edges;
    if (value == null) return null;
    if (_edges is EqualUnmodifiableListView) return _edges;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final PageInfo? pageInfo;
  @override
  final int? totalCount;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AppUsersConnection(appUsers: $appUsers, edges: $edges, pageInfo: $pageInfo, totalCount: $totalCount)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AppUsersConnection'))
      ..add(DiagnosticsProperty('appUsers', appUsers))
      ..add(DiagnosticsProperty('edges', edges))
      ..add(DiagnosticsProperty('pageInfo', pageInfo))
      ..add(DiagnosticsProperty('totalCount', totalCount));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppUsersConnectionImpl &&
            const DeepCollectionEquality().equals(other._appUsers, _appUsers) &&
            const DeepCollectionEquality().equals(other._edges, _edges) &&
            (identical(other.pageInfo, pageInfo) ||
                other.pageInfo == pageInfo) &&
            (identical(other.totalCount, totalCount) ||
                other.totalCount == totalCount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_appUsers),
      const DeepCollectionEquality().hash(_edges),
      pageInfo,
      totalCount);

  /// Create a copy of AppUsersConnection
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AppUsersConnectionImplCopyWith<_$AppUsersConnectionImpl> get copyWith =>
      __$$AppUsersConnectionImplCopyWithImpl<_$AppUsersConnectionImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AppUsersConnectionImplToJson(
      this,
    );
  }
}

abstract class _AppUsersConnection extends AppUsersConnection {
  factory _AppUsersConnection(
      {final List<AppUser?>? appUsers,
      final List<AppUsersEdge?>? edges,
      final PageInfo? pageInfo,
      final int? totalCount}) = _$AppUsersConnectionImpl;
  _AppUsersConnection._() : super._();

  factory _AppUsersConnection.fromJson(Map<String, dynamic> json) =
      _$AppUsersConnectionImpl.fromJson;

  @override
  List<AppUser?>? get appUsers;
  @override
  List<AppUsersEdge?>? get edges;
  @override
  PageInfo? get pageInfo;
  @override
  int? get totalCount;

  /// Create a copy of AppUsersConnection
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AppUsersConnectionImplCopyWith<_$AppUsersConnectionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AppUsersEdge _$AppUsersEdgeFromJson(Map<String, dynamic> json) {
  return _AppUsersEdge.fromJson(json);
}

/// @nodoc
mixin _$AppUsersEdge {
  AppUser get node => throw _privateConstructorUsedError;

  /// Serializes this AppUsersEdge to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AppUsersEdge
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AppUsersEdgeCopyWith<AppUsersEdge> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppUsersEdgeCopyWith<$Res> {
  factory $AppUsersEdgeCopyWith(
          AppUsersEdge value, $Res Function(AppUsersEdge) then) =
      _$AppUsersEdgeCopyWithImpl<$Res, AppUsersEdge>;
  @useResult
  $Res call({AppUser node});

  $AppUserCopyWith<$Res> get node;
}

/// @nodoc
class _$AppUsersEdgeCopyWithImpl<$Res, $Val extends AppUsersEdge>
    implements $AppUsersEdgeCopyWith<$Res> {
  _$AppUsersEdgeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AppUsersEdge
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? node = null,
  }) {
    return _then(_value.copyWith(
      node: null == node
          ? _value.node
          : node // ignore: cast_nullable_to_non_nullable
              as AppUser,
    ) as $Val);
  }

  /// Create a copy of AppUsersEdge
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppUserCopyWith<$Res> get node {
    return $AppUserCopyWith<$Res>(_value.node, (value) {
      return _then(_value.copyWith(node: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AppUsersEdgeImplCopyWith<$Res>
    implements $AppUsersEdgeCopyWith<$Res> {
  factory _$$AppUsersEdgeImplCopyWith(
          _$AppUsersEdgeImpl value, $Res Function(_$AppUsersEdgeImpl) then) =
      __$$AppUsersEdgeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({AppUser node});

  @override
  $AppUserCopyWith<$Res> get node;
}

/// @nodoc
class __$$AppUsersEdgeImplCopyWithImpl<$Res>
    extends _$AppUsersEdgeCopyWithImpl<$Res, _$AppUsersEdgeImpl>
    implements _$$AppUsersEdgeImplCopyWith<$Res> {
  __$$AppUsersEdgeImplCopyWithImpl(
      _$AppUsersEdgeImpl _value, $Res Function(_$AppUsersEdgeImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppUsersEdge
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? node = null,
  }) {
    return _then(_$AppUsersEdgeImpl(
      node: null == node
          ? _value.node
          : node // ignore: cast_nullable_to_non_nullable
              as AppUser,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AppUsersEdgeImpl extends _AppUsersEdge with DiagnosticableTreeMixin {
  _$AppUsersEdgeImpl({required this.node}) : super._();

  factory _$AppUsersEdgeImpl.fromJson(Map<String, dynamic> json) =>
      _$$AppUsersEdgeImplFromJson(json);

  @override
  final AppUser node;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AppUsersEdge(node: $node)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AppUsersEdge'))
      ..add(DiagnosticsProperty('node', node));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppUsersEdgeImpl &&
            (identical(other.node, node) || other.node == node));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, node);

  /// Create a copy of AppUsersEdge
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AppUsersEdgeImplCopyWith<_$AppUsersEdgeImpl> get copyWith =>
      __$$AppUsersEdgeImplCopyWithImpl<_$AppUsersEdgeImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AppUsersEdgeImplToJson(
      this,
    );
  }
}

abstract class _AppUsersEdge extends AppUsersEdge {
  factory _AppUsersEdge({required final AppUser node}) = _$AppUsersEdgeImpl;
  _AppUsersEdge._() : super._();

  factory _AppUsersEdge.fromJson(Map<String, dynamic> json) =
      _$AppUsersEdgeImpl.fromJson;

  @override
  AppUser get node;

  /// Create a copy of AppUsersEdge
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AppUsersEdgeImplCopyWith<_$AppUsersEdgeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Contact _$ContactFromJson(Map<String, dynamic> json) {
  return _Contact.fromJson(json);
}

/// @nodoc
mixin _$Contact {
  String? get description => throw _privateConstructorUsedError;
  String? get emailAddress => throw _privateConstructorUsedError;
  String? get phoneNumber => throw _privateConstructorUsedError;
  ContactType get type => throw _privateConstructorUsedError;

  /// Serializes this Contact to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Contact
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ContactCopyWith<Contact> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContactCopyWith<$Res> {
  factory $ContactCopyWith(Contact value, $Res Function(Contact) then) =
      _$ContactCopyWithImpl<$Res, Contact>;
  @useResult
  $Res call(
      {String? description,
      String? emailAddress,
      String? phoneNumber,
      ContactType type});
}

/// @nodoc
class _$ContactCopyWithImpl<$Res, $Val extends Contact>
    implements $ContactCopyWith<$Res> {
  _$ContactCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Contact
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? description = freezed,
    Object? emailAddress = freezed,
    Object? phoneNumber = freezed,
    Object? type = null,
  }) {
    return _then(_value.copyWith(
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      emailAddress: freezed == emailAddress
          ? _value.emailAddress
          : emailAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as ContactType,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ContactImplCopyWith<$Res> implements $ContactCopyWith<$Res> {
  factory _$$ContactImplCopyWith(
          _$ContactImpl value, $Res Function(_$ContactImpl) then) =
      __$$ContactImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? description,
      String? emailAddress,
      String? phoneNumber,
      ContactType type});
}

/// @nodoc
class __$$ContactImplCopyWithImpl<$Res>
    extends _$ContactCopyWithImpl<$Res, _$ContactImpl>
    implements _$$ContactImplCopyWith<$Res> {
  __$$ContactImplCopyWithImpl(
      _$ContactImpl _value, $Res Function(_$ContactImpl) _then)
      : super(_value, _then);

  /// Create a copy of Contact
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? description = freezed,
    Object? emailAddress = freezed,
    Object? phoneNumber = freezed,
    Object? type = null,
  }) {
    return _then(_$ContactImpl(
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      emailAddress: freezed == emailAddress
          ? _value.emailAddress
          : emailAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as ContactType,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ContactImpl extends _Contact with DiagnosticableTreeMixin {
  _$ContactImpl(
      {this.description,
      this.emailAddress,
      this.phoneNumber,
      required this.type})
      : super._();

  factory _$ContactImpl.fromJson(Map<String, dynamic> json) =>
      _$$ContactImplFromJson(json);

  @override
  final String? description;
  @override
  final String? emailAddress;
  @override
  final String? phoneNumber;
  @override
  final ContactType type;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'Contact(description: $description, emailAddress: $emailAddress, phoneNumber: $phoneNumber, type: $type)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'Contact'))
      ..add(DiagnosticsProperty('description', description))
      ..add(DiagnosticsProperty('emailAddress', emailAddress))
      ..add(DiagnosticsProperty('phoneNumber', phoneNumber))
      ..add(DiagnosticsProperty('type', type));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ContactImpl &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.emailAddress, emailAddress) ||
                other.emailAddress == emailAddress) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.type, type) || other.type == type));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, description, emailAddress, phoneNumber, type);

  /// Create a copy of Contact
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ContactImplCopyWith<_$ContactImpl> get copyWith =>
      __$$ContactImplCopyWithImpl<_$ContactImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ContactImplToJson(
      this,
    );
  }
}

abstract class _Contact extends Contact {
  factory _Contact(
      {final String? description,
      final String? emailAddress,
      final String? phoneNumber,
      required final ContactType type}) = _$ContactImpl;
  _Contact._() : super._();

  factory _Contact.fromJson(Map<String, dynamic> json) = _$ContactImpl.fromJson;

  @override
  String? get description;
  @override
  String? get emailAddress;
  @override
  String? get phoneNumber;
  @override
  ContactType get type;

  /// Create a copy of Contact
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ContactImplCopyWith<_$ContactImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ContactInput _$ContactInputFromJson(Map<String, dynamic> json) {
  return _ContactInput.fromJson(json);
}

/// @nodoc
mixin _$ContactInput {
  String? get description => throw _privateConstructorUsedError;
  set description(String? value) => throw _privateConstructorUsedError;
  String? get emailAddress => throw _privateConstructorUsedError;
  set emailAddress(String? value) => throw _privateConstructorUsedError;
  String? get phoneNumber => throw _privateConstructorUsedError;
  set phoneNumber(String? value) => throw _privateConstructorUsedError;
  ContactType get type => throw _privateConstructorUsedError;
  set type(ContactType value) => throw _privateConstructorUsedError;

  /// Serializes this ContactInput to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ContactInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ContactInputCopyWith<ContactInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContactInputCopyWith<$Res> {
  factory $ContactInputCopyWith(
          ContactInput value, $Res Function(ContactInput) then) =
      _$ContactInputCopyWithImpl<$Res, ContactInput>;
  @useResult
  $Res call(
      {String? description,
      String? emailAddress,
      String? phoneNumber,
      ContactType type});
}

/// @nodoc
class _$ContactInputCopyWithImpl<$Res, $Val extends ContactInput>
    implements $ContactInputCopyWith<$Res> {
  _$ContactInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ContactInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? description = freezed,
    Object? emailAddress = freezed,
    Object? phoneNumber = freezed,
    Object? type = null,
  }) {
    return _then(_value.copyWith(
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      emailAddress: freezed == emailAddress
          ? _value.emailAddress
          : emailAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as ContactType,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ContactInputImplCopyWith<$Res>
    implements $ContactInputCopyWith<$Res> {
  factory _$$ContactInputImplCopyWith(
          _$ContactInputImpl value, $Res Function(_$ContactInputImpl) then) =
      __$$ContactInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? description,
      String? emailAddress,
      String? phoneNumber,
      ContactType type});
}

/// @nodoc
class __$$ContactInputImplCopyWithImpl<$Res>
    extends _$ContactInputCopyWithImpl<$Res, _$ContactInputImpl>
    implements _$$ContactInputImplCopyWith<$Res> {
  __$$ContactInputImplCopyWithImpl(
      _$ContactInputImpl _value, $Res Function(_$ContactInputImpl) _then)
      : super(_value, _then);

  /// Create a copy of ContactInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? description = freezed,
    Object? emailAddress = freezed,
    Object? phoneNumber = freezed,
    Object? type = null,
  }) {
    return _then(_$ContactInputImpl(
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      emailAddress: freezed == emailAddress
          ? _value.emailAddress
          : emailAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as ContactType,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ContactInputImpl extends _ContactInput with DiagnosticableTreeMixin {
  _$ContactInputImpl(
      {this.description,
      this.emailAddress,
      this.phoneNumber,
      required this.type})
      : super._();

  factory _$ContactInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$ContactInputImplFromJson(json);

  @override
  String? description;
  @override
  String? emailAddress;
  @override
  String? phoneNumber;
  @override
  ContactType type;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ContactInput(description: $description, emailAddress: $emailAddress, phoneNumber: $phoneNumber, type: $type)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ContactInput'))
      ..add(DiagnosticsProperty('description', description))
      ..add(DiagnosticsProperty('emailAddress', emailAddress))
      ..add(DiagnosticsProperty('phoneNumber', phoneNumber))
      ..add(DiagnosticsProperty('type', type));
  }

  /// Create a copy of ContactInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ContactInputImplCopyWith<_$ContactInputImpl> get copyWith =>
      __$$ContactInputImplCopyWithImpl<_$ContactInputImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ContactInputImplToJson(
      this,
    );
  }
}

abstract class _ContactInput extends ContactInput {
  factory _ContactInput(
      {String? description,
      String? emailAddress,
      String? phoneNumber,
      required ContactType type}) = _$ContactInputImpl;
  _ContactInput._() : super._();

  factory _ContactInput.fromJson(Map<String, dynamic> json) =
      _$ContactInputImpl.fromJson;

  @override
  String? get description;
  set description(String? value);
  @override
  String? get emailAddress;
  set emailAddress(String? value);
  @override
  String? get phoneNumber;
  set phoneNumber(String? value);
  @override
  ContactType get type;
  set type(ContactType value);

  /// Create a copy of ContactInput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ContactInputImplCopyWith<_$ContactInputImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Cursor _$CursorFromJson(Map<String, dynamic> json) {
  return _Cursor.fromJson(json);
}

/// @nodoc
mixin _$Cursor {
  int get index => throw _privateConstructorUsedError;
  List<String?> get tokens => throw _privateConstructorUsedError;

  /// Serializes this Cursor to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Cursor
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CursorCopyWith<Cursor> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CursorCopyWith<$Res> {
  factory $CursorCopyWith(Cursor value, $Res Function(Cursor) then) =
      _$CursorCopyWithImpl<$Res, Cursor>;
  @useResult
  $Res call({int index, List<String?> tokens});
}

/// @nodoc
class _$CursorCopyWithImpl<$Res, $Val extends Cursor>
    implements $CursorCopyWith<$Res> {
  _$CursorCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Cursor
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? index = null,
    Object? tokens = null,
  }) {
    return _then(_value.copyWith(
      index: null == index
          ? _value.index
          : index // ignore: cast_nullable_to_non_nullable
              as int,
      tokens: null == tokens
          ? _value.tokens
          : tokens // ignore: cast_nullable_to_non_nullable
              as List<String?>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CursorImplCopyWith<$Res> implements $CursorCopyWith<$Res> {
  factory _$$CursorImplCopyWith(
          _$CursorImpl value, $Res Function(_$CursorImpl) then) =
      __$$CursorImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int index, List<String?> tokens});
}

/// @nodoc
class __$$CursorImplCopyWithImpl<$Res>
    extends _$CursorCopyWithImpl<$Res, _$CursorImpl>
    implements _$$CursorImplCopyWith<$Res> {
  __$$CursorImplCopyWithImpl(
      _$CursorImpl _value, $Res Function(_$CursorImpl) _then)
      : super(_value, _then);

  /// Create a copy of Cursor
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? index = null,
    Object? tokens = null,
  }) {
    return _then(_$CursorImpl(
      index: null == index
          ? _value.index
          : index // ignore: cast_nullable_to_non_nullable
              as int,
      tokens: null == tokens
          ? _value._tokens
          : tokens // ignore: cast_nullable_to_non_nullable
              as List<String?>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CursorImpl extends _Cursor with DiagnosticableTreeMixin {
  _$CursorImpl({required this.index, required final List<String?> tokens})
      : _tokens = tokens,
        super._();

  factory _$CursorImpl.fromJson(Map<String, dynamic> json) =>
      _$$CursorImplFromJson(json);

  @override
  final int index;
  final List<String?> _tokens;
  @override
  List<String?> get tokens {
    if (_tokens is EqualUnmodifiableListView) return _tokens;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tokens);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'Cursor(index: $index, tokens: $tokens)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'Cursor'))
      ..add(DiagnosticsProperty('index', index))
      ..add(DiagnosticsProperty('tokens', tokens));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CursorImpl &&
            (identical(other.index, index) || other.index == index) &&
            const DeepCollectionEquality().equals(other._tokens, _tokens));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, index, const DeepCollectionEquality().hash(_tokens));

  /// Create a copy of Cursor
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CursorImplCopyWith<_$CursorImpl> get copyWith =>
      __$$CursorImplCopyWithImpl<_$CursorImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CursorImplToJson(
      this,
    );
  }
}

abstract class _Cursor extends Cursor {
  factory _Cursor(
      {required final int index,
      required final List<String?> tokens}) = _$CursorImpl;
  _Cursor._() : super._();

  factory _Cursor.fromJson(Map<String, dynamic> json) = _$CursorImpl.fromJson;

  @override
  int get index;
  @override
  List<String?> get tokens;

  /// Create a copy of Cursor
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CursorImplCopyWith<_$CursorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CursorInput _$CursorInputFromJson(Map<String, dynamic> json) {
  return _CursorInput.fromJson(json);
}

/// @nodoc
mixin _$CursorInput {
  int get index => throw _privateConstructorUsedError;
  set index(int value) => throw _privateConstructorUsedError;
  List<String?> get tokens => throw _privateConstructorUsedError;
  set tokens(List<String?> value) => throw _privateConstructorUsedError;

  /// Serializes this CursorInput to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CursorInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CursorInputCopyWith<CursorInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CursorInputCopyWith<$Res> {
  factory $CursorInputCopyWith(
          CursorInput value, $Res Function(CursorInput) then) =
      _$CursorInputCopyWithImpl<$Res, CursorInput>;
  @useResult
  $Res call({int index, List<String?> tokens});
}

/// @nodoc
class _$CursorInputCopyWithImpl<$Res, $Val extends CursorInput>
    implements $CursorInputCopyWith<$Res> {
  _$CursorInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CursorInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? index = null,
    Object? tokens = null,
  }) {
    return _then(_value.copyWith(
      index: null == index
          ? _value.index
          : index // ignore: cast_nullable_to_non_nullable
              as int,
      tokens: null == tokens
          ? _value.tokens
          : tokens // ignore: cast_nullable_to_non_nullable
              as List<String?>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CursorInputImplCopyWith<$Res>
    implements $CursorInputCopyWith<$Res> {
  factory _$$CursorInputImplCopyWith(
          _$CursorInputImpl value, $Res Function(_$CursorInputImpl) then) =
      __$$CursorInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int index, List<String?> tokens});
}

/// @nodoc
class __$$CursorInputImplCopyWithImpl<$Res>
    extends _$CursorInputCopyWithImpl<$Res, _$CursorInputImpl>
    implements _$$CursorInputImplCopyWith<$Res> {
  __$$CursorInputImplCopyWithImpl(
      _$CursorInputImpl _value, $Res Function(_$CursorInputImpl) _then)
      : super(_value, _then);

  /// Create a copy of CursorInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? index = null,
    Object? tokens = null,
  }) {
    return _then(_$CursorInputImpl(
      index: null == index
          ? _value.index
          : index // ignore: cast_nullable_to_non_nullable
              as int,
      tokens: null == tokens
          ? _value.tokens
          : tokens // ignore: cast_nullable_to_non_nullable
              as List<String?>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CursorInputImpl extends _CursorInput with DiagnosticableTreeMixin {
  _$CursorInputImpl({required this.index, required this.tokens}) : super._();

  factory _$CursorInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$CursorInputImplFromJson(json);

  @override
  int index;
  @override
  List<String?> tokens;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'CursorInput(index: $index, tokens: $tokens)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'CursorInput'))
      ..add(DiagnosticsProperty('index', index))
      ..add(DiagnosticsProperty('tokens', tokens));
  }

  /// Create a copy of CursorInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CursorInputImplCopyWith<_$CursorInputImpl> get copyWith =>
      __$$CursorInputImplCopyWithImpl<_$CursorInputImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CursorInputImplToJson(
      this,
    );
  }
}

abstract class _CursorInput extends CursorInput {
  factory _CursorInput({required int index, required List<String?> tokens}) =
      _$CursorInputImpl;
  _CursorInput._() : super._();

  factory _CursorInput.fromJson(Map<String, dynamic> json) =
      _$CursorInputImpl.fromJson;

  @override
  int get index;
  set index(int value);
  @override
  List<String?> get tokens;
  set tokens(List<String?> value);

  /// Create a copy of CursorInput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CursorInputImplCopyWith<_$CursorInputImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Device _$DeviceFromJson(Map<String, dynamic> json) {
  return _Device.fromJson(json);
}

/// @nodoc
mixin _$Device {
  String? get certificate => throw _privateConstructorUsedError;
  String? get clientVersion => throw _privateConstructorUsedError;
  @JsonKey(name: 'deviceID')
  String get deviceId => throw _privateConstructorUsedError;
  String? get deviceName => throw _privateConstructorUsedError;
  String? get deviceType => throw _privateConstructorUsedError;
  String? get managedBy => throw _privateConstructorUsedError;
  List<Device?>? get managedDevices => throw _privateConstructorUsedError;
  UserRef? get owner => throw _privateConstructorUsedError;
  String? get publicKey => throw _privateConstructorUsedError;
  String? get settings => throw _privateConstructorUsedError;
  DeviceUsersConnection? get users => throw _privateConstructorUsedError;

  /// Serializes this Device to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Device
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DeviceCopyWith<Device> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeviceCopyWith<$Res> {
  factory $DeviceCopyWith(Device value, $Res Function(Device) then) =
      _$DeviceCopyWithImpl<$Res, Device>;
  @useResult
  $Res call(
      {String? certificate,
      String? clientVersion,
      @JsonKey(name: 'deviceID') String deviceId,
      String? deviceName,
      String? deviceType,
      String? managedBy,
      List<Device?>? managedDevices,
      UserRef? owner,
      String? publicKey,
      String? settings,
      DeviceUsersConnection? users});

  $UserRefCopyWith<$Res>? get owner;
  $DeviceUsersConnectionCopyWith<$Res>? get users;
}

/// @nodoc
class _$DeviceCopyWithImpl<$Res, $Val extends Device>
    implements $DeviceCopyWith<$Res> {
  _$DeviceCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Device
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? certificate = freezed,
    Object? clientVersion = freezed,
    Object? deviceId = null,
    Object? deviceName = freezed,
    Object? deviceType = freezed,
    Object? managedBy = freezed,
    Object? managedDevices = freezed,
    Object? owner = freezed,
    Object? publicKey = freezed,
    Object? settings = freezed,
    Object? users = freezed,
  }) {
    return _then(_value.copyWith(
      certificate: freezed == certificate
          ? _value.certificate
          : certificate // ignore: cast_nullable_to_non_nullable
              as String?,
      clientVersion: freezed == clientVersion
          ? _value.clientVersion
          : clientVersion // ignore: cast_nullable_to_non_nullable
              as String?,
      deviceId: null == deviceId
          ? _value.deviceId
          : deviceId // ignore: cast_nullable_to_non_nullable
              as String,
      deviceName: freezed == deviceName
          ? _value.deviceName
          : deviceName // ignore: cast_nullable_to_non_nullable
              as String?,
      deviceType: freezed == deviceType
          ? _value.deviceType
          : deviceType // ignore: cast_nullable_to_non_nullable
              as String?,
      managedBy: freezed == managedBy
          ? _value.managedBy
          : managedBy // ignore: cast_nullable_to_non_nullable
              as String?,
      managedDevices: freezed == managedDevices
          ? _value.managedDevices
          : managedDevices // ignore: cast_nullable_to_non_nullable
              as List<Device?>?,
      owner: freezed == owner
          ? _value.owner
          : owner // ignore: cast_nullable_to_non_nullable
              as UserRef?,
      publicKey: freezed == publicKey
          ? _value.publicKey
          : publicKey // ignore: cast_nullable_to_non_nullable
              as String?,
      settings: freezed == settings
          ? _value.settings
          : settings // ignore: cast_nullable_to_non_nullable
              as String?,
      users: freezed == users
          ? _value.users
          : users // ignore: cast_nullable_to_non_nullable
              as DeviceUsersConnection?,
    ) as $Val);
  }

  /// Create a copy of Device
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserRefCopyWith<$Res>? get owner {
    if (_value.owner == null) {
      return null;
    }

    return $UserRefCopyWith<$Res>(_value.owner!, (value) {
      return _then(_value.copyWith(owner: value) as $Val);
    });
  }

  /// Create a copy of Device
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DeviceUsersConnectionCopyWith<$Res>? get users {
    if (_value.users == null) {
      return null;
    }

    return $DeviceUsersConnectionCopyWith<$Res>(_value.users!, (value) {
      return _then(_value.copyWith(users: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$DeviceImplCopyWith<$Res> implements $DeviceCopyWith<$Res> {
  factory _$$DeviceImplCopyWith(
          _$DeviceImpl value, $Res Function(_$DeviceImpl) then) =
      __$$DeviceImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? certificate,
      String? clientVersion,
      @JsonKey(name: 'deviceID') String deviceId,
      String? deviceName,
      String? deviceType,
      String? managedBy,
      List<Device?>? managedDevices,
      UserRef? owner,
      String? publicKey,
      String? settings,
      DeviceUsersConnection? users});

  @override
  $UserRefCopyWith<$Res>? get owner;
  @override
  $DeviceUsersConnectionCopyWith<$Res>? get users;
}

/// @nodoc
class __$$DeviceImplCopyWithImpl<$Res>
    extends _$DeviceCopyWithImpl<$Res, _$DeviceImpl>
    implements _$$DeviceImplCopyWith<$Res> {
  __$$DeviceImplCopyWithImpl(
      _$DeviceImpl _value, $Res Function(_$DeviceImpl) _then)
      : super(_value, _then);

  /// Create a copy of Device
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? certificate = freezed,
    Object? clientVersion = freezed,
    Object? deviceId = null,
    Object? deviceName = freezed,
    Object? deviceType = freezed,
    Object? managedBy = freezed,
    Object? managedDevices = freezed,
    Object? owner = freezed,
    Object? publicKey = freezed,
    Object? settings = freezed,
    Object? users = freezed,
  }) {
    return _then(_$DeviceImpl(
      certificate: freezed == certificate
          ? _value.certificate
          : certificate // ignore: cast_nullable_to_non_nullable
              as String?,
      clientVersion: freezed == clientVersion
          ? _value.clientVersion
          : clientVersion // ignore: cast_nullable_to_non_nullable
              as String?,
      deviceId: null == deviceId
          ? _value.deviceId
          : deviceId // ignore: cast_nullable_to_non_nullable
              as String,
      deviceName: freezed == deviceName
          ? _value.deviceName
          : deviceName // ignore: cast_nullable_to_non_nullable
              as String?,
      deviceType: freezed == deviceType
          ? _value.deviceType
          : deviceType // ignore: cast_nullable_to_non_nullable
              as String?,
      managedBy: freezed == managedBy
          ? _value.managedBy
          : managedBy // ignore: cast_nullable_to_non_nullable
              as String?,
      managedDevices: freezed == managedDevices
          ? _value._managedDevices
          : managedDevices // ignore: cast_nullable_to_non_nullable
              as List<Device?>?,
      owner: freezed == owner
          ? _value.owner
          : owner // ignore: cast_nullable_to_non_nullable
              as UserRef?,
      publicKey: freezed == publicKey
          ? _value.publicKey
          : publicKey // ignore: cast_nullable_to_non_nullable
              as String?,
      settings: freezed == settings
          ? _value.settings
          : settings // ignore: cast_nullable_to_non_nullable
              as String?,
      users: freezed == users
          ? _value.users
          : users // ignore: cast_nullable_to_non_nullable
              as DeviceUsersConnection?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DeviceImpl extends _Device with DiagnosticableTreeMixin {
  _$DeviceImpl(
      {this.certificate,
      this.clientVersion,
      @JsonKey(name: 'deviceID') required this.deviceId,
      this.deviceName,
      this.deviceType,
      this.managedBy,
      final List<Device?>? managedDevices,
      this.owner,
      this.publicKey,
      this.settings,
      this.users})
      : _managedDevices = managedDevices,
        super._();

  factory _$DeviceImpl.fromJson(Map<String, dynamic> json) =>
      _$$DeviceImplFromJson(json);

  @override
  final String? certificate;
  @override
  final String? clientVersion;
  @override
  @JsonKey(name: 'deviceID')
  final String deviceId;
  @override
  final String? deviceName;
  @override
  final String? deviceType;
  @override
  final String? managedBy;
  final List<Device?>? _managedDevices;
  @override
  List<Device?>? get managedDevices {
    final value = _managedDevices;
    if (value == null) return null;
    if (_managedDevices is EqualUnmodifiableListView) return _managedDevices;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final UserRef? owner;
  @override
  final String? publicKey;
  @override
  final String? settings;
  @override
  final DeviceUsersConnection? users;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'Device(certificate: $certificate, clientVersion: $clientVersion, deviceId: $deviceId, deviceName: $deviceName, deviceType: $deviceType, managedBy: $managedBy, managedDevices: $managedDevices, owner: $owner, publicKey: $publicKey, settings: $settings, users: $users)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'Device'))
      ..add(DiagnosticsProperty('certificate', certificate))
      ..add(DiagnosticsProperty('clientVersion', clientVersion))
      ..add(DiagnosticsProperty('deviceId', deviceId))
      ..add(DiagnosticsProperty('deviceName', deviceName))
      ..add(DiagnosticsProperty('deviceType', deviceType))
      ..add(DiagnosticsProperty('managedBy', managedBy))
      ..add(DiagnosticsProperty('managedDevices', managedDevices))
      ..add(DiagnosticsProperty('owner', owner))
      ..add(DiagnosticsProperty('publicKey', publicKey))
      ..add(DiagnosticsProperty('settings', settings))
      ..add(DiagnosticsProperty('users', users));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeviceImpl &&
            (identical(other.certificate, certificate) ||
                other.certificate == certificate) &&
            (identical(other.clientVersion, clientVersion) ||
                other.clientVersion == clientVersion) &&
            (identical(other.deviceId, deviceId) ||
                other.deviceId == deviceId) &&
            (identical(other.deviceName, deviceName) ||
                other.deviceName == deviceName) &&
            (identical(other.deviceType, deviceType) ||
                other.deviceType == deviceType) &&
            (identical(other.managedBy, managedBy) ||
                other.managedBy == managedBy) &&
            const DeepCollectionEquality()
                .equals(other._managedDevices, _managedDevices) &&
            (identical(other.owner, owner) || other.owner == owner) &&
            (identical(other.publicKey, publicKey) ||
                other.publicKey == publicKey) &&
            (identical(other.settings, settings) ||
                other.settings == settings) &&
            (identical(other.users, users) || other.users == users));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      certificate,
      clientVersion,
      deviceId,
      deviceName,
      deviceType,
      managedBy,
      const DeepCollectionEquality().hash(_managedDevices),
      owner,
      publicKey,
      settings,
      users);

  /// Create a copy of Device
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeviceImplCopyWith<_$DeviceImpl> get copyWith =>
      __$$DeviceImplCopyWithImpl<_$DeviceImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DeviceImplToJson(
      this,
    );
  }
}

abstract class _Device extends Device {
  factory _Device(
      {final String? certificate,
      final String? clientVersion,
      @JsonKey(name: 'deviceID') required final String deviceId,
      final String? deviceName,
      final String? deviceType,
      final String? managedBy,
      final List<Device?>? managedDevices,
      final UserRef? owner,
      final String? publicKey,
      final String? settings,
      final DeviceUsersConnection? users}) = _$DeviceImpl;
  _Device._() : super._();

  factory _Device.fromJson(Map<String, dynamic> json) = _$DeviceImpl.fromJson;

  @override
  String? get certificate;
  @override
  String? get clientVersion;
  @override
  @JsonKey(name: 'deviceID')
  String get deviceId;
  @override
  String? get deviceName;
  @override
  String? get deviceType;
  @override
  String? get managedBy;
  @override
  List<Device?>? get managedDevices;
  @override
  UserRef? get owner;
  @override
  String? get publicKey;
  @override
  String? get settings;
  @override
  DeviceUsersConnection? get users;

  /// Create a copy of Device
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeviceImplCopyWith<_$DeviceImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DeviceAuth _$DeviceAuthFromJson(Map<String, dynamic> json) {
  return _DeviceAuth.fromJson(json);
}

/// @nodoc
mixin _$DeviceAuth {
  AccessType get accessType => throw _privateConstructorUsedError;
  Device? get device => throw _privateConstructorUsedError;

  /// Serializes this DeviceAuth to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DeviceAuth
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DeviceAuthCopyWith<DeviceAuth> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeviceAuthCopyWith<$Res> {
  factory $DeviceAuthCopyWith(
          DeviceAuth value, $Res Function(DeviceAuth) then) =
      _$DeviceAuthCopyWithImpl<$Res, DeviceAuth>;
  @useResult
  $Res call({AccessType accessType, Device? device});

  $DeviceCopyWith<$Res>? get device;
}

/// @nodoc
class _$DeviceAuthCopyWithImpl<$Res, $Val extends DeviceAuth>
    implements $DeviceAuthCopyWith<$Res> {
  _$DeviceAuthCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeviceAuth
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessType = null,
    Object? device = freezed,
  }) {
    return _then(_value.copyWith(
      accessType: null == accessType
          ? _value.accessType
          : accessType // ignore: cast_nullable_to_non_nullable
              as AccessType,
      device: freezed == device
          ? _value.device
          : device // ignore: cast_nullable_to_non_nullable
              as Device?,
    ) as $Val);
  }

  /// Create a copy of DeviceAuth
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DeviceCopyWith<$Res>? get device {
    if (_value.device == null) {
      return null;
    }

    return $DeviceCopyWith<$Res>(_value.device!, (value) {
      return _then(_value.copyWith(device: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$DeviceAuthImplCopyWith<$Res>
    implements $DeviceAuthCopyWith<$Res> {
  factory _$$DeviceAuthImplCopyWith(
          _$DeviceAuthImpl value, $Res Function(_$DeviceAuthImpl) then) =
      __$$DeviceAuthImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({AccessType accessType, Device? device});

  @override
  $DeviceCopyWith<$Res>? get device;
}

/// @nodoc
class __$$DeviceAuthImplCopyWithImpl<$Res>
    extends _$DeviceAuthCopyWithImpl<$Res, _$DeviceAuthImpl>
    implements _$$DeviceAuthImplCopyWith<$Res> {
  __$$DeviceAuthImplCopyWithImpl(
      _$DeviceAuthImpl _value, $Res Function(_$DeviceAuthImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceAuth
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessType = null,
    Object? device = freezed,
  }) {
    return _then(_$DeviceAuthImpl(
      accessType: null == accessType
          ? _value.accessType
          : accessType // ignore: cast_nullable_to_non_nullable
              as AccessType,
      device: freezed == device
          ? _value.device
          : device // ignore: cast_nullable_to_non_nullable
              as Device?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DeviceAuthImpl extends _DeviceAuth with DiagnosticableTreeMixin {
  _$DeviceAuthImpl({required this.accessType, this.device}) : super._();

  factory _$DeviceAuthImpl.fromJson(Map<String, dynamic> json) =>
      _$$DeviceAuthImplFromJson(json);

  @override
  final AccessType accessType;
  @override
  final Device? device;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DeviceAuth(accessType: $accessType, device: $device)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'DeviceAuth'))
      ..add(DiagnosticsProperty('accessType', accessType))
      ..add(DiagnosticsProperty('device', device));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeviceAuthImpl &&
            (identical(other.accessType, accessType) ||
                other.accessType == accessType) &&
            (identical(other.device, device) || other.device == device));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, accessType, device);

  /// Create a copy of DeviceAuth
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeviceAuthImplCopyWith<_$DeviceAuthImpl> get copyWith =>
      __$$DeviceAuthImplCopyWithImpl<_$DeviceAuthImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DeviceAuthImplToJson(
      this,
    );
  }
}

abstract class _DeviceAuth extends DeviceAuth {
  factory _DeviceAuth(
      {required final AccessType accessType,
      final Device? device}) = _$DeviceAuthImpl;
  _DeviceAuth._() : super._();

  factory _DeviceAuth.fromJson(Map<String, dynamic> json) =
      _$DeviceAuthImpl.fromJson;

  @override
  AccessType get accessType;
  @override
  Device? get device;

  /// Create a copy of DeviceAuth
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeviceAuthImplCopyWith<_$DeviceAuthImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DeviceIi _$DeviceIiFromJson(Map<String, dynamic> json) {
  return _DeviceIi.fromJson(json);
}

/// @nodoc
mixin _$DeviceIi {
  DeviceUser get deviceUser => throw _privateConstructorUsedError;
  String get idKey => throw _privateConstructorUsedError;

  /// Serializes this DeviceIi to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DeviceIi
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DeviceIiCopyWith<DeviceIi> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeviceIiCopyWith<$Res> {
  factory $DeviceIiCopyWith(DeviceIi value, $Res Function(DeviceIi) then) =
      _$DeviceIiCopyWithImpl<$Res, DeviceIi>;
  @useResult
  $Res call({DeviceUser deviceUser, String idKey});

  $DeviceUserCopyWith<$Res> get deviceUser;
}

/// @nodoc
class _$DeviceIiCopyWithImpl<$Res, $Val extends DeviceIi>
    implements $DeviceIiCopyWith<$Res> {
  _$DeviceIiCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeviceIi
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? deviceUser = null,
    Object? idKey = null,
  }) {
    return _then(_value.copyWith(
      deviceUser: null == deviceUser
          ? _value.deviceUser
          : deviceUser // ignore: cast_nullable_to_non_nullable
              as DeviceUser,
      idKey: null == idKey
          ? _value.idKey
          : idKey // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }

  /// Create a copy of DeviceIi
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DeviceUserCopyWith<$Res> get deviceUser {
    return $DeviceUserCopyWith<$Res>(_value.deviceUser, (value) {
      return _then(_value.copyWith(deviceUser: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$DeviceIiImplCopyWith<$Res>
    implements $DeviceIiCopyWith<$Res> {
  factory _$$DeviceIiImplCopyWith(
          _$DeviceIiImpl value, $Res Function(_$DeviceIiImpl) then) =
      __$$DeviceIiImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({DeviceUser deviceUser, String idKey});

  @override
  $DeviceUserCopyWith<$Res> get deviceUser;
}

/// @nodoc
class __$$DeviceIiImplCopyWithImpl<$Res>
    extends _$DeviceIiCopyWithImpl<$Res, _$DeviceIiImpl>
    implements _$$DeviceIiImplCopyWith<$Res> {
  __$$DeviceIiImplCopyWithImpl(
      _$DeviceIiImpl _value, $Res Function(_$DeviceIiImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceIi
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? deviceUser = null,
    Object? idKey = null,
  }) {
    return _then(_$DeviceIiImpl(
      deviceUser: null == deviceUser
          ? _value.deviceUser
          : deviceUser // ignore: cast_nullable_to_non_nullable
              as DeviceUser,
      idKey: null == idKey
          ? _value.idKey
          : idKey // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DeviceIiImpl extends _DeviceIi with DiagnosticableTreeMixin {
  _$DeviceIiImpl({required this.deviceUser, required this.idKey}) : super._();

  factory _$DeviceIiImpl.fromJson(Map<String, dynamic> json) =>
      _$$DeviceIiImplFromJson(json);

  @override
  final DeviceUser deviceUser;
  @override
  final String idKey;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DeviceIi(deviceUser: $deviceUser, idKey: $idKey)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'DeviceIi'))
      ..add(DiagnosticsProperty('deviceUser', deviceUser))
      ..add(DiagnosticsProperty('idKey', idKey));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeviceIiImpl &&
            (identical(other.deviceUser, deviceUser) ||
                other.deviceUser == deviceUser) &&
            (identical(other.idKey, idKey) || other.idKey == idKey));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, deviceUser, idKey);

  /// Create a copy of DeviceIi
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeviceIiImplCopyWith<_$DeviceIiImpl> get copyWith =>
      __$$DeviceIiImplCopyWithImpl<_$DeviceIiImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DeviceIiImplToJson(
      this,
    );
  }
}

abstract class _DeviceIi extends DeviceIi {
  factory _DeviceIi(
      {required final DeviceUser deviceUser,
      required final String idKey}) = _$DeviceIiImpl;
  _DeviceIi._() : super._();

  factory _DeviceIi.fromJson(Map<String, dynamic> json) =
      _$DeviceIiImpl.fromJson;

  @override
  DeviceUser get deviceUser;
  @override
  String get idKey;

  /// Create a copy of DeviceIi
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeviceIiImplCopyWith<_$DeviceIiImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DeviceInfo _$DeviceInfoFromJson(Map<String, dynamic> json) {
  return _DeviceInfo.fromJson(json);
}

/// @nodoc
mixin _$DeviceInfo {
  String get clientVersion => throw _privateConstructorUsedError;
  set clientVersion(String value) => throw _privateConstructorUsedError;
  String get deviceType => throw _privateConstructorUsedError;
  set deviceType(String value) => throw _privateConstructorUsedError;
  String? get managedBy => throw _privateConstructorUsedError;
  set managedBy(String? value) => throw _privateConstructorUsedError;

  /// Serializes this DeviceInfo to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DeviceInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DeviceInfoCopyWith<DeviceInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeviceInfoCopyWith<$Res> {
  factory $DeviceInfoCopyWith(
          DeviceInfo value, $Res Function(DeviceInfo) then) =
      _$DeviceInfoCopyWithImpl<$Res, DeviceInfo>;
  @useResult
  $Res call({String clientVersion, String deviceType, String? managedBy});
}

/// @nodoc
class _$DeviceInfoCopyWithImpl<$Res, $Val extends DeviceInfo>
    implements $DeviceInfoCopyWith<$Res> {
  _$DeviceInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeviceInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? clientVersion = null,
    Object? deviceType = null,
    Object? managedBy = freezed,
  }) {
    return _then(_value.copyWith(
      clientVersion: null == clientVersion
          ? _value.clientVersion
          : clientVersion // ignore: cast_nullable_to_non_nullable
              as String,
      deviceType: null == deviceType
          ? _value.deviceType
          : deviceType // ignore: cast_nullable_to_non_nullable
              as String,
      managedBy: freezed == managedBy
          ? _value.managedBy
          : managedBy // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DeviceInfoImplCopyWith<$Res>
    implements $DeviceInfoCopyWith<$Res> {
  factory _$$DeviceInfoImplCopyWith(
          _$DeviceInfoImpl value, $Res Function(_$DeviceInfoImpl) then) =
      __$$DeviceInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String clientVersion, String deviceType, String? managedBy});
}

/// @nodoc
class __$$DeviceInfoImplCopyWithImpl<$Res>
    extends _$DeviceInfoCopyWithImpl<$Res, _$DeviceInfoImpl>
    implements _$$DeviceInfoImplCopyWith<$Res> {
  __$$DeviceInfoImplCopyWithImpl(
      _$DeviceInfoImpl _value, $Res Function(_$DeviceInfoImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? clientVersion = null,
    Object? deviceType = null,
    Object? managedBy = freezed,
  }) {
    return _then(_$DeviceInfoImpl(
      clientVersion: null == clientVersion
          ? _value.clientVersion
          : clientVersion // ignore: cast_nullable_to_non_nullable
              as String,
      deviceType: null == deviceType
          ? _value.deviceType
          : deviceType // ignore: cast_nullable_to_non_nullable
              as String,
      managedBy: freezed == managedBy
          ? _value.managedBy
          : managedBy // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DeviceInfoImpl extends _DeviceInfo with DiagnosticableTreeMixin {
  _$DeviceInfoImpl(
      {required this.clientVersion, required this.deviceType, this.managedBy})
      : super._();

  factory _$DeviceInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$DeviceInfoImplFromJson(json);

  @override
  String clientVersion;
  @override
  String deviceType;
  @override
  String? managedBy;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DeviceInfo(clientVersion: $clientVersion, deviceType: $deviceType, managedBy: $managedBy)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'DeviceInfo'))
      ..add(DiagnosticsProperty('clientVersion', clientVersion))
      ..add(DiagnosticsProperty('deviceType', deviceType))
      ..add(DiagnosticsProperty('managedBy', managedBy));
  }

  /// Create a copy of DeviceInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeviceInfoImplCopyWith<_$DeviceInfoImpl> get copyWith =>
      __$$DeviceInfoImplCopyWithImpl<_$DeviceInfoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DeviceInfoImplToJson(
      this,
    );
  }
}

abstract class _DeviceInfo extends DeviceInfo {
  factory _DeviceInfo(
      {required String clientVersion,
      required String deviceType,
      String? managedBy}) = _$DeviceInfoImpl;
  _DeviceInfo._() : super._();

  factory _DeviceInfo.fromJson(Map<String, dynamic> json) =
      _$DeviceInfoImpl.fromJson;

  @override
  String get clientVersion;
  set clientVersion(String value);
  @override
  String get deviceType;
  set deviceType(String value);
  @override
  String? get managedBy;
  set managedBy(String? value);

  /// Create a copy of DeviceInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeviceInfoImplCopyWith<_$DeviceInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DeviceUpdate _$DeviceUpdateFromJson(Map<String, dynamic> json) {
  return _DeviceUpdate.fromJson(json);
}

/// @nodoc
mixin _$DeviceUpdate {
  Device get device => throw _privateConstructorUsedError;
  @JsonKey(name: 'deviceID')
  String get deviceId => throw _privateConstructorUsedError;
  int? get numUsers => throw _privateConstructorUsedError;

  /// Serializes this DeviceUpdate to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DeviceUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DeviceUpdateCopyWith<DeviceUpdate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeviceUpdateCopyWith<$Res> {
  factory $DeviceUpdateCopyWith(
          DeviceUpdate value, $Res Function(DeviceUpdate) then) =
      _$DeviceUpdateCopyWithImpl<$Res, DeviceUpdate>;
  @useResult
  $Res call(
      {Device device,
      @JsonKey(name: 'deviceID') String deviceId,
      int? numUsers});

  $DeviceCopyWith<$Res> get device;
}

/// @nodoc
class _$DeviceUpdateCopyWithImpl<$Res, $Val extends DeviceUpdate>
    implements $DeviceUpdateCopyWith<$Res> {
  _$DeviceUpdateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeviceUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? device = null,
    Object? deviceId = null,
    Object? numUsers = freezed,
  }) {
    return _then(_value.copyWith(
      device: null == device
          ? _value.device
          : device // ignore: cast_nullable_to_non_nullable
              as Device,
      deviceId: null == deviceId
          ? _value.deviceId
          : deviceId // ignore: cast_nullable_to_non_nullable
              as String,
      numUsers: freezed == numUsers
          ? _value.numUsers
          : numUsers // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }

  /// Create a copy of DeviceUpdate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DeviceCopyWith<$Res> get device {
    return $DeviceCopyWith<$Res>(_value.device, (value) {
      return _then(_value.copyWith(device: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$DeviceUpdateImplCopyWith<$Res>
    implements $DeviceUpdateCopyWith<$Res> {
  factory _$$DeviceUpdateImplCopyWith(
          _$DeviceUpdateImpl value, $Res Function(_$DeviceUpdateImpl) then) =
      __$$DeviceUpdateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Device device,
      @JsonKey(name: 'deviceID') String deviceId,
      int? numUsers});

  @override
  $DeviceCopyWith<$Res> get device;
}

/// @nodoc
class __$$DeviceUpdateImplCopyWithImpl<$Res>
    extends _$DeviceUpdateCopyWithImpl<$Res, _$DeviceUpdateImpl>
    implements _$$DeviceUpdateImplCopyWith<$Res> {
  __$$DeviceUpdateImplCopyWithImpl(
      _$DeviceUpdateImpl _value, $Res Function(_$DeviceUpdateImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? device = null,
    Object? deviceId = null,
    Object? numUsers = freezed,
  }) {
    return _then(_$DeviceUpdateImpl(
      device: null == device
          ? _value.device
          : device // ignore: cast_nullable_to_non_nullable
              as Device,
      deviceId: null == deviceId
          ? _value.deviceId
          : deviceId // ignore: cast_nullable_to_non_nullable
              as String,
      numUsers: freezed == numUsers
          ? _value.numUsers
          : numUsers // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DeviceUpdateImpl extends _DeviceUpdate with DiagnosticableTreeMixin {
  _$DeviceUpdateImpl(
      {required this.device,
      @JsonKey(name: 'deviceID') required this.deviceId,
      this.numUsers})
      : super._();

  factory _$DeviceUpdateImpl.fromJson(Map<String, dynamic> json) =>
      _$$DeviceUpdateImplFromJson(json);

  @override
  final Device device;
  @override
  @JsonKey(name: 'deviceID')
  final String deviceId;
  @override
  final int? numUsers;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DeviceUpdate(device: $device, deviceId: $deviceId, numUsers: $numUsers)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'DeviceUpdate'))
      ..add(DiagnosticsProperty('device', device))
      ..add(DiagnosticsProperty('deviceId', deviceId))
      ..add(DiagnosticsProperty('numUsers', numUsers));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeviceUpdateImpl &&
            (identical(other.device, device) || other.device == device) &&
            (identical(other.deviceId, deviceId) ||
                other.deviceId == deviceId) &&
            (identical(other.numUsers, numUsers) ||
                other.numUsers == numUsers));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, device, deviceId, numUsers);

  /// Create a copy of DeviceUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeviceUpdateImplCopyWith<_$DeviceUpdateImpl> get copyWith =>
      __$$DeviceUpdateImplCopyWithImpl<_$DeviceUpdateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DeviceUpdateImplToJson(
      this,
    );
  }
}

abstract class _DeviceUpdate extends DeviceUpdate {
  factory _DeviceUpdate(
      {required final Device device,
      @JsonKey(name: 'deviceID') required final String deviceId,
      final int? numUsers}) = _$DeviceUpdateImpl;
  _DeviceUpdate._() : super._();

  factory _DeviceUpdate.fromJson(Map<String, dynamic> json) =
      _$DeviceUpdateImpl.fromJson;

  @override
  Device get device;
  @override
  @JsonKey(name: 'deviceID')
  String get deviceId;
  @override
  int? get numUsers;

  /// Create a copy of DeviceUpdate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeviceUpdateImplCopyWith<_$DeviceUpdateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DeviceUser _$DeviceUserFromJson(Map<String, dynamic> json) {
  return _DeviceUser.fromJson(json);
}

/// @nodoc
mixin _$DeviceUser {
  int? get bytesDownloaded => throw _privateConstructorUsedError;
  int? get bytesUploaded => throw _privateConstructorUsedError;
  Device? get device => throw _privateConstructorUsedError;
  bool? get isOwner => throw _privateConstructorUsedError;
  int? get lastAccessTime => throw _privateConstructorUsedError;
  Space? get lastConnectSpace => throw _privateConstructorUsedError;
  List<DeviceUserSpaceConfig?>? get spaceConfigs =>
      throw _privateConstructorUsedError;
  UserAccessStatus? get status => throw _privateConstructorUsedError;
  User? get user => throw _privateConstructorUsedError;

  /// Serializes this DeviceUser to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DeviceUser
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DeviceUserCopyWith<DeviceUser> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeviceUserCopyWith<$Res> {
  factory $DeviceUserCopyWith(
          DeviceUser value, $Res Function(DeviceUser) then) =
      _$DeviceUserCopyWithImpl<$Res, DeviceUser>;
  @useResult
  $Res call(
      {int? bytesDownloaded,
      int? bytesUploaded,
      Device? device,
      bool? isOwner,
      int? lastAccessTime,
      Space? lastConnectSpace,
      List<DeviceUserSpaceConfig?>? spaceConfigs,
      UserAccessStatus? status,
      User? user});

  $DeviceCopyWith<$Res>? get device;
  $SpaceCopyWith<$Res>? get lastConnectSpace;
  $UserCopyWith<$Res>? get user;
}

/// @nodoc
class _$DeviceUserCopyWithImpl<$Res, $Val extends DeviceUser>
    implements $DeviceUserCopyWith<$Res> {
  _$DeviceUserCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeviceUser
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bytesDownloaded = freezed,
    Object? bytesUploaded = freezed,
    Object? device = freezed,
    Object? isOwner = freezed,
    Object? lastAccessTime = freezed,
    Object? lastConnectSpace = freezed,
    Object? spaceConfigs = freezed,
    Object? status = freezed,
    Object? user = freezed,
  }) {
    return _then(_value.copyWith(
      bytesDownloaded: freezed == bytesDownloaded
          ? _value.bytesDownloaded
          : bytesDownloaded // ignore: cast_nullable_to_non_nullable
              as int?,
      bytesUploaded: freezed == bytesUploaded
          ? _value.bytesUploaded
          : bytesUploaded // ignore: cast_nullable_to_non_nullable
              as int?,
      device: freezed == device
          ? _value.device
          : device // ignore: cast_nullable_to_non_nullable
              as Device?,
      isOwner: freezed == isOwner
          ? _value.isOwner
          : isOwner // ignore: cast_nullable_to_non_nullable
              as bool?,
      lastAccessTime: freezed == lastAccessTime
          ? _value.lastAccessTime
          : lastAccessTime // ignore: cast_nullable_to_non_nullable
              as int?,
      lastConnectSpace: freezed == lastConnectSpace
          ? _value.lastConnectSpace
          : lastConnectSpace // ignore: cast_nullable_to_non_nullable
              as Space?,
      spaceConfigs: freezed == spaceConfigs
          ? _value.spaceConfigs
          : spaceConfigs // ignore: cast_nullable_to_non_nullable
              as List<DeviceUserSpaceConfig?>?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as UserAccessStatus?,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User?,
    ) as $Val);
  }

  /// Create a copy of DeviceUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DeviceCopyWith<$Res>? get device {
    if (_value.device == null) {
      return null;
    }

    return $DeviceCopyWith<$Res>(_value.device!, (value) {
      return _then(_value.copyWith(device: value) as $Val);
    });
  }

  /// Create a copy of DeviceUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SpaceCopyWith<$Res>? get lastConnectSpace {
    if (_value.lastConnectSpace == null) {
      return null;
    }

    return $SpaceCopyWith<$Res>(_value.lastConnectSpace!, (value) {
      return _then(_value.copyWith(lastConnectSpace: value) as $Val);
    });
  }

  /// Create a copy of DeviceUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserCopyWith<$Res>? get user {
    if (_value.user == null) {
      return null;
    }

    return $UserCopyWith<$Res>(_value.user!, (value) {
      return _then(_value.copyWith(user: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$DeviceUserImplCopyWith<$Res>
    implements $DeviceUserCopyWith<$Res> {
  factory _$$DeviceUserImplCopyWith(
          _$DeviceUserImpl value, $Res Function(_$DeviceUserImpl) then) =
      __$$DeviceUserImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? bytesDownloaded,
      int? bytesUploaded,
      Device? device,
      bool? isOwner,
      int? lastAccessTime,
      Space? lastConnectSpace,
      List<DeviceUserSpaceConfig?>? spaceConfigs,
      UserAccessStatus? status,
      User? user});

  @override
  $DeviceCopyWith<$Res>? get device;
  @override
  $SpaceCopyWith<$Res>? get lastConnectSpace;
  @override
  $UserCopyWith<$Res>? get user;
}

/// @nodoc
class __$$DeviceUserImplCopyWithImpl<$Res>
    extends _$DeviceUserCopyWithImpl<$Res, _$DeviceUserImpl>
    implements _$$DeviceUserImplCopyWith<$Res> {
  __$$DeviceUserImplCopyWithImpl(
      _$DeviceUserImpl _value, $Res Function(_$DeviceUserImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceUser
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bytesDownloaded = freezed,
    Object? bytesUploaded = freezed,
    Object? device = freezed,
    Object? isOwner = freezed,
    Object? lastAccessTime = freezed,
    Object? lastConnectSpace = freezed,
    Object? spaceConfigs = freezed,
    Object? status = freezed,
    Object? user = freezed,
  }) {
    return _then(_$DeviceUserImpl(
      bytesDownloaded: freezed == bytesDownloaded
          ? _value.bytesDownloaded
          : bytesDownloaded // ignore: cast_nullable_to_non_nullable
              as int?,
      bytesUploaded: freezed == bytesUploaded
          ? _value.bytesUploaded
          : bytesUploaded // ignore: cast_nullable_to_non_nullable
              as int?,
      device: freezed == device
          ? _value.device
          : device // ignore: cast_nullable_to_non_nullable
              as Device?,
      isOwner: freezed == isOwner
          ? _value.isOwner
          : isOwner // ignore: cast_nullable_to_non_nullable
              as bool?,
      lastAccessTime: freezed == lastAccessTime
          ? _value.lastAccessTime
          : lastAccessTime // ignore: cast_nullable_to_non_nullable
              as int?,
      lastConnectSpace: freezed == lastConnectSpace
          ? _value.lastConnectSpace
          : lastConnectSpace // ignore: cast_nullable_to_non_nullable
              as Space?,
      spaceConfigs: freezed == spaceConfigs
          ? _value._spaceConfigs
          : spaceConfigs // ignore: cast_nullable_to_non_nullable
              as List<DeviceUserSpaceConfig?>?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as UserAccessStatus?,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DeviceUserImpl extends _DeviceUser with DiagnosticableTreeMixin {
  _$DeviceUserImpl(
      {this.bytesDownloaded,
      this.bytesUploaded,
      this.device,
      this.isOwner,
      this.lastAccessTime,
      this.lastConnectSpace,
      final List<DeviceUserSpaceConfig?>? spaceConfigs,
      this.status,
      this.user})
      : _spaceConfigs = spaceConfigs,
        super._();

  factory _$DeviceUserImpl.fromJson(Map<String, dynamic> json) =>
      _$$DeviceUserImplFromJson(json);

  @override
  final int? bytesDownloaded;
  @override
  final int? bytesUploaded;
  @override
  final Device? device;
  @override
  final bool? isOwner;
  @override
  final int? lastAccessTime;
  @override
  final Space? lastConnectSpace;
  final List<DeviceUserSpaceConfig?>? _spaceConfigs;
  @override
  List<DeviceUserSpaceConfig?>? get spaceConfigs {
    final value = _spaceConfigs;
    if (value == null) return null;
    if (_spaceConfigs is EqualUnmodifiableListView) return _spaceConfigs;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final UserAccessStatus? status;
  @override
  final User? user;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DeviceUser(bytesDownloaded: $bytesDownloaded, bytesUploaded: $bytesUploaded, device: $device, isOwner: $isOwner, lastAccessTime: $lastAccessTime, lastConnectSpace: $lastConnectSpace, spaceConfigs: $spaceConfigs, status: $status, user: $user)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'DeviceUser'))
      ..add(DiagnosticsProperty('bytesDownloaded', bytesDownloaded))
      ..add(DiagnosticsProperty('bytesUploaded', bytesUploaded))
      ..add(DiagnosticsProperty('device', device))
      ..add(DiagnosticsProperty('isOwner', isOwner))
      ..add(DiagnosticsProperty('lastAccessTime', lastAccessTime))
      ..add(DiagnosticsProperty('lastConnectSpace', lastConnectSpace))
      ..add(DiagnosticsProperty('spaceConfigs', spaceConfigs))
      ..add(DiagnosticsProperty('status', status))
      ..add(DiagnosticsProperty('user', user));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeviceUserImpl &&
            (identical(other.bytesDownloaded, bytesDownloaded) ||
                other.bytesDownloaded == bytesDownloaded) &&
            (identical(other.bytesUploaded, bytesUploaded) ||
                other.bytesUploaded == bytesUploaded) &&
            (identical(other.device, device) || other.device == device) &&
            (identical(other.isOwner, isOwner) || other.isOwner == isOwner) &&
            (identical(other.lastAccessTime, lastAccessTime) ||
                other.lastAccessTime == lastAccessTime) &&
            (identical(other.lastConnectSpace, lastConnectSpace) ||
                other.lastConnectSpace == lastConnectSpace) &&
            const DeepCollectionEquality()
                .equals(other._spaceConfigs, _spaceConfigs) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.user, user) || other.user == user));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      bytesDownloaded,
      bytesUploaded,
      device,
      isOwner,
      lastAccessTime,
      lastConnectSpace,
      const DeepCollectionEquality().hash(_spaceConfigs),
      status,
      user);

  /// Create a copy of DeviceUser
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeviceUserImplCopyWith<_$DeviceUserImpl> get copyWith =>
      __$$DeviceUserImplCopyWithImpl<_$DeviceUserImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DeviceUserImplToJson(
      this,
    );
  }
}

abstract class _DeviceUser extends DeviceUser {
  factory _DeviceUser(
      {final int? bytesDownloaded,
      final int? bytesUploaded,
      final Device? device,
      final bool? isOwner,
      final int? lastAccessTime,
      final Space? lastConnectSpace,
      final List<DeviceUserSpaceConfig?>? spaceConfigs,
      final UserAccessStatus? status,
      final User? user}) = _$DeviceUserImpl;
  _DeviceUser._() : super._();

  factory _DeviceUser.fromJson(Map<String, dynamic> json) =
      _$DeviceUserImpl.fromJson;

  @override
  int? get bytesDownloaded;
  @override
  int? get bytesUploaded;
  @override
  Device? get device;
  @override
  bool? get isOwner;
  @override
  int? get lastAccessTime;
  @override
  Space? get lastConnectSpace;
  @override
  List<DeviceUserSpaceConfig?>? get spaceConfigs;
  @override
  UserAccessStatus? get status;
  @override
  User? get user;

  /// Create a copy of DeviceUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeviceUserImplCopyWith<_$DeviceUserImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DeviceUserSpaceConfig _$DeviceUserSpaceConfigFromJson(
    Map<String, dynamic> json) {
  return _DeviceUserSpaceConfig.fromJson(json);
}

/// @nodoc
mixin _$DeviceUserSpaceConfig {
  Space? get space => throw _privateConstructorUsedError;
  bool? get viewed => throw _privateConstructorUsedError;
  String? get wgConfig => throw _privateConstructorUsedError;
  int? get wgConfigExpireAt => throw _privateConstructorUsedError;
  String? get wgConfigName => throw _privateConstructorUsedError;
  int? get wgInactivityExpireAt => throw _privateConstructorUsedError;
  int? get wgInactivityTimeout => throw _privateConstructorUsedError;

  /// Serializes this DeviceUserSpaceConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DeviceUserSpaceConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DeviceUserSpaceConfigCopyWith<DeviceUserSpaceConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeviceUserSpaceConfigCopyWith<$Res> {
  factory $DeviceUserSpaceConfigCopyWith(DeviceUserSpaceConfig value,
          $Res Function(DeviceUserSpaceConfig) then) =
      _$DeviceUserSpaceConfigCopyWithImpl<$Res, DeviceUserSpaceConfig>;
  @useResult
  $Res call(
      {Space? space,
      bool? viewed,
      String? wgConfig,
      int? wgConfigExpireAt,
      String? wgConfigName,
      int? wgInactivityExpireAt,
      int? wgInactivityTimeout});

  $SpaceCopyWith<$Res>? get space;
}

/// @nodoc
class _$DeviceUserSpaceConfigCopyWithImpl<$Res,
        $Val extends DeviceUserSpaceConfig>
    implements $DeviceUserSpaceConfigCopyWith<$Res> {
  _$DeviceUserSpaceConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeviceUserSpaceConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? space = freezed,
    Object? viewed = freezed,
    Object? wgConfig = freezed,
    Object? wgConfigExpireAt = freezed,
    Object? wgConfigName = freezed,
    Object? wgInactivityExpireAt = freezed,
    Object? wgInactivityTimeout = freezed,
  }) {
    return _then(_value.copyWith(
      space: freezed == space
          ? _value.space
          : space // ignore: cast_nullable_to_non_nullable
              as Space?,
      viewed: freezed == viewed
          ? _value.viewed
          : viewed // ignore: cast_nullable_to_non_nullable
              as bool?,
      wgConfig: freezed == wgConfig
          ? _value.wgConfig
          : wgConfig // ignore: cast_nullable_to_non_nullable
              as String?,
      wgConfigExpireAt: freezed == wgConfigExpireAt
          ? _value.wgConfigExpireAt
          : wgConfigExpireAt // ignore: cast_nullable_to_non_nullable
              as int?,
      wgConfigName: freezed == wgConfigName
          ? _value.wgConfigName
          : wgConfigName // ignore: cast_nullable_to_non_nullable
              as String?,
      wgInactivityExpireAt: freezed == wgInactivityExpireAt
          ? _value.wgInactivityExpireAt
          : wgInactivityExpireAt // ignore: cast_nullable_to_non_nullable
              as int?,
      wgInactivityTimeout: freezed == wgInactivityTimeout
          ? _value.wgInactivityTimeout
          : wgInactivityTimeout // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }

  /// Create a copy of DeviceUserSpaceConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SpaceCopyWith<$Res>? get space {
    if (_value.space == null) {
      return null;
    }

    return $SpaceCopyWith<$Res>(_value.space!, (value) {
      return _then(_value.copyWith(space: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$DeviceUserSpaceConfigImplCopyWith<$Res>
    implements $DeviceUserSpaceConfigCopyWith<$Res> {
  factory _$$DeviceUserSpaceConfigImplCopyWith(
          _$DeviceUserSpaceConfigImpl value,
          $Res Function(_$DeviceUserSpaceConfigImpl) then) =
      __$$DeviceUserSpaceConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Space? space,
      bool? viewed,
      String? wgConfig,
      int? wgConfigExpireAt,
      String? wgConfigName,
      int? wgInactivityExpireAt,
      int? wgInactivityTimeout});

  @override
  $SpaceCopyWith<$Res>? get space;
}

/// @nodoc
class __$$DeviceUserSpaceConfigImplCopyWithImpl<$Res>
    extends _$DeviceUserSpaceConfigCopyWithImpl<$Res,
        _$DeviceUserSpaceConfigImpl>
    implements _$$DeviceUserSpaceConfigImplCopyWith<$Res> {
  __$$DeviceUserSpaceConfigImplCopyWithImpl(_$DeviceUserSpaceConfigImpl _value,
      $Res Function(_$DeviceUserSpaceConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceUserSpaceConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? space = freezed,
    Object? viewed = freezed,
    Object? wgConfig = freezed,
    Object? wgConfigExpireAt = freezed,
    Object? wgConfigName = freezed,
    Object? wgInactivityExpireAt = freezed,
    Object? wgInactivityTimeout = freezed,
  }) {
    return _then(_$DeviceUserSpaceConfigImpl(
      space: freezed == space
          ? _value.space
          : space // ignore: cast_nullable_to_non_nullable
              as Space?,
      viewed: freezed == viewed
          ? _value.viewed
          : viewed // ignore: cast_nullable_to_non_nullable
              as bool?,
      wgConfig: freezed == wgConfig
          ? _value.wgConfig
          : wgConfig // ignore: cast_nullable_to_non_nullable
              as String?,
      wgConfigExpireAt: freezed == wgConfigExpireAt
          ? _value.wgConfigExpireAt
          : wgConfigExpireAt // ignore: cast_nullable_to_non_nullable
              as int?,
      wgConfigName: freezed == wgConfigName
          ? _value.wgConfigName
          : wgConfigName // ignore: cast_nullable_to_non_nullable
              as String?,
      wgInactivityExpireAt: freezed == wgInactivityExpireAt
          ? _value.wgInactivityExpireAt
          : wgInactivityExpireAt // ignore: cast_nullable_to_non_nullable
              as int?,
      wgInactivityTimeout: freezed == wgInactivityTimeout
          ? _value.wgInactivityTimeout
          : wgInactivityTimeout // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DeviceUserSpaceConfigImpl extends _DeviceUserSpaceConfig
    with DiagnosticableTreeMixin {
  _$DeviceUserSpaceConfigImpl(
      {this.space,
      this.viewed,
      this.wgConfig,
      this.wgConfigExpireAt,
      this.wgConfigName,
      this.wgInactivityExpireAt,
      this.wgInactivityTimeout})
      : super._();

  factory _$DeviceUserSpaceConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$DeviceUserSpaceConfigImplFromJson(json);

  @override
  final Space? space;
  @override
  final bool? viewed;
  @override
  final String? wgConfig;
  @override
  final int? wgConfigExpireAt;
  @override
  final String? wgConfigName;
  @override
  final int? wgInactivityExpireAt;
  @override
  final int? wgInactivityTimeout;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DeviceUserSpaceConfig(space: $space, viewed: $viewed, wgConfig: $wgConfig, wgConfigExpireAt: $wgConfigExpireAt, wgConfigName: $wgConfigName, wgInactivityExpireAt: $wgInactivityExpireAt, wgInactivityTimeout: $wgInactivityTimeout)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'DeviceUserSpaceConfig'))
      ..add(DiagnosticsProperty('space', space))
      ..add(DiagnosticsProperty('viewed', viewed))
      ..add(DiagnosticsProperty('wgConfig', wgConfig))
      ..add(DiagnosticsProperty('wgConfigExpireAt', wgConfigExpireAt))
      ..add(DiagnosticsProperty('wgConfigName', wgConfigName))
      ..add(DiagnosticsProperty('wgInactivityExpireAt', wgInactivityExpireAt))
      ..add(DiagnosticsProperty('wgInactivityTimeout', wgInactivityTimeout));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeviceUserSpaceConfigImpl &&
            (identical(other.space, space) || other.space == space) &&
            (identical(other.viewed, viewed) || other.viewed == viewed) &&
            (identical(other.wgConfig, wgConfig) ||
                other.wgConfig == wgConfig) &&
            (identical(other.wgConfigExpireAt, wgConfigExpireAt) ||
                other.wgConfigExpireAt == wgConfigExpireAt) &&
            (identical(other.wgConfigName, wgConfigName) ||
                other.wgConfigName == wgConfigName) &&
            (identical(other.wgInactivityExpireAt, wgInactivityExpireAt) ||
                other.wgInactivityExpireAt == wgInactivityExpireAt) &&
            (identical(other.wgInactivityTimeout, wgInactivityTimeout) ||
                other.wgInactivityTimeout == wgInactivityTimeout));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      space,
      viewed,
      wgConfig,
      wgConfigExpireAt,
      wgConfigName,
      wgInactivityExpireAt,
      wgInactivityTimeout);

  /// Create a copy of DeviceUserSpaceConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeviceUserSpaceConfigImplCopyWith<_$DeviceUserSpaceConfigImpl>
      get copyWith => __$$DeviceUserSpaceConfigImplCopyWithImpl<
          _$DeviceUserSpaceConfigImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DeviceUserSpaceConfigImplToJson(
      this,
    );
  }
}

abstract class _DeviceUserSpaceConfig extends DeviceUserSpaceConfig {
  factory _DeviceUserSpaceConfig(
      {final Space? space,
      final bool? viewed,
      final String? wgConfig,
      final int? wgConfigExpireAt,
      final String? wgConfigName,
      final int? wgInactivityExpireAt,
      final int? wgInactivityTimeout}) = _$DeviceUserSpaceConfigImpl;
  _DeviceUserSpaceConfig._() : super._();

  factory _DeviceUserSpaceConfig.fromJson(Map<String, dynamic> json) =
      _$DeviceUserSpaceConfigImpl.fromJson;

  @override
  Space? get space;
  @override
  bool? get viewed;
  @override
  String? get wgConfig;
  @override
  int? get wgConfigExpireAt;
  @override
  String? get wgConfigName;
  @override
  int? get wgInactivityExpireAt;
  @override
  int? get wgInactivityTimeout;

  /// Create a copy of DeviceUserSpaceConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeviceUserSpaceConfigImplCopyWith<_$DeviceUserSpaceConfigImpl>
      get copyWith => throw _privateConstructorUsedError;
}

DeviceUserUpdate _$DeviceUserUpdateFromJson(Map<String, dynamic> json) {
  return _DeviceUserUpdate.fromJson(json);
}

/// @nodoc
mixin _$DeviceUserUpdate {
  @JsonKey(name: 'deviceID')
  String get deviceId => throw _privateConstructorUsedError;
  DeviceUser get deviceUser => throw _privateConstructorUsedError;
  @JsonKey(name: 'userID')
  String get userId => throw _privateConstructorUsedError;

  /// Serializes this DeviceUserUpdate to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DeviceUserUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DeviceUserUpdateCopyWith<DeviceUserUpdate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeviceUserUpdateCopyWith<$Res> {
  factory $DeviceUserUpdateCopyWith(
          DeviceUserUpdate value, $Res Function(DeviceUserUpdate) then) =
      _$DeviceUserUpdateCopyWithImpl<$Res, DeviceUserUpdate>;
  @useResult
  $Res call(
      {@JsonKey(name: 'deviceID') String deviceId,
      DeviceUser deviceUser,
      @JsonKey(name: 'userID') String userId});

  $DeviceUserCopyWith<$Res> get deviceUser;
}

/// @nodoc
class _$DeviceUserUpdateCopyWithImpl<$Res, $Val extends DeviceUserUpdate>
    implements $DeviceUserUpdateCopyWith<$Res> {
  _$DeviceUserUpdateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeviceUserUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? deviceId = null,
    Object? deviceUser = null,
    Object? userId = null,
  }) {
    return _then(_value.copyWith(
      deviceId: null == deviceId
          ? _value.deviceId
          : deviceId // ignore: cast_nullable_to_non_nullable
              as String,
      deviceUser: null == deviceUser
          ? _value.deviceUser
          : deviceUser // ignore: cast_nullable_to_non_nullable
              as DeviceUser,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }

  /// Create a copy of DeviceUserUpdate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DeviceUserCopyWith<$Res> get deviceUser {
    return $DeviceUserCopyWith<$Res>(_value.deviceUser, (value) {
      return _then(_value.copyWith(deviceUser: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$DeviceUserUpdateImplCopyWith<$Res>
    implements $DeviceUserUpdateCopyWith<$Res> {
  factory _$$DeviceUserUpdateImplCopyWith(_$DeviceUserUpdateImpl value,
          $Res Function(_$DeviceUserUpdateImpl) then) =
      __$$DeviceUserUpdateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'deviceID') String deviceId,
      DeviceUser deviceUser,
      @JsonKey(name: 'userID') String userId});

  @override
  $DeviceUserCopyWith<$Res> get deviceUser;
}

/// @nodoc
class __$$DeviceUserUpdateImplCopyWithImpl<$Res>
    extends _$DeviceUserUpdateCopyWithImpl<$Res, _$DeviceUserUpdateImpl>
    implements _$$DeviceUserUpdateImplCopyWith<$Res> {
  __$$DeviceUserUpdateImplCopyWithImpl(_$DeviceUserUpdateImpl _value,
      $Res Function(_$DeviceUserUpdateImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceUserUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? deviceId = null,
    Object? deviceUser = null,
    Object? userId = null,
  }) {
    return _then(_$DeviceUserUpdateImpl(
      deviceId: null == deviceId
          ? _value.deviceId
          : deviceId // ignore: cast_nullable_to_non_nullable
              as String,
      deviceUser: null == deviceUser
          ? _value.deviceUser
          : deviceUser // ignore: cast_nullable_to_non_nullable
              as DeviceUser,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DeviceUserUpdateImpl extends _DeviceUserUpdate
    with DiagnosticableTreeMixin {
  _$DeviceUserUpdateImpl(
      {@JsonKey(name: 'deviceID') required this.deviceId,
      required this.deviceUser,
      @JsonKey(name: 'userID') required this.userId})
      : super._();

  factory _$DeviceUserUpdateImpl.fromJson(Map<String, dynamic> json) =>
      _$$DeviceUserUpdateImplFromJson(json);

  @override
  @JsonKey(name: 'deviceID')
  final String deviceId;
  @override
  final DeviceUser deviceUser;
  @override
  @JsonKey(name: 'userID')
  final String userId;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DeviceUserUpdate(deviceId: $deviceId, deviceUser: $deviceUser, userId: $userId)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'DeviceUserUpdate'))
      ..add(DiagnosticsProperty('deviceId', deviceId))
      ..add(DiagnosticsProperty('deviceUser', deviceUser))
      ..add(DiagnosticsProperty('userId', userId));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeviceUserUpdateImpl &&
            (identical(other.deviceId, deviceId) ||
                other.deviceId == deviceId) &&
            (identical(other.deviceUser, deviceUser) ||
                other.deviceUser == deviceUser) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, deviceId, deviceUser, userId);

  /// Create a copy of DeviceUserUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeviceUserUpdateImplCopyWith<_$DeviceUserUpdateImpl> get copyWith =>
      __$$DeviceUserUpdateImplCopyWithImpl<_$DeviceUserUpdateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DeviceUserUpdateImplToJson(
      this,
    );
  }
}

abstract class _DeviceUserUpdate extends DeviceUserUpdate {
  factory _DeviceUserUpdate(
          {@JsonKey(name: 'deviceID') required final String deviceId,
          required final DeviceUser deviceUser,
          @JsonKey(name: 'userID') required final String userId}) =
      _$DeviceUserUpdateImpl;
  _DeviceUserUpdate._() : super._();

  factory _DeviceUserUpdate.fromJson(Map<String, dynamic> json) =
      _$DeviceUserUpdateImpl.fromJson;

  @override
  @JsonKey(name: 'deviceID')
  String get deviceId;
  @override
  DeviceUser get deviceUser;
  @override
  @JsonKey(name: 'userID')
  String get userId;

  /// Create a copy of DeviceUserUpdate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeviceUserUpdateImplCopyWith<_$DeviceUserUpdateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DeviceUsersConnection _$DeviceUsersConnectionFromJson(
    Map<String, dynamic> json) {
  return _DeviceUsersConnection.fromJson(json);
}

/// @nodoc
mixin _$DeviceUsersConnection {
  List<DeviceUser?>? get deviceUsers => throw _privateConstructorUsedError;
  List<DeviceUsersEdge?>? get edges => throw _privateConstructorUsedError;
  PageInfo? get pageInfo => throw _privateConstructorUsedError;
  int? get totalCount => throw _privateConstructorUsedError;

  /// Serializes this DeviceUsersConnection to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DeviceUsersConnection
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DeviceUsersConnectionCopyWith<DeviceUsersConnection> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeviceUsersConnectionCopyWith<$Res> {
  factory $DeviceUsersConnectionCopyWith(DeviceUsersConnection value,
          $Res Function(DeviceUsersConnection) then) =
      _$DeviceUsersConnectionCopyWithImpl<$Res, DeviceUsersConnection>;
  @useResult
  $Res call(
      {List<DeviceUser?>? deviceUsers,
      List<DeviceUsersEdge?>? edges,
      PageInfo? pageInfo,
      int? totalCount});

  $PageInfoCopyWith<$Res>? get pageInfo;
}

/// @nodoc
class _$DeviceUsersConnectionCopyWithImpl<$Res,
        $Val extends DeviceUsersConnection>
    implements $DeviceUsersConnectionCopyWith<$Res> {
  _$DeviceUsersConnectionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeviceUsersConnection
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? deviceUsers = freezed,
    Object? edges = freezed,
    Object? pageInfo = freezed,
    Object? totalCount = freezed,
  }) {
    return _then(_value.copyWith(
      deviceUsers: freezed == deviceUsers
          ? _value.deviceUsers
          : deviceUsers // ignore: cast_nullable_to_non_nullable
              as List<DeviceUser?>?,
      edges: freezed == edges
          ? _value.edges
          : edges // ignore: cast_nullable_to_non_nullable
              as List<DeviceUsersEdge?>?,
      pageInfo: freezed == pageInfo
          ? _value.pageInfo
          : pageInfo // ignore: cast_nullable_to_non_nullable
              as PageInfo?,
      totalCount: freezed == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }

  /// Create a copy of DeviceUsersConnection
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PageInfoCopyWith<$Res>? get pageInfo {
    if (_value.pageInfo == null) {
      return null;
    }

    return $PageInfoCopyWith<$Res>(_value.pageInfo!, (value) {
      return _then(_value.copyWith(pageInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$DeviceUsersConnectionImplCopyWith<$Res>
    implements $DeviceUsersConnectionCopyWith<$Res> {
  factory _$$DeviceUsersConnectionImplCopyWith(
          _$DeviceUsersConnectionImpl value,
          $Res Function(_$DeviceUsersConnectionImpl) then) =
      __$$DeviceUsersConnectionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<DeviceUser?>? deviceUsers,
      List<DeviceUsersEdge?>? edges,
      PageInfo? pageInfo,
      int? totalCount});

  @override
  $PageInfoCopyWith<$Res>? get pageInfo;
}

/// @nodoc
class __$$DeviceUsersConnectionImplCopyWithImpl<$Res>
    extends _$DeviceUsersConnectionCopyWithImpl<$Res,
        _$DeviceUsersConnectionImpl>
    implements _$$DeviceUsersConnectionImplCopyWith<$Res> {
  __$$DeviceUsersConnectionImplCopyWithImpl(_$DeviceUsersConnectionImpl _value,
      $Res Function(_$DeviceUsersConnectionImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceUsersConnection
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? deviceUsers = freezed,
    Object? edges = freezed,
    Object? pageInfo = freezed,
    Object? totalCount = freezed,
  }) {
    return _then(_$DeviceUsersConnectionImpl(
      deviceUsers: freezed == deviceUsers
          ? _value._deviceUsers
          : deviceUsers // ignore: cast_nullable_to_non_nullable
              as List<DeviceUser?>?,
      edges: freezed == edges
          ? _value._edges
          : edges // ignore: cast_nullable_to_non_nullable
              as List<DeviceUsersEdge?>?,
      pageInfo: freezed == pageInfo
          ? _value.pageInfo
          : pageInfo // ignore: cast_nullable_to_non_nullable
              as PageInfo?,
      totalCount: freezed == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DeviceUsersConnectionImpl extends _DeviceUsersConnection
    with DiagnosticableTreeMixin {
  _$DeviceUsersConnectionImpl(
      {final List<DeviceUser?>? deviceUsers,
      final List<DeviceUsersEdge?>? edges,
      this.pageInfo,
      this.totalCount})
      : _deviceUsers = deviceUsers,
        _edges = edges,
        super._();

  factory _$DeviceUsersConnectionImpl.fromJson(Map<String, dynamic> json) =>
      _$$DeviceUsersConnectionImplFromJson(json);

  final List<DeviceUser?>? _deviceUsers;
  @override
  List<DeviceUser?>? get deviceUsers {
    final value = _deviceUsers;
    if (value == null) return null;
    if (_deviceUsers is EqualUnmodifiableListView) return _deviceUsers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<DeviceUsersEdge?>? _edges;
  @override
  List<DeviceUsersEdge?>? get edges {
    final value = _edges;
    if (value == null) return null;
    if (_edges is EqualUnmodifiableListView) return _edges;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final PageInfo? pageInfo;
  @override
  final int? totalCount;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DeviceUsersConnection(deviceUsers: $deviceUsers, edges: $edges, pageInfo: $pageInfo, totalCount: $totalCount)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'DeviceUsersConnection'))
      ..add(DiagnosticsProperty('deviceUsers', deviceUsers))
      ..add(DiagnosticsProperty('edges', edges))
      ..add(DiagnosticsProperty('pageInfo', pageInfo))
      ..add(DiagnosticsProperty('totalCount', totalCount));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeviceUsersConnectionImpl &&
            const DeepCollectionEquality()
                .equals(other._deviceUsers, _deviceUsers) &&
            const DeepCollectionEquality().equals(other._edges, _edges) &&
            (identical(other.pageInfo, pageInfo) ||
                other.pageInfo == pageInfo) &&
            (identical(other.totalCount, totalCount) ||
                other.totalCount == totalCount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_deviceUsers),
      const DeepCollectionEquality().hash(_edges),
      pageInfo,
      totalCount);

  /// Create a copy of DeviceUsersConnection
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeviceUsersConnectionImplCopyWith<_$DeviceUsersConnectionImpl>
      get copyWith => __$$DeviceUsersConnectionImplCopyWithImpl<
          _$DeviceUsersConnectionImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DeviceUsersConnectionImplToJson(
      this,
    );
  }
}

abstract class _DeviceUsersConnection extends DeviceUsersConnection {
  factory _DeviceUsersConnection(
      {final List<DeviceUser?>? deviceUsers,
      final List<DeviceUsersEdge?>? edges,
      final PageInfo? pageInfo,
      final int? totalCount}) = _$DeviceUsersConnectionImpl;
  _DeviceUsersConnection._() : super._();

  factory _DeviceUsersConnection.fromJson(Map<String, dynamic> json) =
      _$DeviceUsersConnectionImpl.fromJson;

  @override
  List<DeviceUser?>? get deviceUsers;
  @override
  List<DeviceUsersEdge?>? get edges;
  @override
  PageInfo? get pageInfo;
  @override
  int? get totalCount;

  /// Create a copy of DeviceUsersConnection
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeviceUsersConnectionImplCopyWith<_$DeviceUsersConnectionImpl>
      get copyWith => throw _privateConstructorUsedError;
}

DeviceUsersEdge _$DeviceUsersEdgeFromJson(Map<String, dynamic> json) {
  return _DeviceUsersEdge.fromJson(json);
}

/// @nodoc
mixin _$DeviceUsersEdge {
  DeviceUser get node => throw _privateConstructorUsedError;

  /// Serializes this DeviceUsersEdge to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DeviceUsersEdge
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DeviceUsersEdgeCopyWith<DeviceUsersEdge> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeviceUsersEdgeCopyWith<$Res> {
  factory $DeviceUsersEdgeCopyWith(
          DeviceUsersEdge value, $Res Function(DeviceUsersEdge) then) =
      _$DeviceUsersEdgeCopyWithImpl<$Res, DeviceUsersEdge>;
  @useResult
  $Res call({DeviceUser node});

  $DeviceUserCopyWith<$Res> get node;
}

/// @nodoc
class _$DeviceUsersEdgeCopyWithImpl<$Res, $Val extends DeviceUsersEdge>
    implements $DeviceUsersEdgeCopyWith<$Res> {
  _$DeviceUsersEdgeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeviceUsersEdge
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? node = null,
  }) {
    return _then(_value.copyWith(
      node: null == node
          ? _value.node
          : node // ignore: cast_nullable_to_non_nullable
              as DeviceUser,
    ) as $Val);
  }

  /// Create a copy of DeviceUsersEdge
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DeviceUserCopyWith<$Res> get node {
    return $DeviceUserCopyWith<$Res>(_value.node, (value) {
      return _then(_value.copyWith(node: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$DeviceUsersEdgeImplCopyWith<$Res>
    implements $DeviceUsersEdgeCopyWith<$Res> {
  factory _$$DeviceUsersEdgeImplCopyWith(_$DeviceUsersEdgeImpl value,
          $Res Function(_$DeviceUsersEdgeImpl) then) =
      __$$DeviceUsersEdgeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({DeviceUser node});

  @override
  $DeviceUserCopyWith<$Res> get node;
}

/// @nodoc
class __$$DeviceUsersEdgeImplCopyWithImpl<$Res>
    extends _$DeviceUsersEdgeCopyWithImpl<$Res, _$DeviceUsersEdgeImpl>
    implements _$$DeviceUsersEdgeImplCopyWith<$Res> {
  __$$DeviceUsersEdgeImplCopyWithImpl(
      _$DeviceUsersEdgeImpl _value, $Res Function(_$DeviceUsersEdgeImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceUsersEdge
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? node = null,
  }) {
    return _then(_$DeviceUsersEdgeImpl(
      node: null == node
          ? _value.node
          : node // ignore: cast_nullable_to_non_nullable
              as DeviceUser,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DeviceUsersEdgeImpl extends _DeviceUsersEdge
    with DiagnosticableTreeMixin {
  _$DeviceUsersEdgeImpl({required this.node}) : super._();

  factory _$DeviceUsersEdgeImpl.fromJson(Map<String, dynamic> json) =>
      _$$DeviceUsersEdgeImplFromJson(json);

  @override
  final DeviceUser node;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DeviceUsersEdge(node: $node)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'DeviceUsersEdge'))
      ..add(DiagnosticsProperty('node', node));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeviceUsersEdgeImpl &&
            (identical(other.node, node) || other.node == node));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, node);

  /// Create a copy of DeviceUsersEdge
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeviceUsersEdgeImplCopyWith<_$DeviceUsersEdgeImpl> get copyWith =>
      __$$DeviceUsersEdgeImplCopyWithImpl<_$DeviceUsersEdgeImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DeviceUsersEdgeImplToJson(
      this,
    );
  }
}

abstract class _DeviceUsersEdge extends DeviceUsersEdge {
  factory _DeviceUsersEdge({required final DeviceUser node}) =
      _$DeviceUsersEdgeImpl;
  _DeviceUsersEdge._() : super._();

  factory _DeviceUsersEdge.fromJson(Map<String, dynamic> json) =
      _$DeviceUsersEdgeImpl.fromJson;

  @override
  DeviceUser get node;

  /// Create a copy of DeviceUsersEdge
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeviceUsersEdgeImplCopyWith<_$DeviceUsersEdgeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Key _$KeyFromJson(Map<String, dynamic> json) {
  return _Key.fromJson(json);
}

/// @nodoc
mixin _$Key {
  String? get certificateRequest => throw _privateConstructorUsedError;
  set certificateRequest(String? value) => throw _privateConstructorUsedError;
  String? get keyTimestamp => throw _privateConstructorUsedError;
  set keyTimestamp(String? value) => throw _privateConstructorUsedError;
  String get publicKey => throw _privateConstructorUsedError;
  set publicKey(String value) => throw _privateConstructorUsedError;

  /// Serializes this Key to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Key
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $KeyCopyWith<Key> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $KeyCopyWith<$Res> {
  factory $KeyCopyWith(Key value, $Res Function(Key) then) =
      _$KeyCopyWithImpl<$Res, Key>;
  @useResult
  $Res call(
      {String? certificateRequest, String? keyTimestamp, String publicKey});
}

/// @nodoc
class _$KeyCopyWithImpl<$Res, $Val extends Key> implements $KeyCopyWith<$Res> {
  _$KeyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Key
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? certificateRequest = freezed,
    Object? keyTimestamp = freezed,
    Object? publicKey = null,
  }) {
    return _then(_value.copyWith(
      certificateRequest: freezed == certificateRequest
          ? _value.certificateRequest
          : certificateRequest // ignore: cast_nullable_to_non_nullable
              as String?,
      keyTimestamp: freezed == keyTimestamp
          ? _value.keyTimestamp
          : keyTimestamp // ignore: cast_nullable_to_non_nullable
              as String?,
      publicKey: null == publicKey
          ? _value.publicKey
          : publicKey // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$KeyImplCopyWith<$Res> implements $KeyCopyWith<$Res> {
  factory _$$KeyImplCopyWith(_$KeyImpl value, $Res Function(_$KeyImpl) then) =
      __$$KeyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? certificateRequest, String? keyTimestamp, String publicKey});
}

/// @nodoc
class __$$KeyImplCopyWithImpl<$Res> extends _$KeyCopyWithImpl<$Res, _$KeyImpl>
    implements _$$KeyImplCopyWith<$Res> {
  __$$KeyImplCopyWithImpl(_$KeyImpl _value, $Res Function(_$KeyImpl) _then)
      : super(_value, _then);

  /// Create a copy of Key
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? certificateRequest = freezed,
    Object? keyTimestamp = freezed,
    Object? publicKey = null,
  }) {
    return _then(_$KeyImpl(
      certificateRequest: freezed == certificateRequest
          ? _value.certificateRequest
          : certificateRequest // ignore: cast_nullable_to_non_nullable
              as String?,
      keyTimestamp: freezed == keyTimestamp
          ? _value.keyTimestamp
          : keyTimestamp // ignore: cast_nullable_to_non_nullable
              as String?,
      publicKey: null == publicKey
          ? _value.publicKey
          : publicKey // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$KeyImpl extends _Key with DiagnosticableTreeMixin {
  _$KeyImpl(
      {this.certificateRequest, this.keyTimestamp, required this.publicKey})
      : super._();

  factory _$KeyImpl.fromJson(Map<String, dynamic> json) =>
      _$$KeyImplFromJson(json);

  @override
  String? certificateRequest;
  @override
  String? keyTimestamp;
  @override
  String publicKey;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'Key(certificateRequest: $certificateRequest, keyTimestamp: $keyTimestamp, publicKey: $publicKey)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'Key'))
      ..add(DiagnosticsProperty('certificateRequest', certificateRequest))
      ..add(DiagnosticsProperty('keyTimestamp', keyTimestamp))
      ..add(DiagnosticsProperty('publicKey', publicKey));
  }

  /// Create a copy of Key
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$KeyImplCopyWith<_$KeyImpl> get copyWith =>
      __$$KeyImplCopyWithImpl<_$KeyImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$KeyImplToJson(
      this,
    );
  }
}

abstract class _Key extends Key {
  factory _Key(
      {String? certificateRequest,
      String? keyTimestamp,
      required String publicKey}) = _$KeyImpl;
  _Key._() : super._();

  factory _Key.fromJson(Map<String, dynamic> json) = _$KeyImpl.fromJson;

  @override
  String? get certificateRequest;
  set certificateRequest(String? value);
  @override
  String? get keyTimestamp;
  set keyTimestamp(String? value);
  @override
  String get publicKey;
  set publicKey(String value);

  /// Create a copy of Key
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$KeyImplCopyWith<_$KeyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

MyCsCloudProps _$MyCsCloudPropsFromJson(Map<String, dynamic> json) {
  return _MyCsCloudProps.fromJson(json);
}

/// @nodoc
mixin _$MyCsCloudProps {
  String? get publicKey => throw _privateConstructorUsedError;
  @JsonKey(name: 'publicKeyID')
  String? get publicKeyId => throw _privateConstructorUsedError;

  /// Serializes this MyCsCloudProps to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MyCsCloudProps
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MyCsCloudPropsCopyWith<MyCsCloudProps> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MyCsCloudPropsCopyWith<$Res> {
  factory $MyCsCloudPropsCopyWith(
          MyCsCloudProps value, $Res Function(MyCsCloudProps) then) =
      _$MyCsCloudPropsCopyWithImpl<$Res, MyCsCloudProps>;
  @useResult
  $Res call(
      {String? publicKey, @JsonKey(name: 'publicKeyID') String? publicKeyId});
}

/// @nodoc
class _$MyCsCloudPropsCopyWithImpl<$Res, $Val extends MyCsCloudProps>
    implements $MyCsCloudPropsCopyWith<$Res> {
  _$MyCsCloudPropsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MyCsCloudProps
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? publicKey = freezed,
    Object? publicKeyId = freezed,
  }) {
    return _then(_value.copyWith(
      publicKey: freezed == publicKey
          ? _value.publicKey
          : publicKey // ignore: cast_nullable_to_non_nullable
              as String?,
      publicKeyId: freezed == publicKeyId
          ? _value.publicKeyId
          : publicKeyId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MyCsCloudPropsImplCopyWith<$Res>
    implements $MyCsCloudPropsCopyWith<$Res> {
  factory _$$MyCsCloudPropsImplCopyWith(_$MyCsCloudPropsImpl value,
          $Res Function(_$MyCsCloudPropsImpl) then) =
      __$$MyCsCloudPropsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? publicKey, @JsonKey(name: 'publicKeyID') String? publicKeyId});
}

/// @nodoc
class __$$MyCsCloudPropsImplCopyWithImpl<$Res>
    extends _$MyCsCloudPropsCopyWithImpl<$Res, _$MyCsCloudPropsImpl>
    implements _$$MyCsCloudPropsImplCopyWith<$Res> {
  __$$MyCsCloudPropsImplCopyWithImpl(
      _$MyCsCloudPropsImpl _value, $Res Function(_$MyCsCloudPropsImpl) _then)
      : super(_value, _then);

  /// Create a copy of MyCsCloudProps
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? publicKey = freezed,
    Object? publicKeyId = freezed,
  }) {
    return _then(_$MyCsCloudPropsImpl(
      publicKey: freezed == publicKey
          ? _value.publicKey
          : publicKey // ignore: cast_nullable_to_non_nullable
              as String?,
      publicKeyId: freezed == publicKeyId
          ? _value.publicKeyId
          : publicKeyId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MyCsCloudPropsImpl extends _MyCsCloudProps
    with DiagnosticableTreeMixin {
  _$MyCsCloudPropsImpl(
      {this.publicKey, @JsonKey(name: 'publicKeyID') this.publicKeyId})
      : super._();

  factory _$MyCsCloudPropsImpl.fromJson(Map<String, dynamic> json) =>
      _$$MyCsCloudPropsImplFromJson(json);

  @override
  final String? publicKey;
  @override
  @JsonKey(name: 'publicKeyID')
  final String? publicKeyId;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'MyCsCloudProps(publicKey: $publicKey, publicKeyId: $publicKeyId)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'MyCsCloudProps'))
      ..add(DiagnosticsProperty('publicKey', publicKey))
      ..add(DiagnosticsProperty('publicKeyId', publicKeyId));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MyCsCloudPropsImpl &&
            (identical(other.publicKey, publicKey) ||
                other.publicKey == publicKey) &&
            (identical(other.publicKeyId, publicKeyId) ||
                other.publicKeyId == publicKeyId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, publicKey, publicKeyId);

  /// Create a copy of MyCsCloudProps
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MyCsCloudPropsImplCopyWith<_$MyCsCloudPropsImpl> get copyWith =>
      __$$MyCsCloudPropsImplCopyWithImpl<_$MyCsCloudPropsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MyCsCloudPropsImplToJson(
      this,
    );
  }
}

abstract class _MyCsCloudProps extends MyCsCloudProps {
  factory _MyCsCloudProps(
          {final String? publicKey,
          @JsonKey(name: 'publicKeyID') final String? publicKeyId}) =
      _$MyCsCloudPropsImpl;
  _MyCsCloudProps._() : super._();

  factory _MyCsCloudProps.fromJson(Map<String, dynamic> json) =
      _$MyCsCloudPropsImpl.fromJson;

  @override
  String? get publicKey;
  @override
  @JsonKey(name: 'publicKeyID')
  String? get publicKeyId;

  /// Create a copy of MyCsCloudProps
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MyCsCloudPropsImplCopyWith<_$MyCsCloudPropsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Nv _$NvFromJson(Map<String, dynamic> json) {
  return _Nv.fromJson(json);
}

/// @nodoc
mixin _$Nv {
  String get name => throw _privateConstructorUsedError;
  String? get value => throw _privateConstructorUsedError;

  /// Serializes this Nv to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Nv
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $NvCopyWith<Nv> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NvCopyWith<$Res> {
  factory $NvCopyWith(Nv value, $Res Function(Nv) then) =
      _$NvCopyWithImpl<$Res, Nv>;
  @useResult
  $Res call({String name, String? value});
}

/// @nodoc
class _$NvCopyWithImpl<$Res, $Val extends Nv> implements $NvCopyWith<$Res> {
  _$NvCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Nv
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? value = freezed,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NvImplCopyWith<$Res> implements $NvCopyWith<$Res> {
  factory _$$NvImplCopyWith(_$NvImpl value, $Res Function(_$NvImpl) then) =
      __$$NvImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String name, String? value});
}

/// @nodoc
class __$$NvImplCopyWithImpl<$Res> extends _$NvCopyWithImpl<$Res, _$NvImpl>
    implements _$$NvImplCopyWith<$Res> {
  __$$NvImplCopyWithImpl(_$NvImpl _value, $Res Function(_$NvImpl) _then)
      : super(_value, _then);

  /// Create a copy of Nv
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? value = freezed,
  }) {
    return _then(_$NvImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$NvImpl extends _Nv with DiagnosticableTreeMixin {
  _$NvImpl({required this.name, this.value}) : super._();

  factory _$NvImpl.fromJson(Map<String, dynamic> json) =>
      _$$NvImplFromJson(json);

  @override
  final String name;
  @override
  final String? value;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'Nv(name: $name, value: $value)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'Nv'))
      ..add(DiagnosticsProperty('name', name))
      ..add(DiagnosticsProperty('value', value));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NvImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.value, value) || other.value == value));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, name, value);

  /// Create a copy of Nv
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NvImplCopyWith<_$NvImpl> get copyWith =>
      __$$NvImplCopyWithImpl<_$NvImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$NvImplToJson(
      this,
    );
  }
}

abstract class _Nv extends Nv {
  factory _Nv({required final String name, final String? value}) = _$NvImpl;
  _Nv._() : super._();

  factory _Nv.fromJson(Map<String, dynamic> json) = _$NvImpl.fromJson;

  @override
  String get name;
  @override
  String? get value;

  /// Create a copy of Nv
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NvImplCopyWith<_$NvImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Org _$OrgFromJson(Map<String, dynamic> json) {
  return _Org.fromJson(json);
}

/// @nodoc
mixin _$Org {
  List<Address?>? get addresses => throw _privateConstructorUsedError;
  List<UserRef?>? get admins => throw _privateConstructorUsedError;
  String? get businessRef => throw _privateConstructorUsedError;
  String? get certificate => throw _privateConstructorUsedError;
  String? get config => throw _privateConstructorUsedError;
  List<Contact?>? get contacts => throw _privateConstructorUsedError;
  List<Nv?>? get externalRefs => throw _privateConstructorUsedError;
  @JsonKey(name: 'orgID')
  String get orgId => throw _privateConstructorUsedError;
  String get orgName => throw _privateConstructorUsedError;
  UserRef? get owner => throw _privateConstructorUsedError;
  String? get publicKey => throw _privateConstructorUsedError;
  List<OrgQuota?>? get quotas => throw _privateConstructorUsedError;
  OrgSpacesConnection? get spaces => throw _privateConstructorUsedError;
  OrgUsersConnection? get users => throw _privateConstructorUsedError;
  bool? get verified => throw _privateConstructorUsedError;

  /// Serializes this Org to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Org
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrgCopyWith<Org> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrgCopyWith<$Res> {
  factory $OrgCopyWith(Org value, $Res Function(Org) then) =
      _$OrgCopyWithImpl<$Res, Org>;
  @useResult
  $Res call(
      {List<Address?>? addresses,
      List<UserRef?>? admins,
      String? businessRef,
      String? certificate,
      String? config,
      List<Contact?>? contacts,
      List<Nv?>? externalRefs,
      @JsonKey(name: 'orgID') String orgId,
      String orgName,
      UserRef? owner,
      String? publicKey,
      List<OrgQuota?>? quotas,
      OrgSpacesConnection? spaces,
      OrgUsersConnection? users,
      bool? verified});

  $UserRefCopyWith<$Res>? get owner;
  $OrgSpacesConnectionCopyWith<$Res>? get spaces;
  $OrgUsersConnectionCopyWith<$Res>? get users;
}

/// @nodoc
class _$OrgCopyWithImpl<$Res, $Val extends Org> implements $OrgCopyWith<$Res> {
  _$OrgCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Org
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? addresses = freezed,
    Object? admins = freezed,
    Object? businessRef = freezed,
    Object? certificate = freezed,
    Object? config = freezed,
    Object? contacts = freezed,
    Object? externalRefs = freezed,
    Object? orgId = null,
    Object? orgName = null,
    Object? owner = freezed,
    Object? publicKey = freezed,
    Object? quotas = freezed,
    Object? spaces = freezed,
    Object? users = freezed,
    Object? verified = freezed,
  }) {
    return _then(_value.copyWith(
      addresses: freezed == addresses
          ? _value.addresses
          : addresses // ignore: cast_nullable_to_non_nullable
              as List<Address?>?,
      admins: freezed == admins
          ? _value.admins
          : admins // ignore: cast_nullable_to_non_nullable
              as List<UserRef?>?,
      businessRef: freezed == businessRef
          ? _value.businessRef
          : businessRef // ignore: cast_nullable_to_non_nullable
              as String?,
      certificate: freezed == certificate
          ? _value.certificate
          : certificate // ignore: cast_nullable_to_non_nullable
              as String?,
      config: freezed == config
          ? _value.config
          : config // ignore: cast_nullable_to_non_nullable
              as String?,
      contacts: freezed == contacts
          ? _value.contacts
          : contacts // ignore: cast_nullable_to_non_nullable
              as List<Contact?>?,
      externalRefs: freezed == externalRefs
          ? _value.externalRefs
          : externalRefs // ignore: cast_nullable_to_non_nullable
              as List<Nv?>?,
      orgId: null == orgId
          ? _value.orgId
          : orgId // ignore: cast_nullable_to_non_nullable
              as String,
      orgName: null == orgName
          ? _value.orgName
          : orgName // ignore: cast_nullable_to_non_nullable
              as String,
      owner: freezed == owner
          ? _value.owner
          : owner // ignore: cast_nullable_to_non_nullable
              as UserRef?,
      publicKey: freezed == publicKey
          ? _value.publicKey
          : publicKey // ignore: cast_nullable_to_non_nullable
              as String?,
      quotas: freezed == quotas
          ? _value.quotas
          : quotas // ignore: cast_nullable_to_non_nullable
              as List<OrgQuota?>?,
      spaces: freezed == spaces
          ? _value.spaces
          : spaces // ignore: cast_nullable_to_non_nullable
              as OrgSpacesConnection?,
      users: freezed == users
          ? _value.users
          : users // ignore: cast_nullable_to_non_nullable
              as OrgUsersConnection?,
      verified: freezed == verified
          ? _value.verified
          : verified // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }

  /// Create a copy of Org
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserRefCopyWith<$Res>? get owner {
    if (_value.owner == null) {
      return null;
    }

    return $UserRefCopyWith<$Res>(_value.owner!, (value) {
      return _then(_value.copyWith(owner: value) as $Val);
    });
  }

  /// Create a copy of Org
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OrgSpacesConnectionCopyWith<$Res>? get spaces {
    if (_value.spaces == null) {
      return null;
    }

    return $OrgSpacesConnectionCopyWith<$Res>(_value.spaces!, (value) {
      return _then(_value.copyWith(spaces: value) as $Val);
    });
  }

  /// Create a copy of Org
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OrgUsersConnectionCopyWith<$Res>? get users {
    if (_value.users == null) {
      return null;
    }

    return $OrgUsersConnectionCopyWith<$Res>(_value.users!, (value) {
      return _then(_value.copyWith(users: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$OrgImplCopyWith<$Res> implements $OrgCopyWith<$Res> {
  factory _$$OrgImplCopyWith(_$OrgImpl value, $Res Function(_$OrgImpl) then) =
      __$$OrgImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<Address?>? addresses,
      List<UserRef?>? admins,
      String? businessRef,
      String? certificate,
      String? config,
      List<Contact?>? contacts,
      List<Nv?>? externalRefs,
      @JsonKey(name: 'orgID') String orgId,
      String orgName,
      UserRef? owner,
      String? publicKey,
      List<OrgQuota?>? quotas,
      OrgSpacesConnection? spaces,
      OrgUsersConnection? users,
      bool? verified});

  @override
  $UserRefCopyWith<$Res>? get owner;
  @override
  $OrgSpacesConnectionCopyWith<$Res>? get spaces;
  @override
  $OrgUsersConnectionCopyWith<$Res>? get users;
}

/// @nodoc
class __$$OrgImplCopyWithImpl<$Res> extends _$OrgCopyWithImpl<$Res, _$OrgImpl>
    implements _$$OrgImplCopyWith<$Res> {
  __$$OrgImplCopyWithImpl(_$OrgImpl _value, $Res Function(_$OrgImpl) _then)
      : super(_value, _then);

  /// Create a copy of Org
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? addresses = freezed,
    Object? admins = freezed,
    Object? businessRef = freezed,
    Object? certificate = freezed,
    Object? config = freezed,
    Object? contacts = freezed,
    Object? externalRefs = freezed,
    Object? orgId = null,
    Object? orgName = null,
    Object? owner = freezed,
    Object? publicKey = freezed,
    Object? quotas = freezed,
    Object? spaces = freezed,
    Object? users = freezed,
    Object? verified = freezed,
  }) {
    return _then(_$OrgImpl(
      addresses: freezed == addresses
          ? _value._addresses
          : addresses // ignore: cast_nullable_to_non_nullable
              as List<Address?>?,
      admins: freezed == admins
          ? _value._admins
          : admins // ignore: cast_nullable_to_non_nullable
              as List<UserRef?>?,
      businessRef: freezed == businessRef
          ? _value.businessRef
          : businessRef // ignore: cast_nullable_to_non_nullable
              as String?,
      certificate: freezed == certificate
          ? _value.certificate
          : certificate // ignore: cast_nullable_to_non_nullable
              as String?,
      config: freezed == config
          ? _value.config
          : config // ignore: cast_nullable_to_non_nullable
              as String?,
      contacts: freezed == contacts
          ? _value._contacts
          : contacts // ignore: cast_nullable_to_non_nullable
              as List<Contact?>?,
      externalRefs: freezed == externalRefs
          ? _value._externalRefs
          : externalRefs // ignore: cast_nullable_to_non_nullable
              as List<Nv?>?,
      orgId: null == orgId
          ? _value.orgId
          : orgId // ignore: cast_nullable_to_non_nullable
              as String,
      orgName: null == orgName
          ? _value.orgName
          : orgName // ignore: cast_nullable_to_non_nullable
              as String,
      owner: freezed == owner
          ? _value.owner
          : owner // ignore: cast_nullable_to_non_nullable
              as UserRef?,
      publicKey: freezed == publicKey
          ? _value.publicKey
          : publicKey // ignore: cast_nullable_to_non_nullable
              as String?,
      quotas: freezed == quotas
          ? _value._quotas
          : quotas // ignore: cast_nullable_to_non_nullable
              as List<OrgQuota?>?,
      spaces: freezed == spaces
          ? _value.spaces
          : spaces // ignore: cast_nullable_to_non_nullable
              as OrgSpacesConnection?,
      users: freezed == users
          ? _value.users
          : users // ignore: cast_nullable_to_non_nullable
              as OrgUsersConnection?,
      verified: freezed == verified
          ? _value.verified
          : verified // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OrgImpl extends _Org with DiagnosticableTreeMixin {
  _$OrgImpl(
      {final List<Address?>? addresses,
      final List<UserRef?>? admins,
      this.businessRef,
      this.certificate,
      this.config,
      final List<Contact?>? contacts,
      final List<Nv?>? externalRefs,
      @JsonKey(name: 'orgID') required this.orgId,
      required this.orgName,
      this.owner,
      this.publicKey,
      final List<OrgQuota?>? quotas,
      this.spaces,
      this.users,
      this.verified})
      : _addresses = addresses,
        _admins = admins,
        _contacts = contacts,
        _externalRefs = externalRefs,
        _quotas = quotas,
        super._();

  factory _$OrgImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrgImplFromJson(json);

  final List<Address?>? _addresses;
  @override
  List<Address?>? get addresses {
    final value = _addresses;
    if (value == null) return null;
    if (_addresses is EqualUnmodifiableListView) return _addresses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<UserRef?>? _admins;
  @override
  List<UserRef?>? get admins {
    final value = _admins;
    if (value == null) return null;
    if (_admins is EqualUnmodifiableListView) return _admins;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? businessRef;
  @override
  final String? certificate;
  @override
  final String? config;
  final List<Contact?>? _contacts;
  @override
  List<Contact?>? get contacts {
    final value = _contacts;
    if (value == null) return null;
    if (_contacts is EqualUnmodifiableListView) return _contacts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<Nv?>? _externalRefs;
  @override
  List<Nv?>? get externalRefs {
    final value = _externalRefs;
    if (value == null) return null;
    if (_externalRefs is EqualUnmodifiableListView) return _externalRefs;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey(name: 'orgID')
  final String orgId;
  @override
  final String orgName;
  @override
  final UserRef? owner;
  @override
  final String? publicKey;
  final List<OrgQuota?>? _quotas;
  @override
  List<OrgQuota?>? get quotas {
    final value = _quotas;
    if (value == null) return null;
    if (_quotas is EqualUnmodifiableListView) return _quotas;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final OrgSpacesConnection? spaces;
  @override
  final OrgUsersConnection? users;
  @override
  final bool? verified;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'Org(addresses: $addresses, admins: $admins, businessRef: $businessRef, certificate: $certificate, config: $config, contacts: $contacts, externalRefs: $externalRefs, orgId: $orgId, orgName: $orgName, owner: $owner, publicKey: $publicKey, quotas: $quotas, spaces: $spaces, users: $users, verified: $verified)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'Org'))
      ..add(DiagnosticsProperty('addresses', addresses))
      ..add(DiagnosticsProperty('admins', admins))
      ..add(DiagnosticsProperty('businessRef', businessRef))
      ..add(DiagnosticsProperty('certificate', certificate))
      ..add(DiagnosticsProperty('config', config))
      ..add(DiagnosticsProperty('contacts', contacts))
      ..add(DiagnosticsProperty('externalRefs', externalRefs))
      ..add(DiagnosticsProperty('orgId', orgId))
      ..add(DiagnosticsProperty('orgName', orgName))
      ..add(DiagnosticsProperty('owner', owner))
      ..add(DiagnosticsProperty('publicKey', publicKey))
      ..add(DiagnosticsProperty('quotas', quotas))
      ..add(DiagnosticsProperty('spaces', spaces))
      ..add(DiagnosticsProperty('users', users))
      ..add(DiagnosticsProperty('verified', verified));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrgImpl &&
            const DeepCollectionEquality()
                .equals(other._addresses, _addresses) &&
            const DeepCollectionEquality().equals(other._admins, _admins) &&
            (identical(other.businessRef, businessRef) ||
                other.businessRef == businessRef) &&
            (identical(other.certificate, certificate) ||
                other.certificate == certificate) &&
            (identical(other.config, config) || other.config == config) &&
            const DeepCollectionEquality().equals(other._contacts, _contacts) &&
            const DeepCollectionEquality()
                .equals(other._externalRefs, _externalRefs) &&
            (identical(other.orgId, orgId) || other.orgId == orgId) &&
            (identical(other.orgName, orgName) || other.orgName == orgName) &&
            (identical(other.owner, owner) || other.owner == owner) &&
            (identical(other.publicKey, publicKey) ||
                other.publicKey == publicKey) &&
            const DeepCollectionEquality().equals(other._quotas, _quotas) &&
            (identical(other.spaces, spaces) || other.spaces == spaces) &&
            (identical(other.users, users) || other.users == users) &&
            (identical(other.verified, verified) ||
                other.verified == verified));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_addresses),
      const DeepCollectionEquality().hash(_admins),
      businessRef,
      certificate,
      config,
      const DeepCollectionEquality().hash(_contacts),
      const DeepCollectionEquality().hash(_externalRefs),
      orgId,
      orgName,
      owner,
      publicKey,
      const DeepCollectionEquality().hash(_quotas),
      spaces,
      users,
      verified);

  /// Create a copy of Org
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrgImplCopyWith<_$OrgImpl> get copyWith =>
      __$$OrgImplCopyWithImpl<_$OrgImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrgImplToJson(
      this,
    );
  }
}

abstract class _Org extends Org {
  factory _Org(
      {final List<Address?>? addresses,
      final List<UserRef?>? admins,
      final String? businessRef,
      final String? certificate,
      final String? config,
      final List<Contact?>? contacts,
      final List<Nv?>? externalRefs,
      @JsonKey(name: 'orgID') required final String orgId,
      required final String orgName,
      final UserRef? owner,
      final String? publicKey,
      final List<OrgQuota?>? quotas,
      final OrgSpacesConnection? spaces,
      final OrgUsersConnection? users,
      final bool? verified}) = _$OrgImpl;
  _Org._() : super._();

  factory _Org.fromJson(Map<String, dynamic> json) = _$OrgImpl.fromJson;

  @override
  List<Address?>? get addresses;
  @override
  List<UserRef?>? get admins;
  @override
  String? get businessRef;
  @override
  String? get certificate;
  @override
  String? get config;
  @override
  List<Contact?>? get contacts;
  @override
  List<Nv?>? get externalRefs;
  @override
  @JsonKey(name: 'orgID')
  String get orgId;
  @override
  String get orgName;
  @override
  UserRef? get owner;
  @override
  String? get publicKey;
  @override
  List<OrgQuota?>? get quotas;
  @override
  OrgSpacesConnection? get spaces;
  @override
  OrgUsersConnection? get users;
  @override
  bool? get verified;

  /// Create a copy of Org
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrgImplCopyWith<_$OrgImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OrgIi _$OrgIiFromJson(Map<String, dynamic> json) {
  return _OrgIi.fromJson(json);
}

/// @nodoc
mixin _$OrgIi {
  String get idKey => throw _privateConstructorUsedError;
  OrgUser get orgUser => throw _privateConstructorUsedError;

  /// Serializes this OrgIi to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OrgIi
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrgIiCopyWith<OrgIi> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrgIiCopyWith<$Res> {
  factory $OrgIiCopyWith(OrgIi value, $Res Function(OrgIi) then) =
      _$OrgIiCopyWithImpl<$Res, OrgIi>;
  @useResult
  $Res call({String idKey, OrgUser orgUser});

  $OrgUserCopyWith<$Res> get orgUser;
}

/// @nodoc
class _$OrgIiCopyWithImpl<$Res, $Val extends OrgIi>
    implements $OrgIiCopyWith<$Res> {
  _$OrgIiCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrgIi
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? idKey = null,
    Object? orgUser = null,
  }) {
    return _then(_value.copyWith(
      idKey: null == idKey
          ? _value.idKey
          : idKey // ignore: cast_nullable_to_non_nullable
              as String,
      orgUser: null == orgUser
          ? _value.orgUser
          : orgUser // ignore: cast_nullable_to_non_nullable
              as OrgUser,
    ) as $Val);
  }

  /// Create a copy of OrgIi
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OrgUserCopyWith<$Res> get orgUser {
    return $OrgUserCopyWith<$Res>(_value.orgUser, (value) {
      return _then(_value.copyWith(orgUser: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$OrgIiImplCopyWith<$Res> implements $OrgIiCopyWith<$Res> {
  factory _$$OrgIiImplCopyWith(
          _$OrgIiImpl value, $Res Function(_$OrgIiImpl) then) =
      __$$OrgIiImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String idKey, OrgUser orgUser});

  @override
  $OrgUserCopyWith<$Res> get orgUser;
}

/// @nodoc
class __$$OrgIiImplCopyWithImpl<$Res>
    extends _$OrgIiCopyWithImpl<$Res, _$OrgIiImpl>
    implements _$$OrgIiImplCopyWith<$Res> {
  __$$OrgIiImplCopyWithImpl(
      _$OrgIiImpl _value, $Res Function(_$OrgIiImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrgIi
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? idKey = null,
    Object? orgUser = null,
  }) {
    return _then(_$OrgIiImpl(
      idKey: null == idKey
          ? _value.idKey
          : idKey // ignore: cast_nullable_to_non_nullable
              as String,
      orgUser: null == orgUser
          ? _value.orgUser
          : orgUser // ignore: cast_nullable_to_non_nullable
              as OrgUser,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OrgIiImpl extends _OrgIi with DiagnosticableTreeMixin {
  _$OrgIiImpl({required this.idKey, required this.orgUser}) : super._();

  factory _$OrgIiImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrgIiImplFromJson(json);

  @override
  final String idKey;
  @override
  final OrgUser orgUser;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'OrgIi(idKey: $idKey, orgUser: $orgUser)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'OrgIi'))
      ..add(DiagnosticsProperty('idKey', idKey))
      ..add(DiagnosticsProperty('orgUser', orgUser));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrgIiImpl &&
            (identical(other.idKey, idKey) || other.idKey == idKey) &&
            (identical(other.orgUser, orgUser) || other.orgUser == orgUser));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, idKey, orgUser);

  /// Create a copy of OrgIi
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrgIiImplCopyWith<_$OrgIiImpl> get copyWith =>
      __$$OrgIiImplCopyWithImpl<_$OrgIiImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrgIiImplToJson(
      this,
    );
  }
}

abstract class _OrgIi extends OrgIi {
  factory _OrgIi(
      {required final String idKey,
      required final OrgUser orgUser}) = _$OrgIiImpl;
  _OrgIi._() : super._();

  factory _OrgIi.fromJson(Map<String, dynamic> json) = _$OrgIiImpl.fromJson;

  @override
  String get idKey;
  @override
  OrgUser get orgUser;

  /// Create a copy of OrgIi
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrgIiImplCopyWith<_$OrgIiImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OrgQuota _$OrgQuotaFromJson(Map<String, dynamic> json) {
  return _OrgQuota.fromJson(json);
}

/// @nodoc
mixin _$OrgQuota {
  String? get description => throw _privateConstructorUsedError;
  int get limit => throw _privateConstructorUsedError;
  QuotaPeriod get period => throw _privateConstructorUsedError;
  String get quotaName => throw _privateConstructorUsedError;
  bool get softLimit => throw _privateConstructorUsedError;
  int get used => throw _privateConstructorUsedError;

  /// Serializes this OrgQuota to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OrgQuota
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrgQuotaCopyWith<OrgQuota> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrgQuotaCopyWith<$Res> {
  factory $OrgQuotaCopyWith(OrgQuota value, $Res Function(OrgQuota) then) =
      _$OrgQuotaCopyWithImpl<$Res, OrgQuota>;
  @useResult
  $Res call(
      {String? description,
      int limit,
      QuotaPeriod period,
      String quotaName,
      bool softLimit,
      int used});
}

/// @nodoc
class _$OrgQuotaCopyWithImpl<$Res, $Val extends OrgQuota>
    implements $OrgQuotaCopyWith<$Res> {
  _$OrgQuotaCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrgQuota
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? description = freezed,
    Object? limit = null,
    Object? period = null,
    Object? quotaName = null,
    Object? softLimit = null,
    Object? used = null,
  }) {
    return _then(_value.copyWith(
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      limit: null == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
      period: null == period
          ? _value.period
          : period // ignore: cast_nullable_to_non_nullable
              as QuotaPeriod,
      quotaName: null == quotaName
          ? _value.quotaName
          : quotaName // ignore: cast_nullable_to_non_nullable
              as String,
      softLimit: null == softLimit
          ? _value.softLimit
          : softLimit // ignore: cast_nullable_to_non_nullable
              as bool,
      used: null == used
          ? _value.used
          : used // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OrgQuotaImplCopyWith<$Res>
    implements $OrgQuotaCopyWith<$Res> {
  factory _$$OrgQuotaImplCopyWith(
          _$OrgQuotaImpl value, $Res Function(_$OrgQuotaImpl) then) =
      __$$OrgQuotaImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? description,
      int limit,
      QuotaPeriod period,
      String quotaName,
      bool softLimit,
      int used});
}

/// @nodoc
class __$$OrgQuotaImplCopyWithImpl<$Res>
    extends _$OrgQuotaCopyWithImpl<$Res, _$OrgQuotaImpl>
    implements _$$OrgQuotaImplCopyWith<$Res> {
  __$$OrgQuotaImplCopyWithImpl(
      _$OrgQuotaImpl _value, $Res Function(_$OrgQuotaImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrgQuota
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? description = freezed,
    Object? limit = null,
    Object? period = null,
    Object? quotaName = null,
    Object? softLimit = null,
    Object? used = null,
  }) {
    return _then(_$OrgQuotaImpl(
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      limit: null == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
      period: null == period
          ? _value.period
          : period // ignore: cast_nullable_to_non_nullable
              as QuotaPeriod,
      quotaName: null == quotaName
          ? _value.quotaName
          : quotaName // ignore: cast_nullable_to_non_nullable
              as String,
      softLimit: null == softLimit
          ? _value.softLimit
          : softLimit // ignore: cast_nullable_to_non_nullable
              as bool,
      used: null == used
          ? _value.used
          : used // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OrgQuotaImpl extends _OrgQuota with DiagnosticableTreeMixin {
  _$OrgQuotaImpl(
      {this.description,
      required this.limit,
      required this.period,
      required this.quotaName,
      required this.softLimit,
      required this.used})
      : super._();

  factory _$OrgQuotaImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrgQuotaImplFromJson(json);

  @override
  final String? description;
  @override
  final int limit;
  @override
  final QuotaPeriod period;
  @override
  final String quotaName;
  @override
  final bool softLimit;
  @override
  final int used;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'OrgQuota(description: $description, limit: $limit, period: $period, quotaName: $quotaName, softLimit: $softLimit, used: $used)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'OrgQuota'))
      ..add(DiagnosticsProperty('description', description))
      ..add(DiagnosticsProperty('limit', limit))
      ..add(DiagnosticsProperty('period', period))
      ..add(DiagnosticsProperty('quotaName', quotaName))
      ..add(DiagnosticsProperty('softLimit', softLimit))
      ..add(DiagnosticsProperty('used', used));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrgQuotaImpl &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.period, period) || other.period == period) &&
            (identical(other.quotaName, quotaName) ||
                other.quotaName == quotaName) &&
            (identical(other.softLimit, softLimit) ||
                other.softLimit == softLimit) &&
            (identical(other.used, used) || other.used == used));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, description, limit, period, quotaName, softLimit, used);

  /// Create a copy of OrgQuota
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrgQuotaImplCopyWith<_$OrgQuotaImpl> get copyWith =>
      __$$OrgQuotaImplCopyWithImpl<_$OrgQuotaImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrgQuotaImplToJson(
      this,
    );
  }
}

abstract class _OrgQuota extends OrgQuota {
  factory _OrgQuota(
      {final String? description,
      required final int limit,
      required final QuotaPeriod period,
      required final String quotaName,
      required final bool softLimit,
      required final int used}) = _$OrgQuotaImpl;
  _OrgQuota._() : super._();

  factory _OrgQuota.fromJson(Map<String, dynamic> json) =
      _$OrgQuotaImpl.fromJson;

  @override
  String? get description;
  @override
  int get limit;
  @override
  QuotaPeriod get period;
  @override
  String get quotaName;
  @override
  bool get softLimit;
  @override
  int get used;

  /// Create a copy of OrgQuota
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrgQuotaImplCopyWith<_$OrgQuotaImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OrgSpacesConnection _$OrgSpacesConnectionFromJson(Map<String, dynamic> json) {
  return _OrgSpacesConnection.fromJson(json);
}

/// @nodoc
mixin _$OrgSpacesConnection {
  List<OrgSpacesEdge?>? get edges => throw _privateConstructorUsedError;
  List<Space?>? get orgSpaces => throw _privateConstructorUsedError;
  PageInfo? get pageInfo => throw _privateConstructorUsedError;
  int? get totalCount => throw _privateConstructorUsedError;

  /// Serializes this OrgSpacesConnection to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OrgSpacesConnection
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrgSpacesConnectionCopyWith<OrgSpacesConnection> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrgSpacesConnectionCopyWith<$Res> {
  factory $OrgSpacesConnectionCopyWith(
          OrgSpacesConnection value, $Res Function(OrgSpacesConnection) then) =
      _$OrgSpacesConnectionCopyWithImpl<$Res, OrgSpacesConnection>;
  @useResult
  $Res call(
      {List<OrgSpacesEdge?>? edges,
      List<Space?>? orgSpaces,
      PageInfo? pageInfo,
      int? totalCount});

  $PageInfoCopyWith<$Res>? get pageInfo;
}

/// @nodoc
class _$OrgSpacesConnectionCopyWithImpl<$Res, $Val extends OrgSpacesConnection>
    implements $OrgSpacesConnectionCopyWith<$Res> {
  _$OrgSpacesConnectionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrgSpacesConnection
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? edges = freezed,
    Object? orgSpaces = freezed,
    Object? pageInfo = freezed,
    Object? totalCount = freezed,
  }) {
    return _then(_value.copyWith(
      edges: freezed == edges
          ? _value.edges
          : edges // ignore: cast_nullable_to_non_nullable
              as List<OrgSpacesEdge?>?,
      orgSpaces: freezed == orgSpaces
          ? _value.orgSpaces
          : orgSpaces // ignore: cast_nullable_to_non_nullable
              as List<Space?>?,
      pageInfo: freezed == pageInfo
          ? _value.pageInfo
          : pageInfo // ignore: cast_nullable_to_non_nullable
              as PageInfo?,
      totalCount: freezed == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }

  /// Create a copy of OrgSpacesConnection
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PageInfoCopyWith<$Res>? get pageInfo {
    if (_value.pageInfo == null) {
      return null;
    }

    return $PageInfoCopyWith<$Res>(_value.pageInfo!, (value) {
      return _then(_value.copyWith(pageInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$OrgSpacesConnectionImplCopyWith<$Res>
    implements $OrgSpacesConnectionCopyWith<$Res> {
  factory _$$OrgSpacesConnectionImplCopyWith(_$OrgSpacesConnectionImpl value,
          $Res Function(_$OrgSpacesConnectionImpl) then) =
      __$$OrgSpacesConnectionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<OrgSpacesEdge?>? edges,
      List<Space?>? orgSpaces,
      PageInfo? pageInfo,
      int? totalCount});

  @override
  $PageInfoCopyWith<$Res>? get pageInfo;
}

/// @nodoc
class __$$OrgSpacesConnectionImplCopyWithImpl<$Res>
    extends _$OrgSpacesConnectionCopyWithImpl<$Res, _$OrgSpacesConnectionImpl>
    implements _$$OrgSpacesConnectionImplCopyWith<$Res> {
  __$$OrgSpacesConnectionImplCopyWithImpl(_$OrgSpacesConnectionImpl _value,
      $Res Function(_$OrgSpacesConnectionImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrgSpacesConnection
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? edges = freezed,
    Object? orgSpaces = freezed,
    Object? pageInfo = freezed,
    Object? totalCount = freezed,
  }) {
    return _then(_$OrgSpacesConnectionImpl(
      edges: freezed == edges
          ? _value._edges
          : edges // ignore: cast_nullable_to_non_nullable
              as List<OrgSpacesEdge?>?,
      orgSpaces: freezed == orgSpaces
          ? _value._orgSpaces
          : orgSpaces // ignore: cast_nullable_to_non_nullable
              as List<Space?>?,
      pageInfo: freezed == pageInfo
          ? _value.pageInfo
          : pageInfo // ignore: cast_nullable_to_non_nullable
              as PageInfo?,
      totalCount: freezed == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OrgSpacesConnectionImpl extends _OrgSpacesConnection
    with DiagnosticableTreeMixin {
  _$OrgSpacesConnectionImpl(
      {final List<OrgSpacesEdge?>? edges,
      final List<Space?>? orgSpaces,
      this.pageInfo,
      this.totalCount})
      : _edges = edges,
        _orgSpaces = orgSpaces,
        super._();

  factory _$OrgSpacesConnectionImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrgSpacesConnectionImplFromJson(json);

  final List<OrgSpacesEdge?>? _edges;
  @override
  List<OrgSpacesEdge?>? get edges {
    final value = _edges;
    if (value == null) return null;
    if (_edges is EqualUnmodifiableListView) return _edges;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<Space?>? _orgSpaces;
  @override
  List<Space?>? get orgSpaces {
    final value = _orgSpaces;
    if (value == null) return null;
    if (_orgSpaces is EqualUnmodifiableListView) return _orgSpaces;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final PageInfo? pageInfo;
  @override
  final int? totalCount;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'OrgSpacesConnection(edges: $edges, orgSpaces: $orgSpaces, pageInfo: $pageInfo, totalCount: $totalCount)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'OrgSpacesConnection'))
      ..add(DiagnosticsProperty('edges', edges))
      ..add(DiagnosticsProperty('orgSpaces', orgSpaces))
      ..add(DiagnosticsProperty('pageInfo', pageInfo))
      ..add(DiagnosticsProperty('totalCount', totalCount));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrgSpacesConnectionImpl &&
            const DeepCollectionEquality().equals(other._edges, _edges) &&
            const DeepCollectionEquality()
                .equals(other._orgSpaces, _orgSpaces) &&
            (identical(other.pageInfo, pageInfo) ||
                other.pageInfo == pageInfo) &&
            (identical(other.totalCount, totalCount) ||
                other.totalCount == totalCount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_edges),
      const DeepCollectionEquality().hash(_orgSpaces),
      pageInfo,
      totalCount);

  /// Create a copy of OrgSpacesConnection
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrgSpacesConnectionImplCopyWith<_$OrgSpacesConnectionImpl> get copyWith =>
      __$$OrgSpacesConnectionImplCopyWithImpl<_$OrgSpacesConnectionImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrgSpacesConnectionImplToJson(
      this,
    );
  }
}

abstract class _OrgSpacesConnection extends OrgSpacesConnection {
  factory _OrgSpacesConnection(
      {final List<OrgSpacesEdge?>? edges,
      final List<Space?>? orgSpaces,
      final PageInfo? pageInfo,
      final int? totalCount}) = _$OrgSpacesConnectionImpl;
  _OrgSpacesConnection._() : super._();

  factory _OrgSpacesConnection.fromJson(Map<String, dynamic> json) =
      _$OrgSpacesConnectionImpl.fromJson;

  @override
  List<OrgSpacesEdge?>? get edges;
  @override
  List<Space?>? get orgSpaces;
  @override
  PageInfo? get pageInfo;
  @override
  int? get totalCount;

  /// Create a copy of OrgSpacesConnection
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrgSpacesConnectionImplCopyWith<_$OrgSpacesConnectionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OrgSpacesEdge _$OrgSpacesEdgeFromJson(Map<String, dynamic> json) {
  return _OrgSpacesEdge.fromJson(json);
}

/// @nodoc
mixin _$OrgSpacesEdge {
  Space get node => throw _privateConstructorUsedError;

  /// Serializes this OrgSpacesEdge to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OrgSpacesEdge
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrgSpacesEdgeCopyWith<OrgSpacesEdge> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrgSpacesEdgeCopyWith<$Res> {
  factory $OrgSpacesEdgeCopyWith(
          OrgSpacesEdge value, $Res Function(OrgSpacesEdge) then) =
      _$OrgSpacesEdgeCopyWithImpl<$Res, OrgSpacesEdge>;
  @useResult
  $Res call({Space node});

  $SpaceCopyWith<$Res> get node;
}

/// @nodoc
class _$OrgSpacesEdgeCopyWithImpl<$Res, $Val extends OrgSpacesEdge>
    implements $OrgSpacesEdgeCopyWith<$Res> {
  _$OrgSpacesEdgeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrgSpacesEdge
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? node = null,
  }) {
    return _then(_value.copyWith(
      node: null == node
          ? _value.node
          : node // ignore: cast_nullable_to_non_nullable
              as Space,
    ) as $Val);
  }

  /// Create a copy of OrgSpacesEdge
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SpaceCopyWith<$Res> get node {
    return $SpaceCopyWith<$Res>(_value.node, (value) {
      return _then(_value.copyWith(node: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$OrgSpacesEdgeImplCopyWith<$Res>
    implements $OrgSpacesEdgeCopyWith<$Res> {
  factory _$$OrgSpacesEdgeImplCopyWith(
          _$OrgSpacesEdgeImpl value, $Res Function(_$OrgSpacesEdgeImpl) then) =
      __$$OrgSpacesEdgeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({Space node});

  @override
  $SpaceCopyWith<$Res> get node;
}

/// @nodoc
class __$$OrgSpacesEdgeImplCopyWithImpl<$Res>
    extends _$OrgSpacesEdgeCopyWithImpl<$Res, _$OrgSpacesEdgeImpl>
    implements _$$OrgSpacesEdgeImplCopyWith<$Res> {
  __$$OrgSpacesEdgeImplCopyWithImpl(
      _$OrgSpacesEdgeImpl _value, $Res Function(_$OrgSpacesEdgeImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrgSpacesEdge
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? node = null,
  }) {
    return _then(_$OrgSpacesEdgeImpl(
      node: null == node
          ? _value.node
          : node // ignore: cast_nullable_to_non_nullable
              as Space,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OrgSpacesEdgeImpl extends _OrgSpacesEdge with DiagnosticableTreeMixin {
  _$OrgSpacesEdgeImpl({required this.node}) : super._();

  factory _$OrgSpacesEdgeImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrgSpacesEdgeImplFromJson(json);

  @override
  final Space node;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'OrgSpacesEdge(node: $node)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'OrgSpacesEdge'))
      ..add(DiagnosticsProperty('node', node));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrgSpacesEdgeImpl &&
            (identical(other.node, node) || other.node == node));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, node);

  /// Create a copy of OrgSpacesEdge
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrgSpacesEdgeImplCopyWith<_$OrgSpacesEdgeImpl> get copyWith =>
      __$$OrgSpacesEdgeImplCopyWithImpl<_$OrgSpacesEdgeImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrgSpacesEdgeImplToJson(
      this,
    );
  }
}

abstract class _OrgSpacesEdge extends OrgSpacesEdge {
  factory _OrgSpacesEdge({required final Space node}) = _$OrgSpacesEdgeImpl;
  _OrgSpacesEdge._() : super._();

  factory _OrgSpacesEdge.fromJson(Map<String, dynamic> json) =
      _$OrgSpacesEdgeImpl.fromJson;

  @override
  Space get node;

  /// Create a copy of OrgSpacesEdge
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrgSpacesEdgeImplCopyWith<_$OrgSpacesEdgeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OrgUser _$OrgUserFromJson(Map<String, dynamic> json) {
  return _OrgUser.fromJson(json);
}

/// @nodoc
mixin _$OrgUser {
  SpaceUsersConnection? get accessList => throw _privateConstructorUsedError;
  bool? get isAdmin => throw _privateConstructorUsedError;
  bool? get isOwner => throw _privateConstructorUsedError;
  Org? get org => throw _privateConstructorUsedError;
  UserAccessStatus? get status => throw _privateConstructorUsedError;
  User? get user => throw _privateConstructorUsedError;

  /// Serializes this OrgUser to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OrgUser
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrgUserCopyWith<OrgUser> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrgUserCopyWith<$Res> {
  factory $OrgUserCopyWith(OrgUser value, $Res Function(OrgUser) then) =
      _$OrgUserCopyWithImpl<$Res, OrgUser>;
  @useResult
  $Res call(
      {SpaceUsersConnection? accessList,
      bool? isAdmin,
      bool? isOwner,
      Org? org,
      UserAccessStatus? status,
      User? user});

  $SpaceUsersConnectionCopyWith<$Res>? get accessList;
  $OrgCopyWith<$Res>? get org;
  $UserCopyWith<$Res>? get user;
}

/// @nodoc
class _$OrgUserCopyWithImpl<$Res, $Val extends OrgUser>
    implements $OrgUserCopyWith<$Res> {
  _$OrgUserCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrgUser
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessList = freezed,
    Object? isAdmin = freezed,
    Object? isOwner = freezed,
    Object? org = freezed,
    Object? status = freezed,
    Object? user = freezed,
  }) {
    return _then(_value.copyWith(
      accessList: freezed == accessList
          ? _value.accessList
          : accessList // ignore: cast_nullable_to_non_nullable
              as SpaceUsersConnection?,
      isAdmin: freezed == isAdmin
          ? _value.isAdmin
          : isAdmin // ignore: cast_nullable_to_non_nullable
              as bool?,
      isOwner: freezed == isOwner
          ? _value.isOwner
          : isOwner // ignore: cast_nullable_to_non_nullable
              as bool?,
      org: freezed == org
          ? _value.org
          : org // ignore: cast_nullable_to_non_nullable
              as Org?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as UserAccessStatus?,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User?,
    ) as $Val);
  }

  /// Create a copy of OrgUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SpaceUsersConnectionCopyWith<$Res>? get accessList {
    if (_value.accessList == null) {
      return null;
    }

    return $SpaceUsersConnectionCopyWith<$Res>(_value.accessList!, (value) {
      return _then(_value.copyWith(accessList: value) as $Val);
    });
  }

  /// Create a copy of OrgUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OrgCopyWith<$Res>? get org {
    if (_value.org == null) {
      return null;
    }

    return $OrgCopyWith<$Res>(_value.org!, (value) {
      return _then(_value.copyWith(org: value) as $Val);
    });
  }

  /// Create a copy of OrgUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserCopyWith<$Res>? get user {
    if (_value.user == null) {
      return null;
    }

    return $UserCopyWith<$Res>(_value.user!, (value) {
      return _then(_value.copyWith(user: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$OrgUserImplCopyWith<$Res> implements $OrgUserCopyWith<$Res> {
  factory _$$OrgUserImplCopyWith(
          _$OrgUserImpl value, $Res Function(_$OrgUserImpl) then) =
      __$$OrgUserImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {SpaceUsersConnection? accessList,
      bool? isAdmin,
      bool? isOwner,
      Org? org,
      UserAccessStatus? status,
      User? user});

  @override
  $SpaceUsersConnectionCopyWith<$Res>? get accessList;
  @override
  $OrgCopyWith<$Res>? get org;
  @override
  $UserCopyWith<$Res>? get user;
}

/// @nodoc
class __$$OrgUserImplCopyWithImpl<$Res>
    extends _$OrgUserCopyWithImpl<$Res, _$OrgUserImpl>
    implements _$$OrgUserImplCopyWith<$Res> {
  __$$OrgUserImplCopyWithImpl(
      _$OrgUserImpl _value, $Res Function(_$OrgUserImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrgUser
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessList = freezed,
    Object? isAdmin = freezed,
    Object? isOwner = freezed,
    Object? org = freezed,
    Object? status = freezed,
    Object? user = freezed,
  }) {
    return _then(_$OrgUserImpl(
      accessList: freezed == accessList
          ? _value.accessList
          : accessList // ignore: cast_nullable_to_non_nullable
              as SpaceUsersConnection?,
      isAdmin: freezed == isAdmin
          ? _value.isAdmin
          : isAdmin // ignore: cast_nullable_to_non_nullable
              as bool?,
      isOwner: freezed == isOwner
          ? _value.isOwner
          : isOwner // ignore: cast_nullable_to_non_nullable
              as bool?,
      org: freezed == org
          ? _value.org
          : org // ignore: cast_nullable_to_non_nullable
              as Org?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as UserAccessStatus?,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OrgUserImpl extends _OrgUser with DiagnosticableTreeMixin {
  _$OrgUserImpl(
      {this.accessList,
      this.isAdmin,
      this.isOwner,
      this.org,
      this.status,
      this.user})
      : super._();

  factory _$OrgUserImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrgUserImplFromJson(json);

  @override
  final SpaceUsersConnection? accessList;
  @override
  final bool? isAdmin;
  @override
  final bool? isOwner;
  @override
  final Org? org;
  @override
  final UserAccessStatus? status;
  @override
  final User? user;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'OrgUser(accessList: $accessList, isAdmin: $isAdmin, isOwner: $isOwner, org: $org, status: $status, user: $user)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'OrgUser'))
      ..add(DiagnosticsProperty('accessList', accessList))
      ..add(DiagnosticsProperty('isAdmin', isAdmin))
      ..add(DiagnosticsProperty('isOwner', isOwner))
      ..add(DiagnosticsProperty('org', org))
      ..add(DiagnosticsProperty('status', status))
      ..add(DiagnosticsProperty('user', user));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrgUserImpl &&
            (identical(other.accessList, accessList) ||
                other.accessList == accessList) &&
            (identical(other.isAdmin, isAdmin) || other.isAdmin == isAdmin) &&
            (identical(other.isOwner, isOwner) || other.isOwner == isOwner) &&
            (identical(other.org, org) || other.org == org) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.user, user) || other.user == user));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, accessList, isAdmin, isOwner, org, status, user);

  /// Create a copy of OrgUser
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrgUserImplCopyWith<_$OrgUserImpl> get copyWith =>
      __$$OrgUserImplCopyWithImpl<_$OrgUserImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrgUserImplToJson(
      this,
    );
  }
}

abstract class _OrgUser extends OrgUser {
  factory _OrgUser(
      {final SpaceUsersConnection? accessList,
      final bool? isAdmin,
      final bool? isOwner,
      final Org? org,
      final UserAccessStatus? status,
      final User? user}) = _$OrgUserImpl;
  _OrgUser._() : super._();

  factory _OrgUser.fromJson(Map<String, dynamic> json) = _$OrgUserImpl.fromJson;

  @override
  SpaceUsersConnection? get accessList;
  @override
  bool? get isAdmin;
  @override
  bool? get isOwner;
  @override
  Org? get org;
  @override
  UserAccessStatus? get status;
  @override
  User? get user;

  /// Create a copy of OrgUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrgUserImplCopyWith<_$OrgUserImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OrgUsersConnection _$OrgUsersConnectionFromJson(Map<String, dynamic> json) {
  return _OrgUsersConnection.fromJson(json);
}

/// @nodoc
mixin _$OrgUsersConnection {
  List<OrgUsersEdge?>? get edges => throw _privateConstructorUsedError;
  List<OrgUser?>? get orgUsers => throw _privateConstructorUsedError;
  PageInfo? get pageInfo => throw _privateConstructorUsedError;
  int? get totalCount => throw _privateConstructorUsedError;

  /// Serializes this OrgUsersConnection to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OrgUsersConnection
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrgUsersConnectionCopyWith<OrgUsersConnection> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrgUsersConnectionCopyWith<$Res> {
  factory $OrgUsersConnectionCopyWith(
          OrgUsersConnection value, $Res Function(OrgUsersConnection) then) =
      _$OrgUsersConnectionCopyWithImpl<$Res, OrgUsersConnection>;
  @useResult
  $Res call(
      {List<OrgUsersEdge?>? edges,
      List<OrgUser?>? orgUsers,
      PageInfo? pageInfo,
      int? totalCount});

  $PageInfoCopyWith<$Res>? get pageInfo;
}

/// @nodoc
class _$OrgUsersConnectionCopyWithImpl<$Res, $Val extends OrgUsersConnection>
    implements $OrgUsersConnectionCopyWith<$Res> {
  _$OrgUsersConnectionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrgUsersConnection
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? edges = freezed,
    Object? orgUsers = freezed,
    Object? pageInfo = freezed,
    Object? totalCount = freezed,
  }) {
    return _then(_value.copyWith(
      edges: freezed == edges
          ? _value.edges
          : edges // ignore: cast_nullable_to_non_nullable
              as List<OrgUsersEdge?>?,
      orgUsers: freezed == orgUsers
          ? _value.orgUsers
          : orgUsers // ignore: cast_nullable_to_non_nullable
              as List<OrgUser?>?,
      pageInfo: freezed == pageInfo
          ? _value.pageInfo
          : pageInfo // ignore: cast_nullable_to_non_nullable
              as PageInfo?,
      totalCount: freezed == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }

  /// Create a copy of OrgUsersConnection
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PageInfoCopyWith<$Res>? get pageInfo {
    if (_value.pageInfo == null) {
      return null;
    }

    return $PageInfoCopyWith<$Res>(_value.pageInfo!, (value) {
      return _then(_value.copyWith(pageInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$OrgUsersConnectionImplCopyWith<$Res>
    implements $OrgUsersConnectionCopyWith<$Res> {
  factory _$$OrgUsersConnectionImplCopyWith(_$OrgUsersConnectionImpl value,
          $Res Function(_$OrgUsersConnectionImpl) then) =
      __$$OrgUsersConnectionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<OrgUsersEdge?>? edges,
      List<OrgUser?>? orgUsers,
      PageInfo? pageInfo,
      int? totalCount});

  @override
  $PageInfoCopyWith<$Res>? get pageInfo;
}

/// @nodoc
class __$$OrgUsersConnectionImplCopyWithImpl<$Res>
    extends _$OrgUsersConnectionCopyWithImpl<$Res, _$OrgUsersConnectionImpl>
    implements _$$OrgUsersConnectionImplCopyWith<$Res> {
  __$$OrgUsersConnectionImplCopyWithImpl(_$OrgUsersConnectionImpl _value,
      $Res Function(_$OrgUsersConnectionImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrgUsersConnection
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? edges = freezed,
    Object? orgUsers = freezed,
    Object? pageInfo = freezed,
    Object? totalCount = freezed,
  }) {
    return _then(_$OrgUsersConnectionImpl(
      edges: freezed == edges
          ? _value._edges
          : edges // ignore: cast_nullable_to_non_nullable
              as List<OrgUsersEdge?>?,
      orgUsers: freezed == orgUsers
          ? _value._orgUsers
          : orgUsers // ignore: cast_nullable_to_non_nullable
              as List<OrgUser?>?,
      pageInfo: freezed == pageInfo
          ? _value.pageInfo
          : pageInfo // ignore: cast_nullable_to_non_nullable
              as PageInfo?,
      totalCount: freezed == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OrgUsersConnectionImpl extends _OrgUsersConnection
    with DiagnosticableTreeMixin {
  _$OrgUsersConnectionImpl(
      {final List<OrgUsersEdge?>? edges,
      final List<OrgUser?>? orgUsers,
      this.pageInfo,
      this.totalCount})
      : _edges = edges,
        _orgUsers = orgUsers,
        super._();

  factory _$OrgUsersConnectionImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrgUsersConnectionImplFromJson(json);

  final List<OrgUsersEdge?>? _edges;
  @override
  List<OrgUsersEdge?>? get edges {
    final value = _edges;
    if (value == null) return null;
    if (_edges is EqualUnmodifiableListView) return _edges;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<OrgUser?>? _orgUsers;
  @override
  List<OrgUser?>? get orgUsers {
    final value = _orgUsers;
    if (value == null) return null;
    if (_orgUsers is EqualUnmodifiableListView) return _orgUsers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final PageInfo? pageInfo;
  @override
  final int? totalCount;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'OrgUsersConnection(edges: $edges, orgUsers: $orgUsers, pageInfo: $pageInfo, totalCount: $totalCount)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'OrgUsersConnection'))
      ..add(DiagnosticsProperty('edges', edges))
      ..add(DiagnosticsProperty('orgUsers', orgUsers))
      ..add(DiagnosticsProperty('pageInfo', pageInfo))
      ..add(DiagnosticsProperty('totalCount', totalCount));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrgUsersConnectionImpl &&
            const DeepCollectionEquality().equals(other._edges, _edges) &&
            const DeepCollectionEquality().equals(other._orgUsers, _orgUsers) &&
            (identical(other.pageInfo, pageInfo) ||
                other.pageInfo == pageInfo) &&
            (identical(other.totalCount, totalCount) ||
                other.totalCount == totalCount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_edges),
      const DeepCollectionEquality().hash(_orgUsers),
      pageInfo,
      totalCount);

  /// Create a copy of OrgUsersConnection
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrgUsersConnectionImplCopyWith<_$OrgUsersConnectionImpl> get copyWith =>
      __$$OrgUsersConnectionImplCopyWithImpl<_$OrgUsersConnectionImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrgUsersConnectionImplToJson(
      this,
    );
  }
}

abstract class _OrgUsersConnection extends OrgUsersConnection {
  factory _OrgUsersConnection(
      {final List<OrgUsersEdge?>? edges,
      final List<OrgUser?>? orgUsers,
      final PageInfo? pageInfo,
      final int? totalCount}) = _$OrgUsersConnectionImpl;
  _OrgUsersConnection._() : super._();

  factory _OrgUsersConnection.fromJson(Map<String, dynamic> json) =
      _$OrgUsersConnectionImpl.fromJson;

  @override
  List<OrgUsersEdge?>? get edges;
  @override
  List<OrgUser?>? get orgUsers;
  @override
  PageInfo? get pageInfo;
  @override
  int? get totalCount;

  /// Create a copy of OrgUsersConnection
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrgUsersConnectionImplCopyWith<_$OrgUsersConnectionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OrgUsersEdge _$OrgUsersEdgeFromJson(Map<String, dynamic> json) {
  return _OrgUsersEdge.fromJson(json);
}

/// @nodoc
mixin _$OrgUsersEdge {
  OrgUser get node => throw _privateConstructorUsedError;

  /// Serializes this OrgUsersEdge to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OrgUsersEdge
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrgUsersEdgeCopyWith<OrgUsersEdge> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrgUsersEdgeCopyWith<$Res> {
  factory $OrgUsersEdgeCopyWith(
          OrgUsersEdge value, $Res Function(OrgUsersEdge) then) =
      _$OrgUsersEdgeCopyWithImpl<$Res, OrgUsersEdge>;
  @useResult
  $Res call({OrgUser node});

  $OrgUserCopyWith<$Res> get node;
}

/// @nodoc
class _$OrgUsersEdgeCopyWithImpl<$Res, $Val extends OrgUsersEdge>
    implements $OrgUsersEdgeCopyWith<$Res> {
  _$OrgUsersEdgeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrgUsersEdge
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? node = null,
  }) {
    return _then(_value.copyWith(
      node: null == node
          ? _value.node
          : node // ignore: cast_nullable_to_non_nullable
              as OrgUser,
    ) as $Val);
  }

  /// Create a copy of OrgUsersEdge
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OrgUserCopyWith<$Res> get node {
    return $OrgUserCopyWith<$Res>(_value.node, (value) {
      return _then(_value.copyWith(node: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$OrgUsersEdgeImplCopyWith<$Res>
    implements $OrgUsersEdgeCopyWith<$Res> {
  factory _$$OrgUsersEdgeImplCopyWith(
          _$OrgUsersEdgeImpl value, $Res Function(_$OrgUsersEdgeImpl) then) =
      __$$OrgUsersEdgeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({OrgUser node});

  @override
  $OrgUserCopyWith<$Res> get node;
}

/// @nodoc
class __$$OrgUsersEdgeImplCopyWithImpl<$Res>
    extends _$OrgUsersEdgeCopyWithImpl<$Res, _$OrgUsersEdgeImpl>
    implements _$$OrgUsersEdgeImplCopyWith<$Res> {
  __$$OrgUsersEdgeImplCopyWithImpl(
      _$OrgUsersEdgeImpl _value, $Res Function(_$OrgUsersEdgeImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrgUsersEdge
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? node = null,
  }) {
    return _then(_$OrgUsersEdgeImpl(
      node: null == node
          ? _value.node
          : node // ignore: cast_nullable_to_non_nullable
              as OrgUser,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OrgUsersEdgeImpl extends _OrgUsersEdge with DiagnosticableTreeMixin {
  _$OrgUsersEdgeImpl({required this.node}) : super._();

  factory _$OrgUsersEdgeImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrgUsersEdgeImplFromJson(json);

  @override
  final OrgUser node;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'OrgUsersEdge(node: $node)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'OrgUsersEdge'))
      ..add(DiagnosticsProperty('node', node));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrgUsersEdgeImpl &&
            (identical(other.node, node) || other.node == node));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, node);

  /// Create a copy of OrgUsersEdge
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrgUsersEdgeImplCopyWith<_$OrgUsersEdgeImpl> get copyWith =>
      __$$OrgUsersEdgeImplCopyWithImpl<_$OrgUsersEdgeImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrgUsersEdgeImplToJson(
      this,
    );
  }
}

abstract class _OrgUsersEdge extends OrgUsersEdge {
  factory _OrgUsersEdge({required final OrgUser node}) = _$OrgUsersEdgeImpl;
  _OrgUsersEdge._() : super._();

  factory _OrgUsersEdge.fromJson(Map<String, dynamic> json) =
      _$OrgUsersEdgeImpl.fromJson;

  @override
  OrgUser get node;

  /// Create a copy of OrgUsersEdge
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrgUsersEdgeImplCopyWith<_$OrgUsersEdgeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PageInfo _$PageInfoFromJson(Map<String, dynamic> json) {
  return _PageInfo.fromJson(json);
}

/// @nodoc
mixin _$PageInfo {
  Cursor? get cursor => throw _privateConstructorUsedError;
  bool get hasNextPage => throw _privateConstructorUsedError;
  bool get hasPreviousPage => throw _privateConstructorUsedError;

  /// Serializes this PageInfo to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PageInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PageInfoCopyWith<PageInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PageInfoCopyWith<$Res> {
  factory $PageInfoCopyWith(PageInfo value, $Res Function(PageInfo) then) =
      _$PageInfoCopyWithImpl<$Res, PageInfo>;
  @useResult
  $Res call({Cursor? cursor, bool hasNextPage, bool hasPreviousPage});

  $CursorCopyWith<$Res>? get cursor;
}

/// @nodoc
class _$PageInfoCopyWithImpl<$Res, $Val extends PageInfo>
    implements $PageInfoCopyWith<$Res> {
  _$PageInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PageInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cursor = freezed,
    Object? hasNextPage = null,
    Object? hasPreviousPage = null,
  }) {
    return _then(_value.copyWith(
      cursor: freezed == cursor
          ? _value.cursor
          : cursor // ignore: cast_nullable_to_non_nullable
              as Cursor?,
      hasNextPage: null == hasNextPage
          ? _value.hasNextPage
          : hasNextPage // ignore: cast_nullable_to_non_nullable
              as bool,
      hasPreviousPage: null == hasPreviousPage
          ? _value.hasPreviousPage
          : hasPreviousPage // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  /// Create a copy of PageInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CursorCopyWith<$Res>? get cursor {
    if (_value.cursor == null) {
      return null;
    }

    return $CursorCopyWith<$Res>(_value.cursor!, (value) {
      return _then(_value.copyWith(cursor: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$PageInfoImplCopyWith<$Res>
    implements $PageInfoCopyWith<$Res> {
  factory _$$PageInfoImplCopyWith(
          _$PageInfoImpl value, $Res Function(_$PageInfoImpl) then) =
      __$$PageInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({Cursor? cursor, bool hasNextPage, bool hasPreviousPage});

  @override
  $CursorCopyWith<$Res>? get cursor;
}

/// @nodoc
class __$$PageInfoImplCopyWithImpl<$Res>
    extends _$PageInfoCopyWithImpl<$Res, _$PageInfoImpl>
    implements _$$PageInfoImplCopyWith<$Res> {
  __$$PageInfoImplCopyWithImpl(
      _$PageInfoImpl _value, $Res Function(_$PageInfoImpl) _then)
      : super(_value, _then);

  /// Create a copy of PageInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cursor = freezed,
    Object? hasNextPage = null,
    Object? hasPreviousPage = null,
  }) {
    return _then(_$PageInfoImpl(
      cursor: freezed == cursor
          ? _value.cursor
          : cursor // ignore: cast_nullable_to_non_nullable
              as Cursor?,
      hasNextPage: null == hasNextPage
          ? _value.hasNextPage
          : hasNextPage // ignore: cast_nullable_to_non_nullable
              as bool,
      hasPreviousPage: null == hasPreviousPage
          ? _value.hasPreviousPage
          : hasPreviousPage // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PageInfoImpl extends _PageInfo with DiagnosticableTreeMixin {
  _$PageInfoImpl(
      {this.cursor, required this.hasNextPage, required this.hasPreviousPage})
      : super._();

  factory _$PageInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$PageInfoImplFromJson(json);

  @override
  final Cursor? cursor;
  @override
  final bool hasNextPage;
  @override
  final bool hasPreviousPage;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'PageInfo(cursor: $cursor, hasNextPage: $hasNextPage, hasPreviousPage: $hasPreviousPage)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'PageInfo'))
      ..add(DiagnosticsProperty('cursor', cursor))
      ..add(DiagnosticsProperty('hasNextPage', hasNextPage))
      ..add(DiagnosticsProperty('hasPreviousPage', hasPreviousPage));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PageInfoImpl &&
            (identical(other.cursor, cursor) || other.cursor == cursor) &&
            (identical(other.hasNextPage, hasNextPage) ||
                other.hasNextPage == hasNextPage) &&
            (identical(other.hasPreviousPage, hasPreviousPage) ||
                other.hasPreviousPage == hasPreviousPage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, cursor, hasNextPage, hasPreviousPage);

  /// Create a copy of PageInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PageInfoImplCopyWith<_$PageInfoImpl> get copyWith =>
      __$$PageInfoImplCopyWithImpl<_$PageInfoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PageInfoImplToJson(
      this,
    );
  }
}

abstract class _PageInfo extends PageInfo {
  factory _PageInfo(
      {final Cursor? cursor,
      required final bool hasNextPage,
      required final bool hasPreviousPage}) = _$PageInfoImpl;
  _PageInfo._() : super._();

  factory _PageInfo.fromJson(Map<String, dynamic> json) =
      _$PageInfoImpl.fromJson;

  @override
  Cursor? get cursor;
  @override
  bool get hasNextPage;
  @override
  bool get hasPreviousPage;

  /// Create a copy of PageInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PageInfoImplCopyWith<_$PageInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PublishDataInput _$PublishDataInputFromJson(Map<String, dynamic> json) {
  return _PublishDataInput.fromJson(json);
}

/// @nodoc
mixin _$PublishDataInput {
  bool get compressed => throw _privateConstructorUsedError;
  set compressed(bool value) => throw _privateConstructorUsedError;
  String get payload => throw _privateConstructorUsedError;
  set payload(String value) => throw _privateConstructorUsedError;
  PublishDataType get type => throw _privateConstructorUsedError;
  set type(PublishDataType value) => throw _privateConstructorUsedError;

  /// Serializes this PublishDataInput to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PublishDataInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PublishDataInputCopyWith<PublishDataInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PublishDataInputCopyWith<$Res> {
  factory $PublishDataInputCopyWith(
          PublishDataInput value, $Res Function(PublishDataInput) then) =
      _$PublishDataInputCopyWithImpl<$Res, PublishDataInput>;
  @useResult
  $Res call({bool compressed, String payload, PublishDataType type});
}

/// @nodoc
class _$PublishDataInputCopyWithImpl<$Res, $Val extends PublishDataInput>
    implements $PublishDataInputCopyWith<$Res> {
  _$PublishDataInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PublishDataInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? compressed = null,
    Object? payload = null,
    Object? type = null,
  }) {
    return _then(_value.copyWith(
      compressed: null == compressed
          ? _value.compressed
          : compressed // ignore: cast_nullable_to_non_nullable
              as bool,
      payload: null == payload
          ? _value.payload
          : payload // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as PublishDataType,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PublishDataInputImplCopyWith<$Res>
    implements $PublishDataInputCopyWith<$Res> {
  factory _$$PublishDataInputImplCopyWith(_$PublishDataInputImpl value,
          $Res Function(_$PublishDataInputImpl) then) =
      __$$PublishDataInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool compressed, String payload, PublishDataType type});
}

/// @nodoc
class __$$PublishDataInputImplCopyWithImpl<$Res>
    extends _$PublishDataInputCopyWithImpl<$Res, _$PublishDataInputImpl>
    implements _$$PublishDataInputImplCopyWith<$Res> {
  __$$PublishDataInputImplCopyWithImpl(_$PublishDataInputImpl _value,
      $Res Function(_$PublishDataInputImpl) _then)
      : super(_value, _then);

  /// Create a copy of PublishDataInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? compressed = null,
    Object? payload = null,
    Object? type = null,
  }) {
    return _then(_$PublishDataInputImpl(
      compressed: null == compressed
          ? _value.compressed
          : compressed // ignore: cast_nullable_to_non_nullable
              as bool,
      payload: null == payload
          ? _value.payload
          : payload // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as PublishDataType,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PublishDataInputImpl extends _PublishDataInput
    with DiagnosticableTreeMixin {
  _$PublishDataInputImpl(
      {required this.compressed, required this.payload, required this.type})
      : super._();

  factory _$PublishDataInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$PublishDataInputImplFromJson(json);

  @override
  bool compressed;
  @override
  String payload;
  @override
  PublishDataType type;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'PublishDataInput(compressed: $compressed, payload: $payload, type: $type)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'PublishDataInput'))
      ..add(DiagnosticsProperty('compressed', compressed))
      ..add(DiagnosticsProperty('payload', payload))
      ..add(DiagnosticsProperty('type', type));
  }

  /// Create a copy of PublishDataInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PublishDataInputImplCopyWith<_$PublishDataInputImpl> get copyWith =>
      __$$PublishDataInputImplCopyWithImpl<_$PublishDataInputImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PublishDataInputImplToJson(
      this,
    );
  }
}

abstract class _PublishDataInput extends PublishDataInput {
  factory _PublishDataInput(
      {required bool compressed,
      required String payload,
      required PublishDataType type}) = _$PublishDataInputImpl;
  _PublishDataInput._() : super._();

  factory _PublishDataInput.fromJson(Map<String, dynamic> json) =
      _$PublishDataInputImpl.fromJson;

  @override
  bool get compressed;
  set compressed(bool value);
  @override
  String get payload;
  set payload(String value);
  @override
  PublishDataType get type;
  set type(PublishDataType value);

  /// Create a copy of PublishDataInput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PublishDataInputImplCopyWith<_$PublishDataInputImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PublishResult _$PublishResultFromJson(Map<String, dynamic> json) {
  return _PublishResult.fromJson(json);
}

/// @nodoc
mixin _$PublishResult {
  String? get error => throw _privateConstructorUsedError;
  bool get success => throw _privateConstructorUsedError;

  /// Serializes this PublishResult to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PublishResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PublishResultCopyWith<PublishResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PublishResultCopyWith<$Res> {
  factory $PublishResultCopyWith(
          PublishResult value, $Res Function(PublishResult) then) =
      _$PublishResultCopyWithImpl<$Res, PublishResult>;
  @useResult
  $Res call({String? error, bool success});
}

/// @nodoc
class _$PublishResultCopyWithImpl<$Res, $Val extends PublishResult>
    implements $PublishResultCopyWith<$Res> {
  _$PublishResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PublishResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = freezed,
    Object? success = null,
  }) {
    return _then(_value.copyWith(
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PublishResultImplCopyWith<$Res>
    implements $PublishResultCopyWith<$Res> {
  factory _$$PublishResultImplCopyWith(
          _$PublishResultImpl value, $Res Function(_$PublishResultImpl) then) =
      __$$PublishResultImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? error, bool success});
}

/// @nodoc
class __$$PublishResultImplCopyWithImpl<$Res>
    extends _$PublishResultCopyWithImpl<$Res, _$PublishResultImpl>
    implements _$$PublishResultImplCopyWith<$Res> {
  __$$PublishResultImplCopyWithImpl(
      _$PublishResultImpl _value, $Res Function(_$PublishResultImpl) _then)
      : super(_value, _then);

  /// Create a copy of PublishResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = freezed,
    Object? success = null,
  }) {
    return _then(_$PublishResultImpl(
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PublishResultImpl extends _PublishResult with DiagnosticableTreeMixin {
  _$PublishResultImpl({this.error, required this.success}) : super._();

  factory _$PublishResultImpl.fromJson(Map<String, dynamic> json) =>
      _$$PublishResultImplFromJson(json);

  @override
  final String? error;
  @override
  final bool success;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'PublishResult(error: $error, success: $success)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'PublishResult'))
      ..add(DiagnosticsProperty('error', error))
      ..add(DiagnosticsProperty('success', success));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PublishResultImpl &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.success, success) || other.success == success));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, error, success);

  /// Create a copy of PublishResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PublishResultImplCopyWith<_$PublishResultImpl> get copyWith =>
      __$$PublishResultImplCopyWithImpl<_$PublishResultImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PublishResultImplToJson(
      this,
    );
  }
}

abstract class _PublishResult extends PublishResult {
  factory _PublishResult({final String? error, required final bool success}) =
      _$PublishResultImpl;
  _PublishResult._() : super._();

  factory _PublishResult.fromJson(Map<String, dynamic> json) =
      _$PublishResultImpl.fromJson;

  @override
  String? get error;
  @override
  bool get success;

  /// Create a copy of PublishResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PublishResultImplCopyWith<_$PublishResultImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Space _$SpaceFromJson(Map<String, dynamic> json) {
  return _Space.fromJson(json);
}

/// @nodoc
mixin _$Space {
  List<UserRef?>? get admins => throw _privateConstructorUsedError;
  SpaceAppsConnection? get apps => throw _privateConstructorUsedError;
  String? get certificate => throw _privateConstructorUsedError;
  String? get cookbook => throw _privateConstructorUsedError;
  String? get domainName => throw _privateConstructorUsedError;
  String? get fqdn => throw _privateConstructorUsedError;
  String? get iaas => throw _privateConstructorUsedError;
  String? get ipAddress => throw _privateConstructorUsedError;
  bool? get isEgressNode => throw _privateConstructorUsedError;
  int? get lastSeen => throw _privateConstructorUsedError;
  @JsonKey(name: 'localCARoot')
  String? get localCaRoot => throw _privateConstructorUsedError;
  int? get meshNetworkBitmask => throw _privateConstructorUsedError;
  IPType? get meshNetworkType => throw _privateConstructorUsedError;
  Org? get org => throw _privateConstructorUsedError;
  UserRef? get owner => throw _privateConstructorUsedError;
  int? get port => throw _privateConstructorUsedError;
  String? get publicKey => throw _privateConstructorUsedError;
  String? get recipe => throw _privateConstructorUsedError;
  String? get region => throw _privateConstructorUsedError;
  String? get settings => throw _privateConstructorUsedError;
  @JsonKey(name: 'spaceID')
  String get spaceId => throw _privateConstructorUsedError;
  String? get spaceName => throw _privateConstructorUsedError;
  SpaceStatus? get status => throw _privateConstructorUsedError;
  SpaceUsersConnection? get users => throw _privateConstructorUsedError;
  String? get version => throw _privateConstructorUsedError;
  String? get vpnType => throw _privateConstructorUsedError;

  /// Serializes this Space to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Space
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SpaceCopyWith<Space> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SpaceCopyWith<$Res> {
  factory $SpaceCopyWith(Space value, $Res Function(Space) then) =
      _$SpaceCopyWithImpl<$Res, Space>;
  @useResult
  $Res call(
      {List<UserRef?>? admins,
      SpaceAppsConnection? apps,
      String? certificate,
      String? cookbook,
      String? domainName,
      String? fqdn,
      String? iaas,
      String? ipAddress,
      bool? isEgressNode,
      int? lastSeen,
      @JsonKey(name: 'localCARoot') String? localCaRoot,
      int? meshNetworkBitmask,
      IPType? meshNetworkType,
      Org? org,
      UserRef? owner,
      int? port,
      String? publicKey,
      String? recipe,
      String? region,
      String? settings,
      @JsonKey(name: 'spaceID') String spaceId,
      String? spaceName,
      SpaceStatus? status,
      SpaceUsersConnection? users,
      String? version,
      String? vpnType});

  $SpaceAppsConnectionCopyWith<$Res>? get apps;
  $OrgCopyWith<$Res>? get org;
  $UserRefCopyWith<$Res>? get owner;
  $SpaceUsersConnectionCopyWith<$Res>? get users;
}

/// @nodoc
class _$SpaceCopyWithImpl<$Res, $Val extends Space>
    implements $SpaceCopyWith<$Res> {
  _$SpaceCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Space
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? admins = freezed,
    Object? apps = freezed,
    Object? certificate = freezed,
    Object? cookbook = freezed,
    Object? domainName = freezed,
    Object? fqdn = freezed,
    Object? iaas = freezed,
    Object? ipAddress = freezed,
    Object? isEgressNode = freezed,
    Object? lastSeen = freezed,
    Object? localCaRoot = freezed,
    Object? meshNetworkBitmask = freezed,
    Object? meshNetworkType = freezed,
    Object? org = freezed,
    Object? owner = freezed,
    Object? port = freezed,
    Object? publicKey = freezed,
    Object? recipe = freezed,
    Object? region = freezed,
    Object? settings = freezed,
    Object? spaceId = null,
    Object? spaceName = freezed,
    Object? status = freezed,
    Object? users = freezed,
    Object? version = freezed,
    Object? vpnType = freezed,
  }) {
    return _then(_value.copyWith(
      admins: freezed == admins
          ? _value.admins
          : admins // ignore: cast_nullable_to_non_nullable
              as List<UserRef?>?,
      apps: freezed == apps
          ? _value.apps
          : apps // ignore: cast_nullable_to_non_nullable
              as SpaceAppsConnection?,
      certificate: freezed == certificate
          ? _value.certificate
          : certificate // ignore: cast_nullable_to_non_nullable
              as String?,
      cookbook: freezed == cookbook
          ? _value.cookbook
          : cookbook // ignore: cast_nullable_to_non_nullable
              as String?,
      domainName: freezed == domainName
          ? _value.domainName
          : domainName // ignore: cast_nullable_to_non_nullable
              as String?,
      fqdn: freezed == fqdn
          ? _value.fqdn
          : fqdn // ignore: cast_nullable_to_non_nullable
              as String?,
      iaas: freezed == iaas
          ? _value.iaas
          : iaas // ignore: cast_nullable_to_non_nullable
              as String?,
      ipAddress: freezed == ipAddress
          ? _value.ipAddress
          : ipAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      isEgressNode: freezed == isEgressNode
          ? _value.isEgressNode
          : isEgressNode // ignore: cast_nullable_to_non_nullable
              as bool?,
      lastSeen: freezed == lastSeen
          ? _value.lastSeen
          : lastSeen // ignore: cast_nullable_to_non_nullable
              as int?,
      localCaRoot: freezed == localCaRoot
          ? _value.localCaRoot
          : localCaRoot // ignore: cast_nullable_to_non_nullable
              as String?,
      meshNetworkBitmask: freezed == meshNetworkBitmask
          ? _value.meshNetworkBitmask
          : meshNetworkBitmask // ignore: cast_nullable_to_non_nullable
              as int?,
      meshNetworkType: freezed == meshNetworkType
          ? _value.meshNetworkType
          : meshNetworkType // ignore: cast_nullable_to_non_nullable
              as IPType?,
      org: freezed == org
          ? _value.org
          : org // ignore: cast_nullable_to_non_nullable
              as Org?,
      owner: freezed == owner
          ? _value.owner
          : owner // ignore: cast_nullable_to_non_nullable
              as UserRef?,
      port: freezed == port
          ? _value.port
          : port // ignore: cast_nullable_to_non_nullable
              as int?,
      publicKey: freezed == publicKey
          ? _value.publicKey
          : publicKey // ignore: cast_nullable_to_non_nullable
              as String?,
      recipe: freezed == recipe
          ? _value.recipe
          : recipe // ignore: cast_nullable_to_non_nullable
              as String?,
      region: freezed == region
          ? _value.region
          : region // ignore: cast_nullable_to_non_nullable
              as String?,
      settings: freezed == settings
          ? _value.settings
          : settings // ignore: cast_nullable_to_non_nullable
              as String?,
      spaceId: null == spaceId
          ? _value.spaceId
          : spaceId // ignore: cast_nullable_to_non_nullable
              as String,
      spaceName: freezed == spaceName
          ? _value.spaceName
          : spaceName // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as SpaceStatus?,
      users: freezed == users
          ? _value.users
          : users // ignore: cast_nullable_to_non_nullable
              as SpaceUsersConnection?,
      version: freezed == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as String?,
      vpnType: freezed == vpnType
          ? _value.vpnType
          : vpnType // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of Space
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SpaceAppsConnectionCopyWith<$Res>? get apps {
    if (_value.apps == null) {
      return null;
    }

    return $SpaceAppsConnectionCopyWith<$Res>(_value.apps!, (value) {
      return _then(_value.copyWith(apps: value) as $Val);
    });
  }

  /// Create a copy of Space
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OrgCopyWith<$Res>? get org {
    if (_value.org == null) {
      return null;
    }

    return $OrgCopyWith<$Res>(_value.org!, (value) {
      return _then(_value.copyWith(org: value) as $Val);
    });
  }

  /// Create a copy of Space
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserRefCopyWith<$Res>? get owner {
    if (_value.owner == null) {
      return null;
    }

    return $UserRefCopyWith<$Res>(_value.owner!, (value) {
      return _then(_value.copyWith(owner: value) as $Val);
    });
  }

  /// Create a copy of Space
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SpaceUsersConnectionCopyWith<$Res>? get users {
    if (_value.users == null) {
      return null;
    }

    return $SpaceUsersConnectionCopyWith<$Res>(_value.users!, (value) {
      return _then(_value.copyWith(users: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SpaceImplCopyWith<$Res> implements $SpaceCopyWith<$Res> {
  factory _$$SpaceImplCopyWith(
          _$SpaceImpl value, $Res Function(_$SpaceImpl) then) =
      __$$SpaceImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<UserRef?>? admins,
      SpaceAppsConnection? apps,
      String? certificate,
      String? cookbook,
      String? domainName,
      String? fqdn,
      String? iaas,
      String? ipAddress,
      bool? isEgressNode,
      int? lastSeen,
      @JsonKey(name: 'localCARoot') String? localCaRoot,
      int? meshNetworkBitmask,
      IPType? meshNetworkType,
      Org? org,
      UserRef? owner,
      int? port,
      String? publicKey,
      String? recipe,
      String? region,
      String? settings,
      @JsonKey(name: 'spaceID') String spaceId,
      String? spaceName,
      SpaceStatus? status,
      SpaceUsersConnection? users,
      String? version,
      String? vpnType});

  @override
  $SpaceAppsConnectionCopyWith<$Res>? get apps;
  @override
  $OrgCopyWith<$Res>? get org;
  @override
  $UserRefCopyWith<$Res>? get owner;
  @override
  $SpaceUsersConnectionCopyWith<$Res>? get users;
}

/// @nodoc
class __$$SpaceImplCopyWithImpl<$Res>
    extends _$SpaceCopyWithImpl<$Res, _$SpaceImpl>
    implements _$$SpaceImplCopyWith<$Res> {
  __$$SpaceImplCopyWithImpl(
      _$SpaceImpl _value, $Res Function(_$SpaceImpl) _then)
      : super(_value, _then);

  /// Create a copy of Space
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? admins = freezed,
    Object? apps = freezed,
    Object? certificate = freezed,
    Object? cookbook = freezed,
    Object? domainName = freezed,
    Object? fqdn = freezed,
    Object? iaas = freezed,
    Object? ipAddress = freezed,
    Object? isEgressNode = freezed,
    Object? lastSeen = freezed,
    Object? localCaRoot = freezed,
    Object? meshNetworkBitmask = freezed,
    Object? meshNetworkType = freezed,
    Object? org = freezed,
    Object? owner = freezed,
    Object? port = freezed,
    Object? publicKey = freezed,
    Object? recipe = freezed,
    Object? region = freezed,
    Object? settings = freezed,
    Object? spaceId = null,
    Object? spaceName = freezed,
    Object? status = freezed,
    Object? users = freezed,
    Object? version = freezed,
    Object? vpnType = freezed,
  }) {
    return _then(_$SpaceImpl(
      admins: freezed == admins
          ? _value._admins
          : admins // ignore: cast_nullable_to_non_nullable
              as List<UserRef?>?,
      apps: freezed == apps
          ? _value.apps
          : apps // ignore: cast_nullable_to_non_nullable
              as SpaceAppsConnection?,
      certificate: freezed == certificate
          ? _value.certificate
          : certificate // ignore: cast_nullable_to_non_nullable
              as String?,
      cookbook: freezed == cookbook
          ? _value.cookbook
          : cookbook // ignore: cast_nullable_to_non_nullable
              as String?,
      domainName: freezed == domainName
          ? _value.domainName
          : domainName // ignore: cast_nullable_to_non_nullable
              as String?,
      fqdn: freezed == fqdn
          ? _value.fqdn
          : fqdn // ignore: cast_nullable_to_non_nullable
              as String?,
      iaas: freezed == iaas
          ? _value.iaas
          : iaas // ignore: cast_nullable_to_non_nullable
              as String?,
      ipAddress: freezed == ipAddress
          ? _value.ipAddress
          : ipAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      isEgressNode: freezed == isEgressNode
          ? _value.isEgressNode
          : isEgressNode // ignore: cast_nullable_to_non_nullable
              as bool?,
      lastSeen: freezed == lastSeen
          ? _value.lastSeen
          : lastSeen // ignore: cast_nullable_to_non_nullable
              as int?,
      localCaRoot: freezed == localCaRoot
          ? _value.localCaRoot
          : localCaRoot // ignore: cast_nullable_to_non_nullable
              as String?,
      meshNetworkBitmask: freezed == meshNetworkBitmask
          ? _value.meshNetworkBitmask
          : meshNetworkBitmask // ignore: cast_nullable_to_non_nullable
              as int?,
      meshNetworkType: freezed == meshNetworkType
          ? _value.meshNetworkType
          : meshNetworkType // ignore: cast_nullable_to_non_nullable
              as IPType?,
      org: freezed == org
          ? _value.org
          : org // ignore: cast_nullable_to_non_nullable
              as Org?,
      owner: freezed == owner
          ? _value.owner
          : owner // ignore: cast_nullable_to_non_nullable
              as UserRef?,
      port: freezed == port
          ? _value.port
          : port // ignore: cast_nullable_to_non_nullable
              as int?,
      publicKey: freezed == publicKey
          ? _value.publicKey
          : publicKey // ignore: cast_nullable_to_non_nullable
              as String?,
      recipe: freezed == recipe
          ? _value.recipe
          : recipe // ignore: cast_nullable_to_non_nullable
              as String?,
      region: freezed == region
          ? _value.region
          : region // ignore: cast_nullable_to_non_nullable
              as String?,
      settings: freezed == settings
          ? _value.settings
          : settings // ignore: cast_nullable_to_non_nullable
              as String?,
      spaceId: null == spaceId
          ? _value.spaceId
          : spaceId // ignore: cast_nullable_to_non_nullable
              as String,
      spaceName: freezed == spaceName
          ? _value.spaceName
          : spaceName // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as SpaceStatus?,
      users: freezed == users
          ? _value.users
          : users // ignore: cast_nullable_to_non_nullable
              as SpaceUsersConnection?,
      version: freezed == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as String?,
      vpnType: freezed == vpnType
          ? _value.vpnType
          : vpnType // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SpaceImpl extends _Space with DiagnosticableTreeMixin {
  _$SpaceImpl(
      {final List<UserRef?>? admins,
      this.apps,
      this.certificate,
      this.cookbook,
      this.domainName,
      this.fqdn,
      this.iaas,
      this.ipAddress,
      this.isEgressNode,
      this.lastSeen,
      @JsonKey(name: 'localCARoot') this.localCaRoot,
      this.meshNetworkBitmask,
      this.meshNetworkType,
      this.org,
      this.owner,
      this.port,
      this.publicKey,
      this.recipe,
      this.region,
      this.settings,
      @JsonKey(name: 'spaceID') required this.spaceId,
      this.spaceName,
      this.status,
      this.users,
      this.version,
      this.vpnType})
      : _admins = admins,
        super._();

  factory _$SpaceImpl.fromJson(Map<String, dynamic> json) =>
      _$$SpaceImplFromJson(json);

  final List<UserRef?>? _admins;
  @override
  List<UserRef?>? get admins {
    final value = _admins;
    if (value == null) return null;
    if (_admins is EqualUnmodifiableListView) return _admins;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final SpaceAppsConnection? apps;
  @override
  final String? certificate;
  @override
  final String? cookbook;
  @override
  final String? domainName;
  @override
  final String? fqdn;
  @override
  final String? iaas;
  @override
  final String? ipAddress;
  @override
  final bool? isEgressNode;
  @override
  final int? lastSeen;
  @override
  @JsonKey(name: 'localCARoot')
  final String? localCaRoot;
  @override
  final int? meshNetworkBitmask;
  @override
  final IPType? meshNetworkType;
  @override
  final Org? org;
  @override
  final UserRef? owner;
  @override
  final int? port;
  @override
  final String? publicKey;
  @override
  final String? recipe;
  @override
  final String? region;
  @override
  final String? settings;
  @override
  @JsonKey(name: 'spaceID')
  final String spaceId;
  @override
  final String? spaceName;
  @override
  final SpaceStatus? status;
  @override
  final SpaceUsersConnection? users;
  @override
  final String? version;
  @override
  final String? vpnType;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'Space(admins: $admins, apps: $apps, certificate: $certificate, cookbook: $cookbook, domainName: $domainName, fqdn: $fqdn, iaas: $iaas, ipAddress: $ipAddress, isEgressNode: $isEgressNode, lastSeen: $lastSeen, localCaRoot: $localCaRoot, meshNetworkBitmask: $meshNetworkBitmask, meshNetworkType: $meshNetworkType, org: $org, owner: $owner, port: $port, publicKey: $publicKey, recipe: $recipe, region: $region, settings: $settings, spaceId: $spaceId, spaceName: $spaceName, status: $status, users: $users, version: $version, vpnType: $vpnType)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'Space'))
      ..add(DiagnosticsProperty('admins', admins))
      ..add(DiagnosticsProperty('apps', apps))
      ..add(DiagnosticsProperty('certificate', certificate))
      ..add(DiagnosticsProperty('cookbook', cookbook))
      ..add(DiagnosticsProperty('domainName', domainName))
      ..add(DiagnosticsProperty('fqdn', fqdn))
      ..add(DiagnosticsProperty('iaas', iaas))
      ..add(DiagnosticsProperty('ipAddress', ipAddress))
      ..add(DiagnosticsProperty('isEgressNode', isEgressNode))
      ..add(DiagnosticsProperty('lastSeen', lastSeen))
      ..add(DiagnosticsProperty('localCaRoot', localCaRoot))
      ..add(DiagnosticsProperty('meshNetworkBitmask', meshNetworkBitmask))
      ..add(DiagnosticsProperty('meshNetworkType', meshNetworkType))
      ..add(DiagnosticsProperty('org', org))
      ..add(DiagnosticsProperty('owner', owner))
      ..add(DiagnosticsProperty('port', port))
      ..add(DiagnosticsProperty('publicKey', publicKey))
      ..add(DiagnosticsProperty('recipe', recipe))
      ..add(DiagnosticsProperty('region', region))
      ..add(DiagnosticsProperty('settings', settings))
      ..add(DiagnosticsProperty('spaceId', spaceId))
      ..add(DiagnosticsProperty('spaceName', spaceName))
      ..add(DiagnosticsProperty('status', status))
      ..add(DiagnosticsProperty('users', users))
      ..add(DiagnosticsProperty('version', version))
      ..add(DiagnosticsProperty('vpnType', vpnType));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SpaceImpl &&
            const DeepCollectionEquality().equals(other._admins, _admins) &&
            (identical(other.apps, apps) || other.apps == apps) &&
            (identical(other.certificate, certificate) ||
                other.certificate == certificate) &&
            (identical(other.cookbook, cookbook) ||
                other.cookbook == cookbook) &&
            (identical(other.domainName, domainName) ||
                other.domainName == domainName) &&
            (identical(other.fqdn, fqdn) || other.fqdn == fqdn) &&
            (identical(other.iaas, iaas) || other.iaas == iaas) &&
            (identical(other.ipAddress, ipAddress) ||
                other.ipAddress == ipAddress) &&
            (identical(other.isEgressNode, isEgressNode) ||
                other.isEgressNode == isEgressNode) &&
            (identical(other.lastSeen, lastSeen) ||
                other.lastSeen == lastSeen) &&
            (identical(other.localCaRoot, localCaRoot) ||
                other.localCaRoot == localCaRoot) &&
            (identical(other.meshNetworkBitmask, meshNetworkBitmask) ||
                other.meshNetworkBitmask == meshNetworkBitmask) &&
            (identical(other.meshNetworkType, meshNetworkType) ||
                other.meshNetworkType == meshNetworkType) &&
            (identical(other.org, org) || other.org == org) &&
            (identical(other.owner, owner) || other.owner == owner) &&
            (identical(other.port, port) || other.port == port) &&
            (identical(other.publicKey, publicKey) ||
                other.publicKey == publicKey) &&
            (identical(other.recipe, recipe) || other.recipe == recipe) &&
            (identical(other.region, region) || other.region == region) &&
            (identical(other.settings, settings) ||
                other.settings == settings) &&
            (identical(other.spaceId, spaceId) || other.spaceId == spaceId) &&
            (identical(other.spaceName, spaceName) ||
                other.spaceName == spaceName) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.users, users) || other.users == users) &&
            (identical(other.version, version) || other.version == version) &&
            (identical(other.vpnType, vpnType) || other.vpnType == vpnType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        const DeepCollectionEquality().hash(_admins),
        apps,
        certificate,
        cookbook,
        domainName,
        fqdn,
        iaas,
        ipAddress,
        isEgressNode,
        lastSeen,
        localCaRoot,
        meshNetworkBitmask,
        meshNetworkType,
        org,
        owner,
        port,
        publicKey,
        recipe,
        region,
        settings,
        spaceId,
        spaceName,
        status,
        users,
        version,
        vpnType
      ]);

  /// Create a copy of Space
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SpaceImplCopyWith<_$SpaceImpl> get copyWith =>
      __$$SpaceImplCopyWithImpl<_$SpaceImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SpaceImplToJson(
      this,
    );
  }
}

abstract class _Space extends Space {
  factory _Space(
      {final List<UserRef?>? admins,
      final SpaceAppsConnection? apps,
      final String? certificate,
      final String? cookbook,
      final String? domainName,
      final String? fqdn,
      final String? iaas,
      final String? ipAddress,
      final bool? isEgressNode,
      final int? lastSeen,
      @JsonKey(name: 'localCARoot') final String? localCaRoot,
      final int? meshNetworkBitmask,
      final IPType? meshNetworkType,
      final Org? org,
      final UserRef? owner,
      final int? port,
      final String? publicKey,
      final String? recipe,
      final String? region,
      final String? settings,
      @JsonKey(name: 'spaceID') required final String spaceId,
      final String? spaceName,
      final SpaceStatus? status,
      final SpaceUsersConnection? users,
      final String? version,
      final String? vpnType}) = _$SpaceImpl;
  _Space._() : super._();

  factory _Space.fromJson(Map<String, dynamic> json) = _$SpaceImpl.fromJson;

  @override
  List<UserRef?>? get admins;
  @override
  SpaceAppsConnection? get apps;
  @override
  String? get certificate;
  @override
  String? get cookbook;
  @override
  String? get domainName;
  @override
  String? get fqdn;
  @override
  String? get iaas;
  @override
  String? get ipAddress;
  @override
  bool? get isEgressNode;
  @override
  int? get lastSeen;
  @override
  @JsonKey(name: 'localCARoot')
  String? get localCaRoot;
  @override
  int? get meshNetworkBitmask;
  @override
  IPType? get meshNetworkType;
  @override
  Org? get org;
  @override
  UserRef? get owner;
  @override
  int? get port;
  @override
  String? get publicKey;
  @override
  String? get recipe;
  @override
  String? get region;
  @override
  String? get settings;
  @override
  @JsonKey(name: 'spaceID')
  String get spaceId;
  @override
  String? get spaceName;
  @override
  SpaceStatus? get status;
  @override
  SpaceUsersConnection? get users;
  @override
  String? get version;
  @override
  String? get vpnType;

  /// Create a copy of Space
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SpaceImplCopyWith<_$SpaceImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SpaceAppsConnection _$SpaceAppsConnectionFromJson(Map<String, dynamic> json) {
  return _SpaceAppsConnection.fromJson(json);
}

/// @nodoc
mixin _$SpaceAppsConnection {
  List<SpaceAppsEdge?>? get edges => throw _privateConstructorUsedError;
  PageInfo? get pageInfo => throw _privateConstructorUsedError;
  List<App?>? get spaceApps => throw _privateConstructorUsedError;
  int? get totalCount => throw _privateConstructorUsedError;

  /// Serializes this SpaceAppsConnection to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SpaceAppsConnection
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SpaceAppsConnectionCopyWith<SpaceAppsConnection> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SpaceAppsConnectionCopyWith<$Res> {
  factory $SpaceAppsConnectionCopyWith(
          SpaceAppsConnection value, $Res Function(SpaceAppsConnection) then) =
      _$SpaceAppsConnectionCopyWithImpl<$Res, SpaceAppsConnection>;
  @useResult
  $Res call(
      {List<SpaceAppsEdge?>? edges,
      PageInfo? pageInfo,
      List<App?>? spaceApps,
      int? totalCount});

  $PageInfoCopyWith<$Res>? get pageInfo;
}

/// @nodoc
class _$SpaceAppsConnectionCopyWithImpl<$Res, $Val extends SpaceAppsConnection>
    implements $SpaceAppsConnectionCopyWith<$Res> {
  _$SpaceAppsConnectionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SpaceAppsConnection
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? edges = freezed,
    Object? pageInfo = freezed,
    Object? spaceApps = freezed,
    Object? totalCount = freezed,
  }) {
    return _then(_value.copyWith(
      edges: freezed == edges
          ? _value.edges
          : edges // ignore: cast_nullable_to_non_nullable
              as List<SpaceAppsEdge?>?,
      pageInfo: freezed == pageInfo
          ? _value.pageInfo
          : pageInfo // ignore: cast_nullable_to_non_nullable
              as PageInfo?,
      spaceApps: freezed == spaceApps
          ? _value.spaceApps
          : spaceApps // ignore: cast_nullable_to_non_nullable
              as List<App?>?,
      totalCount: freezed == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }

  /// Create a copy of SpaceAppsConnection
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PageInfoCopyWith<$Res>? get pageInfo {
    if (_value.pageInfo == null) {
      return null;
    }

    return $PageInfoCopyWith<$Res>(_value.pageInfo!, (value) {
      return _then(_value.copyWith(pageInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SpaceAppsConnectionImplCopyWith<$Res>
    implements $SpaceAppsConnectionCopyWith<$Res> {
  factory _$$SpaceAppsConnectionImplCopyWith(_$SpaceAppsConnectionImpl value,
          $Res Function(_$SpaceAppsConnectionImpl) then) =
      __$$SpaceAppsConnectionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<SpaceAppsEdge?>? edges,
      PageInfo? pageInfo,
      List<App?>? spaceApps,
      int? totalCount});

  @override
  $PageInfoCopyWith<$Res>? get pageInfo;
}

/// @nodoc
class __$$SpaceAppsConnectionImplCopyWithImpl<$Res>
    extends _$SpaceAppsConnectionCopyWithImpl<$Res, _$SpaceAppsConnectionImpl>
    implements _$$SpaceAppsConnectionImplCopyWith<$Res> {
  __$$SpaceAppsConnectionImplCopyWithImpl(_$SpaceAppsConnectionImpl _value,
      $Res Function(_$SpaceAppsConnectionImpl) _then)
      : super(_value, _then);

  /// Create a copy of SpaceAppsConnection
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? edges = freezed,
    Object? pageInfo = freezed,
    Object? spaceApps = freezed,
    Object? totalCount = freezed,
  }) {
    return _then(_$SpaceAppsConnectionImpl(
      edges: freezed == edges
          ? _value._edges
          : edges // ignore: cast_nullable_to_non_nullable
              as List<SpaceAppsEdge?>?,
      pageInfo: freezed == pageInfo
          ? _value.pageInfo
          : pageInfo // ignore: cast_nullable_to_non_nullable
              as PageInfo?,
      spaceApps: freezed == spaceApps
          ? _value._spaceApps
          : spaceApps // ignore: cast_nullable_to_non_nullable
              as List<App?>?,
      totalCount: freezed == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SpaceAppsConnectionImpl extends _SpaceAppsConnection
    with DiagnosticableTreeMixin {
  _$SpaceAppsConnectionImpl(
      {final List<SpaceAppsEdge?>? edges,
      this.pageInfo,
      final List<App?>? spaceApps,
      this.totalCount})
      : _edges = edges,
        _spaceApps = spaceApps,
        super._();

  factory _$SpaceAppsConnectionImpl.fromJson(Map<String, dynamic> json) =>
      _$$SpaceAppsConnectionImplFromJson(json);

  final List<SpaceAppsEdge?>? _edges;
  @override
  List<SpaceAppsEdge?>? get edges {
    final value = _edges;
    if (value == null) return null;
    if (_edges is EqualUnmodifiableListView) return _edges;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final PageInfo? pageInfo;
  final List<App?>? _spaceApps;
  @override
  List<App?>? get spaceApps {
    final value = _spaceApps;
    if (value == null) return null;
    if (_spaceApps is EqualUnmodifiableListView) return _spaceApps;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? totalCount;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'SpaceAppsConnection(edges: $edges, pageInfo: $pageInfo, spaceApps: $spaceApps, totalCount: $totalCount)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'SpaceAppsConnection'))
      ..add(DiagnosticsProperty('edges', edges))
      ..add(DiagnosticsProperty('pageInfo', pageInfo))
      ..add(DiagnosticsProperty('spaceApps', spaceApps))
      ..add(DiagnosticsProperty('totalCount', totalCount));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SpaceAppsConnectionImpl &&
            const DeepCollectionEquality().equals(other._edges, _edges) &&
            (identical(other.pageInfo, pageInfo) ||
                other.pageInfo == pageInfo) &&
            const DeepCollectionEquality()
                .equals(other._spaceApps, _spaceApps) &&
            (identical(other.totalCount, totalCount) ||
                other.totalCount == totalCount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_edges),
      pageInfo,
      const DeepCollectionEquality().hash(_spaceApps),
      totalCount);

  /// Create a copy of SpaceAppsConnection
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SpaceAppsConnectionImplCopyWith<_$SpaceAppsConnectionImpl> get copyWith =>
      __$$SpaceAppsConnectionImplCopyWithImpl<_$SpaceAppsConnectionImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SpaceAppsConnectionImplToJson(
      this,
    );
  }
}

abstract class _SpaceAppsConnection extends SpaceAppsConnection {
  factory _SpaceAppsConnection(
      {final List<SpaceAppsEdge?>? edges,
      final PageInfo? pageInfo,
      final List<App?>? spaceApps,
      final int? totalCount}) = _$SpaceAppsConnectionImpl;
  _SpaceAppsConnection._() : super._();

  factory _SpaceAppsConnection.fromJson(Map<String, dynamic> json) =
      _$SpaceAppsConnectionImpl.fromJson;

  @override
  List<SpaceAppsEdge?>? get edges;
  @override
  PageInfo? get pageInfo;
  @override
  List<App?>? get spaceApps;
  @override
  int? get totalCount;

  /// Create a copy of SpaceAppsConnection
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SpaceAppsConnectionImplCopyWith<_$SpaceAppsConnectionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SpaceAppsEdge _$SpaceAppsEdgeFromJson(Map<String, dynamic> json) {
  return _SpaceAppsEdge.fromJson(json);
}

/// @nodoc
mixin _$SpaceAppsEdge {
  App get node => throw _privateConstructorUsedError;

  /// Serializes this SpaceAppsEdge to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SpaceAppsEdge
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SpaceAppsEdgeCopyWith<SpaceAppsEdge> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SpaceAppsEdgeCopyWith<$Res> {
  factory $SpaceAppsEdgeCopyWith(
          SpaceAppsEdge value, $Res Function(SpaceAppsEdge) then) =
      _$SpaceAppsEdgeCopyWithImpl<$Res, SpaceAppsEdge>;
  @useResult
  $Res call({App node});

  $AppCopyWith<$Res> get node;
}

/// @nodoc
class _$SpaceAppsEdgeCopyWithImpl<$Res, $Val extends SpaceAppsEdge>
    implements $SpaceAppsEdgeCopyWith<$Res> {
  _$SpaceAppsEdgeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SpaceAppsEdge
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? node = null,
  }) {
    return _then(_value.copyWith(
      node: null == node
          ? _value.node
          : node // ignore: cast_nullable_to_non_nullable
              as App,
    ) as $Val);
  }

  /// Create a copy of SpaceAppsEdge
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppCopyWith<$Res> get node {
    return $AppCopyWith<$Res>(_value.node, (value) {
      return _then(_value.copyWith(node: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SpaceAppsEdgeImplCopyWith<$Res>
    implements $SpaceAppsEdgeCopyWith<$Res> {
  factory _$$SpaceAppsEdgeImplCopyWith(
          _$SpaceAppsEdgeImpl value, $Res Function(_$SpaceAppsEdgeImpl) then) =
      __$$SpaceAppsEdgeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({App node});

  @override
  $AppCopyWith<$Res> get node;
}

/// @nodoc
class __$$SpaceAppsEdgeImplCopyWithImpl<$Res>
    extends _$SpaceAppsEdgeCopyWithImpl<$Res, _$SpaceAppsEdgeImpl>
    implements _$$SpaceAppsEdgeImplCopyWith<$Res> {
  __$$SpaceAppsEdgeImplCopyWithImpl(
      _$SpaceAppsEdgeImpl _value, $Res Function(_$SpaceAppsEdgeImpl) _then)
      : super(_value, _then);

  /// Create a copy of SpaceAppsEdge
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? node = null,
  }) {
    return _then(_$SpaceAppsEdgeImpl(
      node: null == node
          ? _value.node
          : node // ignore: cast_nullable_to_non_nullable
              as App,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SpaceAppsEdgeImpl extends _SpaceAppsEdge with DiagnosticableTreeMixin {
  _$SpaceAppsEdgeImpl({required this.node}) : super._();

  factory _$SpaceAppsEdgeImpl.fromJson(Map<String, dynamic> json) =>
      _$$SpaceAppsEdgeImplFromJson(json);

  @override
  final App node;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'SpaceAppsEdge(node: $node)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'SpaceAppsEdge'))
      ..add(DiagnosticsProperty('node', node));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SpaceAppsEdgeImpl &&
            (identical(other.node, node) || other.node == node));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, node);

  /// Create a copy of SpaceAppsEdge
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SpaceAppsEdgeImplCopyWith<_$SpaceAppsEdgeImpl> get copyWith =>
      __$$SpaceAppsEdgeImplCopyWithImpl<_$SpaceAppsEdgeImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SpaceAppsEdgeImplToJson(
      this,
    );
  }
}

abstract class _SpaceAppsEdge extends SpaceAppsEdge {
  factory _SpaceAppsEdge({required final App node}) = _$SpaceAppsEdgeImpl;
  _SpaceAppsEdge._() : super._();

  factory _SpaceAppsEdge.fromJson(Map<String, dynamic> json) =
      _$SpaceAppsEdgeImpl.fromJson;

  @override
  App get node;

  /// Create a copy of SpaceAppsEdge
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SpaceAppsEdgeImplCopyWith<_$SpaceAppsEdgeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SpaceIi _$SpaceIiFromJson(Map<String, dynamic> json) {
  return _SpaceIi.fromJson(json);
}

/// @nodoc
mixin _$SpaceIi {
  String get idKey => throw _privateConstructorUsedError;
  SpaceUser get spaceUser => throw _privateConstructorUsedError;

  /// Serializes this SpaceIi to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SpaceIi
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SpaceIiCopyWith<SpaceIi> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SpaceIiCopyWith<$Res> {
  factory $SpaceIiCopyWith(SpaceIi value, $Res Function(SpaceIi) then) =
      _$SpaceIiCopyWithImpl<$Res, SpaceIi>;
  @useResult
  $Res call({String idKey, SpaceUser spaceUser});

  $SpaceUserCopyWith<$Res> get spaceUser;
}

/// @nodoc
class _$SpaceIiCopyWithImpl<$Res, $Val extends SpaceIi>
    implements $SpaceIiCopyWith<$Res> {
  _$SpaceIiCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SpaceIi
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? idKey = null,
    Object? spaceUser = null,
  }) {
    return _then(_value.copyWith(
      idKey: null == idKey
          ? _value.idKey
          : idKey // ignore: cast_nullable_to_non_nullable
              as String,
      spaceUser: null == spaceUser
          ? _value.spaceUser
          : spaceUser // ignore: cast_nullable_to_non_nullable
              as SpaceUser,
    ) as $Val);
  }

  /// Create a copy of SpaceIi
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SpaceUserCopyWith<$Res> get spaceUser {
    return $SpaceUserCopyWith<$Res>(_value.spaceUser, (value) {
      return _then(_value.copyWith(spaceUser: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SpaceIiImplCopyWith<$Res> implements $SpaceIiCopyWith<$Res> {
  factory _$$SpaceIiImplCopyWith(
          _$SpaceIiImpl value, $Res Function(_$SpaceIiImpl) then) =
      __$$SpaceIiImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String idKey, SpaceUser spaceUser});

  @override
  $SpaceUserCopyWith<$Res> get spaceUser;
}

/// @nodoc
class __$$SpaceIiImplCopyWithImpl<$Res>
    extends _$SpaceIiCopyWithImpl<$Res, _$SpaceIiImpl>
    implements _$$SpaceIiImplCopyWith<$Res> {
  __$$SpaceIiImplCopyWithImpl(
      _$SpaceIiImpl _value, $Res Function(_$SpaceIiImpl) _then)
      : super(_value, _then);

  /// Create a copy of SpaceIi
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? idKey = null,
    Object? spaceUser = null,
  }) {
    return _then(_$SpaceIiImpl(
      idKey: null == idKey
          ? _value.idKey
          : idKey // ignore: cast_nullable_to_non_nullable
              as String,
      spaceUser: null == spaceUser
          ? _value.spaceUser
          : spaceUser // ignore: cast_nullable_to_non_nullable
              as SpaceUser,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SpaceIiImpl extends _SpaceIi with DiagnosticableTreeMixin {
  _$SpaceIiImpl({required this.idKey, required this.spaceUser}) : super._();

  factory _$SpaceIiImpl.fromJson(Map<String, dynamic> json) =>
      _$$SpaceIiImplFromJson(json);

  @override
  final String idKey;
  @override
  final SpaceUser spaceUser;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'SpaceIi(idKey: $idKey, spaceUser: $spaceUser)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'SpaceIi'))
      ..add(DiagnosticsProperty('idKey', idKey))
      ..add(DiagnosticsProperty('spaceUser', spaceUser));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SpaceIiImpl &&
            (identical(other.idKey, idKey) || other.idKey == idKey) &&
            (identical(other.spaceUser, spaceUser) ||
                other.spaceUser == spaceUser));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, idKey, spaceUser);

  /// Create a copy of SpaceIi
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SpaceIiImplCopyWith<_$SpaceIiImpl> get copyWith =>
      __$$SpaceIiImplCopyWithImpl<_$SpaceIiImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SpaceIiImplToJson(
      this,
    );
  }
}

abstract class _SpaceIi extends SpaceIi {
  factory _SpaceIi(
      {required final String idKey,
      required final SpaceUser spaceUser}) = _$SpaceIiImpl;
  _SpaceIi._() : super._();

  factory _SpaceIi.fromJson(Map<String, dynamic> json) = _$SpaceIiImpl.fromJson;

  @override
  String get idKey;
  @override
  SpaceUser get spaceUser;

  /// Create a copy of SpaceIi
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SpaceIiImplCopyWith<_$SpaceIiImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SpaceUpdate _$SpaceUpdateFromJson(Map<String, dynamic> json) {
  return _SpaceUpdate.fromJson(json);
}

/// @nodoc
mixin _$SpaceUpdate {
  int? get numApps => throw _privateConstructorUsedError;
  int? get numUsers => throw _privateConstructorUsedError;
  Space get space => throw _privateConstructorUsedError;
  @JsonKey(name: 'spaceID')
  String get spaceId => throw _privateConstructorUsedError;

  /// Serializes this SpaceUpdate to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SpaceUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SpaceUpdateCopyWith<SpaceUpdate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SpaceUpdateCopyWith<$Res> {
  factory $SpaceUpdateCopyWith(
          SpaceUpdate value, $Res Function(SpaceUpdate) then) =
      _$SpaceUpdateCopyWithImpl<$Res, SpaceUpdate>;
  @useResult
  $Res call(
      {int? numApps,
      int? numUsers,
      Space space,
      @JsonKey(name: 'spaceID') String spaceId});

  $SpaceCopyWith<$Res> get space;
}

/// @nodoc
class _$SpaceUpdateCopyWithImpl<$Res, $Val extends SpaceUpdate>
    implements $SpaceUpdateCopyWith<$Res> {
  _$SpaceUpdateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SpaceUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? numApps = freezed,
    Object? numUsers = freezed,
    Object? space = null,
    Object? spaceId = null,
  }) {
    return _then(_value.copyWith(
      numApps: freezed == numApps
          ? _value.numApps
          : numApps // ignore: cast_nullable_to_non_nullable
              as int?,
      numUsers: freezed == numUsers
          ? _value.numUsers
          : numUsers // ignore: cast_nullable_to_non_nullable
              as int?,
      space: null == space
          ? _value.space
          : space // ignore: cast_nullable_to_non_nullable
              as Space,
      spaceId: null == spaceId
          ? _value.spaceId
          : spaceId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }

  /// Create a copy of SpaceUpdate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SpaceCopyWith<$Res> get space {
    return $SpaceCopyWith<$Res>(_value.space, (value) {
      return _then(_value.copyWith(space: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SpaceUpdateImplCopyWith<$Res>
    implements $SpaceUpdateCopyWith<$Res> {
  factory _$$SpaceUpdateImplCopyWith(
          _$SpaceUpdateImpl value, $Res Function(_$SpaceUpdateImpl) then) =
      __$$SpaceUpdateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? numApps,
      int? numUsers,
      Space space,
      @JsonKey(name: 'spaceID') String spaceId});

  @override
  $SpaceCopyWith<$Res> get space;
}

/// @nodoc
class __$$SpaceUpdateImplCopyWithImpl<$Res>
    extends _$SpaceUpdateCopyWithImpl<$Res, _$SpaceUpdateImpl>
    implements _$$SpaceUpdateImplCopyWith<$Res> {
  __$$SpaceUpdateImplCopyWithImpl(
      _$SpaceUpdateImpl _value, $Res Function(_$SpaceUpdateImpl) _then)
      : super(_value, _then);

  /// Create a copy of SpaceUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? numApps = freezed,
    Object? numUsers = freezed,
    Object? space = null,
    Object? spaceId = null,
  }) {
    return _then(_$SpaceUpdateImpl(
      numApps: freezed == numApps
          ? _value.numApps
          : numApps // ignore: cast_nullable_to_non_nullable
              as int?,
      numUsers: freezed == numUsers
          ? _value.numUsers
          : numUsers // ignore: cast_nullable_to_non_nullable
              as int?,
      space: null == space
          ? _value.space
          : space // ignore: cast_nullable_to_non_nullable
              as Space,
      spaceId: null == spaceId
          ? _value.spaceId
          : spaceId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SpaceUpdateImpl extends _SpaceUpdate with DiagnosticableTreeMixin {
  _$SpaceUpdateImpl(
      {this.numApps,
      this.numUsers,
      required this.space,
      @JsonKey(name: 'spaceID') required this.spaceId})
      : super._();

  factory _$SpaceUpdateImpl.fromJson(Map<String, dynamic> json) =>
      _$$SpaceUpdateImplFromJson(json);

  @override
  final int? numApps;
  @override
  final int? numUsers;
  @override
  final Space space;
  @override
  @JsonKey(name: 'spaceID')
  final String spaceId;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'SpaceUpdate(numApps: $numApps, numUsers: $numUsers, space: $space, spaceId: $spaceId)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'SpaceUpdate'))
      ..add(DiagnosticsProperty('numApps', numApps))
      ..add(DiagnosticsProperty('numUsers', numUsers))
      ..add(DiagnosticsProperty('space', space))
      ..add(DiagnosticsProperty('spaceId', spaceId));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SpaceUpdateImpl &&
            (identical(other.numApps, numApps) || other.numApps == numApps) &&
            (identical(other.numUsers, numUsers) ||
                other.numUsers == numUsers) &&
            (identical(other.space, space) || other.space == space) &&
            (identical(other.spaceId, spaceId) || other.spaceId == spaceId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, numApps, numUsers, space, spaceId);

  /// Create a copy of SpaceUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SpaceUpdateImplCopyWith<_$SpaceUpdateImpl> get copyWith =>
      __$$SpaceUpdateImplCopyWithImpl<_$SpaceUpdateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SpaceUpdateImplToJson(
      this,
    );
  }
}

abstract class _SpaceUpdate extends SpaceUpdate {
  factory _SpaceUpdate(
          {final int? numApps,
          final int? numUsers,
          required final Space space,
          @JsonKey(name: 'spaceID') required final String spaceId}) =
      _$SpaceUpdateImpl;
  _SpaceUpdate._() : super._();

  factory _SpaceUpdate.fromJson(Map<String, dynamic> json) =
      _$SpaceUpdateImpl.fromJson;

  @override
  int? get numApps;
  @override
  int? get numUsers;
  @override
  Space get space;
  @override
  @JsonKey(name: 'spaceID')
  String get spaceId;

  /// Create a copy of SpaceUpdate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SpaceUpdateImplCopyWith<_$SpaceUpdateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SpaceUser _$SpaceUserFromJson(Map<String, dynamic> json) {
  return _SpaceUser.fromJson(json);
}

/// @nodoc
mixin _$SpaceUser {
  AppUsersConnection? get accessList => throw _privateConstructorUsedError;
  int? get bytesDownloaded => throw _privateConstructorUsedError;
  int? get bytesUploaded => throw _privateConstructorUsedError;
  bool? get canUseSpaceForEgress => throw _privateConstructorUsedError;
  bool? get enableSiteBlocking => throw _privateConstructorUsedError;
  bool? get isAdmin => throw _privateConstructorUsedError;
  bool? get isOwner => throw _privateConstructorUsedError;
  Device? get lastConnectDevice => throw _privateConstructorUsedError;
  int? get lastConnectTime => throw _privateConstructorUsedError;
  Space? get space => throw _privateConstructorUsedError;
  UserAccessStatus? get status => throw _privateConstructorUsedError;
  User? get user => throw _privateConstructorUsedError;

  /// Serializes this SpaceUser to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SpaceUser
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SpaceUserCopyWith<SpaceUser> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SpaceUserCopyWith<$Res> {
  factory $SpaceUserCopyWith(SpaceUser value, $Res Function(SpaceUser) then) =
      _$SpaceUserCopyWithImpl<$Res, SpaceUser>;
  @useResult
  $Res call(
      {AppUsersConnection? accessList,
      int? bytesDownloaded,
      int? bytesUploaded,
      bool? canUseSpaceForEgress,
      bool? enableSiteBlocking,
      bool? isAdmin,
      bool? isOwner,
      Device? lastConnectDevice,
      int? lastConnectTime,
      Space? space,
      UserAccessStatus? status,
      User? user});

  $AppUsersConnectionCopyWith<$Res>? get accessList;
  $DeviceCopyWith<$Res>? get lastConnectDevice;
  $SpaceCopyWith<$Res>? get space;
  $UserCopyWith<$Res>? get user;
}

/// @nodoc
class _$SpaceUserCopyWithImpl<$Res, $Val extends SpaceUser>
    implements $SpaceUserCopyWith<$Res> {
  _$SpaceUserCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SpaceUser
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessList = freezed,
    Object? bytesDownloaded = freezed,
    Object? bytesUploaded = freezed,
    Object? canUseSpaceForEgress = freezed,
    Object? enableSiteBlocking = freezed,
    Object? isAdmin = freezed,
    Object? isOwner = freezed,
    Object? lastConnectDevice = freezed,
    Object? lastConnectTime = freezed,
    Object? space = freezed,
    Object? status = freezed,
    Object? user = freezed,
  }) {
    return _then(_value.copyWith(
      accessList: freezed == accessList
          ? _value.accessList
          : accessList // ignore: cast_nullable_to_non_nullable
              as AppUsersConnection?,
      bytesDownloaded: freezed == bytesDownloaded
          ? _value.bytesDownloaded
          : bytesDownloaded // ignore: cast_nullable_to_non_nullable
              as int?,
      bytesUploaded: freezed == bytesUploaded
          ? _value.bytesUploaded
          : bytesUploaded // ignore: cast_nullable_to_non_nullable
              as int?,
      canUseSpaceForEgress: freezed == canUseSpaceForEgress
          ? _value.canUseSpaceForEgress
          : canUseSpaceForEgress // ignore: cast_nullable_to_non_nullable
              as bool?,
      enableSiteBlocking: freezed == enableSiteBlocking
          ? _value.enableSiteBlocking
          : enableSiteBlocking // ignore: cast_nullable_to_non_nullable
              as bool?,
      isAdmin: freezed == isAdmin
          ? _value.isAdmin
          : isAdmin // ignore: cast_nullable_to_non_nullable
              as bool?,
      isOwner: freezed == isOwner
          ? _value.isOwner
          : isOwner // ignore: cast_nullable_to_non_nullable
              as bool?,
      lastConnectDevice: freezed == lastConnectDevice
          ? _value.lastConnectDevice
          : lastConnectDevice // ignore: cast_nullable_to_non_nullable
              as Device?,
      lastConnectTime: freezed == lastConnectTime
          ? _value.lastConnectTime
          : lastConnectTime // ignore: cast_nullable_to_non_nullable
              as int?,
      space: freezed == space
          ? _value.space
          : space // ignore: cast_nullable_to_non_nullable
              as Space?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as UserAccessStatus?,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User?,
    ) as $Val);
  }

  /// Create a copy of SpaceUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppUsersConnectionCopyWith<$Res>? get accessList {
    if (_value.accessList == null) {
      return null;
    }

    return $AppUsersConnectionCopyWith<$Res>(_value.accessList!, (value) {
      return _then(_value.copyWith(accessList: value) as $Val);
    });
  }

  /// Create a copy of SpaceUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DeviceCopyWith<$Res>? get lastConnectDevice {
    if (_value.lastConnectDevice == null) {
      return null;
    }

    return $DeviceCopyWith<$Res>(_value.lastConnectDevice!, (value) {
      return _then(_value.copyWith(lastConnectDevice: value) as $Val);
    });
  }

  /// Create a copy of SpaceUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SpaceCopyWith<$Res>? get space {
    if (_value.space == null) {
      return null;
    }

    return $SpaceCopyWith<$Res>(_value.space!, (value) {
      return _then(_value.copyWith(space: value) as $Val);
    });
  }

  /// Create a copy of SpaceUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserCopyWith<$Res>? get user {
    if (_value.user == null) {
      return null;
    }

    return $UserCopyWith<$Res>(_value.user!, (value) {
      return _then(_value.copyWith(user: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SpaceUserImplCopyWith<$Res>
    implements $SpaceUserCopyWith<$Res> {
  factory _$$SpaceUserImplCopyWith(
          _$SpaceUserImpl value, $Res Function(_$SpaceUserImpl) then) =
      __$$SpaceUserImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {AppUsersConnection? accessList,
      int? bytesDownloaded,
      int? bytesUploaded,
      bool? canUseSpaceForEgress,
      bool? enableSiteBlocking,
      bool? isAdmin,
      bool? isOwner,
      Device? lastConnectDevice,
      int? lastConnectTime,
      Space? space,
      UserAccessStatus? status,
      User? user});

  @override
  $AppUsersConnectionCopyWith<$Res>? get accessList;
  @override
  $DeviceCopyWith<$Res>? get lastConnectDevice;
  @override
  $SpaceCopyWith<$Res>? get space;
  @override
  $UserCopyWith<$Res>? get user;
}

/// @nodoc
class __$$SpaceUserImplCopyWithImpl<$Res>
    extends _$SpaceUserCopyWithImpl<$Res, _$SpaceUserImpl>
    implements _$$SpaceUserImplCopyWith<$Res> {
  __$$SpaceUserImplCopyWithImpl(
      _$SpaceUserImpl _value, $Res Function(_$SpaceUserImpl) _then)
      : super(_value, _then);

  /// Create a copy of SpaceUser
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessList = freezed,
    Object? bytesDownloaded = freezed,
    Object? bytesUploaded = freezed,
    Object? canUseSpaceForEgress = freezed,
    Object? enableSiteBlocking = freezed,
    Object? isAdmin = freezed,
    Object? isOwner = freezed,
    Object? lastConnectDevice = freezed,
    Object? lastConnectTime = freezed,
    Object? space = freezed,
    Object? status = freezed,
    Object? user = freezed,
  }) {
    return _then(_$SpaceUserImpl(
      accessList: freezed == accessList
          ? _value.accessList
          : accessList // ignore: cast_nullable_to_non_nullable
              as AppUsersConnection?,
      bytesDownloaded: freezed == bytesDownloaded
          ? _value.bytesDownloaded
          : bytesDownloaded // ignore: cast_nullable_to_non_nullable
              as int?,
      bytesUploaded: freezed == bytesUploaded
          ? _value.bytesUploaded
          : bytesUploaded // ignore: cast_nullable_to_non_nullable
              as int?,
      canUseSpaceForEgress: freezed == canUseSpaceForEgress
          ? _value.canUseSpaceForEgress
          : canUseSpaceForEgress // ignore: cast_nullable_to_non_nullable
              as bool?,
      enableSiteBlocking: freezed == enableSiteBlocking
          ? _value.enableSiteBlocking
          : enableSiteBlocking // ignore: cast_nullable_to_non_nullable
              as bool?,
      isAdmin: freezed == isAdmin
          ? _value.isAdmin
          : isAdmin // ignore: cast_nullable_to_non_nullable
              as bool?,
      isOwner: freezed == isOwner
          ? _value.isOwner
          : isOwner // ignore: cast_nullable_to_non_nullable
              as bool?,
      lastConnectDevice: freezed == lastConnectDevice
          ? _value.lastConnectDevice
          : lastConnectDevice // ignore: cast_nullable_to_non_nullable
              as Device?,
      lastConnectTime: freezed == lastConnectTime
          ? _value.lastConnectTime
          : lastConnectTime // ignore: cast_nullable_to_non_nullable
              as int?,
      space: freezed == space
          ? _value.space
          : space // ignore: cast_nullable_to_non_nullable
              as Space?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as UserAccessStatus?,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SpaceUserImpl extends _SpaceUser with DiagnosticableTreeMixin {
  _$SpaceUserImpl(
      {this.accessList,
      this.bytesDownloaded,
      this.bytesUploaded,
      this.canUseSpaceForEgress,
      this.enableSiteBlocking,
      this.isAdmin,
      this.isOwner,
      this.lastConnectDevice,
      this.lastConnectTime,
      this.space,
      this.status,
      this.user})
      : super._();

  factory _$SpaceUserImpl.fromJson(Map<String, dynamic> json) =>
      _$$SpaceUserImplFromJson(json);

  @override
  final AppUsersConnection? accessList;
  @override
  final int? bytesDownloaded;
  @override
  final int? bytesUploaded;
  @override
  final bool? canUseSpaceForEgress;
  @override
  final bool? enableSiteBlocking;
  @override
  final bool? isAdmin;
  @override
  final bool? isOwner;
  @override
  final Device? lastConnectDevice;
  @override
  final int? lastConnectTime;
  @override
  final Space? space;
  @override
  final UserAccessStatus? status;
  @override
  final User? user;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'SpaceUser(accessList: $accessList, bytesDownloaded: $bytesDownloaded, bytesUploaded: $bytesUploaded, canUseSpaceForEgress: $canUseSpaceForEgress, enableSiteBlocking: $enableSiteBlocking, isAdmin: $isAdmin, isOwner: $isOwner, lastConnectDevice: $lastConnectDevice, lastConnectTime: $lastConnectTime, space: $space, status: $status, user: $user)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'SpaceUser'))
      ..add(DiagnosticsProperty('accessList', accessList))
      ..add(DiagnosticsProperty('bytesDownloaded', bytesDownloaded))
      ..add(DiagnosticsProperty('bytesUploaded', bytesUploaded))
      ..add(DiagnosticsProperty('canUseSpaceForEgress', canUseSpaceForEgress))
      ..add(DiagnosticsProperty('enableSiteBlocking', enableSiteBlocking))
      ..add(DiagnosticsProperty('isAdmin', isAdmin))
      ..add(DiagnosticsProperty('isOwner', isOwner))
      ..add(DiagnosticsProperty('lastConnectDevice', lastConnectDevice))
      ..add(DiagnosticsProperty('lastConnectTime', lastConnectTime))
      ..add(DiagnosticsProperty('space', space))
      ..add(DiagnosticsProperty('status', status))
      ..add(DiagnosticsProperty('user', user));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SpaceUserImpl &&
            (identical(other.accessList, accessList) ||
                other.accessList == accessList) &&
            (identical(other.bytesDownloaded, bytesDownloaded) ||
                other.bytesDownloaded == bytesDownloaded) &&
            (identical(other.bytesUploaded, bytesUploaded) ||
                other.bytesUploaded == bytesUploaded) &&
            (identical(other.canUseSpaceForEgress, canUseSpaceForEgress) ||
                other.canUseSpaceForEgress == canUseSpaceForEgress) &&
            (identical(other.enableSiteBlocking, enableSiteBlocking) ||
                other.enableSiteBlocking == enableSiteBlocking) &&
            (identical(other.isAdmin, isAdmin) || other.isAdmin == isAdmin) &&
            (identical(other.isOwner, isOwner) || other.isOwner == isOwner) &&
            (identical(other.lastConnectDevice, lastConnectDevice) ||
                other.lastConnectDevice == lastConnectDevice) &&
            (identical(other.lastConnectTime, lastConnectTime) ||
                other.lastConnectTime == lastConnectTime) &&
            (identical(other.space, space) || other.space == space) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.user, user) || other.user == user));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      accessList,
      bytesDownloaded,
      bytesUploaded,
      canUseSpaceForEgress,
      enableSiteBlocking,
      isAdmin,
      isOwner,
      lastConnectDevice,
      lastConnectTime,
      space,
      status,
      user);

  /// Create a copy of SpaceUser
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SpaceUserImplCopyWith<_$SpaceUserImpl> get copyWith =>
      __$$SpaceUserImplCopyWithImpl<_$SpaceUserImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SpaceUserImplToJson(
      this,
    );
  }
}

abstract class _SpaceUser extends SpaceUser {
  factory _SpaceUser(
      {final AppUsersConnection? accessList,
      final int? bytesDownloaded,
      final int? bytesUploaded,
      final bool? canUseSpaceForEgress,
      final bool? enableSiteBlocking,
      final bool? isAdmin,
      final bool? isOwner,
      final Device? lastConnectDevice,
      final int? lastConnectTime,
      final Space? space,
      final UserAccessStatus? status,
      final User? user}) = _$SpaceUserImpl;
  _SpaceUser._() : super._();

  factory _SpaceUser.fromJson(Map<String, dynamic> json) =
      _$SpaceUserImpl.fromJson;

  @override
  AppUsersConnection? get accessList;
  @override
  int? get bytesDownloaded;
  @override
  int? get bytesUploaded;
  @override
  bool? get canUseSpaceForEgress;
  @override
  bool? get enableSiteBlocking;
  @override
  bool? get isAdmin;
  @override
  bool? get isOwner;
  @override
  Device? get lastConnectDevice;
  @override
  int? get lastConnectTime;
  @override
  Space? get space;
  @override
  UserAccessStatus? get status;
  @override
  User? get user;

  /// Create a copy of SpaceUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SpaceUserImplCopyWith<_$SpaceUserImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SpaceUserUpdate _$SpaceUserUpdateFromJson(Map<String, dynamic> json) {
  return _SpaceUserUpdate.fromJson(json);
}

/// @nodoc
mixin _$SpaceUserUpdate {
  @JsonKey(name: 'spaceID')
  String get spaceId => throw _privateConstructorUsedError;
  SpaceUser get spaceUser => throw _privateConstructorUsedError;
  @JsonKey(name: 'userID')
  String get userId => throw _privateConstructorUsedError;

  /// Serializes this SpaceUserUpdate to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SpaceUserUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SpaceUserUpdateCopyWith<SpaceUserUpdate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SpaceUserUpdateCopyWith<$Res> {
  factory $SpaceUserUpdateCopyWith(
          SpaceUserUpdate value, $Res Function(SpaceUserUpdate) then) =
      _$SpaceUserUpdateCopyWithImpl<$Res, SpaceUserUpdate>;
  @useResult
  $Res call(
      {@JsonKey(name: 'spaceID') String spaceId,
      SpaceUser spaceUser,
      @JsonKey(name: 'userID') String userId});

  $SpaceUserCopyWith<$Res> get spaceUser;
}

/// @nodoc
class _$SpaceUserUpdateCopyWithImpl<$Res, $Val extends SpaceUserUpdate>
    implements $SpaceUserUpdateCopyWith<$Res> {
  _$SpaceUserUpdateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SpaceUserUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? spaceId = null,
    Object? spaceUser = null,
    Object? userId = null,
  }) {
    return _then(_value.copyWith(
      spaceId: null == spaceId
          ? _value.spaceId
          : spaceId // ignore: cast_nullable_to_non_nullable
              as String,
      spaceUser: null == spaceUser
          ? _value.spaceUser
          : spaceUser // ignore: cast_nullable_to_non_nullable
              as SpaceUser,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }

  /// Create a copy of SpaceUserUpdate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SpaceUserCopyWith<$Res> get spaceUser {
    return $SpaceUserCopyWith<$Res>(_value.spaceUser, (value) {
      return _then(_value.copyWith(spaceUser: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SpaceUserUpdateImplCopyWith<$Res>
    implements $SpaceUserUpdateCopyWith<$Res> {
  factory _$$SpaceUserUpdateImplCopyWith(_$SpaceUserUpdateImpl value,
          $Res Function(_$SpaceUserUpdateImpl) then) =
      __$$SpaceUserUpdateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'spaceID') String spaceId,
      SpaceUser spaceUser,
      @JsonKey(name: 'userID') String userId});

  @override
  $SpaceUserCopyWith<$Res> get spaceUser;
}

/// @nodoc
class __$$SpaceUserUpdateImplCopyWithImpl<$Res>
    extends _$SpaceUserUpdateCopyWithImpl<$Res, _$SpaceUserUpdateImpl>
    implements _$$SpaceUserUpdateImplCopyWith<$Res> {
  __$$SpaceUserUpdateImplCopyWithImpl(
      _$SpaceUserUpdateImpl _value, $Res Function(_$SpaceUserUpdateImpl) _then)
      : super(_value, _then);

  /// Create a copy of SpaceUserUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? spaceId = null,
    Object? spaceUser = null,
    Object? userId = null,
  }) {
    return _then(_$SpaceUserUpdateImpl(
      spaceId: null == spaceId
          ? _value.spaceId
          : spaceId // ignore: cast_nullable_to_non_nullable
              as String,
      spaceUser: null == spaceUser
          ? _value.spaceUser
          : spaceUser // ignore: cast_nullable_to_non_nullable
              as SpaceUser,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SpaceUserUpdateImpl extends _SpaceUserUpdate
    with DiagnosticableTreeMixin {
  _$SpaceUserUpdateImpl(
      {@JsonKey(name: 'spaceID') required this.spaceId,
      required this.spaceUser,
      @JsonKey(name: 'userID') required this.userId})
      : super._();

  factory _$SpaceUserUpdateImpl.fromJson(Map<String, dynamic> json) =>
      _$$SpaceUserUpdateImplFromJson(json);

  @override
  @JsonKey(name: 'spaceID')
  final String spaceId;
  @override
  final SpaceUser spaceUser;
  @override
  @JsonKey(name: 'userID')
  final String userId;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'SpaceUserUpdate(spaceId: $spaceId, spaceUser: $spaceUser, userId: $userId)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'SpaceUserUpdate'))
      ..add(DiagnosticsProperty('spaceId', spaceId))
      ..add(DiagnosticsProperty('spaceUser', spaceUser))
      ..add(DiagnosticsProperty('userId', userId));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SpaceUserUpdateImpl &&
            (identical(other.spaceId, spaceId) || other.spaceId == spaceId) &&
            (identical(other.spaceUser, spaceUser) ||
                other.spaceUser == spaceUser) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, spaceId, spaceUser, userId);

  /// Create a copy of SpaceUserUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SpaceUserUpdateImplCopyWith<_$SpaceUserUpdateImpl> get copyWith =>
      __$$SpaceUserUpdateImplCopyWithImpl<_$SpaceUserUpdateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SpaceUserUpdateImplToJson(
      this,
    );
  }
}

abstract class _SpaceUserUpdate extends SpaceUserUpdate {
  factory _SpaceUserUpdate(
          {@JsonKey(name: 'spaceID') required final String spaceId,
          required final SpaceUser spaceUser,
          @JsonKey(name: 'userID') required final String userId}) =
      _$SpaceUserUpdateImpl;
  _SpaceUserUpdate._() : super._();

  factory _SpaceUserUpdate.fromJson(Map<String, dynamic> json) =
      _$SpaceUserUpdateImpl.fromJson;

  @override
  @JsonKey(name: 'spaceID')
  String get spaceId;
  @override
  SpaceUser get spaceUser;
  @override
  @JsonKey(name: 'userID')
  String get userId;

  /// Create a copy of SpaceUserUpdate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SpaceUserUpdateImplCopyWith<_$SpaceUserUpdateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SpaceUsersConnection _$SpaceUsersConnectionFromJson(Map<String, dynamic> json) {
  return _SpaceUsersConnection.fromJson(json);
}

/// @nodoc
mixin _$SpaceUsersConnection {
  List<SpaceUsersEdge?>? get edges => throw _privateConstructorUsedError;
  PageInfo? get pageInfo => throw _privateConstructorUsedError;
  List<SpaceUser?>? get spaceUsers => throw _privateConstructorUsedError;
  int? get totalCount => throw _privateConstructorUsedError;

  /// Serializes this SpaceUsersConnection to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SpaceUsersConnection
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SpaceUsersConnectionCopyWith<SpaceUsersConnection> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SpaceUsersConnectionCopyWith<$Res> {
  factory $SpaceUsersConnectionCopyWith(SpaceUsersConnection value,
          $Res Function(SpaceUsersConnection) then) =
      _$SpaceUsersConnectionCopyWithImpl<$Res, SpaceUsersConnection>;
  @useResult
  $Res call(
      {List<SpaceUsersEdge?>? edges,
      PageInfo? pageInfo,
      List<SpaceUser?>? spaceUsers,
      int? totalCount});

  $PageInfoCopyWith<$Res>? get pageInfo;
}

/// @nodoc
class _$SpaceUsersConnectionCopyWithImpl<$Res,
        $Val extends SpaceUsersConnection>
    implements $SpaceUsersConnectionCopyWith<$Res> {
  _$SpaceUsersConnectionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SpaceUsersConnection
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? edges = freezed,
    Object? pageInfo = freezed,
    Object? spaceUsers = freezed,
    Object? totalCount = freezed,
  }) {
    return _then(_value.copyWith(
      edges: freezed == edges
          ? _value.edges
          : edges // ignore: cast_nullable_to_non_nullable
              as List<SpaceUsersEdge?>?,
      pageInfo: freezed == pageInfo
          ? _value.pageInfo
          : pageInfo // ignore: cast_nullable_to_non_nullable
              as PageInfo?,
      spaceUsers: freezed == spaceUsers
          ? _value.spaceUsers
          : spaceUsers // ignore: cast_nullable_to_non_nullable
              as List<SpaceUser?>?,
      totalCount: freezed == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }

  /// Create a copy of SpaceUsersConnection
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PageInfoCopyWith<$Res>? get pageInfo {
    if (_value.pageInfo == null) {
      return null;
    }

    return $PageInfoCopyWith<$Res>(_value.pageInfo!, (value) {
      return _then(_value.copyWith(pageInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SpaceUsersConnectionImplCopyWith<$Res>
    implements $SpaceUsersConnectionCopyWith<$Res> {
  factory _$$SpaceUsersConnectionImplCopyWith(_$SpaceUsersConnectionImpl value,
          $Res Function(_$SpaceUsersConnectionImpl) then) =
      __$$SpaceUsersConnectionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<SpaceUsersEdge?>? edges,
      PageInfo? pageInfo,
      List<SpaceUser?>? spaceUsers,
      int? totalCount});

  @override
  $PageInfoCopyWith<$Res>? get pageInfo;
}

/// @nodoc
class __$$SpaceUsersConnectionImplCopyWithImpl<$Res>
    extends _$SpaceUsersConnectionCopyWithImpl<$Res, _$SpaceUsersConnectionImpl>
    implements _$$SpaceUsersConnectionImplCopyWith<$Res> {
  __$$SpaceUsersConnectionImplCopyWithImpl(_$SpaceUsersConnectionImpl _value,
      $Res Function(_$SpaceUsersConnectionImpl) _then)
      : super(_value, _then);

  /// Create a copy of SpaceUsersConnection
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? edges = freezed,
    Object? pageInfo = freezed,
    Object? spaceUsers = freezed,
    Object? totalCount = freezed,
  }) {
    return _then(_$SpaceUsersConnectionImpl(
      edges: freezed == edges
          ? _value._edges
          : edges // ignore: cast_nullable_to_non_nullable
              as List<SpaceUsersEdge?>?,
      pageInfo: freezed == pageInfo
          ? _value.pageInfo
          : pageInfo // ignore: cast_nullable_to_non_nullable
              as PageInfo?,
      spaceUsers: freezed == spaceUsers
          ? _value._spaceUsers
          : spaceUsers // ignore: cast_nullable_to_non_nullable
              as List<SpaceUser?>?,
      totalCount: freezed == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SpaceUsersConnectionImpl extends _SpaceUsersConnection
    with DiagnosticableTreeMixin {
  _$SpaceUsersConnectionImpl(
      {final List<SpaceUsersEdge?>? edges,
      this.pageInfo,
      final List<SpaceUser?>? spaceUsers,
      this.totalCount})
      : _edges = edges,
        _spaceUsers = spaceUsers,
        super._();

  factory _$SpaceUsersConnectionImpl.fromJson(Map<String, dynamic> json) =>
      _$$SpaceUsersConnectionImplFromJson(json);

  final List<SpaceUsersEdge?>? _edges;
  @override
  List<SpaceUsersEdge?>? get edges {
    final value = _edges;
    if (value == null) return null;
    if (_edges is EqualUnmodifiableListView) return _edges;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final PageInfo? pageInfo;
  final List<SpaceUser?>? _spaceUsers;
  @override
  List<SpaceUser?>? get spaceUsers {
    final value = _spaceUsers;
    if (value == null) return null;
    if (_spaceUsers is EqualUnmodifiableListView) return _spaceUsers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? totalCount;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'SpaceUsersConnection(edges: $edges, pageInfo: $pageInfo, spaceUsers: $spaceUsers, totalCount: $totalCount)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'SpaceUsersConnection'))
      ..add(DiagnosticsProperty('edges', edges))
      ..add(DiagnosticsProperty('pageInfo', pageInfo))
      ..add(DiagnosticsProperty('spaceUsers', spaceUsers))
      ..add(DiagnosticsProperty('totalCount', totalCount));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SpaceUsersConnectionImpl &&
            const DeepCollectionEquality().equals(other._edges, _edges) &&
            (identical(other.pageInfo, pageInfo) ||
                other.pageInfo == pageInfo) &&
            const DeepCollectionEquality()
                .equals(other._spaceUsers, _spaceUsers) &&
            (identical(other.totalCount, totalCount) ||
                other.totalCount == totalCount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_edges),
      pageInfo,
      const DeepCollectionEquality().hash(_spaceUsers),
      totalCount);

  /// Create a copy of SpaceUsersConnection
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SpaceUsersConnectionImplCopyWith<_$SpaceUsersConnectionImpl>
      get copyWith =>
          __$$SpaceUsersConnectionImplCopyWithImpl<_$SpaceUsersConnectionImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SpaceUsersConnectionImplToJson(
      this,
    );
  }
}

abstract class _SpaceUsersConnection extends SpaceUsersConnection {
  factory _SpaceUsersConnection(
      {final List<SpaceUsersEdge?>? edges,
      final PageInfo? pageInfo,
      final List<SpaceUser?>? spaceUsers,
      final int? totalCount}) = _$SpaceUsersConnectionImpl;
  _SpaceUsersConnection._() : super._();

  factory _SpaceUsersConnection.fromJson(Map<String, dynamic> json) =
      _$SpaceUsersConnectionImpl.fromJson;

  @override
  List<SpaceUsersEdge?>? get edges;
  @override
  PageInfo? get pageInfo;
  @override
  List<SpaceUser?>? get spaceUsers;
  @override
  int? get totalCount;

  /// Create a copy of SpaceUsersConnection
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SpaceUsersConnectionImplCopyWith<_$SpaceUsersConnectionImpl>
      get copyWith => throw _privateConstructorUsedError;
}

SpaceUsersEdge _$SpaceUsersEdgeFromJson(Map<String, dynamic> json) {
  return _SpaceUsersEdge.fromJson(json);
}

/// @nodoc
mixin _$SpaceUsersEdge {
  SpaceUser get node => throw _privateConstructorUsedError;

  /// Serializes this SpaceUsersEdge to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SpaceUsersEdge
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SpaceUsersEdgeCopyWith<SpaceUsersEdge> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SpaceUsersEdgeCopyWith<$Res> {
  factory $SpaceUsersEdgeCopyWith(
          SpaceUsersEdge value, $Res Function(SpaceUsersEdge) then) =
      _$SpaceUsersEdgeCopyWithImpl<$Res, SpaceUsersEdge>;
  @useResult
  $Res call({SpaceUser node});

  $SpaceUserCopyWith<$Res> get node;
}

/// @nodoc
class _$SpaceUsersEdgeCopyWithImpl<$Res, $Val extends SpaceUsersEdge>
    implements $SpaceUsersEdgeCopyWith<$Res> {
  _$SpaceUsersEdgeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SpaceUsersEdge
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? node = null,
  }) {
    return _then(_value.copyWith(
      node: null == node
          ? _value.node
          : node // ignore: cast_nullable_to_non_nullable
              as SpaceUser,
    ) as $Val);
  }

  /// Create a copy of SpaceUsersEdge
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SpaceUserCopyWith<$Res> get node {
    return $SpaceUserCopyWith<$Res>(_value.node, (value) {
      return _then(_value.copyWith(node: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SpaceUsersEdgeImplCopyWith<$Res>
    implements $SpaceUsersEdgeCopyWith<$Res> {
  factory _$$SpaceUsersEdgeImplCopyWith(_$SpaceUsersEdgeImpl value,
          $Res Function(_$SpaceUsersEdgeImpl) then) =
      __$$SpaceUsersEdgeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({SpaceUser node});

  @override
  $SpaceUserCopyWith<$Res> get node;
}

/// @nodoc
class __$$SpaceUsersEdgeImplCopyWithImpl<$Res>
    extends _$SpaceUsersEdgeCopyWithImpl<$Res, _$SpaceUsersEdgeImpl>
    implements _$$SpaceUsersEdgeImplCopyWith<$Res> {
  __$$SpaceUsersEdgeImplCopyWithImpl(
      _$SpaceUsersEdgeImpl _value, $Res Function(_$SpaceUsersEdgeImpl) _then)
      : super(_value, _then);

  /// Create a copy of SpaceUsersEdge
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? node = null,
  }) {
    return _then(_$SpaceUsersEdgeImpl(
      node: null == node
          ? _value.node
          : node // ignore: cast_nullable_to_non_nullable
              as SpaceUser,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SpaceUsersEdgeImpl extends _SpaceUsersEdge
    with DiagnosticableTreeMixin {
  _$SpaceUsersEdgeImpl({required this.node}) : super._();

  factory _$SpaceUsersEdgeImpl.fromJson(Map<String, dynamic> json) =>
      _$$SpaceUsersEdgeImplFromJson(json);

  @override
  final SpaceUser node;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'SpaceUsersEdge(node: $node)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'SpaceUsersEdge'))
      ..add(DiagnosticsProperty('node', node));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SpaceUsersEdgeImpl &&
            (identical(other.node, node) || other.node == node));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, node);

  /// Create a copy of SpaceUsersEdge
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SpaceUsersEdgeImplCopyWith<_$SpaceUsersEdgeImpl> get copyWith =>
      __$$SpaceUsersEdgeImplCopyWithImpl<_$SpaceUsersEdgeImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SpaceUsersEdgeImplToJson(
      this,
    );
  }
}

abstract class _SpaceUsersEdge extends SpaceUsersEdge {
  factory _SpaceUsersEdge({required final SpaceUser node}) =
      _$SpaceUsersEdgeImpl;
  _SpaceUsersEdge._() : super._();

  factory _SpaceUsersEdge.fromJson(Map<String, dynamic> json) =
      _$SpaceUsersEdgeImpl.fromJson;

  @override
  SpaceUser get node;

  /// Create a copy of SpaceUsersEdge
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SpaceUsersEdgeImplCopyWith<_$SpaceUsersEdgeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SubInfoInput _$SubInfoInputFromJson(Map<String, dynamic> json) {
  return _SubInfoInput.fromJson(json);
}

/// @nodoc
mixin _$SubInfoInput {
  List<String> get args => throw _privateConstructorUsedError;
  set args(List<String> value) => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  set name(String value) => throw _privateConstructorUsedError;

  /// Serializes this SubInfoInput to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SubInfoInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SubInfoInputCopyWith<SubInfoInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SubInfoInputCopyWith<$Res> {
  factory $SubInfoInputCopyWith(
          SubInfoInput value, $Res Function(SubInfoInput) then) =
      _$SubInfoInputCopyWithImpl<$Res, SubInfoInput>;
  @useResult
  $Res call({List<String> args, String name});
}

/// @nodoc
class _$SubInfoInputCopyWithImpl<$Res, $Val extends SubInfoInput>
    implements $SubInfoInputCopyWith<$Res> {
  _$SubInfoInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SubInfoInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? args = null,
    Object? name = null,
  }) {
    return _then(_value.copyWith(
      args: null == args
          ? _value.args
          : args // ignore: cast_nullable_to_non_nullable
              as List<String>,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SubInfoInputImplCopyWith<$Res>
    implements $SubInfoInputCopyWith<$Res> {
  factory _$$SubInfoInputImplCopyWith(
          _$SubInfoInputImpl value, $Res Function(_$SubInfoInputImpl) then) =
      __$$SubInfoInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<String> args, String name});
}

/// @nodoc
class __$$SubInfoInputImplCopyWithImpl<$Res>
    extends _$SubInfoInputCopyWithImpl<$Res, _$SubInfoInputImpl>
    implements _$$SubInfoInputImplCopyWith<$Res> {
  __$$SubInfoInputImplCopyWithImpl(
      _$SubInfoInputImpl _value, $Res Function(_$SubInfoInputImpl) _then)
      : super(_value, _then);

  /// Create a copy of SubInfoInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? args = null,
    Object? name = null,
  }) {
    return _then(_$SubInfoInputImpl(
      args: null == args
          ? _value.args
          : args // ignore: cast_nullable_to_non_nullable
              as List<String>,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SubInfoInputImpl extends _SubInfoInput with DiagnosticableTreeMixin {
  _$SubInfoInputImpl({required this.args, required this.name}) : super._();

  factory _$SubInfoInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$SubInfoInputImplFromJson(json);

  @override
  List<String> args;
  @override
  String name;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'SubInfoInput(args: $args, name: $name)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'SubInfoInput'))
      ..add(DiagnosticsProperty('args', args))
      ..add(DiagnosticsProperty('name', name));
  }

  /// Create a copy of SubInfoInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SubInfoInputImplCopyWith<_$SubInfoInputImpl> get copyWith =>
      __$$SubInfoInputImplCopyWithImpl<_$SubInfoInputImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SubInfoInputImplToJson(
      this,
    );
  }
}

abstract class _SubInfoInput extends SubInfoInput {
  factory _SubInfoInput({required List<String> args, required String name}) =
      _$SubInfoInputImpl;
  _SubInfoInput._() : super._();

  factory _SubInfoInput.fromJson(Map<String, dynamic> json) =
      _$SubInfoInputImpl.fromJson;

  @override
  List<String> get args;
  set args(List<String> value);
  @override
  String get name;
  set name(String value);

  /// Create a copy of SubInfoInput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SubInfoInputImplCopyWith<_$SubInfoInputImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

TableBooleanFilterInput _$TableBooleanFilterInputFromJson(
    Map<String, dynamic> json) {
  return _TableBooleanFilterInput.fromJson(json);
}

/// @nodoc
mixin _$TableBooleanFilterInput {
  bool? get eq => throw _privateConstructorUsedError;
  set eq(bool? value) => throw _privateConstructorUsedError;
  bool? get ne => throw _privateConstructorUsedError;
  set ne(bool? value) => throw _privateConstructorUsedError;

  /// Serializes this TableBooleanFilterInput to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TableBooleanFilterInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TableBooleanFilterInputCopyWith<TableBooleanFilterInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TableBooleanFilterInputCopyWith<$Res> {
  factory $TableBooleanFilterInputCopyWith(TableBooleanFilterInput value,
          $Res Function(TableBooleanFilterInput) then) =
      _$TableBooleanFilterInputCopyWithImpl<$Res, TableBooleanFilterInput>;
  @useResult
  $Res call({bool? eq, bool? ne});
}

/// @nodoc
class _$TableBooleanFilterInputCopyWithImpl<$Res,
        $Val extends TableBooleanFilterInput>
    implements $TableBooleanFilterInputCopyWith<$Res> {
  _$TableBooleanFilterInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TableBooleanFilterInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? eq = freezed,
    Object? ne = freezed,
  }) {
    return _then(_value.copyWith(
      eq: freezed == eq
          ? _value.eq
          : eq // ignore: cast_nullable_to_non_nullable
              as bool?,
      ne: freezed == ne
          ? _value.ne
          : ne // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TableBooleanFilterInputImplCopyWith<$Res>
    implements $TableBooleanFilterInputCopyWith<$Res> {
  factory _$$TableBooleanFilterInputImplCopyWith(
          _$TableBooleanFilterInputImpl value,
          $Res Function(_$TableBooleanFilterInputImpl) then) =
      __$$TableBooleanFilterInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool? eq, bool? ne});
}

/// @nodoc
class __$$TableBooleanFilterInputImplCopyWithImpl<$Res>
    extends _$TableBooleanFilterInputCopyWithImpl<$Res,
        _$TableBooleanFilterInputImpl>
    implements _$$TableBooleanFilterInputImplCopyWith<$Res> {
  __$$TableBooleanFilterInputImplCopyWithImpl(
      _$TableBooleanFilterInputImpl _value,
      $Res Function(_$TableBooleanFilterInputImpl) _then)
      : super(_value, _then);

  /// Create a copy of TableBooleanFilterInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? eq = freezed,
    Object? ne = freezed,
  }) {
    return _then(_$TableBooleanFilterInputImpl(
      eq: freezed == eq
          ? _value.eq
          : eq // ignore: cast_nullable_to_non_nullable
              as bool?,
      ne: freezed == ne
          ? _value.ne
          : ne // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TableBooleanFilterInputImpl extends _TableBooleanFilterInput
    with DiagnosticableTreeMixin {
  _$TableBooleanFilterInputImpl({this.eq, this.ne}) : super._();

  factory _$TableBooleanFilterInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$TableBooleanFilterInputImplFromJson(json);

  @override
  bool? eq;
  @override
  bool? ne;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'TableBooleanFilterInput(eq: $eq, ne: $ne)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'TableBooleanFilterInput'))
      ..add(DiagnosticsProperty('eq', eq))
      ..add(DiagnosticsProperty('ne', ne));
  }

  /// Create a copy of TableBooleanFilterInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TableBooleanFilterInputImplCopyWith<_$TableBooleanFilterInputImpl>
      get copyWith => __$$TableBooleanFilterInputImplCopyWithImpl<
          _$TableBooleanFilterInputImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TableBooleanFilterInputImplToJson(
      this,
    );
  }
}

abstract class _TableBooleanFilterInput extends TableBooleanFilterInput {
  factory _TableBooleanFilterInput({bool? eq, bool? ne}) =
      _$TableBooleanFilterInputImpl;
  _TableBooleanFilterInput._() : super._();

  factory _TableBooleanFilterInput.fromJson(Map<String, dynamic> json) =
      _$TableBooleanFilterInputImpl.fromJson;

  @override
  bool? get eq;
  set eq(bool? value);
  @override
  bool? get ne;
  set ne(bool? value);

  /// Create a copy of TableBooleanFilterInput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TableBooleanFilterInputImplCopyWith<_$TableBooleanFilterInputImpl>
      get copyWith => throw _privateConstructorUsedError;
}

TableFloatFilterInput _$TableFloatFilterInputFromJson(
    Map<String, dynamic> json) {
  return _TableFloatFilterInput.fromJson(json);
}

/// @nodoc
mixin _$TableFloatFilterInput {
  List<double?>? get between => throw _privateConstructorUsedError;
  set between(List<double?>? value) => throw _privateConstructorUsedError;
  double? get contains => throw _privateConstructorUsedError;
  set contains(double? value) => throw _privateConstructorUsedError;
  double? get eq => throw _privateConstructorUsedError;
  set eq(double? value) => throw _privateConstructorUsedError;
  double? get ge => throw _privateConstructorUsedError;
  set ge(double? value) => throw _privateConstructorUsedError;
  double? get gt => throw _privateConstructorUsedError;
  set gt(double? value) => throw _privateConstructorUsedError;
  double? get le => throw _privateConstructorUsedError;
  set le(double? value) => throw _privateConstructorUsedError;
  double? get lt => throw _privateConstructorUsedError;
  set lt(double? value) => throw _privateConstructorUsedError;
  double? get ne => throw _privateConstructorUsedError;
  set ne(double? value) => throw _privateConstructorUsedError;
  double? get notContains => throw _privateConstructorUsedError;
  set notContains(double? value) => throw _privateConstructorUsedError;

  /// Serializes this TableFloatFilterInput to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TableFloatFilterInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TableFloatFilterInputCopyWith<TableFloatFilterInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TableFloatFilterInputCopyWith<$Res> {
  factory $TableFloatFilterInputCopyWith(TableFloatFilterInput value,
          $Res Function(TableFloatFilterInput) then) =
      _$TableFloatFilterInputCopyWithImpl<$Res, TableFloatFilterInput>;
  @useResult
  $Res call(
      {List<double?>? between,
      double? contains,
      double? eq,
      double? ge,
      double? gt,
      double? le,
      double? lt,
      double? ne,
      double? notContains});
}

/// @nodoc
class _$TableFloatFilterInputCopyWithImpl<$Res,
        $Val extends TableFloatFilterInput>
    implements $TableFloatFilterInputCopyWith<$Res> {
  _$TableFloatFilterInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TableFloatFilterInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? between = freezed,
    Object? contains = freezed,
    Object? eq = freezed,
    Object? ge = freezed,
    Object? gt = freezed,
    Object? le = freezed,
    Object? lt = freezed,
    Object? ne = freezed,
    Object? notContains = freezed,
  }) {
    return _then(_value.copyWith(
      between: freezed == between
          ? _value.between
          : between // ignore: cast_nullable_to_non_nullable
              as List<double?>?,
      contains: freezed == contains
          ? _value.contains
          : contains // ignore: cast_nullable_to_non_nullable
              as double?,
      eq: freezed == eq
          ? _value.eq
          : eq // ignore: cast_nullable_to_non_nullable
              as double?,
      ge: freezed == ge
          ? _value.ge
          : ge // ignore: cast_nullable_to_non_nullable
              as double?,
      gt: freezed == gt
          ? _value.gt
          : gt // ignore: cast_nullable_to_non_nullable
              as double?,
      le: freezed == le
          ? _value.le
          : le // ignore: cast_nullable_to_non_nullable
              as double?,
      lt: freezed == lt
          ? _value.lt
          : lt // ignore: cast_nullable_to_non_nullable
              as double?,
      ne: freezed == ne
          ? _value.ne
          : ne // ignore: cast_nullable_to_non_nullable
              as double?,
      notContains: freezed == notContains
          ? _value.notContains
          : notContains // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TableFloatFilterInputImplCopyWith<$Res>
    implements $TableFloatFilterInputCopyWith<$Res> {
  factory _$$TableFloatFilterInputImplCopyWith(
          _$TableFloatFilterInputImpl value,
          $Res Function(_$TableFloatFilterInputImpl) then) =
      __$$TableFloatFilterInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<double?>? between,
      double? contains,
      double? eq,
      double? ge,
      double? gt,
      double? le,
      double? lt,
      double? ne,
      double? notContains});
}

/// @nodoc
class __$$TableFloatFilterInputImplCopyWithImpl<$Res>
    extends _$TableFloatFilterInputCopyWithImpl<$Res,
        _$TableFloatFilterInputImpl>
    implements _$$TableFloatFilterInputImplCopyWith<$Res> {
  __$$TableFloatFilterInputImplCopyWithImpl(_$TableFloatFilterInputImpl _value,
      $Res Function(_$TableFloatFilterInputImpl) _then)
      : super(_value, _then);

  /// Create a copy of TableFloatFilterInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? between = freezed,
    Object? contains = freezed,
    Object? eq = freezed,
    Object? ge = freezed,
    Object? gt = freezed,
    Object? le = freezed,
    Object? lt = freezed,
    Object? ne = freezed,
    Object? notContains = freezed,
  }) {
    return _then(_$TableFloatFilterInputImpl(
      between: freezed == between
          ? _value.between
          : between // ignore: cast_nullable_to_non_nullable
              as List<double?>?,
      contains: freezed == contains
          ? _value.contains
          : contains // ignore: cast_nullable_to_non_nullable
              as double?,
      eq: freezed == eq
          ? _value.eq
          : eq // ignore: cast_nullable_to_non_nullable
              as double?,
      ge: freezed == ge
          ? _value.ge
          : ge // ignore: cast_nullable_to_non_nullable
              as double?,
      gt: freezed == gt
          ? _value.gt
          : gt // ignore: cast_nullable_to_non_nullable
              as double?,
      le: freezed == le
          ? _value.le
          : le // ignore: cast_nullable_to_non_nullable
              as double?,
      lt: freezed == lt
          ? _value.lt
          : lt // ignore: cast_nullable_to_non_nullable
              as double?,
      ne: freezed == ne
          ? _value.ne
          : ne // ignore: cast_nullable_to_non_nullable
              as double?,
      notContains: freezed == notContains
          ? _value.notContains
          : notContains // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TableFloatFilterInputImpl extends _TableFloatFilterInput
    with DiagnosticableTreeMixin {
  _$TableFloatFilterInputImpl(
      {this.between,
      this.contains,
      this.eq,
      this.ge,
      this.gt,
      this.le,
      this.lt,
      this.ne,
      this.notContains})
      : super._();

  factory _$TableFloatFilterInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$TableFloatFilterInputImplFromJson(json);

  @override
  List<double?>? between;
  @override
  double? contains;
  @override
  double? eq;
  @override
  double? ge;
  @override
  double? gt;
  @override
  double? le;
  @override
  double? lt;
  @override
  double? ne;
  @override
  double? notContains;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'TableFloatFilterInput(between: $between, contains: $contains, eq: $eq, ge: $ge, gt: $gt, le: $le, lt: $lt, ne: $ne, notContains: $notContains)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'TableFloatFilterInput'))
      ..add(DiagnosticsProperty('between', between))
      ..add(DiagnosticsProperty('contains', contains))
      ..add(DiagnosticsProperty('eq', eq))
      ..add(DiagnosticsProperty('ge', ge))
      ..add(DiagnosticsProperty('gt', gt))
      ..add(DiagnosticsProperty('le', le))
      ..add(DiagnosticsProperty('lt', lt))
      ..add(DiagnosticsProperty('ne', ne))
      ..add(DiagnosticsProperty('notContains', notContains));
  }

  /// Create a copy of TableFloatFilterInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TableFloatFilterInputImplCopyWith<_$TableFloatFilterInputImpl>
      get copyWith => __$$TableFloatFilterInputImplCopyWithImpl<
          _$TableFloatFilterInputImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TableFloatFilterInputImplToJson(
      this,
    );
  }
}

abstract class _TableFloatFilterInput extends TableFloatFilterInput {
  factory _TableFloatFilterInput(
      {List<double?>? between,
      double? contains,
      double? eq,
      double? ge,
      double? gt,
      double? le,
      double? lt,
      double? ne,
      double? notContains}) = _$TableFloatFilterInputImpl;
  _TableFloatFilterInput._() : super._();

  factory _TableFloatFilterInput.fromJson(Map<String, dynamic> json) =
      _$TableFloatFilterInputImpl.fromJson;

  @override
  List<double?>? get between;
  set between(List<double?>? value);
  @override
  double? get contains;
  set contains(double? value);
  @override
  double? get eq;
  set eq(double? value);
  @override
  double? get ge;
  set ge(double? value);
  @override
  double? get gt;
  set gt(double? value);
  @override
  double? get le;
  set le(double? value);
  @override
  double? get lt;
  set lt(double? value);
  @override
  double? get ne;
  set ne(double? value);
  @override
  double? get notContains;
  set notContains(double? value);

  /// Create a copy of TableFloatFilterInput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TableFloatFilterInputImplCopyWith<_$TableFloatFilterInputImpl>
      get copyWith => throw _privateConstructorUsedError;
}

TableIdFilterInput _$TableIdFilterInputFromJson(Map<String, dynamic> json) {
  return _TableIdFilterInput.fromJson(json);
}

/// @nodoc
mixin _$TableIdFilterInput {
  String? get beginsWith => throw _privateConstructorUsedError;
  set beginsWith(String? value) => throw _privateConstructorUsedError;
  List<String?>? get between => throw _privateConstructorUsedError;
  set between(List<String?>? value) => throw _privateConstructorUsedError;
  String? get contains => throw _privateConstructorUsedError;
  set contains(String? value) => throw _privateConstructorUsedError;
  String? get eq => throw _privateConstructorUsedError;
  set eq(String? value) => throw _privateConstructorUsedError;
  String? get ge => throw _privateConstructorUsedError;
  set ge(String? value) => throw _privateConstructorUsedError;
  String? get gt => throw _privateConstructorUsedError;
  set gt(String? value) => throw _privateConstructorUsedError;
  String? get le => throw _privateConstructorUsedError;
  set le(String? value) => throw _privateConstructorUsedError;
  String? get lt => throw _privateConstructorUsedError;
  set lt(String? value) => throw _privateConstructorUsedError;
  String? get ne => throw _privateConstructorUsedError;
  set ne(String? value) => throw _privateConstructorUsedError;
  String? get notContains => throw _privateConstructorUsedError;
  set notContains(String? value) => throw _privateConstructorUsedError;

  /// Serializes this TableIdFilterInput to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TableIdFilterInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TableIdFilterInputCopyWith<TableIdFilterInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TableIdFilterInputCopyWith<$Res> {
  factory $TableIdFilterInputCopyWith(
          TableIdFilterInput value, $Res Function(TableIdFilterInput) then) =
      _$TableIdFilterInputCopyWithImpl<$Res, TableIdFilterInput>;
  @useResult
  $Res call(
      {String? beginsWith,
      List<String?>? between,
      String? contains,
      String? eq,
      String? ge,
      String? gt,
      String? le,
      String? lt,
      String? ne,
      String? notContains});
}

/// @nodoc
class _$TableIdFilterInputCopyWithImpl<$Res, $Val extends TableIdFilterInput>
    implements $TableIdFilterInputCopyWith<$Res> {
  _$TableIdFilterInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TableIdFilterInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? beginsWith = freezed,
    Object? between = freezed,
    Object? contains = freezed,
    Object? eq = freezed,
    Object? ge = freezed,
    Object? gt = freezed,
    Object? le = freezed,
    Object? lt = freezed,
    Object? ne = freezed,
    Object? notContains = freezed,
  }) {
    return _then(_value.copyWith(
      beginsWith: freezed == beginsWith
          ? _value.beginsWith
          : beginsWith // ignore: cast_nullable_to_non_nullable
              as String?,
      between: freezed == between
          ? _value.between
          : between // ignore: cast_nullable_to_non_nullable
              as List<String?>?,
      contains: freezed == contains
          ? _value.contains
          : contains // ignore: cast_nullable_to_non_nullable
              as String?,
      eq: freezed == eq
          ? _value.eq
          : eq // ignore: cast_nullable_to_non_nullable
              as String?,
      ge: freezed == ge
          ? _value.ge
          : ge // ignore: cast_nullable_to_non_nullable
              as String?,
      gt: freezed == gt
          ? _value.gt
          : gt // ignore: cast_nullable_to_non_nullable
              as String?,
      le: freezed == le
          ? _value.le
          : le // ignore: cast_nullable_to_non_nullable
              as String?,
      lt: freezed == lt
          ? _value.lt
          : lt // ignore: cast_nullable_to_non_nullable
              as String?,
      ne: freezed == ne
          ? _value.ne
          : ne // ignore: cast_nullable_to_non_nullable
              as String?,
      notContains: freezed == notContains
          ? _value.notContains
          : notContains // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TableIdFilterInputImplCopyWith<$Res>
    implements $TableIdFilterInputCopyWith<$Res> {
  factory _$$TableIdFilterInputImplCopyWith(_$TableIdFilterInputImpl value,
          $Res Function(_$TableIdFilterInputImpl) then) =
      __$$TableIdFilterInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? beginsWith,
      List<String?>? between,
      String? contains,
      String? eq,
      String? ge,
      String? gt,
      String? le,
      String? lt,
      String? ne,
      String? notContains});
}

/// @nodoc
class __$$TableIdFilterInputImplCopyWithImpl<$Res>
    extends _$TableIdFilterInputCopyWithImpl<$Res, _$TableIdFilterInputImpl>
    implements _$$TableIdFilterInputImplCopyWith<$Res> {
  __$$TableIdFilterInputImplCopyWithImpl(_$TableIdFilterInputImpl _value,
      $Res Function(_$TableIdFilterInputImpl) _then)
      : super(_value, _then);

  /// Create a copy of TableIdFilterInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? beginsWith = freezed,
    Object? between = freezed,
    Object? contains = freezed,
    Object? eq = freezed,
    Object? ge = freezed,
    Object? gt = freezed,
    Object? le = freezed,
    Object? lt = freezed,
    Object? ne = freezed,
    Object? notContains = freezed,
  }) {
    return _then(_$TableIdFilterInputImpl(
      beginsWith: freezed == beginsWith
          ? _value.beginsWith
          : beginsWith // ignore: cast_nullable_to_non_nullable
              as String?,
      between: freezed == between
          ? _value.between
          : between // ignore: cast_nullable_to_non_nullable
              as List<String?>?,
      contains: freezed == contains
          ? _value.contains
          : contains // ignore: cast_nullable_to_non_nullable
              as String?,
      eq: freezed == eq
          ? _value.eq
          : eq // ignore: cast_nullable_to_non_nullable
              as String?,
      ge: freezed == ge
          ? _value.ge
          : ge // ignore: cast_nullable_to_non_nullable
              as String?,
      gt: freezed == gt
          ? _value.gt
          : gt // ignore: cast_nullable_to_non_nullable
              as String?,
      le: freezed == le
          ? _value.le
          : le // ignore: cast_nullable_to_non_nullable
              as String?,
      lt: freezed == lt
          ? _value.lt
          : lt // ignore: cast_nullable_to_non_nullable
              as String?,
      ne: freezed == ne
          ? _value.ne
          : ne // ignore: cast_nullable_to_non_nullable
              as String?,
      notContains: freezed == notContains
          ? _value.notContains
          : notContains // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TableIdFilterInputImpl extends _TableIdFilterInput
    with DiagnosticableTreeMixin {
  _$TableIdFilterInputImpl(
      {this.beginsWith,
      this.between,
      this.contains,
      this.eq,
      this.ge,
      this.gt,
      this.le,
      this.lt,
      this.ne,
      this.notContains})
      : super._();

  factory _$TableIdFilterInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$TableIdFilterInputImplFromJson(json);

  @override
  String? beginsWith;
  @override
  List<String?>? between;
  @override
  String? contains;
  @override
  String? eq;
  @override
  String? ge;
  @override
  String? gt;
  @override
  String? le;
  @override
  String? lt;
  @override
  String? ne;
  @override
  String? notContains;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'TableIdFilterInput(beginsWith: $beginsWith, between: $between, contains: $contains, eq: $eq, ge: $ge, gt: $gt, le: $le, lt: $lt, ne: $ne, notContains: $notContains)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'TableIdFilterInput'))
      ..add(DiagnosticsProperty('beginsWith', beginsWith))
      ..add(DiagnosticsProperty('between', between))
      ..add(DiagnosticsProperty('contains', contains))
      ..add(DiagnosticsProperty('eq', eq))
      ..add(DiagnosticsProperty('ge', ge))
      ..add(DiagnosticsProperty('gt', gt))
      ..add(DiagnosticsProperty('le', le))
      ..add(DiagnosticsProperty('lt', lt))
      ..add(DiagnosticsProperty('ne', ne))
      ..add(DiagnosticsProperty('notContains', notContains));
  }

  /// Create a copy of TableIdFilterInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TableIdFilterInputImplCopyWith<_$TableIdFilterInputImpl> get copyWith =>
      __$$TableIdFilterInputImplCopyWithImpl<_$TableIdFilterInputImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TableIdFilterInputImplToJson(
      this,
    );
  }
}

abstract class _TableIdFilterInput extends TableIdFilterInput {
  factory _TableIdFilterInput(
      {String? beginsWith,
      List<String?>? between,
      String? contains,
      String? eq,
      String? ge,
      String? gt,
      String? le,
      String? lt,
      String? ne,
      String? notContains}) = _$TableIdFilterInputImpl;
  _TableIdFilterInput._() : super._();

  factory _TableIdFilterInput.fromJson(Map<String, dynamic> json) =
      _$TableIdFilterInputImpl.fromJson;

  @override
  String? get beginsWith;
  set beginsWith(String? value);
  @override
  List<String?>? get between;
  set between(List<String?>? value);
  @override
  String? get contains;
  set contains(String? value);
  @override
  String? get eq;
  set eq(String? value);
  @override
  String? get ge;
  set ge(String? value);
  @override
  String? get gt;
  set gt(String? value);
  @override
  String? get le;
  set le(String? value);
  @override
  String? get lt;
  set lt(String? value);
  @override
  String? get ne;
  set ne(String? value);
  @override
  String? get notContains;
  set notContains(String? value);

  /// Create a copy of TableIdFilterInput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TableIdFilterInputImplCopyWith<_$TableIdFilterInputImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

TableIntFilterInput _$TableIntFilterInputFromJson(Map<String, dynamic> json) {
  return _TableIntFilterInput.fromJson(json);
}

/// @nodoc
mixin _$TableIntFilterInput {
  List<int?>? get between => throw _privateConstructorUsedError;
  set between(List<int?>? value) => throw _privateConstructorUsedError;
  int? get contains => throw _privateConstructorUsedError;
  set contains(int? value) => throw _privateConstructorUsedError;
  int? get eq => throw _privateConstructorUsedError;
  set eq(int? value) => throw _privateConstructorUsedError;
  int? get ge => throw _privateConstructorUsedError;
  set ge(int? value) => throw _privateConstructorUsedError;
  int? get gt => throw _privateConstructorUsedError;
  set gt(int? value) => throw _privateConstructorUsedError;
  int? get le => throw _privateConstructorUsedError;
  set le(int? value) => throw _privateConstructorUsedError;
  int? get lt => throw _privateConstructorUsedError;
  set lt(int? value) => throw _privateConstructorUsedError;
  int? get ne => throw _privateConstructorUsedError;
  set ne(int? value) => throw _privateConstructorUsedError;
  int? get notContains => throw _privateConstructorUsedError;
  set notContains(int? value) => throw _privateConstructorUsedError;

  /// Serializes this TableIntFilterInput to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TableIntFilterInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TableIntFilterInputCopyWith<TableIntFilterInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TableIntFilterInputCopyWith<$Res> {
  factory $TableIntFilterInputCopyWith(
          TableIntFilterInput value, $Res Function(TableIntFilterInput) then) =
      _$TableIntFilterInputCopyWithImpl<$Res, TableIntFilterInput>;
  @useResult
  $Res call(
      {List<int?>? between,
      int? contains,
      int? eq,
      int? ge,
      int? gt,
      int? le,
      int? lt,
      int? ne,
      int? notContains});
}

/// @nodoc
class _$TableIntFilterInputCopyWithImpl<$Res, $Val extends TableIntFilterInput>
    implements $TableIntFilterInputCopyWith<$Res> {
  _$TableIntFilterInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TableIntFilterInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? between = freezed,
    Object? contains = freezed,
    Object? eq = freezed,
    Object? ge = freezed,
    Object? gt = freezed,
    Object? le = freezed,
    Object? lt = freezed,
    Object? ne = freezed,
    Object? notContains = freezed,
  }) {
    return _then(_value.copyWith(
      between: freezed == between
          ? _value.between
          : between // ignore: cast_nullable_to_non_nullable
              as List<int?>?,
      contains: freezed == contains
          ? _value.contains
          : contains // ignore: cast_nullable_to_non_nullable
              as int?,
      eq: freezed == eq
          ? _value.eq
          : eq // ignore: cast_nullable_to_non_nullable
              as int?,
      ge: freezed == ge
          ? _value.ge
          : ge // ignore: cast_nullable_to_non_nullable
              as int?,
      gt: freezed == gt
          ? _value.gt
          : gt // ignore: cast_nullable_to_non_nullable
              as int?,
      le: freezed == le
          ? _value.le
          : le // ignore: cast_nullable_to_non_nullable
              as int?,
      lt: freezed == lt
          ? _value.lt
          : lt // ignore: cast_nullable_to_non_nullable
              as int?,
      ne: freezed == ne
          ? _value.ne
          : ne // ignore: cast_nullable_to_non_nullable
              as int?,
      notContains: freezed == notContains
          ? _value.notContains
          : notContains // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TableIntFilterInputImplCopyWith<$Res>
    implements $TableIntFilterInputCopyWith<$Res> {
  factory _$$TableIntFilterInputImplCopyWith(_$TableIntFilterInputImpl value,
          $Res Function(_$TableIntFilterInputImpl) then) =
      __$$TableIntFilterInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<int?>? between,
      int? contains,
      int? eq,
      int? ge,
      int? gt,
      int? le,
      int? lt,
      int? ne,
      int? notContains});
}

/// @nodoc
class __$$TableIntFilterInputImplCopyWithImpl<$Res>
    extends _$TableIntFilterInputCopyWithImpl<$Res, _$TableIntFilterInputImpl>
    implements _$$TableIntFilterInputImplCopyWith<$Res> {
  __$$TableIntFilterInputImplCopyWithImpl(_$TableIntFilterInputImpl _value,
      $Res Function(_$TableIntFilterInputImpl) _then)
      : super(_value, _then);

  /// Create a copy of TableIntFilterInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? between = freezed,
    Object? contains = freezed,
    Object? eq = freezed,
    Object? ge = freezed,
    Object? gt = freezed,
    Object? le = freezed,
    Object? lt = freezed,
    Object? ne = freezed,
    Object? notContains = freezed,
  }) {
    return _then(_$TableIntFilterInputImpl(
      between: freezed == between
          ? _value.between
          : between // ignore: cast_nullable_to_non_nullable
              as List<int?>?,
      contains: freezed == contains
          ? _value.contains
          : contains // ignore: cast_nullable_to_non_nullable
              as int?,
      eq: freezed == eq
          ? _value.eq
          : eq // ignore: cast_nullable_to_non_nullable
              as int?,
      ge: freezed == ge
          ? _value.ge
          : ge // ignore: cast_nullable_to_non_nullable
              as int?,
      gt: freezed == gt
          ? _value.gt
          : gt // ignore: cast_nullable_to_non_nullable
              as int?,
      le: freezed == le
          ? _value.le
          : le // ignore: cast_nullable_to_non_nullable
              as int?,
      lt: freezed == lt
          ? _value.lt
          : lt // ignore: cast_nullable_to_non_nullable
              as int?,
      ne: freezed == ne
          ? _value.ne
          : ne // ignore: cast_nullable_to_non_nullable
              as int?,
      notContains: freezed == notContains
          ? _value.notContains
          : notContains // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TableIntFilterInputImpl extends _TableIntFilterInput
    with DiagnosticableTreeMixin {
  _$TableIntFilterInputImpl(
      {this.between,
      this.contains,
      this.eq,
      this.ge,
      this.gt,
      this.le,
      this.lt,
      this.ne,
      this.notContains})
      : super._();

  factory _$TableIntFilterInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$TableIntFilterInputImplFromJson(json);

  @override
  List<int?>? between;
  @override
  int? contains;
  @override
  int? eq;
  @override
  int? ge;
  @override
  int? gt;
  @override
  int? le;
  @override
  int? lt;
  @override
  int? ne;
  @override
  int? notContains;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'TableIntFilterInput(between: $between, contains: $contains, eq: $eq, ge: $ge, gt: $gt, le: $le, lt: $lt, ne: $ne, notContains: $notContains)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'TableIntFilterInput'))
      ..add(DiagnosticsProperty('between', between))
      ..add(DiagnosticsProperty('contains', contains))
      ..add(DiagnosticsProperty('eq', eq))
      ..add(DiagnosticsProperty('ge', ge))
      ..add(DiagnosticsProperty('gt', gt))
      ..add(DiagnosticsProperty('le', le))
      ..add(DiagnosticsProperty('lt', lt))
      ..add(DiagnosticsProperty('ne', ne))
      ..add(DiagnosticsProperty('notContains', notContains));
  }

  /// Create a copy of TableIntFilterInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TableIntFilterInputImplCopyWith<_$TableIntFilterInputImpl> get copyWith =>
      __$$TableIntFilterInputImplCopyWithImpl<_$TableIntFilterInputImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TableIntFilterInputImplToJson(
      this,
    );
  }
}

abstract class _TableIntFilterInput extends TableIntFilterInput {
  factory _TableIntFilterInput(
      {List<int?>? between,
      int? contains,
      int? eq,
      int? ge,
      int? gt,
      int? le,
      int? lt,
      int? ne,
      int? notContains}) = _$TableIntFilterInputImpl;
  _TableIntFilterInput._() : super._();

  factory _TableIntFilterInput.fromJson(Map<String, dynamic> json) =
      _$TableIntFilterInputImpl.fromJson;

  @override
  List<int?>? get between;
  set between(List<int?>? value);
  @override
  int? get contains;
  set contains(int? value);
  @override
  int? get eq;
  set eq(int? value);
  @override
  int? get ge;
  set ge(int? value);
  @override
  int? get gt;
  set gt(int? value);
  @override
  int? get le;
  set le(int? value);
  @override
  int? get lt;
  set lt(int? value);
  @override
  int? get ne;
  set ne(int? value);
  @override
  int? get notContains;
  set notContains(int? value);

  /// Create a copy of TableIntFilterInput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TableIntFilterInputImplCopyWith<_$TableIntFilterInputImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

TableStringFilterInput _$TableStringFilterInputFromJson(
    Map<String, dynamic> json) {
  return _TableStringFilterInput.fromJson(json);
}

/// @nodoc
mixin _$TableStringFilterInput {
  String? get beginsWith => throw _privateConstructorUsedError;
  set beginsWith(String? value) => throw _privateConstructorUsedError;
  List<String?>? get between => throw _privateConstructorUsedError;
  set between(List<String?>? value) => throw _privateConstructorUsedError;
  String? get contains => throw _privateConstructorUsedError;
  set contains(String? value) => throw _privateConstructorUsedError;
  String? get eq => throw _privateConstructorUsedError;
  set eq(String? value) => throw _privateConstructorUsedError;
  String? get ge => throw _privateConstructorUsedError;
  set ge(String? value) => throw _privateConstructorUsedError;
  String? get gt => throw _privateConstructorUsedError;
  set gt(String? value) => throw _privateConstructorUsedError;
  String? get le => throw _privateConstructorUsedError;
  set le(String? value) => throw _privateConstructorUsedError;
  String? get lt => throw _privateConstructorUsedError;
  set lt(String? value) => throw _privateConstructorUsedError;
  String? get ne => throw _privateConstructorUsedError;
  set ne(String? value) => throw _privateConstructorUsedError;
  String? get notContains => throw _privateConstructorUsedError;
  set notContains(String? value) => throw _privateConstructorUsedError;

  /// Serializes this TableStringFilterInput to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TableStringFilterInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TableStringFilterInputCopyWith<TableStringFilterInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TableStringFilterInputCopyWith<$Res> {
  factory $TableStringFilterInputCopyWith(TableStringFilterInput value,
          $Res Function(TableStringFilterInput) then) =
      _$TableStringFilterInputCopyWithImpl<$Res, TableStringFilterInput>;
  @useResult
  $Res call(
      {String? beginsWith,
      List<String?>? between,
      String? contains,
      String? eq,
      String? ge,
      String? gt,
      String? le,
      String? lt,
      String? ne,
      String? notContains});
}

/// @nodoc
class _$TableStringFilterInputCopyWithImpl<$Res,
        $Val extends TableStringFilterInput>
    implements $TableStringFilterInputCopyWith<$Res> {
  _$TableStringFilterInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TableStringFilterInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? beginsWith = freezed,
    Object? between = freezed,
    Object? contains = freezed,
    Object? eq = freezed,
    Object? ge = freezed,
    Object? gt = freezed,
    Object? le = freezed,
    Object? lt = freezed,
    Object? ne = freezed,
    Object? notContains = freezed,
  }) {
    return _then(_value.copyWith(
      beginsWith: freezed == beginsWith
          ? _value.beginsWith
          : beginsWith // ignore: cast_nullable_to_non_nullable
              as String?,
      between: freezed == between
          ? _value.between
          : between // ignore: cast_nullable_to_non_nullable
              as List<String?>?,
      contains: freezed == contains
          ? _value.contains
          : contains // ignore: cast_nullable_to_non_nullable
              as String?,
      eq: freezed == eq
          ? _value.eq
          : eq // ignore: cast_nullable_to_non_nullable
              as String?,
      ge: freezed == ge
          ? _value.ge
          : ge // ignore: cast_nullable_to_non_nullable
              as String?,
      gt: freezed == gt
          ? _value.gt
          : gt // ignore: cast_nullable_to_non_nullable
              as String?,
      le: freezed == le
          ? _value.le
          : le // ignore: cast_nullable_to_non_nullable
              as String?,
      lt: freezed == lt
          ? _value.lt
          : lt // ignore: cast_nullable_to_non_nullable
              as String?,
      ne: freezed == ne
          ? _value.ne
          : ne // ignore: cast_nullable_to_non_nullable
              as String?,
      notContains: freezed == notContains
          ? _value.notContains
          : notContains // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TableStringFilterInputImplCopyWith<$Res>
    implements $TableStringFilterInputCopyWith<$Res> {
  factory _$$TableStringFilterInputImplCopyWith(
          _$TableStringFilterInputImpl value,
          $Res Function(_$TableStringFilterInputImpl) then) =
      __$$TableStringFilterInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? beginsWith,
      List<String?>? between,
      String? contains,
      String? eq,
      String? ge,
      String? gt,
      String? le,
      String? lt,
      String? ne,
      String? notContains});
}

/// @nodoc
class __$$TableStringFilterInputImplCopyWithImpl<$Res>
    extends _$TableStringFilterInputCopyWithImpl<$Res,
        _$TableStringFilterInputImpl>
    implements _$$TableStringFilterInputImplCopyWith<$Res> {
  __$$TableStringFilterInputImplCopyWithImpl(
      _$TableStringFilterInputImpl _value,
      $Res Function(_$TableStringFilterInputImpl) _then)
      : super(_value, _then);

  /// Create a copy of TableStringFilterInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? beginsWith = freezed,
    Object? between = freezed,
    Object? contains = freezed,
    Object? eq = freezed,
    Object? ge = freezed,
    Object? gt = freezed,
    Object? le = freezed,
    Object? lt = freezed,
    Object? ne = freezed,
    Object? notContains = freezed,
  }) {
    return _then(_$TableStringFilterInputImpl(
      beginsWith: freezed == beginsWith
          ? _value.beginsWith
          : beginsWith // ignore: cast_nullable_to_non_nullable
              as String?,
      between: freezed == between
          ? _value.between
          : between // ignore: cast_nullable_to_non_nullable
              as List<String?>?,
      contains: freezed == contains
          ? _value.contains
          : contains // ignore: cast_nullable_to_non_nullable
              as String?,
      eq: freezed == eq
          ? _value.eq
          : eq // ignore: cast_nullable_to_non_nullable
              as String?,
      ge: freezed == ge
          ? _value.ge
          : ge // ignore: cast_nullable_to_non_nullable
              as String?,
      gt: freezed == gt
          ? _value.gt
          : gt // ignore: cast_nullable_to_non_nullable
              as String?,
      le: freezed == le
          ? _value.le
          : le // ignore: cast_nullable_to_non_nullable
              as String?,
      lt: freezed == lt
          ? _value.lt
          : lt // ignore: cast_nullable_to_non_nullable
              as String?,
      ne: freezed == ne
          ? _value.ne
          : ne // ignore: cast_nullable_to_non_nullable
              as String?,
      notContains: freezed == notContains
          ? _value.notContains
          : notContains // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TableStringFilterInputImpl extends _TableStringFilterInput
    with DiagnosticableTreeMixin {
  _$TableStringFilterInputImpl(
      {this.beginsWith,
      this.between,
      this.contains,
      this.eq,
      this.ge,
      this.gt,
      this.le,
      this.lt,
      this.ne,
      this.notContains})
      : super._();

  factory _$TableStringFilterInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$TableStringFilterInputImplFromJson(json);

  @override
  String? beginsWith;
  @override
  List<String?>? between;
  @override
  String? contains;
  @override
  String? eq;
  @override
  String? ge;
  @override
  String? gt;
  @override
  String? le;
  @override
  String? lt;
  @override
  String? ne;
  @override
  String? notContains;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'TableStringFilterInput(beginsWith: $beginsWith, between: $between, contains: $contains, eq: $eq, ge: $ge, gt: $gt, le: $le, lt: $lt, ne: $ne, notContains: $notContains)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'TableStringFilterInput'))
      ..add(DiagnosticsProperty('beginsWith', beginsWith))
      ..add(DiagnosticsProperty('between', between))
      ..add(DiagnosticsProperty('contains', contains))
      ..add(DiagnosticsProperty('eq', eq))
      ..add(DiagnosticsProperty('ge', ge))
      ..add(DiagnosticsProperty('gt', gt))
      ..add(DiagnosticsProperty('le', le))
      ..add(DiagnosticsProperty('lt', lt))
      ..add(DiagnosticsProperty('ne', ne))
      ..add(DiagnosticsProperty('notContains', notContains));
  }

  /// Create a copy of TableStringFilterInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TableStringFilterInputImplCopyWith<_$TableStringFilterInputImpl>
      get copyWith => __$$TableStringFilterInputImplCopyWithImpl<
          _$TableStringFilterInputImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TableStringFilterInputImplToJson(
      this,
    );
  }
}

abstract class _TableStringFilterInput extends TableStringFilterInput {
  factory _TableStringFilterInput(
      {String? beginsWith,
      List<String?>? between,
      String? contains,
      String? eq,
      String? ge,
      String? gt,
      String? le,
      String? lt,
      String? ne,
      String? notContains}) = _$TableStringFilterInputImpl;
  _TableStringFilterInput._() : super._();

  factory _TableStringFilterInput.fromJson(Map<String, dynamic> json) =
      _$TableStringFilterInputImpl.fromJson;

  @override
  String? get beginsWith;
  set beginsWith(String? value);
  @override
  List<String?>? get between;
  set between(List<String?>? value);
  @override
  String? get contains;
  set contains(String? value);
  @override
  String? get eq;
  set eq(String? value);
  @override
  String? get ge;
  set ge(String? value);
  @override
  String? get gt;
  set gt(String? value);
  @override
  String? get le;
  set le(String? value);
  @override
  String? get lt;
  set lt(String? value);
  @override
  String? get ne;
  set ne(String? value);
  @override
  String? get notContains;
  set notContains(String? value);

  /// Create a copy of TableStringFilterInput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TableStringFilterInputImplCopyWith<_$TableStringFilterInputImpl>
      get copyWith => throw _privateConstructorUsedError;
}

User _$UserFromJson(Map<String, dynamic> json) {
  return _User.fromJson(json);
}

/// @nodoc
mixin _$User {
  AppUsersConnection? get apps => throw _privateConstructorUsedError;
  String? get certificate => throw _privateConstructorUsedError;
  bool? get confirmed => throw _privateConstructorUsedError;
  DeviceUsersConnection? get devices => throw _privateConstructorUsedError;
  String? get emailAddress => throw _privateConstructorUsedError;
  List<Nv?>? get externalRefs => throw _privateConstructorUsedError;
  String? get familyName => throw _privateConstructorUsedError;
  String? get firstName => throw _privateConstructorUsedError;
  String? get middleName => throw _privateConstructorUsedError;
  String? get mobilePhone => throw _privateConstructorUsedError;
  OrgUsersConnection? get orgs => throw _privateConstructorUsedError;
  String? get preferredName => throw _privateConstructorUsedError;
  String? get publicKey => throw _privateConstructorUsedError;
  SpaceUsersConnection? get spaces => throw _privateConstructorUsedError;
  String? get universalConfig => throw _privateConstructorUsedError;
  @JsonKey(name: 'userID')
  String get userId => throw _privateConstructorUsedError;
  String? get userName => throw _privateConstructorUsedError;

  /// Serializes this User to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserCopyWith<User> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserCopyWith<$Res> {
  factory $UserCopyWith(User value, $Res Function(User) then) =
      _$UserCopyWithImpl<$Res, User>;
  @useResult
  $Res call(
      {AppUsersConnection? apps,
      String? certificate,
      bool? confirmed,
      DeviceUsersConnection? devices,
      String? emailAddress,
      List<Nv?>? externalRefs,
      String? familyName,
      String? firstName,
      String? middleName,
      String? mobilePhone,
      OrgUsersConnection? orgs,
      String? preferredName,
      String? publicKey,
      SpaceUsersConnection? spaces,
      String? universalConfig,
      @JsonKey(name: 'userID') String userId,
      String? userName});

  $AppUsersConnectionCopyWith<$Res>? get apps;
  $DeviceUsersConnectionCopyWith<$Res>? get devices;
  $OrgUsersConnectionCopyWith<$Res>? get orgs;
  $SpaceUsersConnectionCopyWith<$Res>? get spaces;
}

/// @nodoc
class _$UserCopyWithImpl<$Res, $Val extends User>
    implements $UserCopyWith<$Res> {
  _$UserCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? apps = freezed,
    Object? certificate = freezed,
    Object? confirmed = freezed,
    Object? devices = freezed,
    Object? emailAddress = freezed,
    Object? externalRefs = freezed,
    Object? familyName = freezed,
    Object? firstName = freezed,
    Object? middleName = freezed,
    Object? mobilePhone = freezed,
    Object? orgs = freezed,
    Object? preferredName = freezed,
    Object? publicKey = freezed,
    Object? spaces = freezed,
    Object? universalConfig = freezed,
    Object? userId = null,
    Object? userName = freezed,
  }) {
    return _then(_value.copyWith(
      apps: freezed == apps
          ? _value.apps
          : apps // ignore: cast_nullable_to_non_nullable
              as AppUsersConnection?,
      certificate: freezed == certificate
          ? _value.certificate
          : certificate // ignore: cast_nullable_to_non_nullable
              as String?,
      confirmed: freezed == confirmed
          ? _value.confirmed
          : confirmed // ignore: cast_nullable_to_non_nullable
              as bool?,
      devices: freezed == devices
          ? _value.devices
          : devices // ignore: cast_nullable_to_non_nullable
              as DeviceUsersConnection?,
      emailAddress: freezed == emailAddress
          ? _value.emailAddress
          : emailAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      externalRefs: freezed == externalRefs
          ? _value.externalRefs
          : externalRefs // ignore: cast_nullable_to_non_nullable
              as List<Nv?>?,
      familyName: freezed == familyName
          ? _value.familyName
          : familyName // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      middleName: freezed == middleName
          ? _value.middleName
          : middleName // ignore: cast_nullable_to_non_nullable
              as String?,
      mobilePhone: freezed == mobilePhone
          ? _value.mobilePhone
          : mobilePhone // ignore: cast_nullable_to_non_nullable
              as String?,
      orgs: freezed == orgs
          ? _value.orgs
          : orgs // ignore: cast_nullable_to_non_nullable
              as OrgUsersConnection?,
      preferredName: freezed == preferredName
          ? _value.preferredName
          : preferredName // ignore: cast_nullable_to_non_nullable
              as String?,
      publicKey: freezed == publicKey
          ? _value.publicKey
          : publicKey // ignore: cast_nullable_to_non_nullable
              as String?,
      spaces: freezed == spaces
          ? _value.spaces
          : spaces // ignore: cast_nullable_to_non_nullable
              as SpaceUsersConnection?,
      universalConfig: freezed == universalConfig
          ? _value.universalConfig
          : universalConfig // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      userName: freezed == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppUsersConnectionCopyWith<$Res>? get apps {
    if (_value.apps == null) {
      return null;
    }

    return $AppUsersConnectionCopyWith<$Res>(_value.apps!, (value) {
      return _then(_value.copyWith(apps: value) as $Val);
    });
  }

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DeviceUsersConnectionCopyWith<$Res>? get devices {
    if (_value.devices == null) {
      return null;
    }

    return $DeviceUsersConnectionCopyWith<$Res>(_value.devices!, (value) {
      return _then(_value.copyWith(devices: value) as $Val);
    });
  }

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OrgUsersConnectionCopyWith<$Res>? get orgs {
    if (_value.orgs == null) {
      return null;
    }

    return $OrgUsersConnectionCopyWith<$Res>(_value.orgs!, (value) {
      return _then(_value.copyWith(orgs: value) as $Val);
    });
  }

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SpaceUsersConnectionCopyWith<$Res>? get spaces {
    if (_value.spaces == null) {
      return null;
    }

    return $SpaceUsersConnectionCopyWith<$Res>(_value.spaces!, (value) {
      return _then(_value.copyWith(spaces: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$UserImplCopyWith<$Res> implements $UserCopyWith<$Res> {
  factory _$$UserImplCopyWith(
          _$UserImpl value, $Res Function(_$UserImpl) then) =
      __$$UserImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {AppUsersConnection? apps,
      String? certificate,
      bool? confirmed,
      DeviceUsersConnection? devices,
      String? emailAddress,
      List<Nv?>? externalRefs,
      String? familyName,
      String? firstName,
      String? middleName,
      String? mobilePhone,
      OrgUsersConnection? orgs,
      String? preferredName,
      String? publicKey,
      SpaceUsersConnection? spaces,
      String? universalConfig,
      @JsonKey(name: 'userID') String userId,
      String? userName});

  @override
  $AppUsersConnectionCopyWith<$Res>? get apps;
  @override
  $DeviceUsersConnectionCopyWith<$Res>? get devices;
  @override
  $OrgUsersConnectionCopyWith<$Res>? get orgs;
  @override
  $SpaceUsersConnectionCopyWith<$Res>? get spaces;
}

/// @nodoc
class __$$UserImplCopyWithImpl<$Res>
    extends _$UserCopyWithImpl<$Res, _$UserImpl>
    implements _$$UserImplCopyWith<$Res> {
  __$$UserImplCopyWithImpl(_$UserImpl _value, $Res Function(_$UserImpl) _then)
      : super(_value, _then);

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? apps = freezed,
    Object? certificate = freezed,
    Object? confirmed = freezed,
    Object? devices = freezed,
    Object? emailAddress = freezed,
    Object? externalRefs = freezed,
    Object? familyName = freezed,
    Object? firstName = freezed,
    Object? middleName = freezed,
    Object? mobilePhone = freezed,
    Object? orgs = freezed,
    Object? preferredName = freezed,
    Object? publicKey = freezed,
    Object? spaces = freezed,
    Object? universalConfig = freezed,
    Object? userId = null,
    Object? userName = freezed,
  }) {
    return _then(_$UserImpl(
      apps: freezed == apps
          ? _value.apps
          : apps // ignore: cast_nullable_to_non_nullable
              as AppUsersConnection?,
      certificate: freezed == certificate
          ? _value.certificate
          : certificate // ignore: cast_nullable_to_non_nullable
              as String?,
      confirmed: freezed == confirmed
          ? _value.confirmed
          : confirmed // ignore: cast_nullable_to_non_nullable
              as bool?,
      devices: freezed == devices
          ? _value.devices
          : devices // ignore: cast_nullable_to_non_nullable
              as DeviceUsersConnection?,
      emailAddress: freezed == emailAddress
          ? _value.emailAddress
          : emailAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      externalRefs: freezed == externalRefs
          ? _value._externalRefs
          : externalRefs // ignore: cast_nullable_to_non_nullable
              as List<Nv?>?,
      familyName: freezed == familyName
          ? _value.familyName
          : familyName // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      middleName: freezed == middleName
          ? _value.middleName
          : middleName // ignore: cast_nullable_to_non_nullable
              as String?,
      mobilePhone: freezed == mobilePhone
          ? _value.mobilePhone
          : mobilePhone // ignore: cast_nullable_to_non_nullable
              as String?,
      orgs: freezed == orgs
          ? _value.orgs
          : orgs // ignore: cast_nullable_to_non_nullable
              as OrgUsersConnection?,
      preferredName: freezed == preferredName
          ? _value.preferredName
          : preferredName // ignore: cast_nullable_to_non_nullable
              as String?,
      publicKey: freezed == publicKey
          ? _value.publicKey
          : publicKey // ignore: cast_nullable_to_non_nullable
              as String?,
      spaces: freezed == spaces
          ? _value.spaces
          : spaces // ignore: cast_nullable_to_non_nullable
              as SpaceUsersConnection?,
      universalConfig: freezed == universalConfig
          ? _value.universalConfig
          : universalConfig // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      userName: freezed == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserImpl extends _User with DiagnosticableTreeMixin {
  _$UserImpl(
      {this.apps,
      this.certificate,
      this.confirmed,
      this.devices,
      this.emailAddress,
      final List<Nv?>? externalRefs,
      this.familyName,
      this.firstName,
      this.middleName,
      this.mobilePhone,
      this.orgs,
      this.preferredName,
      this.publicKey,
      this.spaces,
      this.universalConfig,
      @JsonKey(name: 'userID') required this.userId,
      this.userName})
      : _externalRefs = externalRefs,
        super._();

  factory _$UserImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserImplFromJson(json);

  @override
  final AppUsersConnection? apps;
  @override
  final String? certificate;
  @override
  final bool? confirmed;
  @override
  final DeviceUsersConnection? devices;
  @override
  final String? emailAddress;
  final List<Nv?>? _externalRefs;
  @override
  List<Nv?>? get externalRefs {
    final value = _externalRefs;
    if (value == null) return null;
    if (_externalRefs is EqualUnmodifiableListView) return _externalRefs;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? familyName;
  @override
  final String? firstName;
  @override
  final String? middleName;
  @override
  final String? mobilePhone;
  @override
  final OrgUsersConnection? orgs;
  @override
  final String? preferredName;
  @override
  final String? publicKey;
  @override
  final SpaceUsersConnection? spaces;
  @override
  final String? universalConfig;
  @override
  @JsonKey(name: 'userID')
  final String userId;
  @override
  final String? userName;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'User(apps: $apps, certificate: $certificate, confirmed: $confirmed, devices: $devices, emailAddress: $emailAddress, externalRefs: $externalRefs, familyName: $familyName, firstName: $firstName, middleName: $middleName, mobilePhone: $mobilePhone, orgs: $orgs, preferredName: $preferredName, publicKey: $publicKey, spaces: $spaces, universalConfig: $universalConfig, userId: $userId, userName: $userName)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'User'))
      ..add(DiagnosticsProperty('apps', apps))
      ..add(DiagnosticsProperty('certificate', certificate))
      ..add(DiagnosticsProperty('confirmed', confirmed))
      ..add(DiagnosticsProperty('devices', devices))
      ..add(DiagnosticsProperty('emailAddress', emailAddress))
      ..add(DiagnosticsProperty('externalRefs', externalRefs))
      ..add(DiagnosticsProperty('familyName', familyName))
      ..add(DiagnosticsProperty('firstName', firstName))
      ..add(DiagnosticsProperty('middleName', middleName))
      ..add(DiagnosticsProperty('mobilePhone', mobilePhone))
      ..add(DiagnosticsProperty('orgs', orgs))
      ..add(DiagnosticsProperty('preferredName', preferredName))
      ..add(DiagnosticsProperty('publicKey', publicKey))
      ..add(DiagnosticsProperty('spaces', spaces))
      ..add(DiagnosticsProperty('universalConfig', universalConfig))
      ..add(DiagnosticsProperty('userId', userId))
      ..add(DiagnosticsProperty('userName', userName));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserImpl &&
            (identical(other.apps, apps) || other.apps == apps) &&
            (identical(other.certificate, certificate) ||
                other.certificate == certificate) &&
            (identical(other.confirmed, confirmed) ||
                other.confirmed == confirmed) &&
            (identical(other.devices, devices) || other.devices == devices) &&
            (identical(other.emailAddress, emailAddress) ||
                other.emailAddress == emailAddress) &&
            const DeepCollectionEquality()
                .equals(other._externalRefs, _externalRefs) &&
            (identical(other.familyName, familyName) ||
                other.familyName == familyName) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.middleName, middleName) ||
                other.middleName == middleName) &&
            (identical(other.mobilePhone, mobilePhone) ||
                other.mobilePhone == mobilePhone) &&
            (identical(other.orgs, orgs) || other.orgs == orgs) &&
            (identical(other.preferredName, preferredName) ||
                other.preferredName == preferredName) &&
            (identical(other.publicKey, publicKey) ||
                other.publicKey == publicKey) &&
            (identical(other.spaces, spaces) || other.spaces == spaces) &&
            (identical(other.universalConfig, universalConfig) ||
                other.universalConfig == universalConfig) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userName, userName) ||
                other.userName == userName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      apps,
      certificate,
      confirmed,
      devices,
      emailAddress,
      const DeepCollectionEquality().hash(_externalRefs),
      familyName,
      firstName,
      middleName,
      mobilePhone,
      orgs,
      preferredName,
      publicKey,
      spaces,
      universalConfig,
      userId,
      userName);

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserImplCopyWith<_$UserImpl> get copyWith =>
      __$$UserImplCopyWithImpl<_$UserImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserImplToJson(
      this,
    );
  }
}

abstract class _User extends User {
  factory _User(
      {final AppUsersConnection? apps,
      final String? certificate,
      final bool? confirmed,
      final DeviceUsersConnection? devices,
      final String? emailAddress,
      final List<Nv?>? externalRefs,
      final String? familyName,
      final String? firstName,
      final String? middleName,
      final String? mobilePhone,
      final OrgUsersConnection? orgs,
      final String? preferredName,
      final String? publicKey,
      final SpaceUsersConnection? spaces,
      final String? universalConfig,
      @JsonKey(name: 'userID') required final String userId,
      final String? userName}) = _$UserImpl;
  _User._() : super._();

  factory _User.fromJson(Map<String, dynamic> json) = _$UserImpl.fromJson;

  @override
  AppUsersConnection? get apps;
  @override
  String? get certificate;
  @override
  bool? get confirmed;
  @override
  DeviceUsersConnection? get devices;
  @override
  String? get emailAddress;
  @override
  List<Nv?>? get externalRefs;
  @override
  String? get familyName;
  @override
  String? get firstName;
  @override
  String? get middleName;
  @override
  String? get mobilePhone;
  @override
  OrgUsersConnection? get orgs;
  @override
  String? get preferredName;
  @override
  String? get publicKey;
  @override
  SpaceUsersConnection? get spaces;
  @override
  String? get universalConfig;
  @override
  @JsonKey(name: 'userID')
  String get userId;
  @override
  String? get userName;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserImplCopyWith<_$UserImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UserAccessConfig _$UserAccessConfigFromJson(Map<String, dynamic> json) {
  return _UserAccessConfig.fromJson(json);
}

/// @nodoc
mixin _$UserAccessConfig {
  bool? get viewed => throw _privateConstructorUsedError;
  set viewed(bool? value) => throw _privateConstructorUsedError;
  String? get wgConfig => throw _privateConstructorUsedError;
  set wgConfig(String? value) => throw _privateConstructorUsedError;
  String? get wgConfigName => throw _privateConstructorUsedError;
  set wgConfigName(String? value) => throw _privateConstructorUsedError;
  int? get wgExpirationTimeout => throw _privateConstructorUsedError;
  set wgExpirationTimeout(int? value) => throw _privateConstructorUsedError;
  int? get wgInactivityTimeout => throw _privateConstructorUsedError;
  set wgInactivityTimeout(int? value) => throw _privateConstructorUsedError;

  /// Serializes this UserAccessConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserAccessConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserAccessConfigCopyWith<UserAccessConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserAccessConfigCopyWith<$Res> {
  factory $UserAccessConfigCopyWith(
          UserAccessConfig value, $Res Function(UserAccessConfig) then) =
      _$UserAccessConfigCopyWithImpl<$Res, UserAccessConfig>;
  @useResult
  $Res call(
      {bool? viewed,
      String? wgConfig,
      String? wgConfigName,
      int? wgExpirationTimeout,
      int? wgInactivityTimeout});
}

/// @nodoc
class _$UserAccessConfigCopyWithImpl<$Res, $Val extends UserAccessConfig>
    implements $UserAccessConfigCopyWith<$Res> {
  _$UserAccessConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserAccessConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? viewed = freezed,
    Object? wgConfig = freezed,
    Object? wgConfigName = freezed,
    Object? wgExpirationTimeout = freezed,
    Object? wgInactivityTimeout = freezed,
  }) {
    return _then(_value.copyWith(
      viewed: freezed == viewed
          ? _value.viewed
          : viewed // ignore: cast_nullable_to_non_nullable
              as bool?,
      wgConfig: freezed == wgConfig
          ? _value.wgConfig
          : wgConfig // ignore: cast_nullable_to_non_nullable
              as String?,
      wgConfigName: freezed == wgConfigName
          ? _value.wgConfigName
          : wgConfigName // ignore: cast_nullable_to_non_nullable
              as String?,
      wgExpirationTimeout: freezed == wgExpirationTimeout
          ? _value.wgExpirationTimeout
          : wgExpirationTimeout // ignore: cast_nullable_to_non_nullable
              as int?,
      wgInactivityTimeout: freezed == wgInactivityTimeout
          ? _value.wgInactivityTimeout
          : wgInactivityTimeout // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserAccessConfigImplCopyWith<$Res>
    implements $UserAccessConfigCopyWith<$Res> {
  factory _$$UserAccessConfigImplCopyWith(_$UserAccessConfigImpl value,
          $Res Function(_$UserAccessConfigImpl) then) =
      __$$UserAccessConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool? viewed,
      String? wgConfig,
      String? wgConfigName,
      int? wgExpirationTimeout,
      int? wgInactivityTimeout});
}

/// @nodoc
class __$$UserAccessConfigImplCopyWithImpl<$Res>
    extends _$UserAccessConfigCopyWithImpl<$Res, _$UserAccessConfigImpl>
    implements _$$UserAccessConfigImplCopyWith<$Res> {
  __$$UserAccessConfigImplCopyWithImpl(_$UserAccessConfigImpl _value,
      $Res Function(_$UserAccessConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserAccessConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? viewed = freezed,
    Object? wgConfig = freezed,
    Object? wgConfigName = freezed,
    Object? wgExpirationTimeout = freezed,
    Object? wgInactivityTimeout = freezed,
  }) {
    return _then(_$UserAccessConfigImpl(
      viewed: freezed == viewed
          ? _value.viewed
          : viewed // ignore: cast_nullable_to_non_nullable
              as bool?,
      wgConfig: freezed == wgConfig
          ? _value.wgConfig
          : wgConfig // ignore: cast_nullable_to_non_nullable
              as String?,
      wgConfigName: freezed == wgConfigName
          ? _value.wgConfigName
          : wgConfigName // ignore: cast_nullable_to_non_nullable
              as String?,
      wgExpirationTimeout: freezed == wgExpirationTimeout
          ? _value.wgExpirationTimeout
          : wgExpirationTimeout // ignore: cast_nullable_to_non_nullable
              as int?,
      wgInactivityTimeout: freezed == wgInactivityTimeout
          ? _value.wgInactivityTimeout
          : wgInactivityTimeout // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserAccessConfigImpl extends _UserAccessConfig
    with DiagnosticableTreeMixin {
  _$UserAccessConfigImpl(
      {this.viewed,
      this.wgConfig,
      this.wgConfigName,
      this.wgExpirationTimeout,
      this.wgInactivityTimeout})
      : super._();

  factory _$UserAccessConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserAccessConfigImplFromJson(json);

  @override
  bool? viewed;
  @override
  String? wgConfig;
  @override
  String? wgConfigName;
  @override
  int? wgExpirationTimeout;
  @override
  int? wgInactivityTimeout;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'UserAccessConfig(viewed: $viewed, wgConfig: $wgConfig, wgConfigName: $wgConfigName, wgExpirationTimeout: $wgExpirationTimeout, wgInactivityTimeout: $wgInactivityTimeout)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'UserAccessConfig'))
      ..add(DiagnosticsProperty('viewed', viewed))
      ..add(DiagnosticsProperty('wgConfig', wgConfig))
      ..add(DiagnosticsProperty('wgConfigName', wgConfigName))
      ..add(DiagnosticsProperty('wgExpirationTimeout', wgExpirationTimeout))
      ..add(DiagnosticsProperty('wgInactivityTimeout', wgInactivityTimeout));
  }

  /// Create a copy of UserAccessConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserAccessConfigImplCopyWith<_$UserAccessConfigImpl> get copyWith =>
      __$$UserAccessConfigImplCopyWithImpl<_$UserAccessConfigImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserAccessConfigImplToJson(
      this,
    );
  }
}

abstract class _UserAccessConfig extends UserAccessConfig {
  factory _UserAccessConfig(
      {bool? viewed,
      String? wgConfig,
      String? wgConfigName,
      int? wgExpirationTimeout,
      int? wgInactivityTimeout}) = _$UserAccessConfigImpl;
  _UserAccessConfig._() : super._();

  factory _UserAccessConfig.fromJson(Map<String, dynamic> json) =
      _$UserAccessConfigImpl.fromJson;

  @override
  bool? get viewed;
  set viewed(bool? value);
  @override
  String? get wgConfig;
  set wgConfig(String? value);
  @override
  String? get wgConfigName;
  set wgConfigName(String? value);
  @override
  int? get wgExpirationTimeout;
  set wgExpirationTimeout(int? value);
  @override
  int? get wgInactivityTimeout;
  set wgInactivityTimeout(int? value);

  /// Create a copy of UserAccessConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserAccessConfigImplCopyWith<_$UserAccessConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UserRef _$UserRefFromJson(Map<String, dynamic> json) {
  return _UserRef.fromJson(json);
}

/// @nodoc
mixin _$UserRef {
  String? get familyName => throw _privateConstructorUsedError;
  String? get firstName => throw _privateConstructorUsedError;
  String? get middleName => throw _privateConstructorUsedError;
  @JsonKey(name: 'userID')
  String get userId => throw _privateConstructorUsedError;
  String? get userName => throw _privateConstructorUsedError;

  /// Serializes this UserRef to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserRef
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserRefCopyWith<UserRef> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserRefCopyWith<$Res> {
  factory $UserRefCopyWith(UserRef value, $Res Function(UserRef) then) =
      _$UserRefCopyWithImpl<$Res, UserRef>;
  @useResult
  $Res call(
      {String? familyName,
      String? firstName,
      String? middleName,
      @JsonKey(name: 'userID') String userId,
      String? userName});
}

/// @nodoc
class _$UserRefCopyWithImpl<$Res, $Val extends UserRef>
    implements $UserRefCopyWith<$Res> {
  _$UserRefCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserRef
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? familyName = freezed,
    Object? firstName = freezed,
    Object? middleName = freezed,
    Object? userId = null,
    Object? userName = freezed,
  }) {
    return _then(_value.copyWith(
      familyName: freezed == familyName
          ? _value.familyName
          : familyName // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      middleName: freezed == middleName
          ? _value.middleName
          : middleName // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      userName: freezed == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserRefImplCopyWith<$Res> implements $UserRefCopyWith<$Res> {
  factory _$$UserRefImplCopyWith(
          _$UserRefImpl value, $Res Function(_$UserRefImpl) then) =
      __$$UserRefImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? familyName,
      String? firstName,
      String? middleName,
      @JsonKey(name: 'userID') String userId,
      String? userName});
}

/// @nodoc
class __$$UserRefImplCopyWithImpl<$Res>
    extends _$UserRefCopyWithImpl<$Res, _$UserRefImpl>
    implements _$$UserRefImplCopyWith<$Res> {
  __$$UserRefImplCopyWithImpl(
      _$UserRefImpl _value, $Res Function(_$UserRefImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserRef
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? familyName = freezed,
    Object? firstName = freezed,
    Object? middleName = freezed,
    Object? userId = null,
    Object? userName = freezed,
  }) {
    return _then(_$UserRefImpl(
      familyName: freezed == familyName
          ? _value.familyName
          : familyName // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      middleName: freezed == middleName
          ? _value.middleName
          : middleName // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      userName: freezed == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserRefImpl extends _UserRef with DiagnosticableTreeMixin {
  _$UserRefImpl(
      {this.familyName,
      this.firstName,
      this.middleName,
      @JsonKey(name: 'userID') required this.userId,
      this.userName})
      : super._();

  factory _$UserRefImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserRefImplFromJson(json);

  @override
  final String? familyName;
  @override
  final String? firstName;
  @override
  final String? middleName;
  @override
  @JsonKey(name: 'userID')
  final String userId;
  @override
  final String? userName;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'UserRef(familyName: $familyName, firstName: $firstName, middleName: $middleName, userId: $userId, userName: $userName)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'UserRef'))
      ..add(DiagnosticsProperty('familyName', familyName))
      ..add(DiagnosticsProperty('firstName', firstName))
      ..add(DiagnosticsProperty('middleName', middleName))
      ..add(DiagnosticsProperty('userId', userId))
      ..add(DiagnosticsProperty('userName', userName));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserRefImpl &&
            (identical(other.familyName, familyName) ||
                other.familyName == familyName) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.middleName, middleName) ||
                other.middleName == middleName) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userName, userName) ||
                other.userName == userName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, familyName, firstName, middleName, userId, userName);

  /// Create a copy of UserRef
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserRefImplCopyWith<_$UserRefImpl> get copyWith =>
      __$$UserRefImplCopyWithImpl<_$UserRefImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserRefImplToJson(
      this,
    );
  }
}

abstract class _UserRef extends UserRef {
  factory _UserRef(
      {final String? familyName,
      final String? firstName,
      final String? middleName,
      @JsonKey(name: 'userID') required final String userId,
      final String? userName}) = _$UserRefImpl;
  _UserRef._() : super._();

  factory _UserRef.fromJson(Map<String, dynamic> json) = _$UserRefImpl.fromJson;

  @override
  String? get familyName;
  @override
  String? get firstName;
  @override
  String? get middleName;
  @override
  @JsonKey(name: 'userID')
  String get userId;
  @override
  String? get userName;

  /// Create a copy of UserRef
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserRefImplCopyWith<_$UserRefImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UserSearchFilterInput _$UserSearchFilterInputFromJson(
    Map<String, dynamic> json) {
  return _UserSearchFilterInput.fromJson(json);
}

/// @nodoc
mixin _$UserSearchFilterInput {
  String? get emailAddress => throw _privateConstructorUsedError;
  set emailAddress(String? value) => throw _privateConstructorUsedError;
  String? get userName => throw _privateConstructorUsedError;
  set userName(String? value) => throw _privateConstructorUsedError;

  /// Serializes this UserSearchFilterInput to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserSearchFilterInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserSearchFilterInputCopyWith<UserSearchFilterInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserSearchFilterInputCopyWith<$Res> {
  factory $UserSearchFilterInputCopyWith(UserSearchFilterInput value,
          $Res Function(UserSearchFilterInput) then) =
      _$UserSearchFilterInputCopyWithImpl<$Res, UserSearchFilterInput>;
  @useResult
  $Res call({String? emailAddress, String? userName});
}

/// @nodoc
class _$UserSearchFilterInputCopyWithImpl<$Res,
        $Val extends UserSearchFilterInput>
    implements $UserSearchFilterInputCopyWith<$Res> {
  _$UserSearchFilterInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserSearchFilterInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? emailAddress = freezed,
    Object? userName = freezed,
  }) {
    return _then(_value.copyWith(
      emailAddress: freezed == emailAddress
          ? _value.emailAddress
          : emailAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      userName: freezed == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserSearchFilterInputImplCopyWith<$Res>
    implements $UserSearchFilterInputCopyWith<$Res> {
  factory _$$UserSearchFilterInputImplCopyWith(
          _$UserSearchFilterInputImpl value,
          $Res Function(_$UserSearchFilterInputImpl) then) =
      __$$UserSearchFilterInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? emailAddress, String? userName});
}

/// @nodoc
class __$$UserSearchFilterInputImplCopyWithImpl<$Res>
    extends _$UserSearchFilterInputCopyWithImpl<$Res,
        _$UserSearchFilterInputImpl>
    implements _$$UserSearchFilterInputImplCopyWith<$Res> {
  __$$UserSearchFilterInputImplCopyWithImpl(_$UserSearchFilterInputImpl _value,
      $Res Function(_$UserSearchFilterInputImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserSearchFilterInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? emailAddress = freezed,
    Object? userName = freezed,
  }) {
    return _then(_$UserSearchFilterInputImpl(
      emailAddress: freezed == emailAddress
          ? _value.emailAddress
          : emailAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      userName: freezed == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserSearchFilterInputImpl extends _UserSearchFilterInput
    with DiagnosticableTreeMixin {
  _$UserSearchFilterInputImpl({this.emailAddress, this.userName}) : super._();

  factory _$UserSearchFilterInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserSearchFilterInputImplFromJson(json);

  @override
  String? emailAddress;
  @override
  String? userName;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'UserSearchFilterInput(emailAddress: $emailAddress, userName: $userName)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'UserSearchFilterInput'))
      ..add(DiagnosticsProperty('emailAddress', emailAddress))
      ..add(DiagnosticsProperty('userName', userName));
  }

  /// Create a copy of UserSearchFilterInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserSearchFilterInputImplCopyWith<_$UserSearchFilterInputImpl>
      get copyWith => __$$UserSearchFilterInputImplCopyWithImpl<
          _$UserSearchFilterInputImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserSearchFilterInputImplToJson(
      this,
    );
  }
}

abstract class _UserSearchFilterInput extends UserSearchFilterInput {
  factory _UserSearchFilterInput({String? emailAddress, String? userName}) =
      _$UserSearchFilterInputImpl;
  _UserSearchFilterInput._() : super._();

  factory _UserSearchFilterInput.fromJson(Map<String, dynamic> json) =
      _$UserSearchFilterInputImpl.fromJson;

  @override
  String? get emailAddress;
  set emailAddress(String? value);
  @override
  String? get userName;
  set userName(String? value);

  /// Create a copy of UserSearchFilterInput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserSearchFilterInputImplCopyWith<_$UserSearchFilterInputImpl>
      get copyWith => throw _privateConstructorUsedError;
}

UserUpdate _$UserUpdateFromJson(Map<String, dynamic> json) {
  return _UserUpdate.fromJson(json);
}

/// @nodoc
mixin _$UserUpdate {
  int? get numApps => throw _privateConstructorUsedError;
  int? get numDevices => throw _privateConstructorUsedError;
  int? get numSpaces => throw _privateConstructorUsedError;
  User get user => throw _privateConstructorUsedError;
  @JsonKey(name: 'userID')
  String get userId => throw _privateConstructorUsedError;

  /// Serializes this UserUpdate to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserUpdateCopyWith<UserUpdate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserUpdateCopyWith<$Res> {
  factory $UserUpdateCopyWith(
          UserUpdate value, $Res Function(UserUpdate) then) =
      _$UserUpdateCopyWithImpl<$Res, UserUpdate>;
  @useResult
  $Res call(
      {int? numApps,
      int? numDevices,
      int? numSpaces,
      User user,
      @JsonKey(name: 'userID') String userId});

  $UserCopyWith<$Res> get user;
}

/// @nodoc
class _$UserUpdateCopyWithImpl<$Res, $Val extends UserUpdate>
    implements $UserUpdateCopyWith<$Res> {
  _$UserUpdateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? numApps = freezed,
    Object? numDevices = freezed,
    Object? numSpaces = freezed,
    Object? user = null,
    Object? userId = null,
  }) {
    return _then(_value.copyWith(
      numApps: freezed == numApps
          ? _value.numApps
          : numApps // ignore: cast_nullable_to_non_nullable
              as int?,
      numDevices: freezed == numDevices
          ? _value.numDevices
          : numDevices // ignore: cast_nullable_to_non_nullable
              as int?,
      numSpaces: freezed == numSpaces
          ? _value.numSpaces
          : numSpaces // ignore: cast_nullable_to_non_nullable
              as int?,
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }

  /// Create a copy of UserUpdate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserCopyWith<$Res> get user {
    return $UserCopyWith<$Res>(_value.user, (value) {
      return _then(_value.copyWith(user: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$UserUpdateImplCopyWith<$Res>
    implements $UserUpdateCopyWith<$Res> {
  factory _$$UserUpdateImplCopyWith(
          _$UserUpdateImpl value, $Res Function(_$UserUpdateImpl) then) =
      __$$UserUpdateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? numApps,
      int? numDevices,
      int? numSpaces,
      User user,
      @JsonKey(name: 'userID') String userId});

  @override
  $UserCopyWith<$Res> get user;
}

/// @nodoc
class __$$UserUpdateImplCopyWithImpl<$Res>
    extends _$UserUpdateCopyWithImpl<$Res, _$UserUpdateImpl>
    implements _$$UserUpdateImplCopyWith<$Res> {
  __$$UserUpdateImplCopyWithImpl(
      _$UserUpdateImpl _value, $Res Function(_$UserUpdateImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? numApps = freezed,
    Object? numDevices = freezed,
    Object? numSpaces = freezed,
    Object? user = null,
    Object? userId = null,
  }) {
    return _then(_$UserUpdateImpl(
      numApps: freezed == numApps
          ? _value.numApps
          : numApps // ignore: cast_nullable_to_non_nullable
              as int?,
      numDevices: freezed == numDevices
          ? _value.numDevices
          : numDevices // ignore: cast_nullable_to_non_nullable
              as int?,
      numSpaces: freezed == numSpaces
          ? _value.numSpaces
          : numSpaces // ignore: cast_nullable_to_non_nullable
              as int?,
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserUpdateImpl extends _UserUpdate with DiagnosticableTreeMixin {
  _$UserUpdateImpl(
      {this.numApps,
      this.numDevices,
      this.numSpaces,
      required this.user,
      @JsonKey(name: 'userID') required this.userId})
      : super._();

  factory _$UserUpdateImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserUpdateImplFromJson(json);

  @override
  final int? numApps;
  @override
  final int? numDevices;
  @override
  final int? numSpaces;
  @override
  final User user;
  @override
  @JsonKey(name: 'userID')
  final String userId;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'UserUpdate(numApps: $numApps, numDevices: $numDevices, numSpaces: $numSpaces, user: $user, userId: $userId)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'UserUpdate'))
      ..add(DiagnosticsProperty('numApps', numApps))
      ..add(DiagnosticsProperty('numDevices', numDevices))
      ..add(DiagnosticsProperty('numSpaces', numSpaces))
      ..add(DiagnosticsProperty('user', user))
      ..add(DiagnosticsProperty('userId', userId));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserUpdateImpl &&
            (identical(other.numApps, numApps) || other.numApps == numApps) &&
            (identical(other.numDevices, numDevices) ||
                other.numDevices == numDevices) &&
            (identical(other.numSpaces, numSpaces) ||
                other.numSpaces == numSpaces) &&
            (identical(other.user, user) || other.user == user) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, numApps, numDevices, numSpaces, user, userId);

  /// Create a copy of UserUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserUpdateImplCopyWith<_$UserUpdateImpl> get copyWith =>
      __$$UserUpdateImplCopyWithImpl<_$UserUpdateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserUpdateImplToJson(
      this,
    );
  }
}

abstract class _UserUpdate extends UserUpdate {
  factory _UserUpdate(
          {final int? numApps,
          final int? numDevices,
          final int? numSpaces,
          required final User user,
          @JsonKey(name: 'userID') required final String userId}) =
      _$UserUpdateImpl;
  _UserUpdate._() : super._();

  factory _UserUpdate.fromJson(Map<String, dynamic> json) =
      _$UserUpdateImpl.fromJson;

  @override
  int? get numApps;
  @override
  int? get numDevices;
  @override
  int? get numSpaces;
  @override
  User get user;
  @override
  @JsonKey(name: 'userID')
  String get userId;

  /// Create a copy of UserUpdate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserUpdateImplCopyWith<_$UserUpdateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
