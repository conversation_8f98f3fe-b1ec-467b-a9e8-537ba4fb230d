import 'package:json_annotation/json_annotation.dart';

/// Scalar types used in the GraphQL schema

typedef Long = int;

enum UserAccessStatus {
  pending,
  active,
  inactive 
}

enum AddressType {
  main,
  branch 
}

enum ContactType {
  email,
  voiceAndMessaging,
  voiceOnly,
  messagingOnly,
  tollFree,
  fax
}

enum SpaceStatus {
  undeployed,
  running,
  shutdown,
  pending,
  unknown 
}

enum IPType {
  @JsonValue("IPv4")
  ipv4,
  @JsonValue("IPv6")
  ipv6,
}

enum AppStatus {
  undeployed,
  running,
  shutdown,
  pending,
  unknown 
}

enum PublishDataType {
  event
}

enum AccessType {
  unauthorized,
  pending,
  guest,
  admin 
}

typedef AWSTimestamp = int;
typedef AWSEmail = String;
typedef AWSPhone = String;
typedef AWSIPAddress = String;
