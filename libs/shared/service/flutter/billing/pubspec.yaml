name: billing_service
description: "MyCS billing management service module"
publish_to: 'none'
version: 0.1.0
homepage: https://github.com/novassist-ai/novassist/tree/main/libs/shared/service/flutter/billing

environment:
  sdk: ">=3.2.6 <4.0.0"
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  intl: ^0.20.2
  logging: ^1.2.0
  json_annotation: ^4.8.1
  equatable: ^2.0.5
  dio: ^5.8.0+1
  retrofit: ">=4.0.0 <5.0.0"
  get_it: ^8.0.3
  bloc: ^9.0.0

  utilities_ab:
    path: ../../../../commons/dart/utilities
  app_framework_component:
    path: ../../../../component/flutter/app_framework
  user_space_model:
    path: ../user_space

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^4.0.0

  build_runner: ^2.4.8
  json_serializable: ^6.7.1
  retrofit_generator: ">=9.0.0 <10.0.0"

  test: ^1.24.9
  bloc_test: ^10.0.0
  mockito: ^5.4.4

flutter:
  generate: true
  assets: []
