// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in billing_service/test/unit/mock_provider.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:app_framework_component/app_framework.dart' as _i4;
import 'package:billing_service/provider/billing_provider.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i5;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [BillingProvider].
///
/// See the documentation for Mockito's code generation for more information.
class MockBillingProvider extends _i1.Mock implements _i2.BillingProvider {
  MockBillingProvider() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Stream<_i4.ProviderEvent> get providerEventStream => (super.noSuchMethod(
        Invocation.getter(#providerEventStream),
        returnValue: _i3.Stream<_i4.ProviderEvent>.empty(),
      ) as _i3.Stream<_i4.ProviderEvent>);

  @override
  _i3.Future<String> createPaymentLink({
    String? orgId,
    required String? priceId,
    required String? successUrl,
    required String? cancelUrl,
    int? trialDays,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createPaymentLink,
          [],
          {
            #orgId: orgId,
            #priceId: priceId,
            #successUrl: successUrl,
            #cancelUrl: cancelUrl,
            #trialDays: trialDays,
          },
        ),
        returnValue: _i3.Future<String>.value(_i5.dummyValue<String>(
          this,
          Invocation.method(
            #createPaymentLink,
            [],
            {
              #orgId: orgId,
              #priceId: priceId,
              #successUrl: successUrl,
              #cancelUrl: cancelUrl,
              #trialDays: trialDays,
            },
          ),
        )),
      ) as _i3.Future<String>);

  @override
  _i3.Future<void> modifySubscription({
    String? orgId,
    required String? priceId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #modifySubscription,
          [],
          {
            #orgId: orgId,
            #priceId: priceId,
          },
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> unsubscribe({String? orgId}) => (super.noSuchMethod(
        Invocation.method(
          #unsubscribe,
          [],
          {#orgId: orgId},
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> dispose() => (super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);
}
