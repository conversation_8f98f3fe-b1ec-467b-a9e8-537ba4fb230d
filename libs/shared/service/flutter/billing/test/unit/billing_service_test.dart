import 'package:flutter_test/flutter_test.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:mockito/mockito.dart';

import 'package:app_framework_component/app_framework.dart';

import 'package:billing_service/bloc/billing_service.dart';
import 'package:billing_service/bloc/billing_state.dart';
import 'package:billing_service/bloc/loading_states.dart';
import 'package:billing_service/provider/provider_events.dart';
import 'package:billing_service/model/billing_api_types.dart';

import 'mock_provider.dart';

void main() {
  group('BillingService', () {
    late MockBillingProvider mockProvider;
    late BillingService billingService;

    setUp(() async {
      mockProvider = createMockProvider();
      await mockProvider.initialize();
      billingService = BillingService(mockProvider);
    });

    tearDown(() async {
      await billingService.close();
      await mockProvider.dispose();
    });

    group('initialization', () {
      blocTest<BillingService, BillingState>(
        'should create service with initial state',
        build: () => billingService,
        expect: () => [
          const BillingState(),
        ],
      );
    });

    group('event handling', () {
      blocTest<BillingService, BillingState>(
        'should handle PaymentLinkCreated event',
        build: () => billingService,
        act: (bloc) {
          addEventToProviderStream(
            mockProvider,
            PaymentLinkCreated('https://checkout.stripe.com/pay/test_link'),
          );
        },
        expect: () => [
          const BillingState(),
          const BillingState(
            paymentLink: 'https://checkout.stripe.com/pay/test_link',
          ),
        ],
      );

      blocTest<BillingService, BillingState>(
        'should handle PaymentLinkCreationFailed event',
        build: () => billingService,
        act: (bloc) {
          final request = StripeCheckoutRequest(
            priceId: 'price_123',
            successUrl: 'https://success.com',
            cancelUrl: 'https://cancel.com',
          );

          addEventToProviderStream(
            mockProvider,
            PaymentLinkCreationFailed(request: request),
          );
        },
        expect: () => [
          const BillingState(),
          predicate<BillingState>((state) =>
              state.messages.isNotEmpty && state.messages.first.isError),
        ],
      );
    });

    group('createPaymentLink', () {
      blocTest<BillingService, BillingState>(
        'should create payment link successfully',
        setUp: () {
          when(mockProvider.createPaymentLink(
            orgId: 'org_123',
            priceId: 'price_456',
            successUrl: 'https://success.com',
            cancelUrl: 'https://cancel.com',
            trialDays: 7,
          )).thenAnswer(
              (_) async => 'https://checkout.stripe.com/pay/test_link');
        },
        build: () => billingService,
        act: (bloc) => bloc.createPaymentLink(
          orgId: 'org_123',
          priceId: 'price_456',
          successUrl: 'https://success.com',
          cancelUrl: 'https://cancel.com',
          trialDays: 7,
        ),
        expect: () => [
          const BillingState(),
          BillingState(
            loadingStates: {BillingLoadingStates.createPaymentLink},
          ),
          const BillingState(),
        ],
        verify: (bloc) {
          verify(mockProvider.createPaymentLink(
            orgId: 'org_123',
            priceId: 'price_456',
            successUrl: 'https://success.com',
            cancelUrl: 'https://cancel.com',
            trialDays: 7,
          )).called(1);
        },
      );

      blocTest<BillingService, BillingState>(
        'should handle provider errors during payment link creation',
        setUp: () {
          when(mockProvider.createPaymentLink(
            orgId: anyNamed('orgId'),
            priceId: anyNamed('priceId'),
            successUrl: anyNamed('successUrl'),
            cancelUrl: anyNamed('cancelUrl'),
            trialDays: anyNamed('trialDays'),
          )).thenThrow(Exception('Provider error'));
        },
        build: () => billingService,
        act: (bloc) => bloc.createPaymentLink(
          priceId: 'price_123',
          successUrl: 'https://success.com',
          cancelUrl: 'https://cancel.com',
        ),
        expect: () => [
          const BillingState(),
          BillingState(
            loadingStates: {BillingLoadingStates.createPaymentLink},
          ),
          predicate<BillingState>((state) =>
              state.messages.isNotEmpty && state.messages.first.isError),
          predicate<BillingState>((state) =>
              state.messages.isNotEmpty && state.messages.first.isError),
        ],
        verify: (bloc) {
          verify(mockProvider.createPaymentLink(
            orgId: anyNamed('orgId'),
            priceId: anyNamed('priceId'),
            successUrl: anyNamed('successUrl'),
            cancelUrl: anyNamed('cancelUrl'),
            trialDays: anyNamed('trialDays'),
          )).called(1);
        },
      );

      blocTest<BillingService, BillingState>(
        'should set and clear loading state during payment link creation',
        setUp: () {
          when(mockProvider.createPaymentLink(
            orgId: anyNamed('orgId'),
            priceId: anyNamed('priceId'),
            successUrl: anyNamed('successUrl'),
            cancelUrl: anyNamed('cancelUrl'),
            trialDays: anyNamed('trialDays'),
          )).thenAnswer((_) async {
            await Future.delayed(const Duration(milliseconds: 10));
            return 'https://checkout.stripe.com/pay/test_link';
          });
        },
        build: () => billingService,
        act: (bloc) => bloc.createPaymentLink(
          priceId: 'price_123',
          successUrl: 'https://success.com',
          cancelUrl: 'https://cancel.com',
        ),
        expect: () => [
          const BillingState(),
          BillingState(
            loadingStates: {BillingLoadingStates.createPaymentLink},
          ),
          const BillingState(),
        ],
        verify: (bloc) {
          verify(mockProvider.createPaymentLink(
            orgId: anyNamed('orgId'),
            priceId: anyNamed('priceId'),
            successUrl: anyNamed('successUrl'),
            cancelUrl: anyNamed('cancelUrl'),
            trialDays: anyNamed('trialDays'),
          )).called(1);
        },
      );
    });

    group('state management', () {
      blocTest<BillingService, BillingState>(
        'should add error message to state',
        seed: () => const BillingState(),
        build: () => billingService,
        act: (bloc) => bloc.addStateMessage(
          Message.error('Test error message'),
        ),
        expect: () => [
          predicate<BillingState>((state) =>
              state.messages.isNotEmpty && state.messages.first.isError),
        ],
      );

      blocTest<BillingService, BillingState>(
        'should remove message from state',
        seed: () => BillingState(
          messages: {
            Message.info('test message 1'),
            Message.info('test message 2'),
          },
        ),
        build: () => billingService,
        act: (bloc) => bloc.removeStateMessage(
          Message.info('test message 2'),
        ),
        expect: () => [
          BillingState(
            messages: {
              Message.info('test message 1'),
            },
          ),
        ],
      );
    });
  });
}
