import 'dart:async';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'package:app_framework_component/app_framework.dart';
import 'package:billing_service/provider/billing_provider.dart';

import 'mock_provider.mocks.dart';
export 'mock_provider.mocks.dart';

@GenerateMocks([BillingProvider])
class _MockProvider extends MockBillingProvider {
  final _eventStream = StreamController<ProviderEvent>.broadcast();

  _MockProvider() {
    when(providerEventStream).thenAnswer((_) => _eventStream.stream);
  }

  @override
  Future<void> initialize() async {}

  @override
  Future<void> dispose() async {
    await _eventStream.close();
  }
}

MockBillingProvider createMockProvider() => _MockProvider();

void addEventToProviderStream(
  MockBillingProvider provider,
  ProviderEvent event,
) {
  (provider as _MockProvider)._eventStream.add(event);
}
