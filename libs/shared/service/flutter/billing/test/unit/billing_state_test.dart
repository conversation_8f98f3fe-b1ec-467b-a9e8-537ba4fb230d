import 'package:flutter_test/flutter_test.dart';
import 'package:app_framework_component/app_framework.dart';

import 'package:billing_service/bloc/billing_state.dart';
import 'package:billing_service/bloc/loading_states.dart';

void main() {
  group('BillingState', () {
    test('should create initial state with default values', () {
      const state = BillingState();

      expect(state.paymentLink, isNull);
      expect(state.loadingStates, isEmpty);
      expect(state.messages, isEmpty);
    });

    test('should create state with custom values', () {
      const paymentLink = 'https://checkout.stripe.com/pay/test_link';
      final loadingStates = {BillingLoadingStates.createPaymentLink};
      final messages = {Message.info('Test message')};

      final state = BillingState(
        paymentLink: paymentLink,
        loadingStates: loadingStates,
        messages: messages,
      );

      expect(state.paymentLink, equals(paymentLink));
      expect(state.loadingStates, equals(loadingStates));
      expect(state.messages, equals(messages));
    });

    test('copyWith should return new instance with updated values', () {
      final initialState = BillingState(
        paymentLink: 'https://checkout.stripe.com/pay/old_link',
        loadingStates: {BillingLoadingStates.createPaymentLink},
        messages: {Message.info('Old message')},
      );

      const newPaymentLink = 'https://checkout.stripe.com/pay/new_link';
      const newLoadingStates = <LoadingState>{};
      final newMessages = {Message.error('New message')};

      final newState = initialState.copyWith(
        paymentLink: newPaymentLink,
        loadingStates: newLoadingStates,
        messages: newMessages,
      );

      expect(newState, isNot(same(initialState)));
      expect(newState.paymentLink, equals(newPaymentLink));
      expect(newState.loadingStates, equals(newLoadingStates));
      expect(newState.messages, equals(newMessages));
    });

    test('copyWith should preserve unchanged values when not specified', () {
      final initialState = BillingState(
        paymentLink: 'https://checkout.stripe.com/pay/test_link',
        loadingStates: {BillingLoadingStates.createPaymentLink},
        messages: {Message.info('Test message')},
      );

      final newState = initialState.copyWith(
        paymentLink: 'https://checkout.stripe.com/pay/updated_link',
      );

      expect(newState.paymentLink,
          equals('https://checkout.stripe.com/pay/updated_link'));
      expect(newState.loadingStates, equals(initialState.loadingStates));
      expect(newState.messages, equals(initialState.messages));
    });

    test('copyWith should handle null values correctly', () {
      final initialState = BillingState(
        paymentLink: 'https://checkout.stripe.com/pay/test_link',
        loadingStates: {BillingLoadingStates.createPaymentLink},
        messages: {Message.info('Test message')},
      );

      final newState = initialState.copyWith(
        paymentLink: null,
        loadingStates: null,
        messages: null,
      );

      expect(newState.paymentLink, isNull);
      expect(newState.loadingStates, equals(initialState.loadingStates));
      expect(newState.messages, equals(initialState.messages));
    });

    test('should handle loading state management', () {
      const initialState = BillingState();

      final stateWithLoading =
          initialState.startLoading(BillingLoadingStates.createPaymentLink);

      expect(stateWithLoading.loadingStates,
          contains(BillingLoadingStates.createPaymentLink));

      final stateWithoutLoading =
          stateWithLoading.endLoading(BillingLoadingStates.createPaymentLink);

      expect(stateWithoutLoading.loadingStates,
          isNot(contains(BillingLoadingStates.createPaymentLink)));
    });

    test('should handle message management', () {
      const initialState = BillingState();
      final testMessage = Message.info('Test message');

      final stateWithMessage = initialState.addMessage(testMessage);

      expect(stateWithMessage.messages, contains(testMessage));
    });

    test('additionalProps should return empty list', () {
      const state = BillingState();
      expect(state.additionalProps, isEmpty);
    });

    test('should be equatable', () {
      final state1 = BillingState(
        paymentLink: 'https://checkout.stripe.com/pay/test_link',
        loadingStates: {BillingLoadingStates.createPaymentLink},
        messages: {Message.info('Test message')},
      );

      final state2 = BillingState(
        paymentLink: 'https://checkout.stripe.com/pay/test_link',
        loadingStates: {BillingLoadingStates.createPaymentLink},
        messages: {Message.info('Test message')},
      );

      expect(state1, equals(state2));
    });
  });
}
