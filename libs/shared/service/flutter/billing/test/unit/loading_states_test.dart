import 'package:flutter_test/flutter_test.dart';
import 'package:app_framework_component/app_framework.dart';

import 'package:billing_service/bloc/loading_states.dart';

void main() {
  group('BillingLoadingStates', () {
    test('should have createPaymentLink loading state', () {
      expect(BillingLoadingStates.createPaymentLink, isA<LoadingState>());
      expect(BillingLoadingStates.createPaymentLink.name,
          equals('create_payment_link'));
    });

    test('should be constant', () {
      const state1 = BillingLoadingStates.createPaymentLink;
      const state2 = BillingLoadingStates.createPaymentLink;

      expect(state1, same(state2));
    });

    test('should be equatable', () {
      const state1 = BillingLoadingStates.createPaymentLink;
      const state2 = LoadingState('create_payment_link');

      expect(state1, equals(state2));
    });

    test('should have correct string representation', () {
      expect(BillingLoadingStates.createPaymentLink.toString(),
          contains('create_payment_link'));
    });
  });
}
