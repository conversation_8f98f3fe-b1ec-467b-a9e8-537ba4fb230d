@Timeout(Duration(seconds: 1800))
import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:logging/logging.dart';
import 'package:dio/dio.dart' hide Headers;

import 'package:utilities_ab/logging/init_logging.dart';

import 'package:billing_service/provider/billing_provider.dart';
import 'package:billing_service/provider/user_space_billing_api.dart';
import 'package:billing_service/provider/user_space_billing_service.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  // Set a custom override that'll use an unmocked HTTP client
  HttpOverrides.global = _TestHttpOverrides();

  // Ensure the mandatory environment variables are set
  final testBillingApiEndpoint =
      Platform.environment['TEST_BILLING_API_ENDPOINT'];
  if (testBillingApiEndpoint == null) {
    throw Exception(
      'Set the environment variable TEST_BILLING_API_ENDPOINT '
      'with the url endpoint of the User Space Billing service.',
    );
  }
  final testAccessToken = Platform.environment['TEST_ACCESS_TOKEN'];
  if (testAccessToken == null) {
    throw Exception(
      'Set the environment variable TEST_ACCESS_TOKEN '
      'with the JWT token of a valid user.',
    );
  }
  final billingOrg = Platform.environment['TEST_BILLING_ORG'];
  if (billingOrg == null) {
    throw Exception(
      'Set the environment variable TEST_BILLING_ORG '
      'with a valid organization id.',
    );
  }
  final priceId = Platform.environment['TEST_STRIPE_PRICE_ID'];
  if (priceId == null) {
    throw Exception(
      'Set the environment variable TEST_STRIPE_PRICE_ID '
      'with a valid price id from a stripe product.',
    );
  }

  final Logger logger = Logger('BillingApiProviderTest');
  if (Platform.environment['DEBUG'] != null) {
    initLogging(
      Level.ALL,
      logToConsole: true,
    );
  } else {
    initLogging(
      Level.INFO,
      logToConsole: true,
    );
  }

  late final BillingProvider provider;

  // Set up the test suite

  setUpAll(() async {
    logger.info('Setting up test suite');

    provider = UserSpaceBillingService(
      UserSpaceBillingApi(
        Dio(
          BaseOptions(
            headers: {
              'Authorization': 'Bearer $testAccessToken',
            },
          ),
        ),
        baseUrl: testBillingApiEndpoint,
      ),
    );
  });

  tearDownAll(() {
    logger.info('Tearing down test suite');
  });

  // Test the AiReaderProvider

  test(
    'retrieves the checkout url for a '
    'stripe checkout action for user',
    () async {
      final paymentLink = await provider.createPaymentLink(
        priceId: priceId,
        successUrl: 'https://example.com/success',
        cancelUrl: 'https://example.com/cancel',
        trialDays: 30,
      );
      logger.info('Payment link for user subscription: $paymentLink');
      expect(paymentLink, isNotNull);
      expect(paymentLink, isNotEmpty);
    },
  );

  test(
    'retrieves the checkout url for a stripe '
    'checkout action for an organization',
    () async {
      final paymentLink = await provider.createPaymentLink(
        orgId: billingOrg,
        priceId: priceId,
        successUrl: 'https://example.com/success',
        cancelUrl: 'https://example.com/cancel',
        trialDays: 30,
      );
      logger.info('Payment link for org subscription: $paymentLink');
      expect(paymentLink, isNotNull);
      expect(paymentLink, isNotEmpty);
    },
  );
}

// Custom client that overrides the Mock HTTP client
class _TestHttpOverrides extends HttpOverrides {}
