// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'billing_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class BillingLocalizationsEn extends BillingLocalizations {
  BillingLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get linkCreationFailed => 'Failed to create payment link.';

  @override
  String get unsubscribeFailed => 'Failed to unsubscribe from current plan.';

  @override
  String get unknownError =>
      'An unknown error occurred. Please contact support.';
}
