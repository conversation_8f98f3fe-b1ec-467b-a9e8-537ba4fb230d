import 'package:dio/dio.dart' hide Headers;
import 'package:retrofit/retrofit.dart';

import '../model/billing_api_types.dart';

part 'user_space_billing_api.g.dart';

@RestApi()
abstract class UserSpaceBillingApi {
  factory UserSpaceBillingApi(
    Dio dio, {
    String baseUrl,
    ParseErrorLogger? errorLogger,
  }) = _UserSpaceBillingApi;

  @POST('/integrations/stripe/checkout')
  Future<StripeCheckoutResponse> stripeCheckout(
    @Body() StripeCheckoutRequest checkoutRequest,
  );

  @POST('/integrations/stripe/modifySubscription')
  Future<StripeModifySubscriptionResponse> stripeModifySubscription(
    @Body() StripeModifySubscriptionRequest modifySubscriptionRequest,
  );

  @POST('/integrations/stripe/unsubscribe')
  Future<StripeUnsubscribeResponse> stripeUnsubscribe(
    @Body() StripeUnsubscribeRequest unsubscribeRequest,
  );
}
