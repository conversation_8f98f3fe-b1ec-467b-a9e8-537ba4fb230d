import 'package:flutter/foundation.dart';

import 'package:utilities_ab/error/app_exception.dart';

class UserSpaceBillingApiException extends AppException {
  int? httpStatusCode;

  UserSpaceBillingApiException({
    required String super.message,
    this.httpStatusCode,
    super.innerException,
    super.innerStackTrace,
    super.log = kDebugMode,
  }) : super(
          stackTrace: StackTrace.current,
        );
}

sealed class PaymentLinkCreationException extends UserSpaceBillingApiException {
  final String customerId;
  final String priceId;

  PaymentLinkCreationException({
    required this.customerId,
    required this.priceId,
    required super.message,
    super.innerException,
    super.innerStackTrace,
    super.log = kDebugMode,
  });
}
