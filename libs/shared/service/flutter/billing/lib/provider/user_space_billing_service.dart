import 'dart:async';
import 'package:dio/dio.dart' hide Headers;
import 'package:logging/logging.dart' as logging;

import 'package:app_framework_component/app_framework.dart';
import 'package:utilities_ab/error/app_exception.dart';

import '../model/billing_api_types.dart';
import 'user_space_billing_api.dart';
import 'billing_provider.dart';
import 'provider_events.dart';
import 'exceptions.dart';

class UserSpaceBillingService implements BillingProvider {
  static final logging.Logger _logger =
      logging.Logger('UserSpaceBillingService');

  // UserSpaceBillingService singleton instance
  static UserSpaceBillingService? _instance;

  late final UserSpaceBillingApi _api;

  @override
  Stream<ProviderEvent> get providerEventStream => _providerEventStream.stream;
  final _providerEventStream = StreamController<ProviderEvent>.broadcast();

  factory UserSpaceBillingService(
    UserSpaceBillingApi api,
  ) {
    _instance = _instance ?? UserSpaceBillingService._(api);
    return _instance!;
  }

  UserSpaceBillingService._(
    UserSpaceBillingApi api,
  ) : _api = api;

  @override
  Future<String> createPaymentLink({
    String? orgId,
    required String priceId,
    required String successUrl,
    required String cancelUrl,
    int? trialDays,
  }) async {
    final request = StripeCheckoutRequest(
      orgId: orgId,
      priceId: priceId,
      successUrl: successUrl,
      cancelUrl: cancelUrl,
      trialDays: trialDays,
    );

    final completer = Completer<String>();
    _api.stripeCheckout(request).then((response) {
      _enqueueEvent(
        PaymentLinkCreated(response.url),
      );
      completer.complete(response.url);
    }).onError((DioException? error, StackTrace stackTrace) {
      _handleError(
        error,
        stackTrace,
        message: 'Failed to create payment link',
        errorHandler: (appException, httpStatusCode) {
          _enqueueEvent(
            PaymentLinkCreationFailed(
              request: request,
              exception: appException,
            ),
          );
        },
      );
    });
    return completer.future;
  }

  @override
  Future<void> modifySubscription({
    String? orgId,
    required String priceId,
  }) {
    final request = StripeModifySubscriptionRequest(
      orgId: orgId,
      priceId: priceId,
    );

    return _api.stripeModifySubscription(request).then((response) {
      _enqueueEvent(
        SubscriptionModified(
          orgId,
          response.subscriptionId,
          response.message,
        ),
      );
    }).onError((DioException? error, StackTrace stackTrace) {
      _handleError(
        error,
        stackTrace,
        message: 'Failed to modify subscription',
        errorHandler: (appException, httpStatusCode) {
          _enqueueEvent(
            SubscriptionModificationFailed(
              orgId: orgId,
              exception: appException,
            ),
          );
        },
      );
    });
  }

  @override
  Future<void> unsubscribe({
    String? orgId,
  }) async {
    final request = StripeUnsubscribeRequest(
      orgId: orgId,
    );

    return _api.stripeUnsubscribe(request).then((response) {
      _enqueueEvent(
        Unsubscribed(
          orgId,
          response.subscriptionId,
          response.message,
        ),
      );
    }).onError(
      (DioException? error, StackTrace stackTrace) {
        _handleError(
          error,
          stackTrace,
          message: 'Failed to unsubscribe',
          errorHandler: (appException, httpStatusCode) {
            _enqueueEvent(
              UnsubscribeFailed(
                orgId: orgId,
                exception: appException,
              ),
            );
          },
        );
      },
    );
  }

  AppException? _handleError(
    Object? error,
    StackTrace stackTrace, {
    String? message,
    Function(
      AppException e,
      int? httpStatusCode,
    )? errorHandler,
  }) {
    int? httpStatusCode;
    if (error is DioException) {
      _logger.severe(
        '_handleError: Service API responded with error: '
        '${error.response?.data}: ${error.response?.data}',
        error,
        stackTrace,
      );
      httpStatusCode = error.response?.statusCode;
    } else {
      _logger.severe(
        '_handleError: Error invoking service API: ${error.toString()}',
        error,
        stackTrace,
      );
    }

    final apiException = error is AppException
        ? error
        : UserSpaceBillingApiException(
            message: message ?? error.toString(),
            httpStatusCode: httpStatusCode,
            innerException: error is Exception
                ? error
                : Exception(
                    error.toString(),
                  ),
            innerStackTrace: stackTrace,
          );

    if (errorHandler != null) {
      errorHandler(apiException, httpStatusCode);
      return null;
    } else {
      return apiException;
    }
  }

  Future<void> _enqueueEvent(
    ProviderEvent event,
  ) async {
    _logger.finest('_enqueueEvent: Enqueuing provider event: $event');
    _providerEventStream.add(event);
  }

  @override
  Future<void> initialize() async {
    // NoOp
  }

  @override
  Future<void> dispose() async {
    _providerEventStream.close();
  }
}
