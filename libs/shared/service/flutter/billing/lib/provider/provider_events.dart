import 'package:app_framework_component/app_framework.dart';
import 'package:billing_service/model/billing_api_types.dart';
import 'package:utilities_ab/error/app_exception.dart';

// The [BillingProviderEvent] is the base class for all events
// emitted by the [BillingProvider].
sealed class BillingProviderEvent extends ProviderEvent {}

/// The [BillingProviderErrorEvent] is a [ProviderEvent] emitted
/// when there is an error in the [BillingProvider].
sealed class BillingProviderErrorEvent extends ErrorEventType<AppException> {
  final String? _message;
  get message => _message ?? (super.error as AppException).message;

  const BillingProviderErrorEvent({
    String? message,
    AppException? exception,
  })  : _message = message,
        super(exception);
}

/// The [PaymentLinkCreated] is a [BillingProviderEvent] emitted
/// when a payment link is created.
final class PaymentLinkCreated extends BillingProviderEvent {
  final String paymentLink;

  PaymentLinkCreated(this.paymentLink);
}

/// The [PaymentLinkCreationFailed] is a [BillingProviderErrorEvent]
/// emitted when a payment link cannot be created.
class PaymentLinkCreationFailed extends BillingProviderErrorEvent {
  final StripeCheckoutRequest request;

  const PaymentLinkCreationFailed({
    required this.request,
    super.exception,
  });
}

/// The [SubscriptionModified] is a [BillingProviderEvent] emitted
/// when a subscription is modified.
final class SubscriptionModified extends BillingProviderEvent {
  final String? orgId;
  final String subscriptionId;
  final String message;

  SubscriptionModified(this.orgId, this.subscriptionId, this.message);
}

/// The [SubscriptionModificationFailed] is a [BillingProviderErrorEvent]
/// emitted when a subscription cannot be modified.
final class SubscriptionModificationFailed extends BillingProviderErrorEvent {
  final String? orgId;

  const SubscriptionModificationFailed({
    this.orgId,
    super.exception,
  });
}

/// The [Unsubscribed] is a [BillingProviderEvent] emitted
/// when a subscription is unsubscribed.
final class Unsubscribed extends BillingProviderEvent {
  final String? orgId;
  final String subscriptionId;
  final String message;

  Unsubscribed(
    this.orgId,
    this.subscriptionId,
    this.message,
  );
}

/// The [Unsubscribed] is a [BillingProviderErrorEvent] emitted
/// when a subscription cannot be unsubscribed.
final class UnsubscribeFailed extends BillingProviderErrorEvent {
  final String? orgId;

  const UnsubscribeFailed({
    this.orgId,
    super.exception,
  });
}
