import 'package:app_framework_component/app_framework.dart';

/// User-Space billing service
abstract interface class BillingProvider implements RepositoryProvider {
  Future<String> createPaymentLink({
    String? orgId,
    required String priceId,
    required String successUrl,
    required String cancelUrl,
    int? trialDays,
  });

  Future<void> modifySubscription({
    String? orgId,
    required String priceId,
  });

  Future<void> unsubscribe({
    String? orgId,
  });
}
