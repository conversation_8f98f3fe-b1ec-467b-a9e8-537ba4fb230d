import 'package:app_framework_component/app_framework.dart';

class BillingState extends State<BillingState> {
  final String? paymentLink;

  const BillingState({
    super.loadingStates = const {},
    super.messages = const {},
    this.paymentLink,
  });

  @override
  BillingState copyWith({
    Set<LoadingState>? loadingStates,
    Set<Message>? messages,
    String? paymentLink,
  }) {
    return BillingState(
      loadingStates: loadingStates ?? super.loadingStates,
      messages: messages ?? super.messages,
      paymentLink: paymentLink ?? this.paymentLink,
    );
  }

  @override
  List<Object?> get additionalProps => [];
}
