import 'dart:async';
import 'package:flutter/material.dart';
import 'package:logging/logging.dart' as logging;

import 'package:app_framework_component/app_framework.dart';

import '../l10n/billing_localizations.dart';
import '../provider/billing_provider.dart';
import '../provider/provider_events.dart';
import 'billing_state.dart';
import 'loading_states.dart';

class BillingService extends Service<BillingState, BillingProvider> {
  final logging.Logger _logger = logging.Logger('BillingService');

  // Localizations for the billing service
  BillingLocalizations _localizations = lookupBillingLocalizations(
    const Locale('en'),
  );

  late final StreamSubscription<ProviderEvent> _providerEventSubscription;

  BillingService(
    BillingProvider provider,
  ) : super(
          initialState: const BillingState(),
          provider: provider,
        ) {
    // Listen to events from the provider
    // and update the state accordingly
    _providerEventSubscription = provider.providerEventStream.listen((event) {
      _handleEventsFromProvider(event);
    });
  }

  void _handleEventsFromProvider(
    ProviderEvent event,
  ) {
    _logger.fine('Received provider event: $event');
    switch (event) {
      case final PaymentLinkCreated _:
        emit(
          state.copyWith(
            paymentLink: event.paymentLink,
          ),
        );
        _logger.fine('Payment link created successfully: ${event.paymentLink}');

      case final PaymentLinkCreationFailed _:
        _logger.severe('Payment link creation failed: ${event.message}');
        emit(
          state.addMessage(
            Message.error(_localizations.linkCreationFailed),
          ),
        );

      case final UnsubscribeFailed _:
        _logger.severe('Unsubscribe failed: ${event.message}');
        emit(
          state.addMessage(
            Message.error(_localizations.unsubscribeFailed),
          ),
        );

      default:
        _logger.warning('Ignoring unknown event: $event');
    }
  }

  @override
  void setCurrentContext(BuildContext context) {
    // Update the localizations for the billing
    // service from the given [context]
    _localizations = BillingLocalizations.of(context);
  }

  @override
  Future<void> close() async {
    await _providerEventSubscription.cancel();
    return super.close();
  }

  /// Creates a payment link asynchronously
  /// Sets loading state and invokes the provider method
  Future<String?> createPaymentLink({
    String? orgId,
    required String priceId,
    required String successUrl,
    required String cancelUrl,
    int? trialDays,
  }) async {
    emit(state.startLoading(BillingLoadingStates.createPaymentLink));

    try {
      return await provider.createPaymentLink(
        orgId: orgId,
        priceId: priceId,
        successUrl: successUrl,
        cancelUrl: cancelUrl,
        trialDays: trialDays,
      );
    } catch (error, stackTrace) {
      emit(state.addMessage(
        Message.error(_localizations.unknownError),
        error,
        stackTrace,
      ));
      return null;
    } finally {
      emit(state.endLoading(BillingLoadingStates.createPaymentLink));
    }
  }

  /// Modifies the subscription asynchronously
  /// Sets loading state and invokes the provider method
  Future<void> modifySubscription({
    String? orgId,
    required String priceId,
  }) async {
    emit(state.startLoading(BillingLoadingStates.modifySubscription));

    try {
      return await provider.modifySubscription(
        orgId: orgId,
        priceId: priceId,
      );
    } catch (error, stackTrace) {
      emit(state.addMessage(
        Message.error(_localizations.unknownError),
        error,
        stackTrace,
      ));
      rethrow;
    } finally {
      emit(state.endLoading(BillingLoadingStates.modifySubscription));
    }
  }

  /// Unsubscribes from current subscription asynchronously
  /// Sets loading state and invokes the provider method
  Future<void> unsubscribe({
    String? orgId,
  }) async {
    emit(state.startLoading(BillingLoadingStates.unsubscribe));

    try {
      return await provider.unsubscribe(
        orgId: orgId,
      );
    } catch (error, stackTrace) {
      emit(state.addMessage(
        Message.error(_localizations.unknownError),
        error,
        stackTrace,
      ));
      rethrow;
    } finally {
      emit(state.endLoading(BillingLoadingStates.unsubscribe));
    }
  }
}
