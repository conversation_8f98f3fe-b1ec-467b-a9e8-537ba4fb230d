// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'billing_api_types.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StripeCheckoutRequest _$StripeCheckoutRequestFromJson(
        Map<String, dynamic> json) =>
    StripeCheckoutRequest(
      orgId: json['orgID'] as String?,
      priceId: json['priceId'] as String,
      successUrl: json['successUrl'] as String,
      cancelUrl: json['cancelUrl'] as String,
      trialDays: (json['trialDays'] as num?)?.toInt(),
    );

Map<String, dynamic> _$StripeCheckoutRequestToJson(
        StripeCheckoutRequest instance) =>
    <String, dynamic>{
      if (instance.orgId case final value?) 'orgID': value,
      'priceId': instance.priceId,
      'successUrl': instance.successUrl,
      'cancelUrl': instance.cancelUrl,
      if (instance.trialDays case final value?) 'trialDays': value,
    };

StripeCheckoutResponse _$StripeCheckoutResponseFromJson(
        Map<String, dynamic> json) =>
    StripeCheckoutResponse(
      sessionId: json['sessionId'] as String,
      url: json['url'] as String,
    );

Map<String, dynamic> _$StripeCheckoutResponseToJson(
        StripeCheckoutResponse instance) =>
    <String, dynamic>{
      'sessionId': instance.sessionId,
      'url': instance.url,
    };

StripeModifySubscriptionRequest _$StripeModifySubscriptionRequestFromJson(
        Map<String, dynamic> json) =>
    StripeModifySubscriptionRequest(
      orgId: json['orgID'] as String?,
      priceId: json['priceId'] as String,
    );

Map<String, dynamic> _$StripeModifySubscriptionRequestToJson(
        StripeModifySubscriptionRequest instance) =>
    <String, dynamic>{
      if (instance.orgId case final value?) 'orgID': value,
      'priceId': instance.priceId,
    };

StripeModifySubscriptionResponse _$StripeModifySubscriptionResponseFromJson(
        Map<String, dynamic> json) =>
    StripeModifySubscriptionResponse(
      customerId: json['customerId'] as String,
      subscriptionId: json['subscriptionId'] as String,
      priceId: json['priceId'] as String,
      message: json['message'] as String,
    );

Map<String, dynamic> _$StripeModifySubscriptionResponseToJson(
        StripeModifySubscriptionResponse instance) =>
    <String, dynamic>{
      'customerId': instance.customerId,
      'subscriptionId': instance.subscriptionId,
      'priceId': instance.priceId,
      'message': instance.message,
    };

StripeUnsubscribeRequest _$StripeUnsubscribeRequestFromJson(
        Map<String, dynamic> json) =>
    StripeUnsubscribeRequest(
      orgId: json['orgID'] as String?,
    );

Map<String, dynamic> _$StripeUnsubscribeRequestToJson(
        StripeUnsubscribeRequest instance) =>
    <String, dynamic>{
      if (instance.orgId case final value?) 'orgID': value,
    };

StripeUnsubscribeResponse _$StripeUnsubscribeResponseFromJson(
        Map<String, dynamic> json) =>
    StripeUnsubscribeResponse(
      customerId: json['customerId'] as String,
      subscriptionId: json['subscriptionId'] as String,
      message: json['message'] as String,
    );

Map<String, dynamic> _$StripeUnsubscribeResponseToJson(
        StripeUnsubscribeResponse instance) =>
    <String, dynamic>{
      'customerId': instance.customerId,
      'subscriptionId': instance.subscriptionId,
      'message': instance.message,
    };
