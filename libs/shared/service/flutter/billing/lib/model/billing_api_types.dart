import 'package:json_annotation/json_annotation.dart';

part 'billing_api_types.g.dart';

@JsonSerializable(
  includeIfNull: false,
)
class StripeCheckoutRequest {
  @JsonKey(name: 'orgID')
  final String? orgId;

  final String priceId;
  final String successUrl;
  final String cancelUrl;
  final int? trialDays;

  const StripeCheckoutRequest({
    // If orgID is not provided then the checkout
    // is for the user and not an organization.
    this.orgId,
    required this.priceId,
    required this.successUrl,
    required this.cancelUrl,
    this.trialDays,
  });

  factory StripeCheckoutRequest.fromJson(Map<String, dynamic> json) =>
      _$StripeCheckoutRequestFromJson(json);
  Map<String, dynamic> toJson() {
    return _$StripeCheckoutRequestToJson(this);
  }
}

@JsonSerializable(
  includeIfNull: false,
)
class StripeCheckoutResponse {
  final String sessionId;
  final String url;

  const StripeCheckoutResponse({
    required this.sessionId,
    required this.url,
  });

  factory StripeCheckoutResponse.fromJson(Map<String, dynamic> json) =>
      _$StripeCheckoutResponseFromJson(json);
  Map<String, dynamic> toJson() {
    return _$StripeCheckoutResponseToJson(this);
  }
}

@JsonSerializable(
  includeIfNull: false,
)
class StripeModifySubscriptionRequest {
  @JsonKey(name: 'orgID')
  final String? orgId;

  final String priceId;

  const StripeModifySubscriptionRequest({
    // If orgID is not provided then the unsubscribe
    // is for the user and not an organization.
    this.orgId,
    required this.priceId,
  });

  factory StripeModifySubscriptionRequest.fromJson(Map<String, dynamic> json) =>
      _$StripeModifySubscriptionRequestFromJson(json);
  Map<String, dynamic> toJson() {
    return _$StripeModifySubscriptionRequestToJson(this);
  }
}

@JsonSerializable(
  includeIfNull: false,
)
class StripeModifySubscriptionResponse {
  final String customerId;
  final String subscriptionId;
  final String priceId;
  final String message;

  const StripeModifySubscriptionResponse({
    required this.customerId,
    required this.subscriptionId,
    required this.priceId,
    required this.message,
  });

  factory StripeModifySubscriptionResponse.fromJson(
          Map<String, dynamic> json) =>
      _$StripeModifySubscriptionResponseFromJson(json);
  Map<String, dynamic> toJson() {
    return _$StripeModifySubscriptionResponseToJson(this);
  }
}

@JsonSerializable(
  includeIfNull: false,
)
class StripeUnsubscribeRequest {
  @JsonKey(name: 'orgID')
  final String? orgId;

  const StripeUnsubscribeRequest({
    // If orgID is not provided then the unsubscribe
    // is for the user and not an organization.
    this.orgId,
  });

  factory StripeUnsubscribeRequest.fromJson(Map<String, dynamic> json) =>
      _$StripeUnsubscribeRequestFromJson(json);
  Map<String, dynamic> toJson() {
    return _$StripeUnsubscribeRequestToJson(this);
  }
}

@JsonSerializable(
  includeIfNull: false,
)
class StripeUnsubscribeResponse {
  final String customerId;
  final String subscriptionId;
  final String message;

  const StripeUnsubscribeResponse({
    required this.customerId,
    required this.subscriptionId,
    required this.message,
  });

  factory StripeUnsubscribeResponse.fromJson(Map<String, dynamic> json) =>
      _$StripeUnsubscribeResponseFromJson(json);
  Map<String, dynamic> toJson() {
    return _$StripeUnsubscribeResponseToJson(this);
  }
}
