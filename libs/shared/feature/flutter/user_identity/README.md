# user_identity_feature

User authentication, registration and profile management feature module

## Getting Started

This project is a starting point for a Flutter
[plug-in package](https://flutter.dev/developing-packages/),
a specialized package that includes platform-specific implementation code for
Android and/or iOS.

For help getting started with Flutter development, view the
[online documentation](https://flutter.dev/docs), which offers tutorials,
samples, guidance on mobile development, and a full API reference.

## Feature framework

This module implements a feature framework that needs to be initialized correctly in order for the features to work correctly. The following sequence diagram outlines the initialization sequence for the feature which should be done before the main Flutter window is rendered.

```mermaid
sequenceDiagram
    App Main->>FeatureRegistry: FeatureRegistry.intialize(&configLoaderFn)
    App Main->>ServiceLocator: ServiceLocator.register(identity provider)
    rect rgb(112,128,144)
    note right of App Main: feature registration and initialization
    activate App Main
        App Main->>UserIdentityFeature: UserIdentityFeature.register()
        activate UserIdentityFeature
            UserIdentityFeature->>FeatureRegistry: register(feature singleton)
            activate FeatureRegistry
                FeatureRegistry->>FeatureRegistry: config=configLoaderFn(feature name)
                FeatureRegistry->>UserIdentityFeature: initialize(config, registered features)
                activate UserIdentityFeature
                UserIdentityFeature->>ServiceLocator: provider=ServiceLocator.get(identity provider)
                create participant IdentityProvider
                UserIdentityFeature->>IdentityProvider: service=IdentityProvider(provider)
                UserIdentityFeature->>AppNotificationsComponent: register(service)
                UserIdentityFeature->>UserIdentityFeature: set service
                UserIdentityFeature-->>FeatureRegistry: 
                deactivate UserIdentityFeature
                FeatureRegistry->>FeatureRegistry: set feature
                FeatureRegistry-->>UserIdentityFeature: 
            deactivate FeatureRegistry
            UserIdentityFeature-->>App Main: 
        deactivate UserIdentityFeature
    deactivate App Main
    end
```