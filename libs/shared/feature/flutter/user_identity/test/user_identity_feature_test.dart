import 'package:flutter_test/flutter_test.dart';
import 'package:user_identity_feature/user_identity_feature.dart';
import 'package:user_identity_feature/user_identity_feature_platform_interface.dart';
import 'package:user_identity_feature/user_identity_feature_method_channel.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

class MockUserIdentityFeaturePlatform
    with MockPlatformInterfaceMixin
    implements UserIdentityFeaturePlatform {
  @override
  Future<String?> getPlatformVersion() => Future.value('42');
}

void main() {
  final UserIdentityFeaturePlatform initialPlatform =
      UserIdentityFeaturePlatform.instance;

  test('$MethodChannelUserIdentityFeature is the default instance', () {
    expect(initialPlatform, isInstanceOf<MethodChannelUserIdentityFeature>());
  });

  test('getPlatformVersion', () async {
    UserIdentityFeature userIdentityFeaturePlugin =
        UserIdentityFeature.instance();
    MockUserIdentityFeaturePlatform fakePlatform =
        MockUserIdentityFeaturePlatform();
    UserIdentityFeaturePlatform.instance = fakePlatform;

    expect(await userIdentityFeaturePlugin.getPlatformVersion(), '42');
  });
}
