import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:user_identity_feature/user_identity_feature_method_channel.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  MethodChannelUserIdentityFeature platform = MethodChannelUserIdentityFeature();
  const MethodChannel channel = MethodChannel('user_identity_feature');

  setUp(() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      channel,
      (MethodCall methodCall) async {
        return '42';
      },
    );
  });

  tearDown(() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(channel, null);
  });

  test('getPlatformVersion', () async {
    expect(await platform.getPlatformVersion(), '42');
  });
}
