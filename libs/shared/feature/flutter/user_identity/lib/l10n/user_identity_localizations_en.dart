// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'user_identity_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class UserIdentityLocalizationsEn extends UserIdentityLocalizations {
  UserIdentityLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get sms => 'SMS';

  @override
  String get email => 'email';

  @override
  String get signInWidgetTitle => 'Sign In';

  @override
  String get signUpWidgetTitle => 'Sign Up';

  @override
  String get signUpAgreementText =>
      'By signing up, you agree to our [EULA](eula), [T&C](tandc) and [Privacy Policy](privacypolicy). Please check accept below if you would like to proceed.';

  @override
  String get signUpAgreementCheck => 'I accept and agree to the terms of use';

  @override
  String get semSignUpAgreementCheck =>
      'Check or uncheck terms of user agreement';

  @override
  String get verifySignUpWidgetTitle => 'Verify Sign Up';

  @override
  String get verifySignUpUsingCodeSentText =>
      'Please verify your account using the code that was sent to you via email or text, or request a new code by clicking the link below.';

  @override
  String verifySignUpUsingCodeSentTemplate(
    String deliveryMedium,
    String destination,
  ) {
    return 'Please verify your account using the code that was sent via $deliveryMedium to $destination.';
  }

  @override
  String signUpSuccessfulMessage(String username) {
    return 'Successfully created an account for \"$username\". Please sign in and complete your profile to start using the application.';
  }

  @override
  String get signUpNotInProgressMessage =>
      'Sign up is not in progress. You need to start the sign up process first.';

  @override
  String get verifyMfaWidgetTitle => 'Verify MFA';

  @override
  String get verifyMfaCodeSentText =>
      'Please verify your login using the code that was either sent via SMS to your registered mobile phone or generated by an Authenticator app which was configured when setting up MFA for your account.';

  @override
  String get resetPasswordWidgetTitle => 'Reset Password';

  @override
  String resetPasswordCodeSentText(String deliveryMedium, String username) {
    return 'A verification code was sent via $deliveryMedium that was registered for user \"$username\". You need to provide that along with the new password to change the user\'s current password.';
  }

  @override
  String get passwordResetNotInProgressMessage =>
      'Password reset is not in progress. You need to start the password reset process first.';

  @override
  String get noUsernameProvidedForResetPasswordMessage =>
      'No username provided for password reset. Please provide a username for whom the password reset flow was initiated.';

  @override
  String resetPasswordSuccessfulMessage(Object username) {
    return 'Successfully reset the password for \"$username\". Please sign in using the new password.';
  }

  @override
  String get semProfileMenu =>
      'Click the avatar image to show profile menu options';

  @override
  String get menuOptionProfile => 'Profile';

  @override
  String get menuOptionSecurity => 'Security';

  @override
  String get menuOptionLogout => 'Logout';

  @override
  String get userProfileFormTitle => 'User Profile';

  @override
  String get namesSectionTitle => 'Names';

  @override
  String get contactSectionTitle => 'Contact Information';

  @override
  String get tapToVerifyEmail => 'Tap here to verify the updated email address';

  @override
  String get tapToVerifyMobilePhone =>
      'Tap here to verify the updated mobile phone number';

  @override
  String get emailNotVerifiedWarning =>
      'You need a verified email address for account recovery.';

  @override
  String get sms2FAResetWarning =>
      'Your SMS two factor authentication configuration has been reset. Please reconfigure.';

  @override
  String get mobilePhoneNotVerifiedWarning =>
      'You need a verified mobile phone number for two-factor authentication via SMS.';

  @override
  String get verifyEmailTitle => 'Verify Email Address';

  @override
  String verifyEmailText(String emailAddress) {
    return 'Please verify your email address by entering the code that was emailed to you at **$emailAddress**.';
  }

  @override
  String get verifyPhoneTitle => 'Verify Mobile Phone Number';

  @override
  String verifyPhoneText(String mobilePhone) {
    return 'Please verify your mobile phone number by entering the code that was sent via text to **$mobilePhone**.';
  }

  @override
  String get securityProfileFormTitle => 'Security Profile';

  @override
  String get passwordSectionTitle => 'Password';

  @override
  String get updatePasswordText =>
      'To update your password use the **Reset Password** button below. It will sign you out and take you to the password reset dialog. You need to update your password using the code that will be sent to the verified email or mobile phone you have registered with your account.';

  @override
  String get resetPasswordButtonText => 'Reset Password';

  @override
  String get enhancedSecuritySectionTitle => 'Enhanced Security';

  @override
  String get enableMFAText => 'Enable Multi-Factor Authentication';

  @override
  String get mfaViaSMS => 'via an SMS code sent to your mobile phone';

  @override
  String get mfaViaTOTP => 'via a token using an Authenticator app';

  @override
  String get enableBiometricText => 'Enable biometric security where available';

  @override
  String get rememberMeText => 'Remember sign-on state for 24 hours';

  @override
  String get setupTOTPFormTitle => 'Setup Authenticator App';

  @override
  String get totpSectionTitle => 'TOTP Secret Setup';

  @override
  String totpInstructionsText(String totpSetupLinkText) {
    return 'You will need to use the Google Authenticator app on your mobile device to generate the time-based one time password required for multi-factor authentication at sign-in. The app can be downloaded from either the Google Play Store or Apple App Store. Once installed scan the QR-Code below to add a token generator for your sign-in and enter a generated token to verify.$totpSetupLinkText';
  }

  @override
  String get totpSetupLinkText =>
      ' You can also click on this [link](https://totpsetup) to configure the token directly on the installed app if you are on a mobile device.';

  @override
  String get cancelButton => 'Cancel';

  @override
  String get signInButton => 'Sign In';

  @override
  String get signUpButton => 'Sign Up';

  @override
  String get verifyButton => 'Verify';

  @override
  String get resetButton => 'Reset';

  @override
  String get saveButton => 'Save';

  @override
  String get usernameFieldName => 'Username';

  @override
  String get passwordFieldName => 'Password';

  @override
  String get newPasswordFieldName => 'New Password';

  @override
  String get confirmPasswordFieldName => 'Confirm Password';

  @override
  String get emailAddressFieldName => 'Email Address';

  @override
  String get phoneNumberFieldName => 'Mobile Phone Number';

  @override
  String get firstNameFieldName => 'First Name';

  @override
  String get middleNameFieldName => 'Middle Name';

  @override
  String get familyNameFieldName => 'Family Name';

  @override
  String get preferredNameFieldName => 'Preferred Name';

  @override
  String get resetPasswordText => 'Reset Password';

  @override
  String get codeNotReceivedText => 'Didn\'t receive the code?';

  @override
  String get resendCodeText => 'Resend';

  @override
  String get invalidMessage => 'invalid';

  @override
  String get tooShortMessage => 'too short';

  @override
  String get invalidPhoneNumberErrorMessage => 'Invalid phone number';

  @override
  String get passwordTooShortErrorMessage =>
      'Password must be at least 8 characters';

  @override
  String get passwordSpecialCharErrorMessage =>
      'Password must contain at least one special character';

  @override
  String get passwordLowercaseCharErrorMessage =>
      'Password must contain at least one lowercase letter';

  @override
  String get passwordUppercaseCharErrorMessage =>
      'Password must contain at least one uppercase letter';

  @override
  String get passwordNumberErrorMessage =>
      'Password must contain at least one number';

  @override
  String get confirmPasswordLongErrorMessage => 'Passwords do not match';

  @override
  String get confirmPasswordShortErrorMessage => 'no match';

  @override
  String tooShortErrorMessage(String fieldName, int length) {
    return '$fieldName must be at least $length characters long';
  }

  @override
  String invalidEmailErrorMessage(String value) {
    return '\"$value\" is not a valid email address.';
  }

  @override
  String get logoutTitle => 'Logout';

  @override
  String get logoutTooltip => 'logout';

  @override
  String get semStatusUserProfile => 'Click to open user profile dialog';

  @override
  String semCustomInputOptions(String inputName) {
    return 'Click to open pick list for $inputName';
  }
}
