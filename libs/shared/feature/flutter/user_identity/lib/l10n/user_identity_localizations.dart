import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'user_identity_localizations_en.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of UserIdentityLocalizations
/// returned by `UserIdentityLocalizations.of(context)`.
///
/// Applications need to include `UserIdentityLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/user_identity_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: UserIdentityLocalizations.localizationsDelegates,
///   supportedLocales: UserIdentityLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the UserIdentityLocalizations.supportedLocales
/// property.
abstract class UserIdentityLocalizations {
  UserIdentityLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static UserIdentityLocalizations of(BuildContext context) {
    return Localizations.of<UserIdentityLocalizations>(
      context,
      UserIdentityLocalizations,
    )!;
  }

  static const LocalizationsDelegate<UserIdentityLocalizations> delegate =
      _UserIdentityLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[Locale('en')];

  /// No description provided for @sms.
  ///
  /// In en, this message translates to:
  /// **'SMS'**
  String get sms;

  /// No description provided for @email.
  ///
  /// In en, this message translates to:
  /// **'email'**
  String get email;

  /// No description provided for @signInWidgetTitle.
  ///
  /// In en, this message translates to:
  /// **'Sign In'**
  String get signInWidgetTitle;

  /// No description provided for @signUpWidgetTitle.
  ///
  /// In en, this message translates to:
  /// **'Sign Up'**
  String get signUpWidgetTitle;

  /// No description provided for @signUpAgreementText.
  ///
  /// In en, this message translates to:
  /// **'By signing up, you agree to our [EULA](eula), [T&C](tandc) and [Privacy Policy](privacypolicy). Please check accept below if you would like to proceed.'**
  String get signUpAgreementText;

  /// No description provided for @signUpAgreementCheck.
  ///
  /// In en, this message translates to:
  /// **'I accept and agree to the terms of use'**
  String get signUpAgreementCheck;

  /// No description provided for @semSignUpAgreementCheck.
  ///
  /// In en, this message translates to:
  /// **'Check or uncheck terms of user agreement'**
  String get semSignUpAgreementCheck;

  /// No description provided for @verifySignUpWidgetTitle.
  ///
  /// In en, this message translates to:
  /// **'Verify Sign Up'**
  String get verifySignUpWidgetTitle;

  /// No description provided for @verifySignUpUsingCodeSentText.
  ///
  /// In en, this message translates to:
  /// **'Please verify your account using the code that was sent to you via email or text, or request a new code by clicking the link below.'**
  String get verifySignUpUsingCodeSentText;

  /// No description provided for @verifySignUpUsingCodeSentTemplate.
  ///
  /// In en, this message translates to:
  /// **'Please verify your account using the code that was sent via {deliveryMedium} to {destination}.'**
  String verifySignUpUsingCodeSentTemplate(
    String deliveryMedium,
    String destination,
  );

  /// No description provided for @signUpSuccessfulMessage.
  ///
  /// In en, this message translates to:
  /// **'Successfully created an account for \"{username}\". Please sign in and complete your profile to start using the application.'**
  String signUpSuccessfulMessage(String username);

  /// No description provided for @signUpNotInProgressMessage.
  ///
  /// In en, this message translates to:
  /// **'Sign up is not in progress. You need to start the sign up process first.'**
  String get signUpNotInProgressMessage;

  /// No description provided for @verifyMfaWidgetTitle.
  ///
  /// In en, this message translates to:
  /// **'Verify MFA'**
  String get verifyMfaWidgetTitle;

  /// No description provided for @verifyMfaCodeSentText.
  ///
  /// In en, this message translates to:
  /// **'Please verify your login using the code that was either sent via SMS to your registered mobile phone or generated by an Authenticator app which was configured when setting up MFA for your account.'**
  String get verifyMfaCodeSentText;

  /// No description provided for @resetPasswordWidgetTitle.
  ///
  /// In en, this message translates to:
  /// **'Reset Password'**
  String get resetPasswordWidgetTitle;

  /// No description provided for @resetPasswordCodeSentText.
  ///
  /// In en, this message translates to:
  /// **'A verification code was sent via {deliveryMedium} that was registered for user \"{username}\". You need to provide that along with the new password to change the user\'s current password.'**
  String resetPasswordCodeSentText(String deliveryMedium, String username);

  /// No description provided for @passwordResetNotInProgressMessage.
  ///
  /// In en, this message translates to:
  /// **'Password reset is not in progress. You need to start the password reset process first.'**
  String get passwordResetNotInProgressMessage;

  /// No description provided for @noUsernameProvidedForResetPasswordMessage.
  ///
  /// In en, this message translates to:
  /// **'No username provided for password reset. Please provide a username for whom the password reset flow was initiated.'**
  String get noUsernameProvidedForResetPasswordMessage;

  /// No description provided for @resetPasswordSuccessfulMessage.
  ///
  /// In en, this message translates to:
  /// **'Successfully reset the password for \"{username}\". Please sign in using the new password.'**
  String resetPasswordSuccessfulMessage(Object username);

  /// No description provided for @semProfileMenu.
  ///
  /// In en, this message translates to:
  /// **'Click the avatar image to show profile menu options'**
  String get semProfileMenu;

  /// No description provided for @menuOptionProfile.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get menuOptionProfile;

  /// No description provided for @menuOptionSecurity.
  ///
  /// In en, this message translates to:
  /// **'Security'**
  String get menuOptionSecurity;

  /// No description provided for @menuOptionLogout.
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get menuOptionLogout;

  /// No description provided for @userProfileFormTitle.
  ///
  /// In en, this message translates to:
  /// **'User Profile'**
  String get userProfileFormTitle;

  /// No description provided for @namesSectionTitle.
  ///
  /// In en, this message translates to:
  /// **'Names'**
  String get namesSectionTitle;

  /// No description provided for @contactSectionTitle.
  ///
  /// In en, this message translates to:
  /// **'Contact Information'**
  String get contactSectionTitle;

  /// No description provided for @tapToVerifyEmail.
  ///
  /// In en, this message translates to:
  /// **'Tap here to verify the updated email address'**
  String get tapToVerifyEmail;

  /// No description provided for @tapToVerifyMobilePhone.
  ///
  /// In en, this message translates to:
  /// **'Tap here to verify the updated mobile phone number'**
  String get tapToVerifyMobilePhone;

  /// No description provided for @emailNotVerifiedWarning.
  ///
  /// In en, this message translates to:
  /// **'You need a verified email address for account recovery.'**
  String get emailNotVerifiedWarning;

  /// No description provided for @sms2FAResetWarning.
  ///
  /// In en, this message translates to:
  /// **'Your SMS two factor authentication configuration has been reset. Please reconfigure.'**
  String get sms2FAResetWarning;

  /// No description provided for @mobilePhoneNotVerifiedWarning.
  ///
  /// In en, this message translates to:
  /// **'You need a verified mobile phone number for two-factor authentication via SMS.'**
  String get mobilePhoneNotVerifiedWarning;

  /// No description provided for @verifyEmailTitle.
  ///
  /// In en, this message translates to:
  /// **'Verify Email Address'**
  String get verifyEmailTitle;

  /// No description provided for @verifyEmailText.
  ///
  /// In en, this message translates to:
  /// **'Please verify your email address by entering the code that was emailed to you at **{emailAddress}**.'**
  String verifyEmailText(String emailAddress);

  /// No description provided for @verifyPhoneTitle.
  ///
  /// In en, this message translates to:
  /// **'Verify Mobile Phone Number'**
  String get verifyPhoneTitle;

  /// No description provided for @verifyPhoneText.
  ///
  /// In en, this message translates to:
  /// **'Please verify your mobile phone number by entering the code that was sent via text to **{mobilePhone}**.'**
  String verifyPhoneText(String mobilePhone);

  /// No description provided for @securityProfileFormTitle.
  ///
  /// In en, this message translates to:
  /// **'Security Profile'**
  String get securityProfileFormTitle;

  /// No description provided for @passwordSectionTitle.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get passwordSectionTitle;

  /// No description provided for @updatePasswordText.
  ///
  /// In en, this message translates to:
  /// **'To update your password use the **Reset Password** button below. It will sign you out and take you to the password reset dialog. You need to update your password using the code that will be sent to the verified email or mobile phone you have registered with your account.'**
  String get updatePasswordText;

  /// No description provided for @resetPasswordButtonText.
  ///
  /// In en, this message translates to:
  /// **'Reset Password'**
  String get resetPasswordButtonText;

  /// No description provided for @enhancedSecuritySectionTitle.
  ///
  /// In en, this message translates to:
  /// **'Enhanced Security'**
  String get enhancedSecuritySectionTitle;

  /// No description provided for @enableMFAText.
  ///
  /// In en, this message translates to:
  /// **'Enable Multi-Factor Authentication'**
  String get enableMFAText;

  /// No description provided for @mfaViaSMS.
  ///
  /// In en, this message translates to:
  /// **'via an SMS code sent to your mobile phone'**
  String get mfaViaSMS;

  /// No description provided for @mfaViaTOTP.
  ///
  /// In en, this message translates to:
  /// **'via a token using an Authenticator app'**
  String get mfaViaTOTP;

  /// No description provided for @enableBiometricText.
  ///
  /// In en, this message translates to:
  /// **'Enable biometric security where available'**
  String get enableBiometricText;

  /// No description provided for @rememberMeText.
  ///
  /// In en, this message translates to:
  /// **'Remember sign-on state for 24 hours'**
  String get rememberMeText;

  /// No description provided for @setupTOTPFormTitle.
  ///
  /// In en, this message translates to:
  /// **'Setup Authenticator App'**
  String get setupTOTPFormTitle;

  /// No description provided for @totpSectionTitle.
  ///
  /// In en, this message translates to:
  /// **'TOTP Secret Setup'**
  String get totpSectionTitle;

  /// No description provided for @totpInstructionsText.
  ///
  /// In en, this message translates to:
  /// **'You will need to use the Google Authenticator app on your mobile device to generate the time-based one time password required for multi-factor authentication at sign-in. The app can be downloaded from either the Google Play Store or Apple App Store. Once installed scan the QR-Code below to add a token generator for your sign-in and enter a generated token to verify.{totpSetupLinkText}'**
  String totpInstructionsText(String totpSetupLinkText);

  /// No description provided for @totpSetupLinkText.
  ///
  /// In en, this message translates to:
  /// **' You can also click on this [link](https://totpsetup) to configure the token directly on the installed app if you are on a mobile device.'**
  String get totpSetupLinkText;

  /// No description provided for @cancelButton.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancelButton;

  /// No description provided for @signInButton.
  ///
  /// In en, this message translates to:
  /// **'Sign In'**
  String get signInButton;

  /// No description provided for @signUpButton.
  ///
  /// In en, this message translates to:
  /// **'Sign Up'**
  String get signUpButton;

  /// No description provided for @verifyButton.
  ///
  /// In en, this message translates to:
  /// **'Verify'**
  String get verifyButton;

  /// No description provided for @resetButton.
  ///
  /// In en, this message translates to:
  /// **'Reset'**
  String get resetButton;

  /// No description provided for @saveButton.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get saveButton;

  /// No description provided for @usernameFieldName.
  ///
  /// In en, this message translates to:
  /// **'Username'**
  String get usernameFieldName;

  /// No description provided for @passwordFieldName.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get passwordFieldName;

  /// No description provided for @newPasswordFieldName.
  ///
  /// In en, this message translates to:
  /// **'New Password'**
  String get newPasswordFieldName;

  /// No description provided for @confirmPasswordFieldName.
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get confirmPasswordFieldName;

  /// No description provided for @emailAddressFieldName.
  ///
  /// In en, this message translates to:
  /// **'Email Address'**
  String get emailAddressFieldName;

  /// No description provided for @phoneNumberFieldName.
  ///
  /// In en, this message translates to:
  /// **'Mobile Phone Number'**
  String get phoneNumberFieldName;

  /// No description provided for @firstNameFieldName.
  ///
  /// In en, this message translates to:
  /// **'First Name'**
  String get firstNameFieldName;

  /// No description provided for @middleNameFieldName.
  ///
  /// In en, this message translates to:
  /// **'Middle Name'**
  String get middleNameFieldName;

  /// No description provided for @familyNameFieldName.
  ///
  /// In en, this message translates to:
  /// **'Family Name'**
  String get familyNameFieldName;

  /// No description provided for @preferredNameFieldName.
  ///
  /// In en, this message translates to:
  /// **'Preferred Name'**
  String get preferredNameFieldName;

  /// No description provided for @resetPasswordText.
  ///
  /// In en, this message translates to:
  /// **'Reset Password'**
  String get resetPasswordText;

  /// No description provided for @codeNotReceivedText.
  ///
  /// In en, this message translates to:
  /// **'Didn\'t receive the code?'**
  String get codeNotReceivedText;

  /// No description provided for @resendCodeText.
  ///
  /// In en, this message translates to:
  /// **'Resend'**
  String get resendCodeText;

  /// No description provided for @invalidMessage.
  ///
  /// In en, this message translates to:
  /// **'invalid'**
  String get invalidMessage;

  /// No description provided for @tooShortMessage.
  ///
  /// In en, this message translates to:
  /// **'too short'**
  String get tooShortMessage;

  /// No description provided for @invalidPhoneNumberErrorMessage.
  ///
  /// In en, this message translates to:
  /// **'Invalid phone number'**
  String get invalidPhoneNumberErrorMessage;

  /// No description provided for @passwordTooShortErrorMessage.
  ///
  /// In en, this message translates to:
  /// **'Password must be at least 8 characters'**
  String get passwordTooShortErrorMessage;

  /// No description provided for @passwordSpecialCharErrorMessage.
  ///
  /// In en, this message translates to:
  /// **'Password must contain at least one special character'**
  String get passwordSpecialCharErrorMessage;

  /// No description provided for @passwordLowercaseCharErrorMessage.
  ///
  /// In en, this message translates to:
  /// **'Password must contain at least one lowercase letter'**
  String get passwordLowercaseCharErrorMessage;

  /// No description provided for @passwordUppercaseCharErrorMessage.
  ///
  /// In en, this message translates to:
  /// **'Password must contain at least one uppercase letter'**
  String get passwordUppercaseCharErrorMessage;

  /// No description provided for @passwordNumberErrorMessage.
  ///
  /// In en, this message translates to:
  /// **'Password must contain at least one number'**
  String get passwordNumberErrorMessage;

  /// No description provided for @confirmPasswordLongErrorMessage.
  ///
  /// In en, this message translates to:
  /// **'Passwords do not match'**
  String get confirmPasswordLongErrorMessage;

  /// No description provided for @confirmPasswordShortErrorMessage.
  ///
  /// In en, this message translates to:
  /// **'no match'**
  String get confirmPasswordShortErrorMessage;

  /// No description provided for @tooShortErrorMessage.
  ///
  /// In en, this message translates to:
  /// **'{fieldName} must be at least {length} characters long'**
  String tooShortErrorMessage(String fieldName, int length);

  /// No description provided for @invalidEmailErrorMessage.
  ///
  /// In en, this message translates to:
  /// **'\"{value}\" is not a valid email address.'**
  String invalidEmailErrorMessage(String value);

  /// No description provided for @logoutTitle.
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get logoutTitle;

  /// No description provided for @logoutTooltip.
  ///
  /// In en, this message translates to:
  /// **'logout'**
  String get logoutTooltip;

  /// No description provided for @semStatusUserProfile.
  ///
  /// In en, this message translates to:
  /// **'Click to open user profile dialog'**
  String get semStatusUserProfile;

  /// No description provided for @semCustomInputOptions.
  ///
  /// In en, this message translates to:
  /// **'Click to open pick list for {inputName}'**
  String semCustomInputOptions(String inputName);
}

class _UserIdentityLocalizationsDelegate
    extends LocalizationsDelegate<UserIdentityLocalizations> {
  const _UserIdentityLocalizationsDelegate();

  @override
  Future<UserIdentityLocalizations> load(Locale locale) {
    return SynchronousFuture<UserIdentityLocalizations>(
      lookupUserIdentityLocalizations(locale),
    );
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en'].contains(locale.languageCode);

  @override
  bool shouldReload(_UserIdentityLocalizationsDelegate old) => false;
}

UserIdentityLocalizations lookupUserIdentityLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return UserIdentityLocalizationsEn();
  }

  throw FlutterError(
    'UserIdentityLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
