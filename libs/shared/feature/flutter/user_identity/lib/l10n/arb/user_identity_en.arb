{"@@locale": "en", "sms": "SMS", "email": "email", "signInWidgetTitle": "Sign In", "signUpWidgetTitle": "Sign Up", "signUpAgreementText": "By signing up, you agree to our [EULA](eula), [T&C](tandc) and [Privacy Policy](privacypolicy). Please check accept below if you would like to proceed.", "signUpAgreementCheck": "I accept and agree to the terms of use", "semSignUpAgreementCheck": "Check or uncheck terms of user agreement", "verifySignUpWidgetTitle": "Verify Sign Up", "verifySignUpUsingCodeSentText": "Please verify your account using the code that was sent to you via email or text, or request a new code by clicking the link below.", "verifySignUpUsingCodeSentTemplate": "Please verify your account using the code that was sent via {deliveryMedium} to {destination}.", "@verifySignUpUsingCodeSentTemplate": {"placeholders": {"deliveryMedium": {"type": "String"}, "destination": {"type": "String"}}}, "signUpSuccessfulMessage": "Successfully created an account for \"{username}\". Please sign in and complete your profile to start using the application.", "@signUpSuccessfulMessage": {"placeholders": {"username": {"type": "String"}}}, "signUpNotInProgressMessage": "Sign up is not in progress. You need to start the sign up process first.", "verifyMfaWidgetTitle": "Verify MFA", "verifyMfaCodeSentText": "Please verify your login using the code that was either sent via SMS to your registered mobile phone or generated by an Authenticator app which was configured when setting up MFA for your account.", "resetPasswordWidgetTitle": "Reset Password", "resetPasswordCodeSentText": "A verification code was sent via {deliveryMedium} that was registered for user \"{username}\". You need to provide that along with the new password to change the user's current password.", "@resetPasswordCodeSentText": {"placeholders": {"deliveryMedium": {"type": "String"}, "username": {"type": "String"}}}, "passwordResetNotInProgressMessage": "Password reset is not in progress. You need to start the password reset process first.", "noUsernameProvidedForResetPasswordMessage": "No username provided for password reset. Please provide a username for whom the password reset flow was initiated.", "resetPasswordSuccessfulMessage": "Successfully reset the password for \"{username}\". Please sign in using the new password.", "semProfileMenu": "Click the avatar image to show profile menu options", "menuOptionProfile": "Profile", "menuOptionSecurity": "Security", "menuOptionLogout": "Logout", "userProfileFormTitle": "User Profile", "namesSectionTitle": "Names", "contactSectionTitle": "Contact Information", "tapToVerifyEmail": "Tap here to verify the updated email address", "tapToVerifyMobilePhone": "Tap here to verify the updated mobile phone number", "emailNotVerifiedWarning": "You need a verified email address for account recovery.", "sms2FAResetWarning": "Your SMS two factor authentication configuration has been reset. Please reconfigure.", "mobilePhoneNotVerifiedWarning": "You need a verified mobile phone number for two-factor authentication via SMS.", "verifyEmailTitle": "Verify Em<PERSON> Address", "verifyEmailText": "Please verify your email address by entering the code that was emailed to you at **{emailAddress}**.", "@verifyEmailText": {"placeholders": {"emailAddress": {"type": "String"}}}, "verifyPhoneTitle": "Verify Mobile Phone Number", "verifyPhoneText": "Please verify your mobile phone number by entering the code that was sent via text to **{mobilePhone}**.", "@verifyPhoneText": {"placeholders": {"mobilePhone": {"type": "String"}}}, "securityProfileFormTitle": "Security Profile", "passwordSectionTitle": "Password", "updatePasswordText": "To update your password use the **Reset Password** button below. It will sign you out and take you to the password reset dialog. You need to update your password using the code that will be sent to the verified email or mobile phone you have registered with your account.", "resetPasswordButtonText": "Reset Password", "enhancedSecuritySectionTitle": "Enhanced Security", "enableMFAText": "Enable Multi-Factor Authentication", "mfaViaSMS": "via an SMS code sent to your mobile phone", "mfaViaTOTP": "via a token using an Authenticator app", "enableBiometricText": "Enable biometric security where available", "rememberMeText": "Remember sign-on state for 24 hours", "setupTOTPFormTitle": "Setup Authenticator App", "totpSectionTitle": "TOTP Secret Setup", "totpInstructionsText": "You will need to use the Google Authenticator app on your mobile device to generate the time-based one time password required for multi-factor authentication at sign-in. The app can be downloaded from either the Google Play Store or Apple App Store. Once installed scan the QR-Code below to add a token generator for your sign-in and enter a generated token to verify.{totpSetupLinkText}", "@totpInstructionsText": {"placeholders": {"totpSetupLinkText": {"type": "String"}}}, "totpSetupLinkText": " You can also click on this [link](https://totpsetup) to configure the token directly on the installed app if you are on a mobile device.", "cancelButton": "Cancel", "signInButton": "Sign In", "signUpButton": "Sign Up", "verifyButton": "Verify", "resetButton": "Reset", "saveButton": "Save", "usernameFieldName": "Username", "passwordFieldName": "Password", "newPasswordFieldName": "New Password", "confirmPasswordFieldName": "Confirm Password", "emailAddressFieldName": "Email Address", "phoneNumberFieldName": "Mobile Phone Number", "firstNameFieldName": "First Name", "middleNameFieldName": "Middle Name", "familyNameFieldName": "Family Name", "preferredNameFieldName": "Preferred Name", "resetPasswordText": "Reset Password", "codeNotReceivedText": "Didn't receive the code?", "resendCodeText": "Resend", "invalidMessage": "invalid", "tooShortMessage": "too short", "invalidPhoneNumberErrorMessage": "Invalid phone number", "passwordTooShortErrorMessage": "Password must be at least 8 characters", "passwordSpecialCharErrorMessage": "Password must contain at least one special character", "passwordLowercaseCharErrorMessage": "Password must contain at least one lowercase letter", "passwordUppercaseCharErrorMessage": "Password must contain at least one uppercase letter", "passwordNumberErrorMessage": "Password must contain at least one number", "confirmPasswordLongErrorMessage": "Passwords do not match", "confirmPasswordShortErrorMessage": "no match", "tooShortErrorMessage": "{fieldName} must be at least {length} characters long", "@tooShortErrorMessage": {"placeholders": {"fieldName": {"type": "String"}, "length": {"type": "int"}}}, "invalidEmailErrorMessage": "\"{value}\" is not a valid email address.", "@invalidEmailErrorMessage": {"placeholders": {"value": {"type": "String"}}}, "logoutTitle": "Logout", "logoutTooltip": "logout", "semStatusUserProfile": "Click to open user profile dialog", "semCustomInputOptions": "Click to open pick list for {inputName}", "@semCustomInputOptions": {"placeholders": {"inputName": {"type": "String"}}}}