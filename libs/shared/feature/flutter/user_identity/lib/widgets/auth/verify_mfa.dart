import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:ui_widgets_component/ui_widgets.dart';
import 'package:nav_layouts_component/nav_layouts.dart';
import 'package:identity_service/identity_service.dart' as identity;

import 'sign_in.dart';

import '../../l10n/l10n.dart';

class VerifyMfaWidget extends StatefulNavigableWidget {
  @override
  String get routeName => name;
  static const String name = 'verifyMfa';

  @override
  String get routePath => path;
  static const String path = 'verify-mfa';

  /// The name of the initial logged in route
  final String initialLoggedInRoute;

  const VerifyMfaWidget({
    super.key,
    super.parentNavigatorKey,
    super.pageBuilder,
    required this.initialLoggedInRoute,
  });

  @override
  State<StatefulWidget> createState() => _VerifyMfaWidgetState();
}

class _VerifyMfaWidgetState extends State<VerifyMfaWidget> {
  // declare a GlobalKey
  final formKey = GlobalKey<FormState>();

  String mfaCode = '';

  final controller = TextEditingController();
  final focusNode = FocusNode();
  bool showError = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = context.l10n;

    return BlocListener<identity.IdentityService, identity.IdentityState>(
      listener: (context, identityState) {
        if (identityState.isLoggedIn) {
          widget.goTo(widget.initialLoggedInRoute);
        } else if (!identityState.isLoginInProgress) {
          widget.goTo(SignInWidget.name);
          return;
        }
      },
      child: BlocBuilder<identity.IdentityService, identity.IdentityState>(
        builder: (
          context,
          identityState,
        ) {
          final isLoading = identityState.isLoading(
            [
              identity.loginLoading,
              identity.validateMFALoading,
            ],
          );
          final inputEnabled = !isLoading;

          return DialogForm(
            key: formKey,
            wrapWithDisplayAreaSizeProvider: false,
            title: l10n.verifyMfaWidgetTitle,
            body: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.fromLTRB(25.0, 8.0, 25.0, 16.0),
                  child: Text(
                    l10n.verifyMfaCodeSentText,
                    textAlign: TextAlign.center,
                    style: theme.textTheme.labelLarge,
                  ),
                ),
                CodeInput(
                  length: 6,
                  isRequired: true,
                  enabled: inputEnabled,
                  onCompleted: (value) => setState(
                    () => mfaCode = value,
                  ),
                ),
              ],
            ),
            actions: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 8),
                  child: ElevatedButton.icon(
                    label: Text(l10n.cancelButton),
                    icon: const Icon(
                      Icons.cancel,
                    ),
                    iconAlignment: IconAlignment.end,
                    onPressed:
                        inputEnabled ? () => _returnToSignIn(context) : null,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 8),
                  child: ElevatedIconButton(
                    label: Text(l10n.verifyButton),
                    icon: Icons.check,
                    isLoading: isLoading,
                    onPressed: inputEnabled && mfaCode.length == 6
                        ? _verifyMfaCode(context)
                        : null,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  void _returnToSignIn(BuildContext context) {
    final identityService = context.read<identity.IdentityService>();
    identityService.signOut();
  }

  Function() _verifyMfaCode(BuildContext context) {
    final identityService = context.read<identity.IdentityService>();

    return () {
      identityService.validateMFACode(
        mfaCode,
      );
    };
  }
}
