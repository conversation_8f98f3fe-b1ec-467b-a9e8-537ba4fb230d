import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import 'package:ui_widgets_component/ui_widgets.dart';
import 'package:nav_layouts_component/nav_layouts.dart';
import 'package:app_framework_component/app_framework.dart' as app;
import 'package:identity_service/identity_service.dart' as identity;

import '../../l10n/l10n.dart';

import 'sign_in.dart';

class VerifySignUpWidget extends StatefulNavigableWidget {
  @override
  String get routeName => name;
  static const String name = 'verifySignUp';

  @override
  String get routePath => path;
  static const String path = 'verify-sign-up';

  VerifySignUpWidget({
    Key? key,
    super.parentNavigatorKey,
    super.pageBuilder,
  }) : super(key: key ?? _VerifySignUpWidgetState.stateKey);

  @override
  Future<String?> evaluateRedirect(
    BuildContext context,
    GoRouterState state,
  ) async {
    final identityService = context.read<identity.IdentityService>();

    String? redirectTo;
    if (!identityService.state.isSignUpInProgress &&
        !identityService.state.needsSignUpConfirmation) {
      // navigate back to the sign-in
      // with error message as sign-up
      // is not in progress
      identityService.addStateMessage(
        app.Message.error(
          context.l10n.signUpNotInProgressMessage,
        ),
      );

      await identityService.clearAwaitingVerification();
      redirectTo = SignInWidget.name;
    }
    return redirectTo;
  }

  @override
  State<StatefulWidget> createState() => _VerifySignUpWidgetState();
}

class _VerifySignUpWidgetState extends State<VerifySignUpWidget> {
  // declare a GlobalKey for this state which is required
  // to trigger evaluation of the redirect logic
  static final stateKey = GlobalKey<_VerifySignUpWidgetState>();

  // declare a GlobalKey
  final formKey = GlobalKey<FormState>();

  String signUpCode = '';

  final controller = TextEditingController();
  final focusNode = FocusNode();

  bool signUpCodeResent = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = context.l10n;

    return BlocListener<identity.IdentityService, identity.IdentityState>(
      listenWhen: (_, __) {
        if (signUpCodeResent) {
          signUpCodeResent = false;
          return false;
        } else {
          return true;
        }
      },
      listener: (context, identityState) {
        final identityService = context.read<identity.IdentityService>();

        // check if sign up flow is in progress
        // if not return to sign-in page
        if (!identityState.isSignUpInProgress &&
            !identityService.state.needsSignUpConfirmation) {
          // show a message that the sign up was
          // successful if the user is confirmed
          if (identityState.user.status == identity.UserStatus.confirmed) {
            Future(() {
              identityService.addStateMessage(
                app.Message.info(
                  l10n.signUpSuccessfulMessage(
                    identityState.user.username,
                  ),
                ),
              );
            });
          }

          widget.goTo(SignInWidget.name);
          return;
        }
      },
      child: BlocBuilder<identity.IdentityService, identity.IdentityState>(
        builder: (
          context,
          identityState,
        ) {
          final isLoading = identityState.isLoading(
            [identity.confirmSignUpLoading],
          );
          final inputEnabled = !isLoading;

          String codeSentText = l10n.verifySignUpUsingCodeSentText;
          final verification = identityState.awaitingVerification;
          if (verification != null && verification.destination != null) {
            if (verification.type == identity.VerificationType.email) {
              codeSentText = l10n.verifySignUpUsingCodeSentTemplate(
                l10n.email,
                verification.destination!,
              );
            } else if (verification.type == identity.VerificationType.sms) {
              codeSentText = l10n.verifySignUpUsingCodeSentTemplate(
                l10n.email,
                verification.destination!,
              );
            }
          }

          return DialogForm(
            key: formKey,
            wrapWithDisplayAreaSizeProvider: false,
            title: l10n.verifySignUpWidgetTitle,
            body: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.fromLTRB(25.0, 8.0, 25.0, 16.0),
                  child: Text(
                    codeSentText,
                    textAlign: TextAlign.center,
                    style: theme.textTheme.labelLarge,
                  ),
                ),
                CodeInput(
                  length: 6,
                  isRequired: true,
                  enabled: inputEnabled,
                  onCompleted: (value) => setState(
                    () => signUpCode = value,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Text(
                    l10n.codeNotReceivedText,
                    style: theme.textTheme.labelLarge,
                  ),
                ),
                TextLink(
                  text: l10n.resendCodeText,
                  padding: const EdgeInsets.only(top: 2, bottom: 8),
                  onPressed: _resendSignUpCode(context, identityState),
                ),
              ],
            ),
            actions: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 8),
                  child: ElevatedButton.icon(
                    label: Text(l10n.cancelButton),
                    icon: const Icon(Icons.cancel),
                    iconAlignment: IconAlignment.end,
                    onPressed: inputEnabled ? _cancel(context) : null,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 8),
                  child: ElevatedIconButton(
                    label: Text(l10n.verifyButton),
                    icon: Icons.check,
                    isLoading: isLoading,
                    onPressed: signUpCode.length == 6 && inputEnabled
                        ? _verifySignUpCode(context, identityState)
                        : null,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  void Function() _cancel(BuildContext context) {
    final identityService = context.read<identity.IdentityService>();

    return () {
      if (identityService.state.needsSignUpConfirmation) {
        // initiate a sign-out to clear the session
        // as this state occurs when a user with an
        // unconfirmed account tries to sign in
        identityService.signOut();
      }
      widget.goTo(SignInWidget.name);
    };
  }

  void Function() _resendSignUpCode(
    BuildContext context,
    identity.IdentityState identityState,
  ) {
    final identityService = context.read<identity.IdentityService>();
    signUpCodeResent = true;

    return () => identityService.resendSignUpCode(
          identityState.user.username,
        );
  }

  void Function() _verifySignUpCode(
    BuildContext context,
    identity.IdentityState identityState,
  ) {
    final identityService = context.read<identity.IdentityService>();

    return () => identityService.confirmSignUpCode(
          identityState.user.username,
          signUpCode,
        );
  }
}
