import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:dlibphonenumber/dlibphonenumber.dart';

import 'package:ui_widgets_component/ui_widgets.dart';
import 'package:nav_layouts_component/nav_layouts.dart';
import 'package:identity_service/identity_service.dart' as identity;

import '../../user_identity_feature.dart';
import '../../l10n/l10n.dart';
import '../../config/user_identity_feature_config.dart';

import 'sign_in.dart';
import 'verify_sign_up.dart';

class SignUpWidget extends StatefulNavigableWidget {
  @override
  String get routeName => name;
  static const String name = 'signUp';

  @override
  String get routePath => path;
  static const String path = 'sign-up';

  const SignUpWidget({
    super.key,
    super.parentNavigatorKey,
    super.pageBuilder,
  });

  @override
  State<StatefulWidget> createState() => _SignUpWidgetState();
}

class _SignUpWidgetState extends State<SignUpWidget> {
  // declare a GlobalKey
  final formKey = GlobalKey<FormState>();

  late final UserIdentityFeatureConfig config;

  String username = '';
  String password = '';
  String confirmPassword = '';
  String emailAddress = '';
  String mobilePhone = '';

  bool isAgreementChecked = false;

  late final bool isMobileNumberRequired;
  late final bool mobileDialCodeReadOnly;
  late final String countryCode;

  late final Validator emailValidator;

  ScrollMetrics? scrollMetrics;

  @override
  void initState() {
    super.initState();

    config = UserIdentityFeature.instance().config;
    isMobileNumberRequired = config.mobileNumberRequired;
    mobileDialCodeReadOnly = config.mobileDialCodeReadOnly;
    countryCode = config.mobileNumberCountryCode;
    emailValidator = config.emailValidator;

    final identityService = context.read<identity.IdentityService>();
    identityService.clearAwaitingVerification();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = context.l10n;

    // this value should be customizable. currently it is
    // hardcoded based on total height based on the [Card]
    // container in UserAuthFeature.buildFeatureWidget that
    // wraps the shell route widgets.
    const formViewHeight = 657.0;

    return BlocListener<identity.IdentityService, identity.IdentityState>(
      listener: (context, identityState) {
        if (identityState.isSignUpInProgress) {
          widget.goTo(VerifySignUpWidget.name);
        }
      },
      child: BlocBuilder<identity.IdentityService, identity.IdentityState>(
        builder: (
          context,
          identityState,
        ) {
          final isLoading = identityState.isLoading(
            [identity.signUpLoading],
          );
          final inputEnabled = !isLoading;

          return DialogForm(
            formKey: formKey,
            formViewHeight: formViewHeight,
            // 160 is the height of the non-scrollable part card
            // includes card padding, title and actions
            nonScrollableHeight: 160,
            wrapWithDisplayAreaSizeProvider: false,
            title: l10n.signUpWidgetTitle,
            body: Column(
              children: [
                UsernameInput(
                  enabled: inputEnabled,
                  isRequired: true,
                  validator: MinLengthValidator(
                    length: 3,
                  ),
                  onChanged: (value, _) => setState(
                    () => username = value!,
                  ),
                ),
                PasswordInput(
                  enabled: inputEnabled,
                  isRequired: true,
                  validator: PasswordValidator(),
                  onChanged: (value) => setState(
                    () => password = value,
                  ),
                ),
                ConfirmPasswordInput(
                  enabled: inputEnabled,
                  isRequired: true,
                  validator: ConfirmPasswordValidator(
                    value: password,
                  ),
                  onChanged: (value) => setState(
                    () => confirmPassword = value,
                  ),
                ),
                EmailAddressInput(
                  enabled: inputEnabled,
                  isRequired: true,
                  validator: emailValidator,
                  onChanged: (value, _) => setState(
                    () => emailAddress = value!,
                  ),
                ),
                if (isMobileNumberRequired)
                  PhoneNumberInput(
                    defaultCountryCode: countryCode,
                    enabled: inputEnabled,
                    isRequired: true,
                    enableCountrySelection: !mobileDialCodeReadOnly,
                    onChanged: (value, isValid) => setState(
                      () {
                        if (isValid) {
                          mobilePhone = PhoneNumberUtil.instance.format(
                            value,
                            PhoneNumberFormat.e164,
                          );
                        }
                      },
                    ),
                  ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(20.0, 8.0, 20.0, 0.0),
                  child: MarkdownBody(
                    data: l10n.signUpAgreementText,
                    onTapLink: (_, String? key, __) async {
                      final href = config.agreements[key];
                      if (href != null) {
                        await launchUrl(Uri.parse(href));
                      }
                    },
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(30.0, 8.0, 20.0, 0.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Checkbox(
                        value: isAgreementChecked,
                        onChanged: inputEnabled
                            ? (bool? value) => setState(
                                  () => isAgreementChecked = value!,
                                )
                            : null,
                        semanticLabel: l10n.semSignUpAgreementCheck,
                      ),
                      const SizedBox(width: 4.0),
                      Flexible(
                        child: Text(
                          l10n.signUpAgreementCheck,
                          style: theme.textTheme.bodyLarge,
                          softWrap: true,
                          maxLines: 2,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            actions: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 8),
                  child: ElevatedButton.icon(
                    label: Text(l10n.cancelButton),
                    icon: const Icon(Icons.cancel),
                    iconAlignment: IconAlignment.end,
                    onPressed: inputEnabled
                        ? () => widget.goTo(SignInWidget.name)
                        : null,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 8),
                  child: ElevatedIconButton(
                    label: Text(l10n.signUpButton),
                    icon: Icons.person_add,
                    isLoading: isLoading,
                    onPressed: username.isNotEmpty &&
                            password.isNotEmpty &&
                            confirmPassword.isNotEmpty &&
                            emailAddress.isNotEmpty &&
                            (!isMobileNumberRequired ||
                                mobilePhone.isNotEmpty) &&
                            isAgreementChecked &&
                            inputEnabled &&
                            formKey.currentState != null &&
                            formKey.currentState!.validate()
                        ? _signUp(context)
                        : null,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  void Function() _signUp(BuildContext context) {
    final identityService = context.read<identity.IdentityService>();

    return () {
      identityService.signUp(
        identity.User(
          username: username,
          emailAddress: emailAddress,
          mobilePhone: mobilePhone,
        ),
        password,
      );
    };
  }
}
