import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import 'package:ui_widgets_component/ui_widgets.dart';
import 'package:nav_layouts_component/nav_layouts.dart';
import 'package:identity_service/identity_service.dart' as identity;

import 'sign_up.dart';
import 'verify_sign_up.dart';
import 'verify_mfa.dart';
import 'reset_password.dart';

import '../../l10n/l10n.dart';

class SignInWidget extends StatefulNavigableWidget {
  @override
  String get routeName => name;
  static const String name = 'signIn';

  @override
  String get routePath => path;
  static const String path = '/sign-in';

  @override
  List<Navigable> get childRoutes => [
        SignUpWidget(
          parentNavigatorKey: super.parentNavigatorKey,
          pageBuilder: super.pageBuilder,
        ),
        VerifySignUpWidget(
          parentNavigatorKey: super.parentNavigatorKey,
          pageBuilder: super.pageBuilder,
        ),
        VerifyMfaWidget(
          parentNavigatorKey: super.parentNavigatorKey,
          pageBuilder: super.pageBuilder,
          initialLoggedInRoute: initialLoggedInRoute,
        ),
        ResetPasswordWidget(
          parentNavigatorKey: super.parentNavigatorKey,
          pageBuilder: super.pageBuilder,
        ),
      ];

  /// The name of the initial logged in route
  final String initialLoggedInRoute;

  SignInWidget({
    Key? key,
    super.parentNavigatorKey,
    super.pageBuilder,
    required this.initialLoggedInRoute,
  }) : super(key: key ?? _SignInWidgetState.stateKey);

  @override
  Future<String?> evaluateRedirect(
    BuildContext context,
    GoRouterState state,
  ) async {
    final identityService = context.read<identity.IdentityService>();
    // if logged in then redirect to the initial logged in route
    return identityService.state.isLoggedIn ? initialLoggedInRoute : null;
  }

  @override
  State<StatefulWidget> createState() => _SignInWidgetState();
}

class _SignInWidgetState extends State<SignInWidget> {
  // declare a GlobalKey for this state which is required
  // to trigger evaluation of the redirect logic
  static final stateKey = GlobalKey<_SignInWidgetState>();

  // declare a GlobalKey
  final formKey = GlobalKey<FormState>();

  String username = '';
  String password = '';

  @override
  void initState() {
    super.initState();

    final identityState = context.read<identity.IdentityService>().state;
    if (identityState.user.status != identity.UserStatus.unknown &&
        identityState.user.username.isNotEmpty) {
      setState(() {
        username = identityState.user.username;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;

    return BlocListener<identity.IdentityService, identity.IdentityState>(
      listener: (context, identityState) {
        // password reset flow initiated
        if (identityState.isPasswordResetInitiated) {
          widget.goTo(
            ResetPasswordWidget.name,
            queryParameters: {
              'username': username,
            },
          );
          return;
        }

        if (identityState.isLoginInProgress) {
          switch (identityState.awaitingLogin) {
            case identity.AuthType.authMFAwithSMS:
            case identity.AuthType.authMFAwithTOTP:
              widget.goTo(VerifyMfaWidget.name);
              break;
            case identity.AuthType.needsPasswordReset:
              widget.goTo(ResetPasswordWidget.name);
              break;
            case identity.AuthType.needsSignUpConfirmation:
              widget.goTo(VerifySignUpWidget.name);
              break;
            default:
              break;
          }
        } else if (identityState.isLoggedIn) {
          widget.goTo(widget.initialLoggedInRoute);
        }
      },
      child: BlocBuilder<identity.IdentityService, identity.IdentityState>(
        builder: (
          context,
          identityState,
        ) {
          final isLoading = identityState.isLoading(
            [
              identity.loginLoading,
              identity.resetPasswordLoading,
            ],
          );
          final inputEnabled = !isLoading;
          final signInEnabled =
              username.isNotEmpty && password.isNotEmpty && inputEnabled;

          return DialogForm(
            key: formKey,
            wrapWithDisplayAreaSizeProvider: false,
            title: l10n.signInWidgetTitle,
            body: Column(
              children: [
                UsernameInput(
                  padding: const EdgeInsets.fromLTRB(8.0, 8.0, 8.0, 8.0),
                  initialValue: username,
                  enabled: inputEnabled,
                  onChanged: (value, _) => setState(
                    () => username = value!,
                  ),
                  onEnter: (_) {
                    if (signInEnabled) {
                      _signIn(context)();
                    }
                  },
                ),
                PasswordInput(
                  enabled: inputEnabled,
                  onChanged: (value) => setState(
                    () => password = value,
                  ),
                  onEnter: (_) {
                    if (signInEnabled) {
                      _signIn(context)();
                    }
                  },
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextLink(
                      text: l10n.resetPasswordText,
                      enabled: username.isNotEmpty && inputEnabled,
                      padding: const EdgeInsets.only(top: 4),
                      onPressed: _resetPassword(context),
                    ),
                  ],
                ),
              ],
            ),
            actions: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 8),
                  child: ElevatedButton.icon(
                    label: Text(l10n.signUpButton),
                    icon: const Icon(Icons.person_add),
                    iconAlignment: IconAlignment.end,
                    onPressed: inputEnabled
                        ? () {
                            widget.goTo(SignUpWidget.name);
                          }
                        : null,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 8),
                  child: ElevatedIconButton(
                    label: Text(l10n.signInButton),
                    icon: Icons.login,
                    isLoading: isLoading,
                    onPressed: signInEnabled ? _signIn(context) : null,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  void Function() _resetPassword(BuildContext context) {
    final identityService = context.read<identity.IdentityService>();

    return () {
      identityService.resetPassword(
        username,
      );
    };
  }

  void Function() _signIn(BuildContext context) {
    final identityService = context.read<identity.IdentityService>();

    return () {
      identityService.signIn(
        username,
        password,
      );
    };
  }
}
