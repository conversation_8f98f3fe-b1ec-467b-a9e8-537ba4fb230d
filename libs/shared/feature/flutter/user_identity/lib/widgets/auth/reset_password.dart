import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import 'package:ui_widgets_component/ui_widgets.dart';
import 'package:nav_layouts_component/nav_layouts.dart';
import 'package:app_framework_component/app_framework.dart' as app;
import 'package:identity_service/identity_service.dart' as identity;

import 'sign_in.dart';

import '../../l10n/l10n.dart';

class ResetPasswordWidget extends StatefulNavigableWidget {
  @override
  String get routeName => name;
  static const String name = 'resetPassword';

  @override
  String get routePath => path;
  static const String path = 'reset-password';

  ResetPasswordWidget({
    Key? key,
    super.parentNavigatorKey,
    super.pageBuilder,
  }) : super(key: key ?? _ResetPasswordWidgetState.stateKey);

  @override
  Future<String?> evaluateRedirect(
    BuildContext context,
    GoRouterState state,
  ) async {
    final identityService = context.read<identity.IdentityService>();

    String? redirectTo;
    if (!identityService.state.isPasswordResetInProgress) {
      // navigate back to the sign-in
      // with error message as password
      // reset flow is not in progress
      identityService.addStateMessage(
        app.Message.error(
          context.l10n.passwordResetNotInProgressMessage,
        ),
      );

      await identityService.clearAwaitingVerification();
      redirectTo = SignInWidget.name;
    } else {
      final username = state.uri.queryParameters['username'];
      if (username == null || username.isEmpty) {
        // navigate back to the sign-in
        // page with error message as
        // no username was provided
        identityService.addStateMessage(
          app.Message.error(
            context.l10n.noUsernameProvidedForResetPasswordMessage,
          ),
        );

        await identityService.clearAwaitingVerification();
        redirectTo = SignInWidget.name;
      }
    }
    return redirectTo;
  }

  @override
  State<StatefulWidget> createState() => _ResetPasswordWidgetState();
}

class _ResetPasswordWidgetState extends State<ResetPasswordWidget> {
  // declare a GlobalKey for this state which is required
  // to trigger evaluation of the redirect logic
  static final stateKey = GlobalKey<_ResetPasswordWidgetState>();

  // declare a GlobalKey for the states form
  final formKey = GlobalKey<FormState>();

  String username = '';
  String resetPasswordCode = '';
  String password = '';
  String confirmPassword = '';

  final controller = TextEditingController();
  final focusNode = FocusNode();
  bool showError = false;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      // BuildContext is not fully initialized at that point
      // so we need to wait for the next frame to get the
      // router state from the context
      final routerState = GoRouterState.of(context);

      setState(() {
        username = routerState.uri.queryParameters['username'] ?? '';
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = context.l10n;

    return BlocListener<identity.IdentityService, identity.IdentityState>(
      listener: (context, identityState) {
        // check if password reset flow is in
        // progress if not return to sign-in page
        if (!identityState.isPasswordResetInProgress) {
          widget.goTo(SignInWidget.name);
          return;
        }
      },
      child: BlocBuilder<identity.IdentityService, identity.IdentityState>(
        builder: (
          context,
          identityState,
        ) {
          final isLoading = identityState.isLoading(
            [
              identity.loginLoading,
              identity.updatePasswordLoading,
            ],
          );
          final inputEnabled = !isLoading;

          final verificationType = identityState.awaitingVerification?.type;
          final deliveryMedium =
              verificationType == identity.VerificationType.email
                  ? l10n.email
                  : l10n.sms;

          return Form(
            key: formKey,
            child: Column(
              children: [
                Text(
                  l10n.resetPasswordWidgetTitle,
                  style: theme.textTheme.headlineSmall,
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Divider(
                    color: theme.colorScheme.outline.withValues(alpha: 0.5),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(25.0, 8.0, 25.0, 16.0),
                  child: Text(
                    l10n.resetPasswordCodeSentText(
                      deliveryMedium,
                      username,
                    ),
                    textAlign: TextAlign.center,
                    style: theme.textTheme.labelLarge,
                  ),
                ),
                CodeInput(
                  length: 6,
                  isRequired: true,
                  enabled: inputEnabled,
                  onCompleted: (value) => setState(
                    () => resetPasswordCode = value,
                  ),
                ),
                PasswordInput(
                  isRequired: true,
                  labelText: l10n.newPasswordFieldName,
                  validator: PasswordValidator(),
                  enabled: inputEnabled,
                  onChanged: (value) => setState(
                    () => password = value,
                  ),
                ),
                PasswordInput(
                  isRequired: true,
                  labelText: l10n.confirmPasswordFieldName,
                  enabled: inputEnabled,
                  validator: CallbackValidator(
                    callback: (value) => value == password,
                    longErrorMessage: l10n.confirmPasswordLongErrorMessage,
                    shortErrorMessage: l10n.confirmPasswordShortErrorMessage,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Divider(
                    color: theme.colorScheme.outline.withValues(alpha: 0.5),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(8.0, 0.0, 8.0, 0.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(left: 8),
                        child: ElevatedButton.icon(
                          label: Text(l10n.cancelButton),
                          icon: const Icon(Icons.cancel),
                          iconAlignment: IconAlignment.end,
                          onPressed: inputEnabled
                              ? () => _returnToSignIn(context)
                              : null,
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 8),
                        child: ElevatedIconButton(
                          label: Text(l10n.resetButton),
                          icon: Icons.lock_reset,
                          isLoading: isLoading,
                          onPressed: inputEnabled &&
                                  resetPasswordCode.length == 6 &&
                                  password.isNotEmpty
                              ? _resetPassword(context)
                              : null,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  void _returnToSignIn(BuildContext context) {
    final identityService = context.read<identity.IdentityService>();
    identityService.clearAwaitingVerification();
    widget.goTo(SignInWidget.name);
  }

  void Function() _resetPassword(BuildContext context) {
    final identityService = context.read<identity.IdentityService>();

    return () {
      identityService.updatePassword(
        username,
        password,
        resetPasswordCode,
      );
    };
  }
}
