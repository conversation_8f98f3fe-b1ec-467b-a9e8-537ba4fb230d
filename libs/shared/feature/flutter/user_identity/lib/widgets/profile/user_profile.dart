import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:dlibphonenumber/dlibphonenumber.dart';

import 'package:app_framework_component/app_framework.dart' as app;
import 'package:ui_widgets_component/ui_widgets.dart';
import 'package:identity_service/identity_service.dart' as identity;

import '../../user_identity_feature.dart';
import '../../config/user_identity_feature_config.dart';
import '../../l10n/l10n.dart';

import 'user_profile_form.dart';
import 'verify_attribute_form.dart';

class UserProfile extends MultiFormController {
  /// This callback is used to build the dialog container
  /// and it allows you to provide different constrainted
  /// containers for the form and verification views.
  @override
  BuildContainerCallack? get buildContainer => _buildContainer;
  final BuildContainerCallack? _buildContainer;

  final VoidCallback _dismissDialog;
  final bool _dismissOnTap;

  const UserProfile({
    super.key,
    BuildContainerCallack? buildContainer,
    required VoidCallback dismissDialog,
    bool dismissOnTap = true,
  })  : _buildContainer = buildContainer,
        _dismissDialog = dismissDialog,
        _dismissOnTap = dismissOnTap;

  static void asModal(
    BuildContext context, {
    bool dismissOnTap = true,
  }) {
    showDialog<void>(
      context: context,
      barrierDismissible: dismissOnTap,
      builder: (dialogContext) {
        return Dialog(
          child: UserProfile(
            buildContainer: (child, width, height) {
              return SizedBox(
                width: width + 32,
                height: height + 32,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: child,
                ),
              );
            },
            dismissDialog: () {
              Navigator.pop(dialogContext);
            },
            dismissOnTap: dismissOnTap,
          ),
        );
      },
    );
  }

  @override
  MultiFormControllerState<UserProfile> createState() => _UserProfileState();
}

class _UserProfileState extends MultiFormControllerState<UserProfile> {
  bool saveInitiated = false;

  AttributeType verifyAttributeType = AttributeType.none;
  String verifyAttributeValue = '';

  TextEditingController firstName = TextEditingController();
  TextEditingController middleName = TextEditingController();
  TextEditingController familyName = TextEditingController();
  TextEditingController preferredName = TextEditingController();
  TextEditingController emailAddress = TextEditingController();
  TextEditingController mobilePhone = TextEditingController();

  late Map<String, String?> userProfile;

  String mobilePhoneNumber = '';

  late final UserCustomProfileInputConfig? customProfileInput;

  @override
  void initState() {
    super.initState();

    final config = UserIdentityFeature.instance().config;
    customProfileInput = config.customProfileInput;

    final identityState = context.read<identity.IdentityService>().state;
    final user = identityState.user;

    firstName.text = user.firstName ?? '';
    middleName.text = user.middleName ?? '';
    familyName.text = user.familyName ?? '';
    preferredName.text = user.preferredName ?? '';
    emailAddress.text = user.emailAddress;
    mobilePhoneNumber = user.mobilePhone;
    userProfile = Map<String, String?>.from(user.userProfile);
  }

  @override
  void dispose() {
    firstName.dispose();
    middleName.dispose();
    familyName.dispose();
    preferredName.dispose();
    emailAddress.dispose();
    mobilePhone.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<identity.IdentityService, identity.IdentityState>(
      listener: (context, identityState) {
        final isLoading = identityState.isLoading([
          identity.saveUserLoading,
          identity.sendVerificationCodeLoading,
          identity.confirmVerificationCodeLoading,
        ]);

        // if save was initiated but loading state has
        // completed with no errors then dismiss the dialog
        if (!isLoading &&
            saveInitiated &&
            verifyAttributeType == AttributeType.none) {
          setState(() {
            saveInitiated = false;
          });
          if (!identityState.hasError) {
            widget._dismissDialog();
          }
        }
      },
      child: verifyAttributeType == AttributeType.none
          ? super.buildContainer(
              UserProfileForm(
                firstName: firstName,
                middleName: middleName,
                familyName: familyName,
                preferredName: preferredName,
                userProfile: userProfile,
                emailAddress: emailAddress,
                mobilePhone: mobilePhone,
                mobilePhoneNumber: mobilePhoneNumber,
                onPhoneNumberChanged: (value, isValid) {
                  if (isValid) {
                    setState(() {
                      mobilePhoneNumber = PhoneNumberUtil.instance.format(
                        value,
                        PhoneNumberFormat.e164,
                      );
                    });
                  }
                },
                verifyAttribute: _initiateVerifyAttribute,
                saveProfile: _saveProfile,
                dismissDialog: widget._dismissDialog,
                dismissOnTap: widget._dismissOnTap,
                customProfileInput: customProfileInput,
              ),
              UserProfileForm.formViewWidth,
              UserProfileForm.formViewHeight +
                  (customProfileInput?.addHeight ?? 0),
            )
          : super.buildContainer(
              VerifyAttributeForm(
                type: verifyAttributeType,
                value: verifyAttributeValue,
                verifyCallback: _verifyAttribute,
                goBack: _resetVerifyAttribute,
                dismissDialog: widget._dismissDialog,
                dismissOnTap: widget._dismissOnTap,
              ),
              VerifyAttributeForm.formViewWidth,
              VerifyAttributeForm.formViewHeight,
            ),
    );
  }

  void _initiateVerifyAttribute(
    AttributeType type,
  ) {
    final identityService = context.read<identity.IdentityService>();
    final user = identityService.state.user;

    final value = type == AttributeType.emailAddress
        ? emailAddress.text
        : mobilePhoneNumber;

    void requestVerificationCode() {
      identityService.sendVerificationCode(
        type.name,
      );
      setState(() {
        verifyAttributeType = type;
        verifyAttributeValue = value;
      });
    }

    if (type == AttributeType.emailAddress && user.emailAddress != value) {
      // If email was changed then the user's new email has
      // to be saved before a verification code can be sent
      identityService
          .saveUser(
            user.copyWith(
              emailAddress: value,
              emailAddressVerified: false,
            ),
          )
          .then(
            (_) => requestVerificationCode(),
          );
    } else if (type == AttributeType.mobilePhone && user.mobilePhone != value) {
      // If SMS 2FA was configure then reset 2FA
      if (user.enableMFA && !user.enableTOTP) {
        identityService.addStateMessage(
          app.Message.warning(context.l10n.sms2FAResetWarning),
        );
      }
      // If phone number was changed then the user's new phone number
      // has to be saved before a verification code can be sent
      identityService
          .saveUser(
            user.copyWith(
              mobilePhone: value,
              mobilePhoneVerified: false,
              // Do not reset MFA if TOTP is enabled
              enableMFA: user.enableTOTP,
            ),
          )
          .then(
            (_) => requestVerificationCode(),
          );
    } else {
      requestVerificationCode();
    }
  }

  void _verifyAttribute(
    AttributeType type,
    String code,
  ) {
    final identityService = context.read<identity.IdentityService>();

    identityService.confirmVerificationCode(
      type.name,
      code,
    );
    setState(() {
      verifyAttributeType = AttributeType.none;
      verifyAttributeValue = '';
    });
  }

  void _resetVerifyAttribute() {
    setState(() {
      verifyAttributeType = AttributeType.none;
      verifyAttributeValue = '';
      saveInitiated = false;
    });
  }

  void _saveProfile(
    identity.User user,
    bool isEmailVerified,
    bool isMobilePhoneVerified,
  ) {
    final l10n = context.l10n;
    final identityService = context.read<identity.IdentityService>();

    if (emailAddress.text.isNotEmpty && !isEmailVerified) {
      identityService.addStateMessage(
        app.Message.warning(l10n.emailNotVerifiedWarning),
      );
    }
    if (!isMobilePhoneVerified) {
      if (user.enableMFA && !user.enableTOTP) {
        identityService.addStateMessage(
          app.Message.warning(l10n.mobilePhoneNotVerifiedWarning),
        );
      }
      if (mobilePhone.text.isNotEmpty) {
        identityService.addStateMessage(
          app.Message.warning(l10n.mobilePhoneNotVerifiedWarning),
        );
      }
    }

    setState(() {
      saveInitiated = true;

      if (identityService.state.user.emailAddressVerified && !isEmailVerified) {
        /// If email was changed then a verification code will
        /// be automatically sent when the profile is saved
        /// so we show the verification dialog page
        verifyAttributeType = AttributeType.emailAddress;
        verifyAttributeValue = emailAddress.text;
      }
    });
    identityService.saveUser(
      user.copyWith(
        firstName: firstName.text,
        middleName: middleName.text,
        familyName: familyName.text,
        preferredName: preferredName.text,
        emailAddress: emailAddress.text,
        emailAddressVerified: isEmailVerified,
        mobilePhone: mobilePhoneNumber,
        mobilePhoneVerified: isMobilePhoneVerified,
        userProfile: userProfile,
        enableMFA: user.enableTOTP || // Do not reset MFA if TOTP is enabled
            (user.enableMFA && isMobilePhoneVerified),
      ),
    );
  }
}
