import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:ui_widgets_component/ui_widgets.dart';
import 'package:identity_service/identity_service.dart' as identity;

import '../../user_identity_feature.dart';

import 'security_profile_form.dart';
import 'setup_totp_form.dart';

class SecurityProfile extends MultiFormController {
  /// This callback is used to build the dialog container
  /// and it allows you to provide different constrainted
  /// containers for the form and verification views.
  @override
  BuildContainerCallack? get buildContainer => _buildContainer;
  final BuildContainerCallack? _buildContainer;

  final VoidCallback _dismissDialog;
  final bool _dismissOnTap;

  const SecurityProfile({
    super.key,
    BuildContainerCallack? buildContainer,
    required VoidCallback dismissDialog,
    bool dismissOnTap = true,
  })  : _buildContainer = buildContainer,
        _dismissDialog = dismissDialog,
        _dismissOnTap = dismissOnTap;

  static void asModal(
    BuildContext context, {
    bool dismissOnTap = true,
  }) {
    showDialog<void>(
      context: context,
      barrierDismissible: dismissOnTap,
      builder: (dialogContext) {
        return Dialog(
          child: SecurityProfile(
            buildContainer: (child, width, height) {
              return SizedBox(
                width: width,
                height: height,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: child,
                ),
              );
            },
            dismissDialog: () {
              Navigator.pop(dialogContext);
            },
            dismissOnTap: dismissOnTap,
          ),
        );
      },
    );
  }

  @override
  MultiFormControllerState<SecurityProfile> createState() =>
      _SecurityProfileState();
}

class _SecurityProfileState extends MultiFormControllerState<SecurityProfile> {
  bool saveInitiated = false;
  bool totpSetupInitiated = false;

  identity.User? unsavedUser;

  @override
  Widget build(BuildContext context) {
    return BlocListener<identity.IdentityService, identity.IdentityState>(
      listener: (context, identityState) {
        final identityService = context.read<identity.IdentityService>();

        if (identityState.isTOTPSetupInitiated) {
          setState(() {
            totpSetupInitiated = true;
          });
        } else if (!identityState.isTOTPSetupInProgress) {
          final isLoading = identityState.isLoading([
            identity.saveUserLoading,
            identity.totpSetupLoading,
            identity.verifyTOTPLoading,
          ]);
          if (!isLoading && saveInitiated) {
            if (totpSetupInitiated) {
              assert(unsavedUser != null);
              identityService.saveUser(unsavedUser!);
              setState(() {
                totpSetupInitiated = false;
              });
              return;
            } else if (!identityState.hasError) {
              widget._dismissDialog();
            }
            setState(() {
              saveInitiated = false;
              unsavedUser = null;
            });
          }
        }
      },
      child: !totpSetupInitiated
          ? super.buildContainer(
              SecurityProfileForm(
                unsavedUser: unsavedUser,
                saveProfile: _saveProfile,
                dismissDialog: widget._dismissDialog,
                dismissOnTap: widget._dismissOnTap,
              ),
              SecurityProfileForm.formViewWidth,
              SecurityProfileForm.formViewHeight,
            )
          : super.buildContainer(
              SetupTOTPForm(
                confirmCallback: _confirmTOTPCode,
                goBack: _resetTOTPSetup,
                dismissDialog: widget._dismissDialog,
                dismissOnTap: widget._dismissOnTap,
              ),
              SetupTOTPForm.formViewWidth,
              SetupTOTPForm.formViewHeight,
            ),
    );
  }

  void _confirmTOTPCode(
    String code,
  ) {
    final identityService = context.read<identity.IdentityService>();
    identityService.verifyTOTP(
      code,
    );
  }

  void _resetTOTPSetup() {
    final identityService = context.read<identity.IdentityService>();
    identityService.cancelTOTPSetup();

    setState(() {
      totpSetupInitiated = false;
      saveInitiated = false;
      unsavedUser = null;
    });
  }

  void _saveProfile(
    identity.User user,
    MFAType mfaType,
    bool enableBiometric,
    bool rememberFor24h,
  ) {
    final identityService = context.read<identity.IdentityService>();

    // If MFA was enabled and TOTP was selected then initiate TOTP setup
    final setupTotp =
        mfaType == MFAType.totp && (!user.enableMFA || !user.enableTOTP);

    if (setupTotp) {
      identityService.setupTOTP(
        appName: UserIdentityFeature.instance().config.appName,
      );

      setState(() {
        saveInitiated = true;

        unsavedUser = user.copyWith(
          enableMFA: mfaType != MFAType.none,
          enableTOTP: mfaType == MFAType.totp,
          enableBiometric: enableBiometric,
          rememberFor24h: rememberFor24h,
        );
      });
    } else {
      identityService.saveUser(
        user.copyWith(
          enableMFA: mfaType != MFAType.none,
          enableTOTP: mfaType == MFAType.totp,
          enableBiometric: enableBiometric,
          rememberFor24h: rememberFor24h,
        ),
      );

      setState(() {
        saveInitiated = true;
      });
    }
  }
}
