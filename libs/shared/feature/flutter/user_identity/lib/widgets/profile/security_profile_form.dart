import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

import 'package:ui_widgets_component/ui_widgets.dart';
import 'package:identity_service/identity_service.dart' as identity;

import '../../l10n/l10n.dart';

class SecurityProfileForm extends StatefulWidget {
  static const double formViewWidth = 480.0;
  static const double formViewHeight = 632.0;

  final identity.User? _unsavedUser;

  final SaveProfileCallback _saveProfile;

  final VoidCallback? _dismissDialog;
  final bool _dismissOnTap;

  const SecurityProfileForm({
    super.key,
    identity.User? unsavedUser,
    required SaveProfileCallback saveProfile,
    VoidCallback? dismissDialog,
    bool dismissOnTap = true,
  })  : _unsavedUser = unsavedUser,
        _saveProfile = saveProfile,
        _dismissDialog = dismissDialog,
        _dismissOnTap = dismissOnTap;

  @override
  State<SecurityProfileForm> createState() => _SecurityProfileFormState();
}

class _SecurityProfileFormState extends State<SecurityProfileForm> {
  // declare a GlobalKey
  final formKey = GlobalKey<FormState>();

  bool enableBiometric = false;
  bool rememberFor24h = false;

  MFAType mfaType = MFAType.none;

  @override
  void initState() {
    super.initState();

    final user = widget._unsavedUser ??
        context.read<identity.IdentityService>().state.user;

    setState(() {
      mfaType = user.enableMFA
          ? user.enableTOTP
              ? MFAType.totp
              : MFAType.sms
          : MFAType.none;
      enableBiometric = user.enableBiometric;
      rememberFor24h = user.rememberFor24h;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = context.l10n;

    return BlocBuilder<identity.IdentityService, identity.IdentityState>(
      builder: (
        context,
        identityState,
      ) {
        final user = identityState.user;

        final mfaEnabled = mfaType != MFAType.none;
        final enableSave =
            formKey.currentState != null && formKey.currentState!.validate();

        final isLoading = identityState.isLoading([
          identity.saveUserLoading,
          identity.totpSetupLoading,
          identity.verifyTOTPLoading,
        ]);
        final inputEnabled = !isLoading;

        return DialogForm(
          formKey: formKey,
          title: l10n.securityProfileFormTitle,
          formViewHeight: SecurityProfileForm.formViewHeight,
          body: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(8.0, 0.0, 0.0, 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      l10n.passwordSectionTitle,
                      style: theme.textTheme.titleLarge,
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(32.0, 8.0, 32.0, 16.0),
                child: MarkdownBody(
                  data: l10n.updatePasswordText,
                ),
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(0, 8.0, 0, 16.0),
                child: ElevatedButton.icon(
                  label: SizedBox(
                    width: 200,
                    child: Text(
                      l10n.resetPasswordButtonText,
                      textAlign: TextAlign.center,
                    ),
                  ),
                  iconAlignment: IconAlignment.end,
                  onPressed: inputEnabled ? () {} : null,
                ),
              ),
              const Divider(),
              Padding(
                padding: const EdgeInsets.fromLTRB(8.0, 8.0, 0.0, 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      l10n.enhancedSecuritySectionTitle,
                      style: theme.textTheme.titleLarge,
                    ),
                  ],
                ),
              ),
              Padding(
                  padding: const EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 0.0),
                  child: Column(
                    children: <Widget>[
                      ListTile(
                        title: Text(l10n.enableMFAText),
                        titleTextStyle: theme.textTheme.bodyLarge,
                        enabled: inputEnabled,
                        horizontalTitleGap: 4.0,
                        visualDensity: const VisualDensity(vertical: -4.0),
                        leading: Checkbox(
                          value: mfaEnabled,
                          semanticLabel: l10n.enableMFAText,
                          onChanged: inputEnabled
                              ? (bool? value) => setState(
                                    () {
                                      setState(() {
                                        if (value != null && value) {
                                          mfaType = MFAType.totp;
                                        } else {
                                          mfaType = MFAType.none;
                                        }
                                      });
                                    },
                                  )
                              : null,
                        ),
                      ),
                      ListTile(
                        title: Text(l10n.mfaViaTOTP),
                        titleTextStyle: theme.textTheme.bodyLarge,
                        enabled: inputEnabled && mfaEnabled,
                        horizontalTitleGap: 4.0,
                        contentPadding: const EdgeInsets.only(left: 48.0),
                        visualDensity: const VisualDensity(vertical: -4.0),
                        leading: Semantics(
                          label: l10n.mfaViaTOTP,
                          child: Radio<MFAType>(
                            value: MFAType.totp,
                            groupValue: mfaType,
                            onChanged: inputEnabled && mfaType != MFAType.none
                                ? (MFAType? value) {
                                    setState(() {
                                      if (value != null) {
                                        mfaType = value;
                                      }
                                    });
                                  }
                                : null,
                          ),
                        ),
                      ),
                      ListTile(
                        title: Text(l10n.mfaViaSMS),
                        titleTextStyle: theme.textTheme.bodyLarge,
                        enabled: inputEnabled &&
                            mfaEnabled &&
                            user.mobilePhoneVerified,
                        horizontalTitleGap: 4.0,
                        contentPadding: const EdgeInsets.only(left: 48.0),
                        visualDensity: const VisualDensity(vertical: -4.0),
                        leading: Semantics(
                          label: l10n.mfaViaSMS,
                          child: Radio<MFAType>(
                            value: MFAType.sms,
                            groupValue: mfaType,
                            onChanged: inputEnabled &&
                                    mfaEnabled &&
                                    user.mobilePhoneVerified
                                ? (MFAType? value) {
                                    setState(() {
                                      if (value != null) {
                                        mfaType = value;
                                      }
                                    });
                                  }
                                : null,
                          ),
                        ),
                      ),
                      ListTile(
                        title: Text(l10n.enableBiometricText),
                        titleTextStyle: theme.textTheme.bodyLarge,
                        enabled: inputEnabled,
                        horizontalTitleGap: 4.0,
                        leading: Checkbox(
                          value: enableBiometric,
                          semanticLabel: l10n.enableBiometricText,
                          onChanged: null,
                          //
                          // TODO: this capability is not yet implemented
                          //
                          // onChanged: inputEnabled
                          //     ? (bool? value) => setState(
                          //           () => enableBiometric = value ?? false,
                          //         )
                          //     : null,
                        ),
                      ),
                      ListTile(
                        title: Text(l10n.rememberMeText),
                        titleTextStyle: theme.textTheme.bodyLarge,
                        enabled: inputEnabled,
                        horizontalTitleGap: 4.0,
                        leading: Checkbox(
                          value: rememberFor24h,
                          semanticLabel: l10n.rememberMeText,
                          onChanged: null,
                          //
                          // TODO: this capability is not yet implemented
                          //
                          // onChanged: inputEnabled
                          //     ? (bool? value) => setState(
                          //           () => rememberFor24h = value ?? false,
                          //         )
                          //     : null,
                        ),
                      ),
                    ],
                  )),
            ],
          ),
          actions: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 8),
                child: ElevatedButton.icon(
                  label: Text(l10n.cancelButton),
                  icon: const Icon(Icons.cancel),
                  iconAlignment: IconAlignment.end,
                  onPressed: inputEnabled ? widget._dismissDialog : null,
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 8),
                child: ElevatedIconButton(
                  label: Text(l10n.saveButton),
                  icon: Icons.save,
                  iconAlignment: IconAlignment.end,
                  isLoading: isLoading,
                  onPressed: inputEnabled && enableSave
                      ? () => widget._saveProfile(
                            user,
                            mfaType,
                            enableBiometric,
                            rememberFor24h,
                          )
                      : null,
                ),
              ),
            ],
          ),
          onCloseDialog: widget._dismissOnTap ? null : widget._dismissDialog,
        );
      },
    );
  }
}

enum MFAType { none, sms, totp }

typedef SaveProfileCallback = void Function(
  identity.User user,
  MFAType mfaType,
  bool enableBiometric,
  bool rememberFor24h,
);
