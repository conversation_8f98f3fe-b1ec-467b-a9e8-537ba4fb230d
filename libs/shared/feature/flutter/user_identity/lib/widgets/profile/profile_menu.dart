import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:ui_widgets_component/ui_widgets.dart';
import 'package:app_framework_component/app_framework.dart' as app;
import 'package:nav_layouts_component/nav_layouts.dart';
import 'package:identity_service/identity_service.dart' as identity;

import '../../l10n/l10n.dart';
import '../auth/sign_in.dart';

import 'user_profile.dart';
import 'security_profile.dart';

class ProfileMenu extends StatefulWidget {
  static const String hookName = 'profile_menu';

  final bool showLogout;
  final bool dismissOnTap;

  final ImageProvider? defaultAvatarImage;

  const ProfileMenu({
    super.key,
    this.defaultAvatarImage,
    this.showLogout = true,
    this.dismissOnTap = true,
  });

  @override
  State<ProfileMenu> createState() => _ProfileMenuState();
}

class _ProfileMenuState extends State<ProfileMenu> {
  ImageProvider? avatarImage;
  String? avatarImageUrl;
  bool showAvatarInitials = true;

  @override
  void initState() {
    super.initState();

    final identityService = context.read<identity.IdentityService>();
    final profilePictureUrl = identityService.state.user.profilePictureUrl;
    if (profilePictureUrl != null) {
      _loadAvatarImage(profilePictureUrl);
    } else {
      avatarImage = widget.defaultAvatarImage;
    }
  }

  @override
  Widget build(BuildContext context) {
    return app.FeatureContainer<ProfileMenuOptionGroup>(
      hookName: ProfileMenu.hookName,
      initializeAnchors: (_, anchors) {
        // Sort the menu option group anchors
        // by their order property
        anchors.sort((a, b) => a.order.compareTo(b.order));
        return anchors;
      },
      builder: (context, anchors) {
        return BlocListener<identity.IdentityService, identity.IdentityState>(
          listener: (context, identityState) {
            if (!identityState.isLoggedIn) {
              // If the user is not logged in,
              // don't show the profile menu
              RootNavRouter.goNamed(SignInWidget.name);
            }

            final profilePictureUrl = identityState.user.profilePictureUrl;
            if (profilePictureUrl == null && avatarImageUrl != null) {
              // If the user has removed the profile picture
              // then show the default avatar image
              avatarImageUrl = null;
              avatarImage = widget.defaultAvatarImage;
            } else if (profilePictureUrl != null &&
                profilePictureUrl != avatarImageUrl) {
              _loadAvatarImage(profilePictureUrl);
            }
          },
          child: BlocBuilder<identity.IdentityService, identity.IdentityState>(
            builder: (
              context,
              identityState,
            ) {
              final l10n = context.l10n;
              final identityService = context.read<identity.IdentityService>();

              // Build the profile menu hook with the menu
              // items from the anchors of all the features
              // that are registered with the feature
              // registry and have a profile menu anchor.
              final List<_ProfileMenuEntry> hook = [];
              for (var anchor in anchors) {
                final group = anchor.options
                    .map(
                      (option) => _ProfileMenuItem(
                        value: option.onSelected,
                        child: ListTile(
                          leading: Icon(option.icon),
                          title: Text(option.title),
                          subtitle: option.subtitle != null
                              ? Text(option.subtitle!)
                              : null,
                        ),
                      ),
                    )
                    .toList() as List<_ProfileMenuEntry>;
                hook.add(const PopupMenuDivider());
                hook.addAll(group);
              }

              // Show the user's initials as the avatar initials
              // only if the user has not set an avatar image
              final avatarInitials = showAvatarInitials //
                  ? identityService.state.user.initials
                  : null;

              return Padding(
                padding: const EdgeInsets.fromLTRB(8.0, 0, 20.0, 0.0),
                child: AvatarPopupMenu<OnSelectedCallback?>(
                  avatarBgImage: avatarImage,
                  avatarInitials: avatarInitials,
                  semanticsLabel: l10n.semProfileMenu,
                  offset: const Offset(15, 10),
                  menuItems: <_ProfileMenuEntry>[
                    _ProfileMenuItem(
                      value: (context) {
                        UserProfile.asModal(
                          context,
                          dismissOnTap: widget.dismissOnTap,
                        );
                      },
                      child: ListTile(
                        leading: const Icon(
                          Icons.manage_accounts_outlined,
                        ),
                        title: Text(l10n.menuOptionProfile),
                      ),
                    ),
                    _ProfileMenuItem(
                      value: (context) {
                        SecurityProfile.asModal(
                          context,
                          dismissOnTap: widget.dismissOnTap,
                        );
                      },
                      child: ListTile(
                        leading: const Icon(
                          Icons.admin_panel_settings_outlined,
                        ),
                        title: Text(l10n.menuOptionSecurity),
                      ),
                    ),

                    /// Add the feature anchors to
                    /// the profile menu hook
                    ...hook,

                    if (widget.showLogout) ...[
                      const PopupMenuDivider(),
                      _ProfileMenuItem(
                        value: (context) {
                          identityService.signOut();
                        },
                        child: ListTile(
                          leading: const Icon(Icons.logout_outlined),
                          title: Text(l10n.menuOptionLogout),
                        ),
                      ),
                    ],
                  ],
                  onSelected: (OnSelectedCallback? onOptionSelected) {
                    if (onOptionSelected != null) {
                      onOptionSelected(context);
                    }
                  },
                ),
              );
            },
          ),
        );
      },
    );
  }

  void _loadAvatarImage(String imageUrl) {
    http.get(Uri.parse(imageUrl)).then(
      (response) {
        if (response.statusCode == 200) {
          setState(() {
            avatarImageUrl = imageUrl;
            avatarImage = MemoryImage(response.bodyBytes);
            showAvatarInitials = false;
          });
        } else if (response.statusCode == 404) {
          setState(() {
            avatarImageUrl = imageUrl;
            avatarImage = widget.defaultAvatarImage;
            showAvatarInitials = true;
          });
        } else {
          setState(() {
            avatarImageUrl = null;
            avatarImage = widget.defaultAvatarImage;
            showAvatarInitials = true;
          });
        }
      },
    ).onError(
      (error, stackTrace) {
        setState(() {
          avatarImageUrl = null;
          avatarImage = widget.defaultAvatarImage;
          showAvatarInitials = true;
        });
      },
    );
  }
}

/// A feature anchor that represents a group of
/// profile menu options that will be displayed
/// in the profile menu hook.
class ProfileMenuOptionGroup extends app.FeatureAnchor {
  @override
  String get hookName => ProfileMenu.hookName;

  final int order;
  final List<ProfileMenuOption> options;

  const ProfileMenuOptionGroup({
    this.order = 0,
    required this.options,
  });
}

/// A profile menu option that represents a
/// menu item that will be displayed in the
/// within a profile menu option group that
/// will be added to the profile menu hook.
class ProfileMenuOption {
  final String title;
  final String? subtitle;
  final IconData icon;

  final OnSelectedCallback? onSelected;

  const ProfileMenuOption({
    required this.title,
    this.subtitle,
    required this.icon,
    this.onSelected,
  });
}

typedef OnSelectedCallback = void Function(BuildContext context);
typedef _ProfileMenuEntry = PopupMenuEntry<OnSelectedCallback?>;
typedef _ProfileMenuItem = PopupMenuItem<OnSelectedCallback?>;
