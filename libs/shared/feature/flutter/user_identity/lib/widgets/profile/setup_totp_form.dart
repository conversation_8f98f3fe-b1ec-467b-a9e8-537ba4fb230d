import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

import 'package:ui_widgets_component/ui_widgets.dart';
import 'package:identity_service/identity_service.dart' as identity;

import '../../user_identity_feature.dart';
import '../../l10n/l10n.dart';

class SetupTOTPForm extends StatefulWidget {
  static const double formViewWidth = 480.0;
  static const double formViewHeight = 725.0;

  final ConfirmCallback _confirmCallback;
  final VoidCallback _goBack;

  final VoidCallback? _dismissDialog;
  final bool _dismissOnTap;

  const SetupTOTPForm({
    super.key,
    required ConfirmCallback confirmCallback,
    required VoidCallback goBack,
    VoidCallback? dismissDialog,
    bool dismissOnTap = true,
  })  : _confirmCallback = confirmCallback,
        _goBack = goBack,
        _dismissDialog = dismissDialog,
        _dismissOnTap = dismissOnTap;

  @override
  State<SetupTOTPForm> createState() => _SetupTOTPFormState();
}

class _SetupTOTPFormState extends State<SetupTOTPForm> {
  // declare a GlobalKey
  final formKey = GlobalKey<FormState>();

  String verificationCode = '';

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = context.l10n;

    return BlocBuilder<identity.IdentityService, identity.IdentityState>(
      builder: (
        context,
        identityState,
      ) {
        final isLoading = identityState.isLoading(
          [identity.verifyTOTPLoading],
        );
        final inputEnabled = !isLoading;

        assert(identityState.totpSetupSecret != null);

        late final String totpSetupUri;
        if (identityState.totpSetupUri == null) {
          final appName = Uri.encodeFull(
            UserIdentityFeature.instance().config.appName,
          );
          totpSetupUri = Uri.parse(
            'otpauth://totp/My%20App:${identityState.user.username}?'
            'secret=${identityState.totpSetupSecret}&issuer=$appName',
          ).toString();
        } else {
          totpSetupUri = identityState.totpSetupUri!.toString();
        }

        return DialogForm(
          formKey: formKey,
          title: l10n.setupTOTPFormTitle,
          formViewHeight: SetupTOTPForm.formViewHeight,
          body: Column(
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(8.0, 0.0, 0.0, 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      l10n.totpSectionTitle,
                      style: theme.textTheme.titleLarge,
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(32.0, 8.0, 32.0, 16.0),
                child: MarkdownBody(
                  data: l10n.totpInstructionsText(
                      identityState.totpSetupUri != null
                          ? l10n.totpSetupLinkText
                          : ''),
                  onTapLink: (_, String? href, __) async {
                    if (href == 'https://totpsetup') {
                      await launchUrl(identityState.totpSetupUri!);
                    }
                  },
                ),
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(0.0, 8.0, 0.0, 16.0),
                child: QrImageView(
                  data: totpSetupUri,
                  version: QrVersions.auto,
                  size: 256.0,
                  eyeStyle: QrEyeStyle(
                    eyeShape: QrEyeShape.square,
                    color: theme.iconTheme.color!,
                  ),
                  dataModuleStyle: QrDataModuleStyle(
                    dataModuleShape: QrDataModuleShape.square,
                    color: theme.iconTheme.color!,
                  ),
                ),
              ),
              CodeInput(
                length: 6,
                isRequired: true,
                enabled: inputEnabled,
                onCompleted: (value) => setState(
                  () => verificationCode = value,
                ),
              ),
            ],
          ),
          actions: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 8),
                child: ElevatedButton.icon(
                  label: Text(l10n.cancelButton),
                  icon: const Icon(Icons.arrow_back),
                  iconAlignment: IconAlignment.end,
                  onPressed: inputEnabled ? widget._goBack : null,
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 8),
                child: ElevatedIconButton(
                  label: Text(l10n.verifyButton),
                  icon: Icons.check,
                  iconAlignment: IconAlignment.end,
                  isLoading: isLoading,
                  onPressed: inputEnabled
                      ? () => widget._confirmCallback(
                            verificationCode,
                          )
                      : null,
                ),
              ),
            ],
          ),
          onCloseDialog: widget._dismissOnTap ? null : widget._dismissDialog,
        );
      },
    );
  }
}

typedef ConfirmCallback = void Function(
  String code,
);
