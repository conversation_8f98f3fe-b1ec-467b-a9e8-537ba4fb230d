import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

import 'package:ui_widgets_component/ui_widgets.dart';
import 'package:identity_service/identity_service.dart' as identity;

import '../../l10n/l10n.dart';

class VerifyAttributeForm extends StatefulWidget {
  static const double formViewWidth = 424.0;
  static const double formViewHeight = 268.0;

  final AttributeType _type;
  final String _value;

  final VerifyCallback _verifyCallback;
  final VoidCallback _goBack;

  final VoidCallback? _dismissDialog;
  final bool _dismissOnTap;

  const VerifyAttributeForm({
    super.key,
    required AttributeType type,
    required String value,
    required VerifyCallback verifyCallback,
    required VoidCallback goBack,
    VoidCallback? dismissDialog,
    bool dismissOnTap = true,
  })  : assert(type != AttributeType.none),
        _type = type,
        _value = value,
        _verifyCallback = verifyCallback,
        _goBack = goBack,
        _dismissDialog = dismissDialog,
        _dismissOnTap = dismissOnTap;

  @override
  State<VerifyAttributeForm> createState() => _VerifyAttributeFormState();
}

class _VerifyAttributeFormState extends State<VerifyAttributeForm> {
  // declare a GlobalKey
  final formKey = GlobalKey<FormState>();

  String verificationCode = '';

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;

    final verifyText = widget._type == AttributeType.emailAddress
        ? l10n.verifyEmailText
        : l10n.verifyPhoneText;

    return BlocBuilder<identity.IdentityService, identity.IdentityState>(
      builder: (
        context,
        identityState,
      ) {
        final isLoading = identityState.isLoading(
          [identity.sendVerificationCodeLoading],
        );
        final inputEnabled = !isLoading;

        return DialogForm(
          formKey: formKey,
          title: widget._type == AttributeType.emailAddress
              ? l10n.verifyEmailTitle
              : l10n.verifyPhoneTitle,
          formViewHeight: VerifyAttributeForm.formViewHeight,
          body: Column(
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(40.0, 8.0, 40.0, 16.0),
                child: MarkdownBody(
                  data: verifyText(widget._value),
                ),
              ),
              CodeInput(
                length: 6,
                isRequired: true,
                enabled: inputEnabled,
                onCompleted: (value) => setState(
                  () => verificationCode = value,
                ),
              ),
            ],
          ),
          actions: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 8),
                child: ElevatedButton.icon(
                  label: Text(l10n.cancelButton),
                  icon: const Icon(Icons.arrow_back),
                  iconAlignment: IconAlignment.end,
                  onPressed: inputEnabled ? widget._goBack : null,
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 8),
                child: ElevatedIconButton(
                  label: Text(l10n.verifyButton),
                  icon: Icons.check,
                  iconAlignment: IconAlignment.end,
                  isLoading: isLoading,
                  onPressed: inputEnabled
                      ? () => widget._verifyCallback(
                            widget._type,
                            verificationCode,
                          )
                      : null,
                ),
              ),
            ],
          ),
          onCloseDialog: widget._dismissOnTap ? null : widget._dismissDialog,
        );
      },
    );
  }
}

/// AttributeTypes that can be verified
enum AttributeType {
  none('none'),
  // TODO: These names AWS provider specific
  //  and need a better abstraction.
  emailAddress('email'),
  mobilePhone('phone_number');

  final String? _attributeName;
  const AttributeType([this._attributeName]);

  String get name => _attributeName ?? '';
}

typedef VerifyCallback = void Function(
  AttributeType type,
  String code,
);
