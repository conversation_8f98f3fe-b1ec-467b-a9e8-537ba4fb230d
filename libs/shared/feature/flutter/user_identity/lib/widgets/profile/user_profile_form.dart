import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:dlibphonenumber/dlibphonenumber.dart';

import 'package:ui_widgets_component/ui_widgets.dart';
import 'package:identity_service/identity_service.dart' as identity;

import '../../user_identity_feature.dart';
import '../../config/user_identity_feature_config.dart';
import '../../l10n/l10n.dart';

import 'verify_attribute_form.dart';

class UserProfileForm extends StatefulWidget {
  static const double formViewWidth = 588.0;
  static const double formViewHeight = 566.0;
  static const double inputWidth = formViewWidth / 2;

  final TextEditingController firstName;
  final TextEditingController middleName;
  final TextEditingController familyName;
  final TextEditingController preferredName;
  final TextEditingController emailAddress;
  final TextEditingController mobilePhone;

  final Map<String, dynamic> userProfile;

  final String mobilePhoneNumber;
  final ValidatedValueChanged<PhoneNumber> onPhoneNumberChanged;

  final InitiateVerifyCallback _verifyAttribute;
  final SaveProfileCallback _saveProfile;

  final VoidCallback? _dismissDialog;
  final bool _dismissOnTap;

  final UserCustomProfileInputConfig? customProfileInput;

  const UserProfileForm({
    super.key,
    required this.firstName,
    required this.middleName,
    required this.familyName,
    required this.preferredName,
    required this.emailAddress,
    required this.mobilePhone,
    required this.mobilePhoneNumber,
    required this.userProfile,
    required this.onPhoneNumberChanged,
    required InitiateVerifyCallback verifyAttribute,
    required SaveProfileCallback saveProfile,
    VoidCallback? dismissDialog,
    bool dismissOnTap = true,
    this.customProfileInput,
  })  : _verifyAttribute = verifyAttribute,
        _saveProfile = saveProfile,
        _dismissDialog = dismissDialog,
        _dismissOnTap = dismissOnTap;

  @override
  State<UserProfileForm> createState() => _UserProfileFormState();
}

class _UserProfileFormState extends State<UserProfileForm> {
  // declare a GlobalKey
  final formKey = GlobalKey<FormState>();

  late final bool isMobileNumberRequired;
  late final bool isMobileDialCodeReadOnly;
  String? defaultCountryCode;

  bool isUserProfileGroupValid = true;

  late final Validator emailValidator;

  @override
  void initState() {
    super.initState();

    final config = UserIdentityFeature.instance().config;
    isMobileNumberRequired = config.mobileNumberRequired;
    isMobileDialCodeReadOnly = config.mobileDialCodeReadOnly;
    defaultCountryCode = config.mobileNumberCountryCode;
    emailValidator = config.emailValidator;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = context.l10n;

    return BlocBuilder<identity.IdentityService, identity.IdentityState>(
      builder: (
        context,
        identityState,
      ) {
        final user = identityState.user;

        final firstName = widget.firstName.text;
        final familyName = widget.familyName.text;
        final emailAddress = widget.emailAddress.text;
        final mobilePhone = widget.mobilePhoneNumber;

        final isEmailVerified = emailAddress.isNotEmpty &&
            emailAddress == user.emailAddress &&
            user.emailAddressVerified;
        final isMobilePhoneVerified = mobilePhone.isNotEmpty &&
            mobilePhone == user.mobilePhone &&
            user.mobilePhoneVerified;
        final enableSave = firstName.isNotEmpty &&
            familyName.isNotEmpty &&
            emailAddress.isNotEmpty &&
            (!isMobileNumberRequired || mobilePhone.isNotEmpty) &&
            isUserProfileGroupValid &&
            ((user.firstName ?? '') != firstName ||
                (user.middleName ?? '') != widget.middleName.text ||
                (user.familyName ?? '') != familyName ||
                (user.preferredName ?? '') != widget.preferredName.text ||
                user.emailAddress != emailAddress ||
                user.mobilePhone != mobilePhone ||
                !mapEquals(user.userProfile, widget.userProfile)) &&
            formKey.currentState != null &&
            formKey.currentState!.validate();

        final isLoading = identityState.isLoading(
          [
            identity.saveUserLoading,
            identity.confirmVerificationCodeLoading,
          ],
        );
        final inputEnabled = !isLoading;

        // trigger a rebuild when a form field changes as we dynamically
        // determine whether to enable or disable the save button
        void onFormFieldChanged(String? value, _) {
          if (mounted) {
            setState(() {});
          }
        }

        return DialogForm(
          formKey: formKey,
          title: l10n.userProfileFormTitle,
          formViewHeight: UserProfileForm.formViewHeight +
              (widget.customProfileInput?.addHeight ?? 0.0),
          body: Column(
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(8.0, 0.0, 0.0, 16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      l10n.contactSectionTitle,
                      style: theme.textTheme.titleLarge,
                    ),
                  ],
                ),
              ),
              isEmailVerified
                  ? EmailAddressInput(
                      icon: Icons.email_sharp,
                      iconColor: widget.emailAddress.text.isEmpty //
                          ? null
                          : Colors.lightGreenAccent,
                      isRequired: true,
                      enabled: inputEnabled,
                      validator: emailValidator,
                      controller: widget.emailAddress,
                      onChanged: onFormFieldChanged,
                    )
                  : widget.emailAddress.text.isNotEmpty
                      ? EmailAddressInput(
                          icon: Icons.email_sharp,
                          iconColor: Colors.redAccent,
                          onIconTap: () => widget._verifyAttribute(
                            AttributeType.emailAddress,
                          ),
                          iconTooltip: l10n.tapToVerifyEmail,
                          isRequired: true,
                          enabled: inputEnabled,
                          validator: emailValidator,
                          controller: widget.emailAddress,
                          onChanged: onFormFieldChanged,
                        )
                      : EmailAddressInput(
                          icon: Icons.email_sharp,
                          isRequired: true,
                          enabled: inputEnabled,
                          validator: emailValidator,
                          controller: widget.emailAddress,
                          onChanged: onFormFieldChanged,
                        ),
              isMobilePhoneVerified
                  ? PhoneNumberInput(
                      icon: Icons.phone_enabled_sharp,
                      iconColor: widget.mobilePhone.text.isEmpty //
                          ? null
                          : Colors.lightGreenAccent,
                      isRequired: isMobileNumberRequired,
                      enabled: inputEnabled,
                      enableCountrySelection: !isMobileDialCodeReadOnly,
                      defaultCountryCode: defaultCountryCode,
                      controller: widget.mobilePhone,
                      onChanged: widget.onPhoneNumberChanged,
                    )
                  : mobilePhone.isNotEmpty
                      ? PhoneNumberInput(
                          icon: Icons.phone_disabled_sharp,
                          iconColor: Colors.redAccent,
                          onIconTap: () => widget._verifyAttribute(
                            AttributeType.mobilePhone,
                          ),
                          iconTooltip: l10n.tapToVerifyMobilePhone,
                          isRequired: isMobileNumberRequired,
                          enabled: inputEnabled,
                          initialValue: mobilePhone,
                          enableCountrySelection: !isMobileDialCodeReadOnly,
                          defaultCountryCode: defaultCountryCode,
                          controller: widget.mobilePhone,
                          onChanged: widget.onPhoneNumberChanged,
                        )
                      : PhoneNumberInput(
                          icon: Icons.phone_disabled_sharp,
                          isRequired: isMobileNumberRequired,
                          enabled: inputEnabled,
                          initialValue: mobilePhone,
                          enableCountrySelection: !isMobileDialCodeReadOnly,
                          defaultCountryCode: defaultCountryCode,
                          controller: widget.mobilePhone,
                          onChanged: widget.onPhoneNumberChanged,
                        ),
              const Divider(),
              Padding(
                padding: const EdgeInsets.fromLTRB(8.0, 0.0, 0.0, 16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      l10n.namesSectionTitle,
                      style: theme.textTheme.titleLarge,
                    ),
                  ],
                ),
              ),
              Wrap(
                children: [
                  TextInput(
                    labelText: l10n.firstNameFieldName,
                    isRequired: true,
                    enabled: inputEnabled,
                    controller: widget.firstName,
                    onChanged: onFormFieldChanged,
                  ),
                  TextInput(
                    labelText: l10n.middleNameFieldName,
                    enabled: inputEnabled,
                    controller: widget.middleName,
                    onChanged: onFormFieldChanged,
                  ),
                  TextInput(
                    labelText: l10n.familyNameFieldName,
                    isRequired: true,
                    enabled: inputEnabled,
                    controller: widget.familyName,
                    onChanged: onFormFieldChanged,
                  ),
                  TextInput(
                    labelText: l10n.preferredNameFieldName,
                    enabled: inputEnabled,
                    controller: widget.preferredName,
                    onChanged: onFormFieldChanged,
                  ),
                ]
                    .map(
                      (input) => SizedBox(
                        width: UserProfileForm.inputWidth,
                        child: input,
                      ),
                    )
                    .toList(),
              ),
              if (widget.customProfileInput != null) ...[
                const Divider(),
                ...() {
                  final customProfileInput = widget.customProfileInput!;
                  final numFieldsPerRow = customProfileInput.numFieldsPerRow;
                  final minFieldWrapWidth =
                      UserProfileForm.formViewWidth / numFieldsPerRow;

                  return customProfileInput.inputGroups.map(
                    (config) => FormInputGroup(
                      config: config,
                      initialValues: widget.userProfile,
                      onChanged: ({
                        required FormDataValue changedValue,
                        required bool isInputGroupValid,
                      }) {
                        setState(() {
                          isUserProfileGroupValid = isInputGroupValid;
                          widget.userProfile[changedValue.id] =
                              changedValue.value;
                        });
                      },
                      numFieldsPerRow: numFieldsPerRow,
                      minFieldWrapWidth: minFieldWrapWidth,
                    ),
                  );
                }()
              ],
            ],
          ),
          actions: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 8),
                child: ElevatedButton.icon(
                  label: Text(l10n.cancelButton),
                  icon: const Icon(Icons.cancel),
                  iconAlignment: IconAlignment.end,
                  onPressed: inputEnabled ? widget._dismissDialog : null,
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 8),
                child: ElevatedIconButton(
                  label: Text(l10n.saveButton),
                  icon: Icons.save,
                  iconAlignment: IconAlignment.end,
                  isLoading: isLoading,
                  onPressed: inputEnabled && enableSave
                      ? () => widget._saveProfile(
                            user,
                            isEmailVerified,
                            isMobilePhoneVerified,
                          )
                      : null,
                ),
              ),
            ],
          ),
          onCloseDialog: widget._dismissOnTap ? null : widget._dismissDialog,
        );
      },
    );
  }
}

typedef InitiateVerifyCallback = void Function(
  AttributeType type,
);

typedef SaveProfileCallback = void Function(
  identity.User user,
  bool isEmailVerified,
  bool isMobilePhoneVerified,
);
