import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'user_identity_feature_method_channel.dart';

abstract class UserIdentityFeaturePlatform extends PlatformInterface {
  /// Constructs a UserIdentityFeaturePlatform.
  UserIdentityFeaturePlatform() : super(token: _token);

  static final Object _token = Object();

  static UserIdentityFeaturePlatform _instance = MethodChannelUserIdentityFeature();

  /// The default instance of [UserIdentityFeaturePlatform] to use.
  ///
  /// Defaults to [MethodChannelUserIdentityFeature].
  static UserIdentityFeaturePlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [UserIdentityFeaturePlatform] when
  /// they register themselves.
  static set instance(UserIdentityFeaturePlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  Future<String?> getPlatformVersion() {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }
}
