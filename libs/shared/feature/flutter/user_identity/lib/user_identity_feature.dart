import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

import 'package:ui_widgets_component/ui_widgets.dart' as ui;
import 'package:app_framework_component/app_framework.dart' as app;
import 'package:nav_layouts_component/nav_layouts.dart' as nav;
import 'package:identity_service/identity_service.dart' as service;

import 'l10n/l10n.dart';

import 'config/user_identity_feature_config.dart';
import 'components/user_identity_scope.dart';
import 'widgets/profile/user_profile.dart';
import 'user_identity_feature_platform_interface.dart';

final class UserIdentityFeature extends app.Feature {
  static String get featureName => 'user_identity';

  /// The configuration for this feature.
  late final UserIdentityFeatureConfig config;

  @override
  String get name => featureName;

  @override
  app.FeatureConfigDeserializer get configDeserializer =>
      UserIdentityFeatureConfig.fromJson;

  @override
  List<LocalizationsDelegate> get localizationsDelegates => [
        // The localization delegates for this feature includes
        // its own delegates as well as the delegates from the
        // identity service the feature depends on.
        UserIdentityLocalizations.delegate,
        service.IdentityLocalizations.delegate,
      ];

  @override
  List<app.FeatureAnchor> anchors(
    BuildContext? context,
  ) {
    final l10n = context?.l10n ?? UserIdentityLocalizationsEn();
    final user = context?.read<service.IdentityService>().state.user;

    return <app.FeatureAnchor>[
      if (config.showLogoutOnTitleBar)
        nav.AppTitleTool(
          hookName: nav.AppTitleToolBar.featureHookName,
          order: 0,
          title: l10n.logoutTitle,
          tooltip: l10n.logoutTooltip,
          iconData: Icons.logout_outlined,
          onPressed: (context) {
            UserIdentityFeature.logout();
          },
        ),
      if (user != null)
        nav.AppStatusTool(
          hookName: nav.AppStatusToolBar.featureHookName,
          order: 0,
          divider: nav.AppStatusToolDivider.auto,
          statusTool: ui.TextButtonTool.statusbar(
            iconData: (_) => Icons.person,
            text: (_) => user.name,
            buttonSemanticsLabel: l10n.semStatusUserProfile,
            onPressed: (_) {
              UserProfile.asModal(
                context!,
                dismissOnTap: true,
              );
            },
          ),
        ),
    ];
  }

  @override
  Future<void> initialize(
    app.FeatureConfig? config,
    Map<String, app.Feature> registeredFeatures,
  ) async {
    assert(
      config != null,
      'A configuration for the UserIdentityFeature was not provided',
    );

    this.config = config as UserIdentityFeatureConfig;
  }

  @override
  Widget scope(
    BuildContext context, {
    Key? key,
    required Widget child,
  }) {
    return UserIdentityScope(
      child: child,
    );
  }

  // User Identity Feature creation and registration

  static Future<void> register() async {
    final featureRegistry = app.FeatureRegistry.instance();
    await featureRegistry.registerFeature(UserIdentityFeature._());
  }

  factory UserIdentityFeature.instance() {
    final feature = app.FeatureRegistry.instance().getFeature(featureName);
    assert(
      feature != null,
      'UserIdentityFeature is not registered with the feature registry',
    );
    return feature as UserIdentityFeature;
  }

  UserIdentityFeature._() {
    // Register the identity service provider
    services.add<service.IdentityService>((context) {
      final authProvider = GetIt.instance.get<service.IdentityProvider>();
      return service.IdentityService(authProvider);
    });
  }

  // User Identity Feature API methods

  static void registerSignOutHook(SignOutHook hook) {
    final feature = UserIdentityFeature.instance();
    final identityService = feature.services.get<service.IdentityService>();
    identityService.registerSignOutHook(hook);
  }

  static void unregisterSignOutHook(SignOutHook hook) {
    final feature = UserIdentityFeature.instance();
    final identityService = feature.services.get<service.IdentityService>();
    identityService.unregisterSignOutHook(hook);
  }

  static Future<bool> isSessionValid() {
    final feature = UserIdentityFeature.instance();
    final identityService = feature.services.get<service.IdentityService>();
    return identityService.isSessionValid();
  }

  static void logout() {
    final feature = UserIdentityFeature.instance();
    final identityService = feature.services.get<service.IdentityService>();
    identityService.signOut();
  }

  static service.User getLoggedInUser() {
    final feature = UserIdentityFeature.instance();
    final identityService = feature.services.get<service.IdentityService>();
    return identityService.state.user;
  }

  static String? getDefaultOrg() {
    final feature = UserIdentityFeature.instance();
    final identityService = feature.services.get<service.IdentityService>();
    final identityState = identityService.state;
    if (identityState.isLoggedIn) {
      return identityState.user.defaultOrg;
    } else {
      return null;
    }
  }

  static void setDefaultOrg(
    String orgId, {
    bool logout = false,
  }) {
    final feature = UserIdentityFeature.instance();
    final identityService = feature.services.get<service.IdentityService>();
    final identityState = identityService.state;

    if (identityState.isLoggedIn) {
      final updatedUser = identityState.user.copyWith(
        defaultOrg: orgId,
      );
      identityService.saveUser(updatedUser).then(
        (_) {
          if (logout) {
            identityService.signOut();
          }
        },
      );
    }
  }

  static String? getDefaultSpace() {
    final feature = UserIdentityFeature.instance();
    final identityService = feature.services.get<service.IdentityService>();
    final identityState = identityService.state;
    if (identityState.isLoggedIn) {
      return identityState.user.defaultSpace;
    } else {
      return null;
    }
  }

  static void setDefaultSpace(String spaceId) {
    final feature = UserIdentityFeature.instance();
    final identityService = feature.services.get<service.IdentityService>();
    final identityState = identityService.state;
    if (identityState.isLoggedIn) {
      identityService.saveUser(identityState.user.copyWith(
        defaultSpace: spaceId,
      ));
    }
  }

  Future<String?> getPlatformVersion() {
    return UserIdentityFeaturePlatform.instance.getPlatformVersion();
  }
}

typedef SignOutHook = service.SignOutHook;
