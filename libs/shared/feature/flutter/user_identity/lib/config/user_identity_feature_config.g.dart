// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_identity_feature_config.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserIdentityFeatureConfig _$UserIdentityFeatureConfigFromJson(
        Map<String, dynamic> json) =>
    UserIdentityFeatureConfig(
      json['app-name'] as String,
      (json['auth-timeout'] as num?)?.toInt(),
      json['show-logout-on-title-bar'] as bool?,
      json['allowed-email-domains'] as String?,
      json['allowed-email-example'] as String?,
      json['mobile-number-required'] as bool?,
      json['mobile-dial-code-read-only'] as bool?,
      json['mobile-number-country-code'] as String?,
      Map<String, String>.from(json['agreements'] as Map),
      json['custom-profile-input'] == null
          ? null
          : UserCustomProfileInputConfig.fromJson(
              json['custom-profile-input'] as Map<String, dynamic>),
    );

UserCustomProfileInputConfig _$UserCustomProfileInputConfigFromJson(
        Map<String, dynamic> json) =>
    UserCustomProfileInputConfig(
      addHeight: (json['add-height'] as num).toDouble(),
      numFieldsPerRow: (json['num-fields-per-row'] as num).toInt(),
      inputGroups: (json['input-groups'] as List<dynamic>)
          .map((e) => InputGroupConfig.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
