import 'package:json_annotation/json_annotation.dart';
import 'package:intl/intl.dart';

import 'package:ui_widgets_component/ui_widgets.dart';
import 'package:app_framework_component/app_framework.dart';

part 'user_identity_feature_config.g.dart';

@JsonSerializable(
  fieldRename: FieldRename.kebab,
  createToJson: false,
)
class UserIdentityFeatureConfig extends FeatureConfig {
  final String appName;

  late final int authTimeout;

  final bool showLogoutOnTitleBar;

  final String? allowedEmailDomains;
  final String? allowedEmailExample;

  final bool mobileNumberRequired;
  final bool mobileDialCodeReadOnly;
  late final String mobileNumberCountryCode;

  final Map<String, String> agreements;

  final UserCustomProfileInputConfig? customProfileInput;

  /// Returns a validator that checks that
  /// email is valid and that it matches
  /// the allowed email domains if provided.
  Validator get emailValidator {
    if (allowedEmailDomains == null || allowedEmailDomains!.isEmpty) {
      return EmailAddressValidator();
    } else {
      return ChainedValidator(
        [
          EmailAddressValidator(),
          RegexValidator(
            regex: RegExp(allowedEmailDomains!),
            examples: allowedEmailExample != null //
                ? [allowedEmailExample!]
                : [],
          ),
        ],
      );
    }
  }

  UserIdentityFeatureConfig(
    this.appName,
    int? authTimeout,
    bool? showLogoutOnTitleBar,
    this.allowedEmailDomains,
    this.allowedEmailExample,
    bool? mobileNumberRequired,
    bool? mobileDialCodeReadOnly,
    String? mobileNumberCountryCode,
    Map<String, String>? agreements,
    this.customProfileInput,
  )   : showLogoutOnTitleBar = showLogoutOnTitleBar ?? false,
        mobileNumberRequired = mobileNumberRequired ?? true,
        mobileDialCodeReadOnly = mobileDialCodeReadOnly ?? false,
        agreements = agreements ?? const <String, String>{} {
    // Determine country code for mobile number
    // from locale if not explicitly provided
    if (mobileNumberCountryCode == null) {
      final locale = Intl.getCurrentLocale();
      this.mobileNumberCountryCode = locale.substring(locale.length - 2);
    } else {
      this.mobileNumberCountryCode = mobileNumberCountryCode;
    }
    // Default auth timeout to 15 minutes
    this.authTimeout = authTimeout ?? 15;
  }

  factory UserIdentityFeatureConfig.fromJson(Map<String, dynamic> json) =>
      _$UserIdentityFeatureConfigFromJson(json);
}

@JsonSerializable(
  fieldRename: FieldRename.kebab,
  createToJson: false,
)
class UserCustomProfileInputConfig {
  final double addHeight;
  final int numFieldsPerRow;

  final List<InputGroupConfig> inputGroups;

  UserCustomProfileInputConfig({
    required this.addHeight,
    required this.numFieldsPerRow,
    required this.inputGroups,
  });

  factory UserCustomProfileInputConfig.fromJson(Map<String, dynamic> json) =>
      _$UserCustomProfileInputConfigFromJson(json);
}
