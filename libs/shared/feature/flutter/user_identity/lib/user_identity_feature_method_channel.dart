import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'user_identity_feature_platform_interface.dart';

/// An implementation of [UserIdentityFeaturePlatform] that uses method channels.
class MethodChannelUserIdentityFeature extends UserIdentityFeaturePlatform {
  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('user_identity_feature');

  @override
  Future<String?> getPlatformVersion() async {
    final version = await methodChannel.invokeMethod<String>('getPlatformVersion');
    return version;
  }
}
