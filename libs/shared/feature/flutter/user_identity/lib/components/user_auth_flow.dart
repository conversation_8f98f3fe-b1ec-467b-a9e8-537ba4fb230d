import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:nav_layouts_component/nav_layouts.dart';
import 'package:ui_widgets_component/ui_widgets.dart';

import '../user_identity_feature_platform_interface.dart';
import '../widgets/auth/sign_in.dart';

abstract class UserAuthFlow implements Navigable {
  @override
  String get routeName => name;
  static const String name = SignInWidget.name;

  @override
  String get routePath => path;
  static const String path = SignInWidget.path;

  /// Shell navigation key for this feature
  final _shellNavigatorKey = GlobalKey<NavigatorState>();

  /// The name of the initial logged in route
  final String initialLoggedInRoute;

  UserAuthFlow({
    required this.initialLoggedInRoute,
  });

  @override
  @nonVirtual
  RouteBase route() {
    return ShellRoute(
      navigatorKey: _shellNavigatorKey,
      builder: buildFeatureContainer,
      routes: [
        SignInWidget(
          parentNavigatorKey: _shellNavigatorKey,
          // all routes within the shell should use
          // the same transition page builder
          pageBuilder: _transitionPageBuilder,
          // the sign in widget needs the initial
          // logged in route to navigate to after
          // successful sign in
          initialLoggedInRoute: initialLoggedInRoute,
        ).route(),
      ],
    );
  }

  /// The transition page builder for routes within [ShellRoute].
  Page<dynamic> _transitionPageBuilder(
    BuildContext context,
    GoRouterState state,
    Widget featureWidget,
  ) {
    return CustomTransitionPage<void>(
      key: state.pageKey,
      child: Center(
        child: DisplayAreaSizeProvider(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              buildFeatureWidget(
                context,
                featureWidget,
              ),
            ],
          ),
        ),
      ),
      transitionDuration: const Duration(milliseconds: 150),
      reverseTransitionDuration: Duration.zero,
      transitionsBuilder: (BuildContext context, Animation<double> animation,
          Animation<double> secondaryAnimation, Widget child) {
        // Change the opacity of the screen using a
        // Curve based on the the animation's value
        return FadeTransition(
          opacity: CurveTween(curve: Curves.easeInOut).animate(animation),
          child: child,
        );
      },
    );
  }

  /// The outer shell widget builder for [ShellRoute].
  /// Implement this method to customize the container
  /// of the feature's shell route.
  Widget buildFeatureContainer(
    BuildContext context,
    GoRouterState state,

    /// The user authentication feature display
    Widget feature,
  );

  /// The widget builder for the navigable feature widget.
  /// Override this method to customize the container of
  /// the feature widget.
  Widget buildFeatureWidget(BuildContext context, Widget featureWidget) {
    return Card(
      color: Theme.of(context).cardColor.withValues(alpha: 0.85),
      child: Padding(
        padding: const EdgeInsets.only(top: 16.0, bottom: 16.0),
        child: featureWidget,
      ),
    );
  }

  /// Get the platform version for the plugin
  Future<String?> getPlatformVersion() {
    return UserIdentityFeaturePlatform.instance.getPlatformVersion();
  }
}
