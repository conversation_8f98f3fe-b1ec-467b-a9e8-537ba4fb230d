import 'dart:async';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:identity_service/bloc/identity_service.dart';
import 'package:logging/logging.dart' as logging;

import 'package:nav_layouts_component/nav_layouts.dart';
import 'package:app_framework_component/app_framework.dart' as app;
import 'package:identity_service/identity_service.dart' as identity;

import '../user_identity_feature.dart';

/// Returns a router that handles authentication.
final class AuthRouter {
  static final logging.Logger _logger = logging.Logger('AuthRouter');

  final String _initialLocation;
  final Navigable _authRoute;
  final List<RouteBase> _publicRoutes;
  final List<RouteBase> _privateRoutes;

  AuthRouter({
    GlobalKey<NavigatorState>? navigatorKey,
    required String initialLocation,
    required Navigable authRoute,
    required List<RouteBase> publicRoutes,
    required List<RouteBase> privateRoutes,
  })  : _initialLocation = initialLocation,
        _authRoute = authRoute,
        _publicRoutes = publicRoutes,
        _privateRoutes = privateRoutes {
    if (navigatorKey != null) {
      _navigatorKey = navigatorKey;
    }
  }

  /// Creates an instance of the [GoRouter] class
  /// to handle unauthenticated and authenticated
  /// routes, and sets its as the root navigator
  /// router.
  GoRouter get config {
    return RootNavRouter(_createRouter).goRouter;
  }

  GoRouter _createRouter() => GoRouter(
        navigatorKey: _navigatorKey,
        initialLocation: _initialLocation,
        routes: [
          // Authentication route
          _authRoute.route(),
          // Add routes here
          ..._publicRoutes,
          ..._privateRoutes,
        ],
        redirect: (context, state) async {
          // Update the localizations for all registered
          // features. We do this here because localizations
          // delegates are configuered at the MaterialApp
          // level for this router and this callback happens
          // Update the context for all registered features
          // and services. For example this would take care
          // of updating the localizations for features and
          // services that depend on the localization
          // delegates which are configured at  MaterialApp
          // level for this router. This callback happens
          // for every single route within the MaterialApp's
          // GoRouter.
          app.FeatureRegistry.instance().setCurrentContext(context);

          // Using `of` method creates a dependency of
          // StreamAuthScope. It will cause go_router to
          // reparse current route if StreamAuth has a
          // new IdentityState instance.
          final identityState = _StreamAuthScope.of(context);

          _logger.fine(
            'Validating if route "${state.fullPath}" is authorized '
            'within scope of the identity state: $identityState',
          );

          // If the current route is a private route that
          // requires authentication, redirect to the
          // authentication route.
          final isPrivateRoute = _privateRoutes.any(
            (route) =>
                route is GoRoute &&
                state.fullPath != null &&
                state.fullPath!.startsWith(route.path),
          );
          if (isPrivateRoute && !identityState.isLoggedIn) {
            _logger.fine(
              'Private route "${state.fullPath}" not allowed as not logged in. '
              'Redirecting to authentication route "${_authRoute.routePath}"',
            );
            return _authRoute.routePath;
          }

          return null;
        },
      );
}

/// A scope that provides [IdentityService] state
/// of the user identity feature to a routable subtree.
class UserIdentityScope extends StatefulWidget {
  final Widget child;

  const UserIdentityScope({
    super.key,
    required this.child,
  });

  @override
  State<UserIdentityScope> createState() => _UserIdentityScopeState();
}

class _UserIdentityScopeState extends State<UserIdentityScope> {
  static final logging.Logger _logger = logging.Logger('UserIdentityScope');

  late Duration idleTime;
  Timer? timer;

  @override
  void initState() {
    super.initState();

    final config = UserIdentityFeature.instance().config;
    idleTime = Duration(minutes: config.authTimeout);
    _resetTimer();
  }

  @override
  Widget build(BuildContext context) {
    final identityService = context.read<identity.IdentityService>();
    return GestureDetector(
      behavior: HitTestBehavior.deferToChild,
      onPanDown: (details) {
        _resetTimer();
      },
      child: _StreamAuthScope(
        identityService: identityService,
        child: widget.child,
      ),
    );
  }

  void _resetTimer() {
    Future.microtask(() {
      if (timer != null) {
        timer!.cancel();
      }
      timer = Timer(idleTime, () {
        _logger.fine('User is idle for $idleTime. Signing out.');

        final identityService = context.read<identity.IdentityService>();
        identityService.signOut();
      });
    });
  }
}

/// A scope that provides the [identity.IdentityState]
/// stream for the subtree.
class _StreamAuthScope extends InheritedNotifier<_StreamAuthNotifier> {
  /// Creates a [_StreamAuthScope] scope that
  /// handles access to public and private routes.
  _StreamAuthScope({
    required identity.IdentityService identityService,
    required super.child,
  }) : super(
          notifier: _StreamAuthNotifier(identityService),
        );

  /// Gets the [identity.IdentityState].
  static identity.IdentityState of(BuildContext context) {
    return context
        .dependOnInheritedWidgetOfExactType<_StreamAuthScope>()!
        .notifier!
        .state;
  }
}

/// A class that converts the [identity.IdentityState] stream
/// into a [ChangeNotifier].
class _StreamAuthNotifier extends ChangeNotifier {
  /// Creates a [_StreamAuthNotifier].
  _StreamAuthNotifier(identity.IdentityService identityService)
      : state = identityService.state {
    identityService.stream.listen((event) {
      // Update the state
      state = identityService.state;
      // Notify listeners when the state changes
      notifyListeners();
    });
  }

  /// The stream auth client.
  identity.IdentityState state;
}

GlobalKey<NavigatorState>? _navigatorKey = GlobalKey<NavigatorState>();
