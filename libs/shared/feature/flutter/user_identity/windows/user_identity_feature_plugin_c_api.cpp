#include "include/user_identity_feature/user_identity_feature_plugin_c_api.h"

#include <flutter/plugin_registrar_windows.h>

#include "user_identity_feature_plugin.h"

void UserIdentityFeaturePluginCApiRegisterWithRegistrar(
    FlutterDesktopPluginRegistrarRef registrar) {
  user_identity_feature::UserIdentityFeaturePlugin::RegisterWithRegistrar(
      flutter::PluginRegistrarManager::GetInstance()
          ->GetRegistrar<flutter::PluginRegistrarWindows>(registrar));
}
