#ifndef FLUTTER_PLUGIN_USER_IDENTITY_FEATURE_PLUGIN_H_
#define FLUTTER_PLUGIN_USER_IDENTITY_FEATURE_PLUGIN_H_

#include <flutter/method_channel.h>
#include <flutter/plugin_registrar_windows.h>

#include <memory>

namespace user_identity_feature {

class UserIdentityFeaturePlugin : public flutter::Plugin {
 public:
  static void RegisterWithRegistrar(flutter::PluginRegistrarWindows *registrar);

  UserIdentityFeaturePlugin();

  virtual ~UserIdentityFeaturePlugin();

  // Disallow copy and assign.
  UserIdentityFeaturePlugin(const UserIdentityFeaturePlugin&) = delete;
  UserIdentityFeaturePlugin& operator=(const UserIdentityFeaturePlugin&) = delete;

  // Called when a method is called on this plugin's channel from Dart.
  void HandleMethodCall(
      const flutter::MethodCall<flutter::EncodableValue> &method_call,
      std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result);
};

}  // namespace user_identity_feature

#endif  // FLUTTER_PLUGIN_USER_IDENTITY_FEATURE_PLUGIN_H_
