import 'package:flutter_test/flutter_test.dart';
import 'package:billing_feature/billing_feature_platform_interface.dart';
import 'package:billing_feature/billing_feature_method_channel.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

class MockBillingFeaturePlatform
    with MockPlatformInterfaceMixin
    implements BillingFeaturePlatform {
  @override
  Future<String?> getPlatformVersion() => Future.value('42');
}

void main() {
  final BillingFeaturePlatform initialPlatform =
      BillingFeaturePlatform.instance;

  test('$MethodChannelBillingFeature is the default instance', () {
    expect(initialPlatform, isInstanceOf<MethodChannelBillingFeature>());
  });

  test('getPlatformVersion', () async {
    MockBillingFeaturePlatform fakePlatform = MockBillingFeaturePlatform();
    BillingFeaturePlatform.instance = fakePlatform;

    // Test the platform interface directly
    expect(await BillingFeaturePlatform.instance.getPlatformVersion(), '42');
  });
}
