import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:web/web.dart' as web;
import 'package:logging/logging.dart' as logging;

import 'package:platform_utilities_component/platform_utilities.dart';
import 'package:ui_widgets_component/ui_widgets.dart';
import 'package:app_framework_component/app_framework.dart' as app;
import 'package:user_space_model/user_space_model.dart';
import 'package:billing_service/billing_service.dart';

import '../billing_feature.dart';
import '../config/billing_feature_config.dart';
import '../l10n/l10n.dart';

class PlanSelectionDialog extends StatefulWidget {
  final Product product;
  final String title;
  final String subtitle;

  final String? orgId;

  final String? planId;
  final String? subscriptionId;
  final SubscriptionStatus? planStatus;
  final DateTime? planEndDate;

  final bool _dismissOnProcessingDone;
  final WaitForUpdates? _waitForSubscriptionUpdates;

  final VoidCallback? _goBack;
  final VoidCallback? _dismissDialog;
  final bool _dismissOnTap;

  const PlanSelectionDialog({
    super.key,
    required this.product,
    required this.title,
    required this.subtitle,
    this.orgId,
    this.planId,
    this.subscriptionId,
    this.planStatus,
    this.planEndDate,
    bool dismissOnPaymentTimeout = false,
    WaitForUpdates? waitForUpdates,
    VoidCallback? goBack,
    VoidCallback? dismissDialog,
    bool dismissOnTap = true,
  })  : _dismissOnProcessingDone = dismissOnPaymentTimeout,
        _waitForSubscriptionUpdates = waitForUpdates,
        _goBack = goBack,
        _dismissDialog = dismissDialog,
        _dismissOnTap = dismissOnTap;

  @override
  State<PlanSelectionDialog> createState() => _PlanSelectionDialogState();
}

class _PlanSelectionDialogState extends State<PlanSelectionDialog> {
  final logging.Logger logger = logging.Logger('PlanSelectionDialog');

  late final BillingFeature feature;
  late final BillingService billingService;

  // declare a GlobalKey
  final formKey = GlobalKey<FormState>();

  Plan? selectedPlan;

  int selectedPlanIndex = -1;
  int currentPlanIndex = -1;

  bool hasActivePlan = false;

  late final NumberFormat format;

  Action action = Action.none;

  @override
  void initState() {
    super.initState();
    feature = BillingFeature.instance();
    billingService = context.read<BillingService>();

    format = NumberFormat.currency(
      locale: widget.product.locale,
      symbol: widget.product.currencySymbol,
    );

    hasActivePlan = widget.subscriptionId != null &&
        (widget.planStatus == SubscriptionStatus.active ||
            widget.planStatus == SubscriptionStatus.trialing);

    _setSelectedPlan();
  }

  @override
  void didUpdateWidget(PlanSelectionDialog oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.planId != widget.planId ||
        oldWidget.subscriptionId != widget.subscriptionId ||
        oldWidget.planStatus != widget.planStatus ||
        oldWidget.planEndDate != widget.planEndDate) {
      _setSelectedPlan();
    }
  }

  void _setSelectedPlan() {
    late final String? planId;

    if (kIsWeb) {
      final uri = Uri.parse(web.window.location.href);
      final queryParams = uri.queryParameters.isEmpty && uri.fragment.isNotEmpty
          ? Uri.parse(uri.fragment).queryParameters
          : uri.queryParameters;

      action = Action.fromParams(queryParams);
      planId = queryParams['planId'];
    } else {
      planId = null;
    }

    // Set selected plan if provided
    for (var i = 0; i < widget.product.plans.length; i++) {
      final plan = widget.product.plans[i];
      if (plan.isDefault) {
        selectedPlan = plan;
        selectedPlanIndex = i;
      }
      if (planId == null) {
        if (plan.planId == widget.planId) {
          setState(() {
            selectedPlan = plan;
            selectedPlanIndex = i;
            currentPlanIndex = i;
          });
          break;
        }
      } else if (plan.planId == planId) {
        setState(() {
          selectedPlan = plan;
          selectedPlanIndex = i;
          currentPlanIndex = i;
        });
        break;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;

    // Convert plans of given product to SelectionList items
    final planSelection = widget.product.plans.map((plan) {
      final subtitle = plan.description
          .replaceAll(
            '{price}',
            format.format(plan.price),
          )
          .replaceAll(
            '{interval}',
            plan.interval,
          );

      final trialText = widget.planId == null && plan.trialDays != null
          ? l10n.trialText(plan.trialDays!)
          : '';

      late final Widget? iconImage;
      if (plan.iconImageAssetDark != null && plan.iconImageAssetLight != null) {
        iconImage = Image.asset(
          AppPlatform.isDarkMode
              ? plan.iconImageAssetDark!
              : plan.iconImageAssetLight!,
          width: 24,
          height: 24,
        );
      } else {
        iconImage = null;
      }

      if (plan.planId == widget.planId) {
        final isCancelable = hasActivePlan && widget.planEndDate == null;

        return SelectedListItem<Plan>(
          value: plan,
          title: plan.name,
          subtitle: subtitle,
          badge: widget.planEndDate != null
              ? l10n.planCanceled(
                  widget.planStatus == SubscriptionStatus.trialing
                      ? l10n.trialType
                      : '',
                  DateFormat('d MMM, yyyy').format(widget.planEndDate!),
                )
              : widget.planStatus?.toString(),
          badgeColor: hasActivePlan //
              ? Colors.green
              : widget.planStatus == SubscriptionStatus.pastDue ||
                      widget.planStatus == SubscriptionStatus.suspended //
                  ? Colors.redAccent
                  : Colors.orangeAccent,
          icon: Icons.credit_card,
          iconWidget: iconImage,
          trailingIcons: hasActivePlan && isCancelable
              ? [
                  TrailingIcon(
                    icon: Icons.cancel,
                    color: Colors.red,
                    tooltip: l10n.ttCancelPlan,
                    onTap: _onCancelSubscription,
                  ),
                ]
              : null,
        );
      } else {
        return SelectedListItem<Plan>(
          value: plan,
          title: plan.name,
          subtitle: '$subtitle$trialText',
          icon: Icons.credit_card,
          iconWidget: iconImage,
        );
      }
    }).toList();

    return BlocBuilder<BillingService, BillingState>(
      builder: (
        context,
        organizationState,
      ) {
        if (action != Action.waitingForUpdate && action != Action.none) {
          final actionToWaitOn = action;
          Future.microtask(() {
            if (widget._waitForSubscriptionUpdates != null) {
              setState(() {
                action = Action.waitingForUpdate;
              });
              switch (actionToWaitOn) {
                case Action.processing:
                  widget._waitForSubscriptionUpdates!(
                          timeout: feature.config.paymentTimeout,
                          statuses: [
                            SubscriptionStatus.active,
                            SubscriptionStatus.trialing,
                          ],
                          planId: selectedPlan?.planId,
                          canceled: false)
                      .then(_handleProcessingCompleted);
                case Action.cancelling:
                  widget._waitForSubscriptionUpdates!(
                          timeout: feature.config.paymentTimeout,
                          canceled: true)
                      .then(_handleProcessingCompleted);
                default:
                  assert(
                    false,
                    'Not a valid action to wait on: $action',
                  );
              }
            } else {
              setState(() {
                action = Action.none;
              });
            }
          });
        }

        final subscriptionType = currentPlanIndex == -1 || !hasActivePlan
            ? _SubUpdateType.create
            : currentPlanIndex == selectedPlanIndex
                ? _SubUpdateType.none
                : currentPlanIndex > selectedPlanIndex
                    ? _SubUpdateType.downgrade
                    : _SubUpdateType.upgrade;

        final isLoading = organizationState.isLoading(
              [
                BillingLoadingStates.createPaymentLink,
                BillingLoadingStates.modifySubscription,
                BillingLoadingStates.unsubscribe,
              ],
            ) ||
            action != Action.none;
        final inputEnabled = !isLoading;

        return DialogForm(
          formKey: formKey,
          title: widget.title,
          formViewHeight: widget.product.formViewHeight,
          body: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              MarkdownBody(
                styleSheet: createMarkDownStyleSheet(
                  context,
                ),
                data: widget.subtitle,
              ),
              const SizedBox(height: 16),
              Padding(
                padding: const EdgeInsets.only(left: 12, right: 12),
                child: SelectionList<Plan>(
                  items: planSelection,
                  selectedValue: selectedPlan,
                  listPadding: const EdgeInsets.all(0),
                  enabled: inputEnabled,
                  onSelectionChanged: (plan) {
                    setState(() {
                      selectedPlan = plan;
                      selectedPlanIndex = widget.product.plans.indexOf(plan);
                    });
                  },
                ),
              ),
            ],
          ),
          actions: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 8),
                child: widget._goBack == null
                    ? ElevatedButton.icon(
                        label: Text(l10n.cancelButton),
                        icon: const Icon(Icons.cancel),
                        iconAlignment: IconAlignment.end,
                        onPressed: inputEnabled ? widget._dismissDialog : null,
                      )
                    : ElevatedButton.icon(
                        label: Text(l10n.backButton),
                        icon: const Icon(Icons.arrow_back),
                        iconAlignment: IconAlignment.end,
                        onPressed: inputEnabled ? widget._goBack : null,
                      ),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 8),
                child: ElevatedIconButton(
                  label: Text(
                    switch (subscriptionType) {
                      _SubUpdateType.upgrade => l10n.upgradePlanButton,
                      _SubUpdateType.downgrade => l10n.downgradePlanButton,
                      _SubUpdateType.create => l10n.selectPlanButton,
                      _SubUpdateType.none => l10n.selectPlanButton,
                    },
                  ),
                  icon: Icons.payments,
                  iconAlignment: IconAlignment.end,
                  isLoading: isLoading,
                  onPressed:
                      inputEnabled && subscriptionType != _SubUpdateType.none
                          ? subscriptionType == _SubUpdateType.create
                              ? _onPlanSelected
                              : () => _onModifySubscription(subscriptionType)
                          : null,
                ),
              ),
            ],
          ),
          onCloseDialog: widget._dismissOnTap ? null : widget._dismissDialog,
        );
      },
    );
  }

  void _handleProcessingCompleted(bool completed) {
    final l10n = context.l10n;
    if (completed) {
      if (widget._dismissOnProcessingDone && widget._goBack != null) {
        widget._goBack!();
      }
    } else {
      billingService.addStateMessage(
        app.Message.error(
          l10n.errorSubsriptionUpdateFailed(widget.product.name),
        ),
      );
      if (widget._dismissOnProcessingDone && widget._dismissDialog != null) {
        widget._dismissDialog!();
      }
    }
    setState(() {
      action = Action.none;
    });
  }

  void _onPlanSelected() {
    final service = context.read<BillingService>();

    logger.info(
      'Creating payment link for plan ${selectedPlan!.planId}'
      ': ${web.window.location.href}',
    );

    final successUrl = kIsWeb //
        ? '${web.window.location.href}'
            '?action=processing&planId=${selectedPlan!.planId}'
        : feature.config.successUrl;
    final cancelUrl = kIsWeb //
        ? web.window.location.href
        : feature.config.successUrl;

    service
        .createPaymentLink(
      orgId: widget.orgId,
      priceId: selectedPlan!.planId,
      successUrl: successUrl,
      cancelUrl: cancelUrl,
      trialDays: widget.planId == null ? selectedPlan?.trialDays : null,
    )
        .then((paymentLink) {
      if (paymentLink != null) {
        if (kIsWeb) {
          web.window.open(paymentLink, '_self');
        } else {
          setState(() {
            action = Action.processing;
          });
          launchUrl(Uri.parse(paymentLink));
        }
      }
    });
  }

  void _onModifySubscription(_SubUpdateType subscriptionType) {
    final l10n = context.l10n;

    logger.info(
      'Modifying subscription to plan ${selectedPlan!.planId}',
    );

    AlertDialogHelper.show(
      context: context,
      icon: Icons.info,
      iconColor: Colors.amber,
      iconSize: 48,
      markdownText: subscriptionType == _SubUpdateType.upgrade
          ? l10n.upgradePlanConfirmation
          : l10n.downgradePlanConfirmation,
      textConstraints: const BoxConstraints(
        minWidth: 350,
        maxWidth: 350,
      ),
      confirmText: l10n.yesButton,
      cancelText: l10n.noButton,
      onConfirm: () {
        setState(() {
          action = Action.processing;
        });
        billingService
            .modifySubscription(
          orgId: widget.orgId,
          priceId: selectedPlan!.planId,
        )
            .onError((
          _,
          __,
        ) {
          setState(() {
            action = Action.none;
          });
          widget._dismissDialog!();
        });
      },
    );
  }

  void _onCancelSubscription(Plan plan) {
    final l10n = context.l10n;

    logger.info(
      'Cancelling subscription for plan ${selectedPlan!.planId}',
    );

    AlertDialogHelper.show(
      context: context,
      icon: Icons.dangerous,
      iconColor: Colors.red,
      iconSize: 48,
      markdownText: l10n.cancelPlanConfirmation,
      textConstraints: const BoxConstraints(
        minWidth: 350,
        maxWidth: 350,
      ),
      confirmText: l10n.yesButton,
      cancelText: l10n.noButton,
      onConfirm: () {
        setState(() {
          action = Action.cancelling;
        });
        billingService
            .unsubscribe(
          orgId: widget.orgId,
        )
            .onError((
          _,
          __,
        ) {
          setState(() {
            action = Action.none;
          });
          widget._dismissDialog!();
        });
      },
    );
  }
}

enum _SubUpdateType {
  create,
  upgrade,
  downgrade,
  none,
}

enum Action {
  none,
  processing,
  waitingForUpdate,
  cancelling;

  static Action fromParams(Map<String, String> params) {
    switch (params['action']) {
      case 'processing':
        return Action.processing;
      case 'cancelling':
        return Action.cancelling;
      default:
        return Action.none;
    }
  }
}

typedef WaitForUpdates = Future<bool> Function({
  Duration? timeout,
  List<SubscriptionStatus>? statuses,
  String? planId,
  bool? canceled,
});
