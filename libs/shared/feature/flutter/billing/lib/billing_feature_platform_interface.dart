import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'billing_feature_method_channel.dart';

abstract class BillingFeaturePlatform extends PlatformInterface {
  /// Constructs a BillingFeaturePlatform.
  BillingFeaturePlatform() : super(token: _token);

  static final Object _token = Object();

  static BillingFeaturePlatform _instance = MethodChannelBillingFeature();

  /// The default instance of [BillingFeaturePlatform] to use.
  ///
  /// Defaults to [MethodChannelBillingFeature].
  static BillingFeaturePlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [BillingFeaturePlatform] when
  /// they register themselves.
  static set instance(BillingFeaturePlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  Future<String?> getPlatformVersion() {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }
}
