import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'billing_feature_platform_interface.dart';

/// An implementation of [BillingFeaturePlatform] that uses method channels.
class MethodChannelBillingFeature extends BillingFeaturePlatform {
  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('billing_feature');

  @override
  Future<String?> getPlatformVersion() async {
    final version = await methodChannel.invokeMethod<String>('getPlatformVersion');
    return version;
  }
}
