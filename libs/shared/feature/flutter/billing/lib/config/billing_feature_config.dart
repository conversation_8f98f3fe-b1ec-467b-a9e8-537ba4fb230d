import 'package:json_annotation/json_annotation.dart';

import 'package:app_framework_component/app_framework.dart';

part 'billing_feature_config.g.dart';

@JsonSerializable(
  fieldRename: FieldRename.kebab,
  createToJson: false,
)
class BillingFeatureConfig extends FeatureConfig {
  final List<Product> products;

  final Duration paymentTimeout;

  final String successUrl;
  final String cancelUrl;

  const BillingFeatureConfig({
    required this.products,
    this.paymentTimeout = const Duration(seconds: 60),
    this.successUrl = '',
    this.cancelUrl = '',
  });

  factory BillingFeatureConfig.fromJson(Map<String, dynamic> json) =>
      _$BillingFeatureConfigFromJson(json);
}

@JsonSerializable(
  fieldRename: FieldRename.kebab,
  createToJson: false,
)
class Product extends FeatureConfig {
  final String name;
  final String description;
  final String productId;
  final String locale;
  final String currencySymbol;

  final double formViewWidth;
  final double formViewHeight;

  /// The list of plans available to the user
  /// to choose from when subscribing to the
  /// product
  final List<Plan> plans;

  const Product({
    required this.name,
    required this.description,
    required this.productId,
    required this.locale,
    required this.currencySymbol,
    required this.plans,
    this.formViewWidth = 588.0,
    this.formViewHeight = 616.0,
  });

  factory Product.fromJson(Map<String, dynamic> json) =>
      _$ProductFromJson(json);
}

@JsonSerializable(
  fieldRename: FieldRename.kebab,
  createToJson: false,
)
class Plan extends FeatureConfig {
  final String name;
  final String description;
  final String interval;
  final int price;
  final int? trialDays;
  final String planId;
  final bool isDefault;

  final String? iconImageAssetDark;
  final String? iconImageAssetLight;

  const Plan({
    required this.name,
    required this.description,
    required this.interval,
    required this.price,
    this.trialDays,
    required this.planId,
    this.isDefault = false,
    this.iconImageAssetDark,
    this.iconImageAssetLight,
  });

  factory Plan.fromJson(Map<String, dynamic> json) => _$PlanFromJson(json);
}
