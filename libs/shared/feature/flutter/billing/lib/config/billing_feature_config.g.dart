// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'billing_feature_config.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BillingFeatureConfig _$BillingFeatureConfigFromJson(
        Map<String, dynamic> json) =>
    BillingFeatureConfig(
      products: (json['products'] as List<dynamic>)
          .map((e) => Product.fromJson(e as Map<String, dynamic>))
          .toList(),
      paymentTimeout: json['payment-timeout'] == null
          ? const Duration(seconds: 60)
          : Duration(microseconds: (json['payment-timeout'] as num).toInt()),
      successUrl: json['success-url'] as String? ?? '',
      cancelUrl: json['cancel-url'] as String? ?? '',
    );

Product _$ProductFromJson(Map<String, dynamic> json) => Product(
      name: json['name'] as String,
      description: json['description'] as String,
      productId: json['product-id'] as String,
      locale: json['locale'] as String,
      currencySymbol: json['currency-symbol'] as String,
      plans: (json['plans'] as List<dynamic>)
          .map((e) => Plan.fromJson(e as Map<String, dynamic>))
          .toList(),
      formViewWidth: (json['form-view-width'] as num?)?.toDouble() ?? 588.0,
      formViewHeight: (json['form-view-height'] as num?)?.toDouble() ?? 616.0,
    );

Plan _$PlanFromJson(Map<String, dynamic> json) => Plan(
      name: json['name'] as String,
      description: json['description'] as String,
      interval: json['interval'] as String,
      price: (json['price'] as num).toInt(),
      trialDays: (json['trial-days'] as num?)?.toInt(),
      planId: json['plan-id'] as String,
      isDefault: json['is-default'] as bool? ?? false,
      iconImageAssetDark: json['icon-image-asset-dark'] as String?,
      iconImageAssetLight: json['icon-image-asset-light'] as String?,
    );
