import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'billing_localizations_en.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of BillingLocalizations
/// returned by `BillingLocalizations.of(context)`.
///
/// Applications need to include `BillingLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/billing_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: BillingLocalizations.localizationsDelegates,
///   supportedLocales: BillingLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the BillingLocalizations.supportedLocales
/// property.
abstract class BillingLocalizations {
  BillingLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static BillingLocalizations of(BuildContext context) {
    return Localizations.of<BillingLocalizations>(
      context,
      BillingLocalizations,
    )!;
  }

  static const LocalizationsDelegate<BillingLocalizations> delegate =
      _BillingLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[Locale('en')];

  /// No description provided for @billingPlanTitle.
  ///
  /// In en, this message translates to:
  /// **'Subscription Plans'**
  String get billingPlanTitle;

  /// No description provided for @billingPlanSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Select Subscription Plan'**
  String get billingPlanSubtitle;

  /// No description provided for @trialText.
  ///
  /// In en, this message translates to:
  /// **' after {trialDays} day free trial'**
  String trialText(int trialDays);

  /// No description provided for @downgradePlanConfirmation.
  ///
  /// In en, this message translates to:
  /// **'You are downgrading your current plan. The change will go into effect immediately and charges will be prorated in your next billing cycle. You will be refunded any unused portion of the old price with respect to the new price.\\\n\\\nDo you wish to downgrade from your current plan?'**
  String get downgradePlanConfirmation;

  /// No description provided for @upgradePlanConfirmation.
  ///
  /// In en, this message translates to:
  /// **'You are upgrading your current plan. The change will go into effect immediately and charges will be prorated in your next billing cycle. You will be billed the extra cost of the new price minus any unused portion of the old plan.\\\n\\\nDo you wish to upgrade from your current plan?'**
  String get upgradePlanConfirmation;

  /// No description provided for @planCanceled.
  ///
  /// In en, this message translates to:
  /// **'{type}ending on {date}'**
  String planCanceled(String type, String date);

  /// No description provided for @trialType.
  ///
  /// In en, this message translates to:
  /// **'trial '**
  String get trialType;

  /// No description provided for @ttCancelPlan.
  ///
  /// In en, this message translates to:
  /// **'unsubscribe'**
  String get ttCancelPlan;

  /// No description provided for @cancelPlanConfirmation.
  ///
  /// In en, this message translates to:
  /// **'If you cancel your current plan you will lose access to the Smarflow application once the current plan period ends. You can restore access any-time by re-subscribing via this same dialog or the subscribe prompt that will be shown when you attempt to sign back in after your current plan period ends.\\\n\\\nDo you wish to unsubscribe from your current plan?'**
  String get cancelPlanConfirmation;

  /// No description provided for @backButton.
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get backButton;

  /// No description provided for @cancelButton.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancelButton;

  /// No description provided for @selectPlanButton.
  ///
  /// In en, this message translates to:
  /// **'Subscribe'**
  String get selectPlanButton;

  /// No description provided for @downgradePlanButton.
  ///
  /// In en, this message translates to:
  /// **'Downgrade'**
  String get downgradePlanButton;

  /// No description provided for @upgradePlanButton.
  ///
  /// In en, this message translates to:
  /// **'Upgrade'**
  String get upgradePlanButton;

  /// No description provided for @subscribedPlanButton.
  ///
  /// In en, this message translates to:
  /// **'Subscribed'**
  String get subscribedPlanButton;

  /// No description provided for @noButton.
  ///
  /// In en, this message translates to:
  /// **'NO'**
  String get noButton;

  /// No description provided for @yesButton.
  ///
  /// In en, this message translates to:
  /// **'YES'**
  String get yesButton;

  /// No description provided for @errorSubsriptionUpdateFailed.
  ///
  /// In en, this message translates to:
  /// **'Subscription plan for product \'{product}\' failed to activate. Please contact support'**
  String errorSubsriptionUpdateFailed(String product);
}

class _BillingLocalizationsDelegate
    extends LocalizationsDelegate<BillingLocalizations> {
  const _BillingLocalizationsDelegate();

  @override
  Future<BillingLocalizations> load(Locale locale) {
    return SynchronousFuture<BillingLocalizations>(
      lookupBillingLocalizations(locale),
    );
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en'].contains(locale.languageCode);

  @override
  bool shouldReload(_BillingLocalizationsDelegate old) => false;
}

BillingLocalizations lookupBillingLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return BillingLocalizationsEn();
  }

  throw FlutterError(
    'BillingLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
