// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'billing_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class BillingLocalizationsEn extends BillingLocalizations {
  BillingLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get billingPlanTitle => 'Subscription Plans';

  @override
  String get billingPlanSubtitle => 'Select Subscription Plan';

  @override
  String trialText(int trialDays) {
    return ' after $trialDays day free trial';
  }

  @override
  String get downgradePlanConfirmation =>
      'You are downgrading your current plan. The change will go into effect immediately and charges will be prorated in your next billing cycle. You will be refunded any unused portion of the old price with respect to the new price.\\\n\\\nDo you wish to downgrade from your current plan?';

  @override
  String get upgradePlanConfirmation =>
      'You are upgrading your current plan. The change will go into effect immediately and charges will be prorated in your next billing cycle. You will be billed the extra cost of the new price minus any unused portion of the old plan.\\\n\\\nDo you wish to upgrade from your current plan?';

  @override
  String planCanceled(String type, String date) {
    return '${type}ending on $date';
  }

  @override
  String get trialType => 'trial ';

  @override
  String get ttCancelPlan => 'unsubscribe';

  @override
  String get cancelPlanConfirmation =>
      'If you cancel your current plan you will lose access to the Smarflow application once the current plan period ends. You can restore access any-time by re-subscribing via this same dialog or the subscribe prompt that will be shown when you attempt to sign back in after your current plan period ends.\\\n\\\nDo you wish to unsubscribe from your current plan?';

  @override
  String get backButton => 'Back';

  @override
  String get cancelButton => 'Cancel';

  @override
  String get selectPlanButton => 'Subscribe';

  @override
  String get downgradePlanButton => 'Downgrade';

  @override
  String get upgradePlanButton => 'Upgrade';

  @override
  String get subscribedPlanButton => 'Subscribed';

  @override
  String get noButton => 'NO';

  @override
  String get yesButton => 'YES';

  @override
  String errorSubsriptionUpdateFailed(String product) {
    return 'Subscription plan for product \'$product\' failed to activate. Please contact support';
  }
}
