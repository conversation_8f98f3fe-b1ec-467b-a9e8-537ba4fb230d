import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

import 'package:app_framework_component/app_framework.dart' as app;
import 'package:billing_service/billing_service.dart' as service;
import 'package:user_space_model/user_space_model.dart';

import 'l10n/l10n.dart';
import 'l10n/billing_localizations.dart';
import 'config/billing_feature_config.dart';
import 'widgets/plan_selection_dialog.dart';

class BillingFeature extends app.Feature {
  static String get featureName => 'billing';

  /// The configuration for this feature.
  late final BillingFeatureConfig config;

  /// Localization overrides
  late final LocalizationLookup? _localizationLookup;

  @override
  String get name => featureName;

  @override
  app.FeatureConfigDeserializer get configDeserializer =>
      BillingFeatureConfig.fromJson;

  @override
  List<LocalizationsDelegate> get localizationsDelegates => [
        // The localization delegates for this feature
        BillingLocalizations.delegate,
        service.BillingLocalizations.delegate,
      ];

  @override
  List<app.FeatureAnchor> anchors(
    BuildContext? context,
  ) {
    return [
      // Add billing-related anchors here if needed
      // For example, billing status in status bar, billing menu options, etc.
    ];
  }

  @override
  Future<void> initialize(
    app.FeatureConfig? config,
    Map<String, app.Feature> registeredFeatures,
  ) async {
    assert(
      config != null,
      'A configuration for the BillingFeature was not provided',
    );

    this.config = config as BillingFeatureConfig;
  }

  // Billing Feature creation and registration

  static Future<void> register({
    LocalizationLookup? localizationLookup,
  }) async {
    final featureRegistry = app.FeatureRegistry.instance();
    await featureRegistry.registerFeature(
      BillingFeature._(
        localizationLookup: localizationLookup,
      ),
    );
  }

  factory BillingFeature.instance() {
    final feature = app.FeatureRegistry.instance().getFeature(featureName);
    assert(
      feature != null,
      'BillingFeature is not registered with the feature registry',
    );
    return feature as BillingFeature;
  }

  BillingFeature._({
    LocalizationLookup? localizationLookup,
  }) : _localizationLookup = localizationLookup {
    // Register any billing services here if needed
    services.add<service.BillingService>((context) {
      final billingProvider = GetIt.instance.get<service.BillingProvider>();
      return service.BillingService(billingProvider);
    });
  }

  String getLocalizedValue(BuildContext context, String key) {
    String? value = _localizationLookup?.call(
      context,
      key,
    );
    if (value == null) {
      final l10n = context.l10n;
      switch (key) {
        case 'billingPlanTitle':
          return l10n.billingPlanTitle;
        case 'billingPlanSubtitle':
          return l10n.billingPlanSubtitle;
        case 'selectPlanButton':
          return l10n.selectPlanButton;
        case 'cancelButton':
          return l10n.cancelButton;
        default:
          throw StateError(
            'No localized value found for overridable localized key: $key',
          );
      }
    } else {
      return value;
    }
  }

  // Billing Feature API methods

  Product? getBillingProduct(String productName) {
    return config.products.where((p) => p.name == productName).firstOrNull;
  }

  /// Shows a plan selection dialog for the given customer type
  static Future<Plan?> showPlanSelectionDialog(
    BuildContext context,
    Product product, {
    String? title,
    String? subtitle,
    String? orgId,
    String? currentPlanId,
    String? currentSubscriptionId,
    SubscriptionStatus? currentPlanStatus,
    DateTime? planEndDate,
    bool dismissOnPaymentTimeout = false,
    WaitForUpdates? waitForUpdates,
    bool dismissOnTap = true,
  }) async {
    final billingFeature = BillingFeature.instance();
    return showDialog<Plan>(
      context: context,
      barrierDismissible: dismissOnTap,
      builder: (dialogContext) {
        return Dialog(
          child: SizedBox(
            width: product.formViewWidth + 32, // padding (16*2)
            height: product.formViewHeight + 32, // padding (16*2)
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: PlanSelectionDialog(
                product: product,
                title: title ??
                    billingFeature.getLocalizedValue(
                      context,
                      'billingPlanTitle',
                    ),
                subtitle: subtitle ??
                    billingFeature.getLocalizedValue(
                      context,
                      'billingPlanSubtitle',
                    ),
                orgId: orgId,
                planId: currentPlanId,
                subscriptionId: currentSubscriptionId,
                planStatus: currentPlanStatus,
                planEndDate: planEndDate,
                dismissOnPaymentTimeout: dismissOnPaymentTimeout,
                waitForUpdates: waitForUpdates,
                dismissDialog: () {
                  Navigator.pop(context);
                },
                dismissOnTap: dismissOnTap,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget planSelectionDialogCard(
    BuildContext context,
    Product product, {
    String? title,
    String? subtitle,
    String? orgId,
    String? currentPlanId,
    String? currentSubscriptionId,
    SubscriptionStatus? currentPlanStatus,
    DateTime? planEndDate,
    bool dismissOnPaymentTimeout = false,
    WaitForUpdates? waitForUpdates,
    VoidCallback? goBack,
    void Function()? dismissDialog,
    bool dismissOnTap = true,
  }) {
    return SizedBox(
      width: product.formViewWidth + 40, // margins (4*2) + padding (16*2)
      height: product.formViewHeight + 40, // margins (4*2) + padding (16*2)
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: planSelectionDialog(
            context,
            product,
            title: title,
            subtitle: subtitle,
            orgId: orgId,
            currentPlanId: currentPlanId,
            currentSubscriptionId: currentSubscriptionId,
            currentPlanStatus: currentPlanStatus,
            planEndDate: planEndDate,
            dismissOnPaymentTimeout: dismissOnPaymentTimeout,
            waitForUpdates: waitForUpdates,
            goBack: goBack,
            dismissDialog: dismissDialog,
            dismissOnTap: dismissOnTap,
          ),
        ),
      ),
    );
  }

  Widget planSelectionDialog(
    BuildContext context,
    Product product, {
    String? title,
    String? subtitle,
    String? orgId,
    String? currentPlanId,
    String? currentSubscriptionId,
    SubscriptionStatus? currentPlanStatus,
    DateTime? planEndDate,
    bool dismissOnPaymentTimeout = false,
    WaitForUpdates? waitForUpdates,
    VoidCallback? goBack,
    void Function()? dismissDialog,
    bool dismissOnTap = true,
  }) {
    return PlanSelectionDialog(
      product: product,
      title: title ??
          getLocalizedValue(
            context,
            'billingPlanTitle',
          ),
      subtitle: subtitle ??
          getLocalizedValue(
            context,
            'billingPlanSubtitle',
          ),
      orgId: orgId,
      planId: currentPlanId,
      planStatus: currentPlanStatus,
      subscriptionId: currentSubscriptionId,
      planEndDate: planEndDate,
      dismissOnPaymentTimeout: dismissOnPaymentTimeout,
      waitForUpdates: waitForUpdates,
      goBack: goBack,
      dismissDialog: dismissDialog,
      dismissOnTap: dismissOnTap,
    );
  }
}

/// A function that returns a localized string for a given key.
typedef LocalizationLookup = String? Function(BuildContext context, String key);
