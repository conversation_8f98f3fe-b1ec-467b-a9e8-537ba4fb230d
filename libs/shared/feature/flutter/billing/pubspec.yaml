name: billing_feature
description: "MyCS billing and subscription management feature module"
publish_to: 'none'
version: 0.1.0
homepage: https://github.com/novassist-ai/novassist/libs/shared/feature/flutter/billing

environment:
  sdk: '>=3.4.0 <4.0.0'
  flutter: '>=3.3.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  plugin_platform_interface: ^2.0.2
  web: ^1.1.1
  intl: ^0.20.2
  equatable: ^2.0.5
  provider: ^6.1.2
  go_router: ^14.1.2
  bloc: ^9.0.0
  flutter_bloc: ^9.1.0
  get_it: ^8.0.3
  logging: ^1.2.0
  json_annotation: ^4.9.0
  url_launcher: ^6.3.1
  flutter_markdown: ^0.7.7+1

  utilities_ab:
    path: ../../../../commons/dart/utilities
  platform_utilities_component:
    path: ../../../../component/flutter/platform_utilities
  ui_widgets_component:
    path: ../../../../component/flutter/ui_widgets
  app_framework_component:
    path: ../../../../component/flutter/app_framework
  nav_layouts_component:
    path: ../../../../component/flutter/nav_layouts
  user_identity_feature:
    path: ../user_identity
  user_space_model:
    path: ../../../../shared/service/flutter/user_space
  billing_service:
    path: ../../../../shared/service/flutter/billing

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0

  build_runner: ^2.4.8
  json_serializable: ^6.8.0

flutter:
  generate: true

  plugin:
    platforms:
      android:
        package: ai.novassist.shared.feature.flutter.billing.billing_feature
        pluginClass: BillingFeaturePlugin
      ios:
        pluginClass: BillingFeaturePlugin
      linux:
        pluginClass: BillingFeaturePlugin
      macos:
        pluginClass: BillingFeaturePlugin
      windows:
        pluginClass: BillingFeaturePluginCApi
      web:
        pluginClass: BillingFeatureWeb
        fileName: billing_feature_web.dart

  # To add assets to your plugin package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/to/asset-from-package
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # To add custom fonts to your plugin package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/to/font-from-package
