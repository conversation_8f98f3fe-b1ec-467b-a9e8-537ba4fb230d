#ifndef FLUTTER_PLUGIN_BILLING_FEATURE_PLUGIN_H_
#define FLUTTER_PLUGIN_BILLING_FEATURE_PLUGIN_H_

#include <flutter/method_channel.h>
#include <flutter/plugin_registrar_windows.h>

#include <memory>

namespace billing_feature {

class BillingFeaturePlugin : public flutter::Plugin {
 public:
  static void RegisterWithRegistrar(flutter::PluginRegistrarWindows *registrar);

  BillingFeaturePlugin();

  virtual ~BillingFeaturePlugin();

  // Disallow copy and assign.
  BillingFeaturePlugin(const BillingFeaturePlugin&) = delete;
  BillingFeaturePlugin& operator=(const BillingFeaturePlugin&) = delete;

  // Called when a method is called on this plugin's channel from Dart.
  void HandleMethodCall(
      const flutter::MethodCall<flutter::EncodableValue> &method_call,
      std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result);
};

}  // namespace billing_feature

#endif  // FLUTTER_PLUGIN_BILLING_FEATURE_PLUGIN_H_
