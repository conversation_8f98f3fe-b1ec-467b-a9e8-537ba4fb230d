#include "include/billing_feature/billing_feature_plugin_c_api.h"

#include <flutter/plugin_registrar_windows.h>

#include "billing_feature_plugin.h"

void BillingFeaturePluginCApiRegisterWithRegistrar(
    FlutterDesktopPluginRegistrarRef registrar) {
  billing_feature::BillingFeaturePlugin::RegisterWithRegistrar(
      flutter::PluginRegistrarManager::GetInstance()
          ->GetRegistrar<flutter::PluginRegistrarWindows>(registrar));
}
