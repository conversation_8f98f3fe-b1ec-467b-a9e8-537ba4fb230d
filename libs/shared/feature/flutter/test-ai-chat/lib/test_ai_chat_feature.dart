import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';


import 'package:ui_widgets_component/ui_widgets.dart' as ui;
import 'package:app_framework_component/app_framework.dart' as app;
import 'package:nav_layouts_component/nav_layouts.dart' as nav;



final class TestAiChatFeature  extends app.Feature{
  static String get featureName => 'test_ai_chat';

  
  @override
  // TODO: implement name
  String get name => throw UnimplementedError();
  
}