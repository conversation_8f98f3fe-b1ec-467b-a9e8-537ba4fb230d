name: organization_feature
description: "MyCS organization management feature module"
publish_to: 'none'
version: 0.1.0
homepage: https://github.com/novassist-ai/novassist/libs/shared/feature/flutter/organization

environment:
  sdk: '>=3.4.0 <4.0.0'
  flutter: '>=3.3.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  plugin_platform_interface: ^2.0.2
  web: ^1.1.1
  intl: ^0.20.2
  equatable: ^2.0.5
  provider: ^6.1.2
  go_router: ^14.1.2
  bloc: ^9.0.0
  flutter_bloc: ^9.1.0
  get_it: ^8.0.3
  logging: ^1.2.0
  json_annotation: ^4.9.0
  url_launcher: ^6.3.1
  flutter_markdown: ^0.7.7+1

  utilities_ab:
    path: ../../../../commons/dart/utilities
  ui_widgets_component:
    path: ../../../../component/flutter/ui_widgets
  app_framework_component:
    path: ../../../../component/flutter/app_framework
  nav_layouts_component:
    path: ../../../../component/flutter/nav_layouts
  user_identity_feature:
    path: ../user_identity
  user_space_model:
    path: ../../../service/flutter/user_space
  organization_service:
    path: ../../../service/flutter/organization
  billing_service:
    path: ../../../service/flutter/billing
  billing_feature:
    path: ../billing

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  build_runner: ^2.4.8
  json_serializable: ^6.8.0

flutter:
  generate: true

  plugin:
    platforms:
      android:
        package: ai.novassist.shared.feature.flutter.organization.organization_feature
        pluginClass: OrganizationFeaturePlugin
      ios:
        pluginClass: OrganizationFeaturePlugin
      linux:
        pluginClass: OrganizationFeaturePlugin
      macos:
        pluginClass: OrganizationFeaturePlugin
      windows:
        pluginClass: OrganizationFeaturePluginCApi
      web:
        pluginClass: OrganizationFeatureWeb
        fileName: organization_feature_web.dart
