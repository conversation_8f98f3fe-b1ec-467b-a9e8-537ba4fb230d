import 'package:flutter/material.dart';

import 'package:ui_widgets_component/ui_widgets.dart';
import 'package:user_space_model/user_space_model.dart' as model;

final class ContactTypeLabels {
  static ContactLabels<model.ContactType> labels(
    BuildContext context, {
    required String Function(BuildContext, String) localizedValue,
  }) {
    return ContactLabels.fromLocalization<model.ContactType>(
      context,
    ).copyWith(
      contactTypes: [
        ContactTypeSelection<model.ContactType>(
          value: model.ContactType.email,
          label: localizedValue(
            context,
            'contactTypeEmail',
          ),
          icon: const Icon(Icons.contact_mail),
          inputType: ContactInputType.emailAddress,
        ),
        ContactTypeSelection<model.ContactType>(
          value: model.ContactType.tollFree,
          label: localizedValue(
            context,
            'contactTypeTollFree',
          ),
          icon: const Icon(Icons.contact_phone),
          inputType: ContactInputType.phoneNumber,
        ),
        ContactTypeSelection<model.ContactType>(
          value: model.ContactType.voiceAndMessaging,
          label: localizedValue(
            context,
            'contactTypeVoiceAndSMS',
          ),
          icon: const Icon(Icons.perm_phone_msg),
          inputType: ContactInputType.phoneNumber,
        ),
        ContactTypeSelection<model.ContactType>(
          value: model.ContactType.voiceOnly,
          label: localizedValue(
            context,
            'contactTypeVoiceOnly',
          ),
          icon: const Icon(Icons.phone),
          inputType: ContactInputType.phoneNumber,
        ),
        ContactTypeSelection<model.ContactType>(
          value: model.ContactType.messagingOnly,
          label: localizedValue(
            context,
            'contactTypeSMSOnly',
          ),
          icon: const Icon(Icons.sms),
          inputType: ContactInputType.phoneNumber,
        ),
        ContactTypeSelection<model.ContactType>(
          value: model.ContactType.fax,
          label: localizedValue(
            context,
            'contactTypeFax',
          ),
          icon: const Icon(Icons.fax),
          inputType: ContactInputType.phoneNumber,
        ),
      ],
    );
  }
}

extension ContactValueExtension on ContactValue<model.ContactType> {
  model.ContactInput toContactInput() {
    switch (type!) {
      case model.ContactType.email:
        return model.ContactInput(
          type: type!,
          description: description,
          emailAddress: value,
        );
      case model.ContactType.tollFree:
      case model.ContactType.voiceAndMessaging:
      case model.ContactType.voiceOnly:
      case model.ContactType.fax:
      case model.ContactType.messagingOnly:
        return model.ContactInput(
          type: type!,
          description: description,
          phoneNumber: value,
        );
    }
  }

  ContactValue<model.ContactType> fromContact(
    model.Contact contact,
  ) {
    switch (contact.type) {
      case model.ContactType.email:
        return copyWith(
          type: contact.type,
          description: contact.description,
          value: contact.emailAddress,
        );
      case model.ContactType.tollFree:
      case model.ContactType.voiceAndMessaging:
      case model.ContactType.voiceOnly:
      case model.ContactType.fax:
      case model.ContactType.messagingOnly:
        return copyWith(
          type: contact.type,
          description: contact.description,
          value: contact.phoneNumber,
        );
    }
  }
}
