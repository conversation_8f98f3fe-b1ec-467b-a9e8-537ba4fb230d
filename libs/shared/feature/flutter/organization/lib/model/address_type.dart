import 'package:flutter/material.dart';

import 'package:ui_widgets_component/ui_widgets.dart';
import 'package:user_space_model/user_space_model.dart' as model;

final class AddressTypeLabels {
  static AddressLabels<model.AddressType> labels(
    BuildContext context, {
    required String Function(BuildContext, String) localizedValue,
    required bool Function(model.AddressType) enabled,
    required String countryCode,
  }) {
    return AddressLabels.fromLocalization<model.AddressType>(
      context,
      countryCode: countryCode,
    ).copyWith(
      addressTypes: [
        AddressTypeSelection<model.AddressType>(
          value: model.AddressType.main,
          label: localizedValue(
            context,
            'addrTypeMain',
          ),
          icon: const Icon(Icons.business),
          enabled: enabled(model.AddressType.main),
        ),
        AddressTypeSelection<model.AddressType>(
          value: model.AddressType.branch,
          label: localizedValue(
            context,
            'addrTypeBranch',
          ),
          icon: const Icon(Icons.maps_home_work),
          enabled: enabled(model.AddressType.branch),
        ),
        AddressTypeSelection<model.AddressType>(
          value: model.AddressType.billing,
          label: localizedValue(
            context,
            'addrTypeBilling',
          ),
          tooltip: localizedValue(
            context,
            'addrTypeBillingTooltip',
          ),
          icon: const Icon(Icons.payment),
          enabled: enabled(model.AddressType.billing),
        ),
      ],
    );
  }
}

extension AddressValueExtension on AddressValue<model.AddressType> {
  model.AddressInput toAddressInput() {
    return model.AddressInput(
      type: type!,
      description: description,
      number: number,
      street: street,
      other: other,
      municipality: municipality,
      county: county,
      province: province,
      postalCode: postalCode,
      countryCode: countryCode,
    );
  }

  AddressValue<model.AddressType> fromAddress(
    model.Address address,
  ) {
    return copyWith(
      type: address.type,
      description: address.description,
      number: address.number,
      street: address.street,
      other: address.other,
      municipality: address.municipality,
      county: address.county,
      province: address.province,
      postalCode: address.postalCode,
      countryCode: address.countryCode,
    );
  }
}
