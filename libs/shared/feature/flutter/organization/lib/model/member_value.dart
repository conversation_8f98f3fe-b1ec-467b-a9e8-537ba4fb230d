import 'package:flutter/material.dart';

import 'package:ui_widgets_component/ui_widgets.dart';
import 'package:user_space_model/user_space_model.dart' as model;

class MemberValue extends FormValue {
  final String? userId;

  final String value;
  final MemberStatus status;

  @override
  final bool isReadOnly;

  @override
  bool get isEmpty => value.isEmpty;

  @override
  bool get isRequiredNotEmpty => value.isNotEmpty;

  @override
  final Map<String, bool> validFields;

  static MemberValue create({
    String value = '',
    isReadOnly = false,
    MemberStatus? status,
  }) {
    return MemberValue._(
      key: UniqueKey(),
      userId: null,
      value: value,
      isReadOnly: isReadOnly,
      status: status ?? MemberStatus.toBeInvited,
    );
  }

  const MemberValue._({
    super.key,
    required this.userId,
    required this.value,
    required this.isReadOnly,
    this.status = MemberStatus.toBeInvited,
    this.validFields = const {
      'value': true,
    },
  });

  MemberValue copyWith({
    String? userId,
    bool resetUserId = false,
    String? value,
    bool? isReadOnly,
    MemberStatus? status,
    (String, bool)? isFieldValid,
  }) {
    return MemberValue._(
      key: key,
      userId: resetUserId ? null : userId ?? this.userId,
      value: value ?? this.value,
      isReadOnly: isReadOnly ?? this.isReadOnly,
      status: status ?? this.status,
      // if isFieldValid is not null, update the validFields map
      validFields: isFieldValid != null
          ? {
              ...validFields,
              isFieldValid.$1: isFieldValid.$2,
            }
          : validFields,
    );
  }

  MemberValue fromOrgUser(model.OrgUser orgUser) {
    switch (orgUser.status) {
      case model.UserAccessStatus.pending:
        return copyWith(
          userId: orgUser.user?.userId,
          value: orgUser.user?.emailAddress ?? orgUser.user?.userId,
          status: MemberStatus.invited,
        );
      case model.UserAccessStatus.active:
        return copyWith(
          userId: orgUser.user?.userId,
          value: orgUser.user?.fullName ?? orgUser.user?.emailAddress,
          isReadOnly: orgUser.isOwner,
          status: orgUser.isOwner == true
              ? MemberStatus.owner
              : MemberStatus.active,
        );
      case model.UserAccessStatus.inactive:
        return copyWith(
          userId: orgUser.user?.userId,
          value: orgUser.user?.fullName ?? orgUser.user?.emailAddress,
          status: MemberStatus.inactive,
        );
      default:
        throw StateError('Unknown status: ${orgUser.status}');
    }
  }

  @override
  List<Object?> get props => [
        value,
        status,
      ];
}

enum MemberStatus {
  toBeInvited,
  resendInvitation,
  active,
  invited,
  inactive,
  owner,
}
