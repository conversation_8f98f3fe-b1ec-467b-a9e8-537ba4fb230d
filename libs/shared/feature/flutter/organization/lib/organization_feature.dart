import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

import 'package:app_framework_component/app_framework.dart' as app;
import 'package:nav_layouts_component/nav_layouts.dart' as nav;
import 'package:organization_service/organization_service.dart' as service;
import 'package:user_identity_feature/user_identity.dart' as identity;

import 'l10n/l10n.dart';
import 'l10n/organization_localizations.dart';
import 'components/org_selector_status_tool.dart';
import 'components/quota_status_tool.dart';
import 'config/organization_feature_config.dart';
import 'widgets/organization_profile.dart';
import 'organization_feature_platform_interface.dart';

class OrganizationFeature extends app.Feature {
  static String get featureName => 'organization';

  /// The configuration for this feature.
  late final OrganizationFeatureConfig config;

  /// Localization overrides
  late final LocalizationLookup? _localizationLookup;

  @override
  String get name => featureName;

  @override
  app.FeatureConfigDeserializer get configDeserializer =>
      OrganizationFeatureConfig.fromJson;

  @override
  List<LocalizationsDelegate> get localizationsDelegates => [
        // The localization delegates for this feature includes
        // its own delegates as well as the delegates from the
        // identity service the feature depends on.
        OrganizationLocalizations.delegate,
        service.OrganizationLocalizations.delegate,
      ];

  @override
  List<app.FeatureAnchor> anchors(
    BuildContext? context,
  ) {
    final orgId = context == null //
        ? null
        : identity.UserIdentityFeature.getDefaultOrg();

    return [
      const nav.BootstrapTarget(
        hookName: nav.RootNavLayout.postLoginHookName,
        order: 0,
        builder: OrganizationProfile.onBootstrap,
      ),
      identity.ProfileMenuOptionGroup(
        order: 2,
        options: [
          identity.ProfileMenuOption(
            title: 'Organization',
            subtitle: config.optionSubtitle,
            icon: Icons.group,
            onSelected: (context) => OrganizationProfile.asModal(
              context,
              dismissOnTap: true,
            ),
          ),
        ],
      ),
      if (config.showDefaultOrgInStatus && orgId != null)
        OrgSelectorStatusTool(
          hookName: nav.AppStatusToolBar.featureHookName,
          order: 0,
          orgId: orgId,
          context: context!,
          canChangeDefaultOrg: config.canChangeDefaultOrg,
          divider: nav.AppStatusToolDivider.auto,
        ),
      if (orgId != null)
        for (final quota in config.quotasToShowInStatus)
          QuotaStatusTool(
            hookName: nav.AppStatusToolBar.featureHookName,
            order: 1,
            orgId: orgId,
            context: context!,
            quotaName: quota,
            divider: nav.AppStatusToolDivider.auto,
            alignment: nav.AppStatusToolAlignment.right,
          ),
    ];
  }

  @override
  Future<void> initialize(
    app.FeatureConfig? config,
    Map<String, app.Feature> registeredFeatures,
  ) async {
    assert(
      config != null,
      'A configuration for the OrganizationFeature was not provided',
    );

    this.config = config as OrganizationFeatureConfig;

    final billingConfig = config.billing;
    if (billingConfig != null) {
      service.Organization.billingKey = //
          billingConfig.billingKey;
      service.Organization.subscriptionKey = //
          billingConfig.subscriptionKey;
      service.Organization.subscriptionPlanKey = //
          billingConfig.subscriptionPlanKey;
      service.Organization.subscriptionStatusKey = //
          billingConfig.subscriptionStatusKey;
      service.Organization.subscriptionPeriodEndKey = //
          billingConfig.subscriptionPeriodEndKey;
    }
  }

  // Organization Feature creation and registration

  static Future<void> register({
    LocalizationLookup? localizationLookup,
  }) async {
    final featureRegistry = app.FeatureRegistry.instance();
    await featureRegistry.registerFeature(
      OrganizationFeature._(
        localizationLookup: localizationLookup,
      ),
    );
  }

  factory OrganizationFeature.instance() {
    final feature = app.FeatureRegistry.instance().getFeature(featureName);
    assert(
      feature != null,
      'OrganizationFeature is not registered with the feature registry',
    );
    return feature as OrganizationFeature;
  }

  OrganizationFeature._({
    LocalizationLookup? localizationLookup,
  }) : _localizationLookup = localizationLookup {
    // Register the identity service provider
    services.add<service.OrganizationService>((context) {
      final orgProvider = GetIt.instance.get<service.OrgProvider>();
      return service.OrganizationService(orgProvider);
    });
  }

  String getLocalizedValue(BuildContext context, String key) {
    String? value = _localizationLookup?.call(
      context,
      key,
    );
    if (value == null) {
      final l10n = context.l10n;
      switch (key) {
        case 'unverifiedTitle':
          return l10n.unverifiedTitle;
        case 'defaultOrganizationSet':
          return l10n.defaultOrganizationSet;
        case 'noActiveSubscription':
          return l10n.noActiveSubscription;
        case 'setupTitle':
          return l10n.setupTitle;
        case 'selectTitle':
          return l10n.selectTitle;
        case 'createTitle':
          return l10n.createTitle;
        case 'updateTitle':
          return l10n.updateTitle;
        case 'orgTypeText':
          return l10n.orgTypeText;
        case 'notMemberOfOrganizationMessage':
          return l10n.notMemberOfOrganizationMessage;
        case 'acknowledgmentLabelText':
          return l10n.acknowledgmentLabelText;
        case 'nameInputLabelName':
          return l10n.nameInputLabelName;
        case 'refInputLabelName':
          return l10n.refInputLabelName;
        case 'addrTypeMain':
          return l10n.addrTypeMain;
        case 'addrTypeBranch':
          return l10n.addrTypeBranch;
        case 'addrTypeBilling':
          return l10n.addrTypeBilling;
        case 'addrTypeBillingTooltip':
          return l10n.addrTypeBillingTooltip;
        case "contactTypeEmail":
          return l10n.contactTypeEmail;
        case "contactTypeTollFree":
          return l10n.contactTypeTollFree;
        case "contactTypeVoiceAndSMS":
          return l10n.contactTypeVoiceAndSMS;
        case "contactTypeVoiceOnly":
          return l10n.contactTypeVoiceOnly;
        case "contactTypeSMSOnly":
          return l10n.contactTypeSMSOnly;
        case "contactTypeFax":
          return l10n.contactTypeFax;
        case "orgBillingPlanSubtitleBootstrap":
          return l10n.orgBillingPlanSubtitleBootstrap;
        case "orgBillingPlanSubtitle":
          return l10n.orgBillingPlanSubtitle;
        default:
          throw StateError(
            'No localized value found for overridable localized key: $key',
          );
      }
    } else {
      return value;
    }
  }

  // Organization Feature API methods

  static void refreshOrganizations() {
    OrganizationFeature.instance()
        .services
        .get<service.OrganizationService>()
        .loadOrgs();
  }

  static service.Organization? getDefaultOrganization() {
    final orgId = identity.UserIdentityFeature.getDefaultOrg();
    if (orgId != null) {
      return OrganizationFeature.instance()
          .services
          .get<service.OrganizationService>()
          .getOrg(orgId);
    } else {
      throw Exception(
        'No default organization set for current user',
      );
    }
  }

  static Map<String, dynamic> getDefaultOrganizationConfig({
    required String key,
  }) {
    final orgId = identity.UserIdentityFeature.getDefaultOrg();
    if (orgId != null) {
      return OrganizationFeature.instance()
          .services
          .get<service.OrganizationService>()
          .getOrgConfig(
            orgId,
            key: key,
          );
    } else {
      throw Exception(
        'No default organization set for current user',
      );
    }
  }

  static Future<void> updateDefaultOrganizationConfig({
    required String key,
    required Map<String, dynamic> config,
  }) async {
    final orgId = identity.UserIdentityFeature.getDefaultOrg();
    if (orgId != null) {
      return OrganizationFeature.instance()
          .services
          .get<service.OrganizationService>()
          .updateOrgConfig(
            orgId,
            key: key,
            config: config,
          );
    } else {
      throw Exception(
        'No default organization set for current user',
      );
    }
  }

  static String? getOrgUser(String userId) {
    final feature = OrganizationFeature.instance();
    final orgService = feature.services.get<service.OrganizationService>();
    return orgService.state.getOrgUserName(userId);
  }

  Future<String?> getPlatformVersion() {
    return OrganizationFeaturePlatform.instance.getPlatformVersion();
  }
}

/// A function that returns a localized string for a given key.
typedef LocalizationLookup = String? Function(BuildContext context, String key);
