import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'organization_feature_platform_interface.dart';

/// An implementation of [OrganizationFeaturePlatform] that uses method channels.
class MethodChannelOrganizationFeature extends OrganizationFeaturePlatform {
  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('organization_feature');

  @override
  Future<String?> getPlatformVersion() async {
    final version = await methodChannel.invokeMethod<String>('getPlatformVersion');
    return version;
  }
}
