import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'organization_localizations_en.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of OrganizationLocalizations
/// returned by `OrganizationLocalizations.of(context)`.
///
/// Applications need to include `OrganizationLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/organization_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: OrganizationLocalizations.localizationsDelegates,
///   supportedLocales: OrganizationLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the OrganizationLocalizations.supportedLocales
/// property.
abstract class OrganizationLocalizations {
  OrganizationLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static OrganizationLocalizations of(BuildContext context) {
    return Localizations.of<OrganizationLocalizations>(
      context,
      OrganizationLocalizations,
    )!;
  }

  static const LocalizationsDelegate<OrganizationLocalizations> delegate =
      _OrganizationLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[Locale('en')];

  /// No description provided for @unverifiedTitle.
  ///
  /// In en, this message translates to:
  /// **'Organisation Unverified'**
  String get unverifiedTitle;

  /// No description provided for @defaultOrganizationSet.
  ///
  /// In en, this message translates to:
  /// **'Default Organisation Updated'**
  String get defaultOrganizationSet;

  /// No description provided for @noActiveSubscription.
  ///
  /// In en, this message translates to:
  /// **'No Active Subscription'**
  String get noActiveSubscription;

  /// No description provided for @setupTitle.
  ///
  /// In en, this message translates to:
  /// **'Organisation Setup'**
  String get setupTitle;

  /// No description provided for @selectTitle.
  ///
  /// In en, this message translates to:
  /// **'Select Organisation'**
  String get selectTitle;

  /// No description provided for @createTitle.
  ///
  /// In en, this message translates to:
  /// **'Create Organisation'**
  String get createTitle;

  /// No description provided for @updateTitle.
  ///
  /// In en, this message translates to:
  /// **'Update Organisation'**
  String get updateTitle;

  /// No description provided for @namesSectionTitle.
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get namesSectionTitle;

  /// No description provided for @addressesSectionSubTitle.
  ///
  /// In en, this message translates to:
  /// **'Addresses'**
  String get addressesSectionSubTitle;

  /// No description provided for @contactsSectionSubTitle.
  ///
  /// In en, this message translates to:
  /// **'Contacts'**
  String get contactsSectionSubTitle;

  /// No description provided for @nameInputLabelName.
  ///
  /// In en, this message translates to:
  /// **'Organisation Name'**
  String get nameInputLabelName;

  /// No description provided for @refInputLabelName.
  ///
  /// In en, this message translates to:
  /// **'Business Ref'**
  String get refInputLabelName;

  /// No description provided for @notMemberOfOrganizationMessage.
  ///
  /// In en, this message translates to:
  /// **'You need to be a associated with a billing organisation to continue. When you create an organisation you will be considered its owner and primary administrator. As an administrator you can invite users to be part of that organisation. Organisation level subscriptions are shared across all users associated with the organisation.'**
  String get notMemberOfOrganizationMessage;

  /// No description provided for @needsAcknowledgmentMessage.
  ///
  /// In en, this message translates to:
  /// **'To create a new organisation please acknowledge that you are authorized to administer this application for that {orgTypeText}organisation, and click the \"Create\" button.'**
  String needsAcknowledgmentMessage(String orgTypeText);

  /// No description provided for @acknowledgmentLabelText.
  ///
  /// In en, this message translates to:
  /// **'I acknowledge I am an authorized administrator for the business for which I wish to create an organisation.'**
  String get acknowledgmentLabelText;

  /// No description provided for @semAcknowledgeAmin.
  ///
  /// In en, this message translates to:
  /// **'Click here to check or uncheck acknowledgement of admin authoriation'**
  String get semAcknowledgeAmin;

  /// No description provided for @creationPending.
  ///
  /// In en, this message translates to:
  /// **'Your request to create the {orgTypeText}organisation named \"**{name}**\" is under review. Please give us 48 hours to perform the verification. We will reach out to you via email if there are any issues with the verification.'**
  String creationPending(String orgTypeText, String name);

  /// No description provided for @awaitingVerification.
  ///
  /// In en, this message translates to:
  /// **'We are unable to log you in as the {orgTypeText}organisation named \"**{name}**\" which you are associated with, is under review. If you just created the organisation then we will reach out to you via email if there are any issues with the verification. Please give us 48 hours to complete the review. Once complete you can log back in and access all the features available to your organisation.'**
  String awaitingVerification(String orgTypeText, String name);

  /// No description provided for @selectOrganizationText.
  ///
  /// In en, this message translates to:
  /// **'Select the {orgTypeText}organisation you own or administer to update, or select **<CREATE>** to create a new organisation.'**
  String selectOrganizationText(String orgTypeText);

  /// No description provided for @selectOrganizationLabel.
  ///
  /// In en, this message translates to:
  /// **'Organisation'**
  String get selectOrganizationLabel;

  /// No description provided for @ttRefreshOrganizations.
  ///
  /// In en, this message translates to:
  /// **'Refresh list of organisations'**
  String get ttRefreshOrganizations;

  /// No description provided for @semSelectOrganizations.
  ///
  /// In en, this message translates to:
  /// **'Pick organisations from dropdown list'**
  String get semSelectOrganizations;

  /// No description provided for @defaultOrganizationSetNotification.
  ///
  /// In en, this message translates to:
  /// **'Your default organisation has been updated. For the changes to take effect, you will be logged out and will have to log back in.'**
  String get defaultOrganizationSetNotification;

  /// No description provided for @noActiveSubcriptionForOrganization.
  ///
  /// In en, this message translates to:
  /// **'Your current organisation does not have an active subscription. To continue using the application within the context of an organisation it needs to have an active subscription.'**
  String get noActiveSubcriptionForOrganization;

  /// No description provided for @changeOrganizationConfirmation.
  ///
  /// In en, this message translates to:
  /// **'Changing the default organisation will log you out\n\nand will be applied only when you log back in.'**
  String get changeOrganizationConfirmation;

  /// No description provided for @orgTypeText.
  ///
  /// In en, this message translates to:
  /// **''**
  String get orgTypeText;

  /// No description provided for @addrTypeMain.
  ///
  /// In en, this message translates to:
  /// **'Main'**
  String get addrTypeMain;

  /// No description provided for @addrTypeBranch.
  ///
  /// In en, this message translates to:
  /// **'Branch'**
  String get addrTypeBranch;

  /// No description provided for @addrTypeBilling.
  ///
  /// In en, this message translates to:
  /// **'Billing'**
  String get addrTypeBilling;

  /// No description provided for @addrTypeBillingTooltip.
  ///
  /// In en, this message translates to:
  /// **'Defaults to main address if a billing address is not provided'**
  String get addrTypeBillingTooltip;

  /// No description provided for @contactTypeEmail.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get contactTypeEmail;

  /// No description provided for @contactTypeTollFree.
  ///
  /// In en, this message translates to:
  /// **'Toll Free'**
  String get contactTypeTollFree;

  /// No description provided for @contactTypeVoiceAndSMS.
  ///
  /// In en, this message translates to:
  /// **'Voice & SMS'**
  String get contactTypeVoiceAndSMS;

  /// No description provided for @contactTypeVoiceOnly.
  ///
  /// In en, this message translates to:
  /// **'Voice Only'**
  String get contactTypeVoiceOnly;

  /// No description provided for @contactTypeSMSOnly.
  ///
  /// In en, this message translates to:
  /// **'SMS Only'**
  String get contactTypeSMSOnly;

  /// No description provided for @contactTypeFax.
  ///
  /// In en, this message translates to:
  /// **'Fax'**
  String get contactTypeFax;

  /// No description provided for @updateMembersTab.
  ///
  /// In en, this message translates to:
  /// **'Members'**
  String get updateMembersTab;

  /// No description provided for @updateAddressesTab.
  ///
  /// In en, this message translates to:
  /// **'Addresses'**
  String get updateAddressesTab;

  /// No description provided for @updateContactsTab.
  ///
  /// In en, this message translates to:
  /// **'Contacts'**
  String get updateContactsTab;

  /// No description provided for @memberEmailNew.
  ///
  /// In en, this message translates to:
  /// **'New Member Email'**
  String get memberEmailNew;

  /// No description provided for @memberEmailNewTooltip.
  ///
  /// In en, this message translates to:
  /// **'Invitation to new member will be sent on update'**
  String get memberEmailNewTooltip;

  /// No description provided for @memberEmailResend.
  ///
  /// In en, this message translates to:
  /// **'Resend Invitation'**
  String get memberEmailResend;

  /// No description provided for @memberEmailResendTooltip.
  ///
  /// In en, this message translates to:
  /// **'Invitation will be resent on update'**
  String get memberEmailResendTooltip;

  /// No description provided for @memberEmailSent.
  ///
  /// In en, this message translates to:
  /// **'Invitation Sent'**
  String get memberEmailSent;

  /// No description provided for @memberEmailSentTooltip.
  ///
  /// In en, this message translates to:
  /// **'Waiting account creation'**
  String get memberEmailSentTooltip;

  /// No description provided for @memberActive.
  ///
  /// In en, this message translates to:
  /// **'Active'**
  String get memberActive;

  /// No description provided for @memberInactive.
  ///
  /// In en, this message translates to:
  /// **'Inactive'**
  String get memberInactive;

  /// No description provided for @memberOwner.
  ///
  /// In en, this message translates to:
  /// **'Primary Admin'**
  String get memberOwner;

  /// No description provided for @semAddMember.
  ///
  /// In en, this message translates to:
  /// **'Add organisation member'**
  String get semAddMember;

  /// No description provided for @semDeleteMember.
  ///
  /// In en, this message translates to:
  /// **'Delete organisation member'**
  String get semDeleteMember;

  /// No description provided for @semAddAddress.
  ///
  /// In en, this message translates to:
  /// **'Add organisation address'**
  String get semAddAddress;

  /// No description provided for @semDeleteAddress.
  ///
  /// In en, this message translates to:
  /// **'Delete organisation address'**
  String get semDeleteAddress;

  /// No description provided for @semAddContact.
  ///
  /// In en, this message translates to:
  /// **'Add organisation contact'**
  String get semAddContact;

  /// No description provided for @semDeleteContact.
  ///
  /// In en, this message translates to:
  /// **'Delete organisation contact'**
  String get semDeleteContact;

  /// No description provided for @subscriptionsButton.
  ///
  /// In en, this message translates to:
  /// **'Subscriptions'**
  String get subscriptionsButton;

  /// No description provided for @cancelButton.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancelButton;

  /// No description provided for @backButton.
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get backButton;

  /// No description provided for @dismissButton.
  ///
  /// In en, this message translates to:
  /// **'Dismiss'**
  String get dismissButton;

  /// No description provided for @createButton.
  ///
  /// In en, this message translates to:
  /// **'Create'**
  String get createButton;

  /// No description provided for @updateButton.
  ///
  /// In en, this message translates to:
  /// **'Update'**
  String get updateButton;

  /// No description provided for @nextButton.
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get nextButton;

  /// No description provided for @okButton.
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get okButton;

  /// No description provided for @orgBillingPlanSubtitleBootstrap.
  ///
  /// In en, this message translates to:
  /// **'Your current organisation does not have an active subscription. To continue using the application within the context of your organisation you need to subscribe to one of the following plans.'**
  String get orgBillingPlanSubtitleBootstrap;

  /// No description provided for @orgBillingPlanSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Subscription plans available for your organization.'**
  String get orgBillingPlanSubtitle;

  /// No description provided for @semStatusOrgProfile.
  ///
  /// In en, this message translates to:
  /// **'Click to open organisation profile dialog'**
  String get semStatusOrgProfile;

  /// No description provided for @semStatusChangeOrg.
  ///
  /// In en, this message translates to:
  /// **'Click here open organisation selector menu'**
  String get semStatusChangeOrg;
}

class _OrganizationLocalizationsDelegate
    extends LocalizationsDelegate<OrganizationLocalizations> {
  const _OrganizationLocalizationsDelegate();

  @override
  Future<OrganizationLocalizations> load(Locale locale) {
    return SynchronousFuture<OrganizationLocalizations>(
      lookupOrganizationLocalizations(locale),
    );
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en'].contains(locale.languageCode);

  @override
  bool shouldReload(_OrganizationLocalizationsDelegate old) => false;
}

OrganizationLocalizations lookupOrganizationLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return OrganizationLocalizationsEn();
  }

  throw FlutterError(
    'OrganizationLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
