// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'organization_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class OrganizationLocalizationsEn extends OrganizationLocalizations {
  OrganizationLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get unverifiedTitle => 'Organisation Unverified';

  @override
  String get defaultOrganizationSet => 'Default Organisation Updated';

  @override
  String get noActiveSubscription => 'No Active Subscription';

  @override
  String get setupTitle => 'Organisation Setup';

  @override
  String get selectTitle => 'Select Organisation';

  @override
  String get createTitle => 'Create Organisation';

  @override
  String get updateTitle => 'Update Organisation';

  @override
  String get namesSectionTitle => 'Name';

  @override
  String get addressesSectionSubTitle => 'Addresses';

  @override
  String get contactsSectionSubTitle => 'Contacts';

  @override
  String get nameInputLabelName => 'Organisation Name';

  @override
  String get refInputLabelName => 'Business Ref';

  @override
  String get notMemberOfOrganizationMessage =>
      'You need to be a associated with a billing organisation to continue. When you create an organisation you will be considered its owner and primary administrator. As an administrator you can invite users to be part of that organisation. Organisation level subscriptions are shared across all users associated with the organisation.';

  @override
  String needsAcknowledgmentMessage(String orgTypeText) {
    return 'To create a new organisation please acknowledge that you are authorized to administer this application for that ${orgTypeText}organisation, and click the \"Create\" button.';
  }

  @override
  String get acknowledgmentLabelText =>
      'I acknowledge I am an authorized administrator for the business for which I wish to create an organisation.';

  @override
  String get semAcknowledgeAmin =>
      'Click here to check or uncheck acknowledgement of admin authoriation';

  @override
  String creationPending(String orgTypeText, String name) {
    return 'Your request to create the ${orgTypeText}organisation named \"**$name**\" is under review. Please give us 48 hours to perform the verification. We will reach out to you via email if there are any issues with the verification.';
  }

  @override
  String awaitingVerification(String orgTypeText, String name) {
    return 'We are unable to log you in as the ${orgTypeText}organisation named \"**$name**\" which you are associated with, is under review. If you just created the organisation then we will reach out to you via email if there are any issues with the verification. Please give us 48 hours to complete the review. Once complete you can log back in and access all the features available to your organisation.';
  }

  @override
  String selectOrganizationText(String orgTypeText) {
    return 'Select the ${orgTypeText}organisation you own or administer to update, or select **<CREATE>** to create a new organisation.';
  }

  @override
  String get selectOrganizationLabel => 'Organisation';

  @override
  String get ttRefreshOrganizations => 'Refresh list of organisations';

  @override
  String get semSelectOrganizations => 'Pick organisations from dropdown list';

  @override
  String get defaultOrganizationSetNotification =>
      'Your default organisation has been updated. For the changes to take effect, you will be logged out and will have to log back in.';

  @override
  String get noActiveSubcriptionForOrganization =>
      'Your current organisation does not have an active subscription. To continue using the application within the context of an organisation it needs to have an active subscription.';

  @override
  String get changeOrganizationConfirmation =>
      'Changing the default organisation will log you out\n\nand will be applied only when you log back in.';

  @override
  String get orgTypeText => '';

  @override
  String get addrTypeMain => 'Main';

  @override
  String get addrTypeBranch => 'Branch';

  @override
  String get addrTypeBilling => 'Billing';

  @override
  String get addrTypeBillingTooltip =>
      'Defaults to main address if a billing address is not provided';

  @override
  String get contactTypeEmail => 'Email';

  @override
  String get contactTypeTollFree => 'Toll Free';

  @override
  String get contactTypeVoiceAndSMS => 'Voice & SMS';

  @override
  String get contactTypeVoiceOnly => 'Voice Only';

  @override
  String get contactTypeSMSOnly => 'SMS Only';

  @override
  String get contactTypeFax => 'Fax';

  @override
  String get updateMembersTab => 'Members';

  @override
  String get updateAddressesTab => 'Addresses';

  @override
  String get updateContactsTab => 'Contacts';

  @override
  String get memberEmailNew => 'New Member Email';

  @override
  String get memberEmailNewTooltip =>
      'Invitation to new member will be sent on update';

  @override
  String get memberEmailResend => 'Resend Invitation';

  @override
  String get memberEmailResendTooltip => 'Invitation will be resent on update';

  @override
  String get memberEmailSent => 'Invitation Sent';

  @override
  String get memberEmailSentTooltip => 'Waiting account creation';

  @override
  String get memberActive => 'Active';

  @override
  String get memberInactive => 'Inactive';

  @override
  String get memberOwner => 'Primary Admin';

  @override
  String get semAddMember => 'Add organisation member';

  @override
  String get semDeleteMember => 'Delete organisation member';

  @override
  String get semAddAddress => 'Add organisation address';

  @override
  String get semDeleteAddress => 'Delete organisation address';

  @override
  String get semAddContact => 'Add organisation contact';

  @override
  String get semDeleteContact => 'Delete organisation contact';

  @override
  String get subscriptionsButton => 'Subscriptions';

  @override
  String get cancelButton => 'Cancel';

  @override
  String get backButton => 'Back';

  @override
  String get dismissButton => 'Dismiss';

  @override
  String get createButton => 'Create';

  @override
  String get updateButton => 'Update';

  @override
  String get nextButton => 'Next';

  @override
  String get okButton => 'OK';

  @override
  String get orgBillingPlanSubtitleBootstrap =>
      'Your current organisation does not have an active subscription. To continue using the application within the context of your organisation you need to subscribe to one of the following plans.';

  @override
  String get orgBillingPlanSubtitle =>
      'Subscription plans available for your organization.';

  @override
  String get semStatusOrgProfile => 'Click to open organisation profile dialog';

  @override
  String get semStatusChangeOrg => 'Click here open organisation selector menu';
}
