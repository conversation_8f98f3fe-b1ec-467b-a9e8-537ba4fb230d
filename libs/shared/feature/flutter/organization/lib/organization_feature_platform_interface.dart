import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'organization_feature_method_channel.dart';

abstract class OrganizationFeaturePlatform extends PlatformInterface {
  /// Constructs a OrganizationFeaturePlatform.
  OrganizationFeaturePlatform() : super(token: _token);

  static final Object _token = Object();

  static OrganizationFeaturePlatform _instance = MethodChannelOrganizationFeature();

  /// The default instance of [OrganizationFeaturePlatform] to use.
  ///
  /// Defaults to [MethodChannelOrganizationFeature].
  static OrganizationFeaturePlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [OrganizationFeaturePlatform] when
  /// they register themselves.
  static set instance(OrganizationFeaturePlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  Future<String?> getPlatformVersion() {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }
}
