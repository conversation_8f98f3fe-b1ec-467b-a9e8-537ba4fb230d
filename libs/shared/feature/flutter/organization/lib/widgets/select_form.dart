import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

import 'package:ui_widgets_component/ui_widgets.dart';
import 'package:organization_service/organization_service.dart';

import '../organization_feature.dart';
import '../../l10n/l10n.dart';

class SelectForm extends StatefulWidget {
  static const double formViewWidth = 400.0;
  static const double formViewHeight = 273.0;

  final VoidCallback _createOrganization;
  final Function(Organization) _updateOrganization;

  final VoidCallback _dismissDialog;
  final bool _dismissOnTap;

  const SelectForm({
    super.key,
    required VoidCallback createOrganization,
    required Function(Organization) updateOrganization,
    required VoidCallback dismissDialog,
    bool dismissOnTap = true,
  })  : _createOrganization = createOrganization,
        _updateOrganization = updateOrganization,
        _dismissDialog = dismissDialog,
        _dismissOnTap = dismissOnTap;

  @override
  State<SelectForm> createState() => _SelectFormState();
}

class _SelectFormState extends State<SelectForm> {
  late final OrganizationFeature _feature;

  // declare a GlobalKey
  final formKey = GlobalKey<FormState>();

  bool isOrgsLoading = false;
  bool isSelectedOrgLoading = false;

  String selectedOption = '';

  List<DropdownListItem> options = [];

  @override
  void initState() {
    super.initState();

    _feature = OrganizationFeature.instance();

    final organizationService = context.read<OrganizationService>();
    final organizationState = organizationService.state;
    isOrgsLoading = organizationService.state.isLoading(
      [getUserOrgsLoading],
    );
    options = [
      const DropdownListItem(id: '<CREATE>'),
      ...organizationState.organizations.map(
        (org) => DropdownListItem(
          id: org.name,
        ),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;

    return BlocListener<OrganizationService, OrganizationState>(
      listener: (context, organizationState) {
        if (!organizationState.isLoading(
          [getUserOrgsLoading],
        )) {
          setState(() {
            isOrgsLoading = false;
            options = [
              const DropdownListItem(id: '<CREATE>'),
              ...organizationState.organizations.map(
                (org) => DropdownListItem(
                  id: org.name,
                ),
              ),
            ];
          });
        }

        if (!organizationState.isLoading(
          [readOrgDataLoading],
        )) {
          setState(() {
            isSelectedOrgLoading = false;
          });
        }
      },
      child: DialogForm(
        formKey: formKey,
        title: _feature.getLocalizedValue(context, 'selectTitle'),
        formViewHeight: SelectForm.formViewHeight,
        body: Column(
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(8.0, 8.0, 0.0, 16.0),
              child: MarkdownBody(
                styleSheet: createMarkDownStyleSheet(
                  context,
                ),
                data: l10n.selectOrganizationText(
                  _feature.getLocalizedValue(
                    context,
                    'orgTypeText',
                  ),
                ),
              ),
            ),
            TextInput(
              labelText: l10n.selectOrganizationLabel,
              leadingIcon: InputIcon(
                icon: isOrgsLoading ? Icons.hourglass_top : Icons.refresh,
                iconTooltip: l10n.ttRefreshOrganizations,
                onIconTap: _refreshOrgs,
              ),
              fixedHeight: true,
              optionsSemanticLabel: l10n.semSelectOrganizations,
              options: options,
              validator: ListValidator(
                items: options.map((o) => o.label!).toList(),
              ),
              isRequired: true,
              enabled: !isOrgsLoading && !isSelectedOrgLoading,
              onChanged: (value, isValid) {
                if (isValid) {
                  setState(() {
                    selectedOption = value!;
                  });
                } else if (selectedOption.isNotEmpty) {
                  setState(() {
                    selectedOption = '';
                  });
                }
              },
            ),
          ],
        ),
        actions: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 8),
              child: ElevatedButton.icon(
                label: Text(l10n.cancelButton),
                icon: const Icon(Icons.cancel),
                iconAlignment: IconAlignment.end,
                onPressed: isOrgsLoading || isSelectedOrgLoading
                    ? null
                    : widget._dismissDialog,
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 8),
              child: ElevatedIconButton(
                label: Text(l10n.nextButton),
                icon: Icons.create,
                iconAlignment: IconAlignment.end,
                isLoading: isSelectedOrgLoading,
                onPressed: isOrgsLoading ||
                        isSelectedOrgLoading ||
                        selectedOption.isEmpty
                    ? null
                    : _onNext,
              ),
            ),
          ],
        ),
        onCloseDialog: widget._dismissOnTap ? null : widget._dismissDialog,
      ),
    );
  }

  void _refreshOrgs() {
    setState(() {
      isOrgsLoading = true;
    });
    context.read<OrganizationService>().loadOrgs();
  }

  void _onNext() {
    if (formKey.currentState!.validate()) {
      if (selectedOption == '<CREATE>') {
        widget._createOrganization();
      } else {
        final organizationService = context.read<OrganizationService>();
        final organizationState = organizationService.state;

        setState(() {
          isSelectedOrgLoading = true;
        });
        final organization = organizationState.organizations.firstWhere(
          (org) => org.name == selectedOption,
        );
        organizationService.readOrgData(organization).then((_) {
          widget._updateOrganization(organization);
        });
      }
    }
  }
}
