import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:ui_widgets_component/ui_widgets.dart';
import 'package:app_framework_component/app_framework.dart';
import 'package:nav_layouts_component/nav_layouts.dart' as nav;
import 'package:user_identity_feature/user_identity.dart';
import 'package:organization_service/organization_service.dart';
import 'package:billing_feature/billing.dart';

import '../organization_feature.dart';
import '../l10n/l10n.dart';

import 'bootstrap_notification.dart';
import 'needs_acknowledgement.dart';
import 'creation_pending.dart';
import 'select_form.dart';
import 'create_form.dart';
import 'update_form.dart';

class OrganizationProfile extends MultiFormController {
  /// This callback is used to build the dialog container
  /// and it allows you to provide different constrainted
  /// containers for the form and verification views.
  @override
  BuildContainerCallack? get buildContainer => _buildContainer;
  final BuildContainerCallack? _buildContainer;

  final VoidCallback _dismissDialog;
  final bool _dismissOnTap;

  final bool _createOrgOnly;

  const OrganizationProfile({
    super.key,
    BuildContainerCallack? buildContainer,
    required VoidCallback dismissDialog,
    bool dismissOnTap = true,
    bool createOrgOnly = false,
  })  : _buildContainer = buildContainer,
        _dismissDialog = dismissDialog,
        _dismissOnTap = dismissOnTap,
        _createOrgOnly = createOrgOnly;

  static Widget onBootstrap(
    BuildContext context,
    Function(bool) complete,
  ) {
    final organizationsService = context.read<OrganizationService>();
    final feature = OrganizationFeature.instance();

    return Consumer<nav.RootState>(
      builder: (context, state, _) {
        return FutureBuilder(
          future: organizationsService.loadOrgs(),
          builder: (context, snapshot) {
            final l10n = context.l10n;

            if (snapshot.connectionState == ConnectionState.done) {
              final organizations = snapshot.data;
              if (organizations == null) {
                // if no organization list is returned then
                // an error occurred and the user should be
                // be logged out and an error message
                // displayed
                state.setBusy();
                UserIdentityFeature.logout();
              } else {
                // Determine the default organization
                // from the list of organizations
                final defaultOrg = UserIdentityFeature.getDefaultOrg();
                Organization? organization;
                for (final org in organizations) {
                  if (org.orgId == defaultOrg) {
                    organization = org;
                    break;
                  }
                }

                if (feature.config.membershipRequired) {
                  // if membership is required then ensure
                  // the user is associated with an organization
                  if (organizations.isEmpty) {
                    // if the user is not associated with any
                    // organizations then show the create
                    // organization dialog.
                    return OrganizationProfile(
                      buildContainer: (child, width, height) {
                        return SizedBox(
                          width: width + 40, // margins (4*2) + padding (16*2)
                          height: height + 40, // margins (4*2) + padding (16*2)
                          child: Card(
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: child,
                            ),
                          ),
                        );
                      },
                      dismissDialog: () {
                        state.setBusy();
                        UserIdentityFeature.logout();
                        Future.microtask(() => complete(false));
                      },
                      createOrgOnly: true,
                    );
                  } else if (organization == null) {
                    // if membership is required then the user must
                    // be associated with at least one organization
                    // and a default must be set.

                    // pick the first verified organization or the
                    // first organization if none are verified
                    organization = organizations.firstWhere(
                      (org) => org.isVerified,
                      orElse: () => organizations.first,
                    );
                    return BootstrapNotification.buildNotification(
                      title: feature.getLocalizedValue(
                        context,
                        'defaultOrganizationSet',
                      ),
                      body: l10n.defaultOrganizationSetNotification,
                      dialogSize: BootstrapNotification.dialogSize(480, 188),
                      logout: () {
                        UserIdentityFeature.setDefaultOrg(
                          organization!.orgId,
                          logout: true,
                        );
                        Future.microtask(() => complete(false));
                      },
                    );
                  }
                }

                if (organization != null) {
                  dismissDialog() {
                    if (feature.config.canChangeDefaultOrg &&
                        (organizations.length > 1 ||
                            !feature.config.membershipRequired)) {
                      // if there are multiple organizations then
                      // set the default organization to an empty
                      // string and log out the user so they can
                      // select a new organization
                      state.setBusy();
                      UserIdentityFeature.setDefaultOrg(
                        '',
                        logout: true,
                      );
                    } else {
                      // if there is only one organization then
                      // log out the user without changing the
                      // default organization
                      UserIdentityFeature.logout();
                    }
                    Future.microtask(() => complete(false));
                  }

                  if (!organization.isVerified) {
                    // if the organization is not verified then show the
                    // unverified organization alert dialog and allow the
                    // user to logout
                    return BootstrapNotification.buildNotification(
                      title: feature.getLocalizedValue(
                        context,
                        'unverifiedTitle',
                      ),
                      body: l10n.awaitingVerification(
                        feature.getLocalizedValue(
                          context,
                          'orgTypeText',
                        ),
                        organization.name,
                      ),
                      dialogSize: BootstrapNotification.dialogSize(480, 272),
                      logout: dismissDialog,
                    );
                  }

                  // if the feature has a subscription active status
                  // pattern configured then check if the organization
                  // in context has a valid subscription
                  final activeStatusPattern =
                      feature.config.billing?.activeStatusPattern;
                  if (activeStatusPattern != null) {
                    final status = organization.subscriptionStatus?.name;
                    if (status == null ||
                        !RegExp(
                          activeStatusPattern,
                        ).hasMatch(
                          status,
                        )) {
                      final billingFeature = FeatureRegistry.instance()
                          .getFeature<BillingFeature>(
                              BillingFeature.featureName);

                      if (billingFeature == null || !organization.isOwner) {
                        // if the organization has no active subscription
                        // then show the inactive subscription alert
                        // dialog and log out the user
                        return BootstrapNotification.buildNotification(
                          title: feature.getLocalizedValue(
                            context,
                            'noActiveSubscription',
                          ),
                          body: l10n.noActiveSubcriptionForOrganization,
                          dialogSize:
                              BootstrapNotification.dialogSize(480, 209),
                          logout: dismissDialog,
                        );
                      } else {
                        final product =
                            feature.config.billing?.productName != null
                                ? billingFeature.getBillingProduct(
                                    feature.config.billing!.productName,
                                  )
                                : null;
                        assert(
                          product != null,
                          'Product not found for product name: '
                          '${feature.config.billing!.productName}',
                        );

                        return billingFeature.planSelectionDialogCard(
                          context,
                          product!,
                          subtitle: feature.getLocalizedValue(
                            context,
                            'orgBillingPlanSubtitleBootstrap',
                          ),
                          orgId: organization.orgId,
                          currentPlanId: organization.subscriptionPlan,
                          currentSubscriptionId: organization.subscriptionId,
                          currentPlanStatus: organization.subscriptionStatus,
                          planEndDate: organization.subscriptionPeriodEnd,
                          dismissOnPaymentTimeout: true,
                          waitForUpdates: _waitForSubscriptionUpdates(
                            context,
                            organization,
                            onUpdateDone: complete,
                          ),
                          dismissDialog: dismissDialog,
                        );
                      }
                    }

                    // load the organization details and complete
                    // the bootstrapping process once the details
                    // are loaded
                    organizationsService.readOrgData(organization).then((_) {
                      Future.microtask(() => complete(true));
                    });
                    return const SizedBox();
                  }
                }
              }
              // bootstrapping is complete and
              // the user can proceed to the app
              Future.microtask(() => complete(true));
              return const SizedBox();
            } else {
              // show loading indicator while
              // organizations user is associated
              // with are being loaded
              return const CircularProgressIndicator();
            }
          },
        );
      },
    );
  }

  static void asModal(
    BuildContext context, {
    bool dismissOnTap = true,
  }) {
    showDialog<void>(
      context: context,
      barrierDismissible: dismissOnTap,
      builder: (dialogContext) {
        return Dialog(
          child: OrganizationProfile(
            buildContainer: (child, width, height) {
              return SizedBox(
                width: width + 32, // padding (16*2)
                height: height + 32, // padding (16*2)
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: child,
                ),
              );
            },
            dismissDialog: () {
              Navigator.pop(dialogContext);
            },
            dismissOnTap: dismissOnTap,
          ),
        );
      },
    );
  }

  @override
  MultiFormControllerState<OrganizationProfile> createState() =>
      _OrganizationProfile();
}

class _OrganizationProfile
    extends MultiFormControllerState<OrganizationProfile> {
  late final OrganizationFeature feature;

  late final BillingFeature? billingFeature;
  late final OrganizationService organizationService;

  _OrganizationProfileState state =
      _OrganizationProfileState.needsAcknowledgement2;

  Organization? selectedOrganization;

  @override
  void initState() {
    super.initState();

    feature = OrganizationFeature.instance();

    billingFeature = FeatureRegistry.instance()
        .getFeature<BillingFeature>(BillingFeature.featureName);

    organizationService = context.read<OrganizationService>();

    if (widget._createOrgOnly) {
      state = _OrganizationProfileState.needsAcknowledgement2;
    } else if (feature.config.creationRestricted) {
      if (organizationService.state.organizations.isEmpty) {
        state = _OrganizationProfileState.createOrganization;
      } else {
        final organizationState = organizationService.state;
        selectedOrganization =
            organizationService.state.organizations.firstWhere(
          (org) => org.orgId == organizationState.selectedOrgData?.orgId,
          orElse: () => organizationState.organizations.first,
        );
        state = _OrganizationProfileState.updateOrganization;
      }
    } else {
      state = _OrganizationProfileState.selectOrganization;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OrganizationService, OrganizationState>(
      builder: (context, organizationState) {
        switch (state) {
          case _OrganizationProfileState.selectOrganization:
            assert(
              !widget._createOrgOnly,
              'Invalid state ($state) for OrganizationProfile '
              'widget when createOrgOnly is true',
            );

            return super.buildContainer(
              SelectForm(
                createOrganization: _needsAcknowledgement,
                updateOrganization: _updateOrganization,
                dismissDialog: widget._dismissDialog,
                dismissOnTap: widget._dismissOnTap,
              ),
              SelectForm.formViewWidth,
              SelectForm.formViewHeight,
            );

          case _OrganizationProfileState.needsAcknowledgement1:
            assert(
              !widget._createOrgOnly,
              'Invalid state ($state) for OrganizationProfile '
              'widget when createOrgOnly is true',
            );

            return super.buildContainer(
              NeedsAcknowledgementForm(
                showNotAMemberMessage: false,
                goBack: _selectOrganization,
                createOrganization: _createOrganization,
                dismissDialog: widget._dismissDialog,
                dismissOnTap: widget._dismissOnTap,
              ),
              NeedsAcknowledgementForm.formViewWidth,
              NeedsAcknowledgementForm.formViewHeight2,
            );

          case _OrganizationProfileState.needsAcknowledgement2:
            return super.buildContainer(
              NeedsAcknowledgementForm(
                createOrganization: _createOrganization,
                dismissDialog: widget._dismissDialog,
                dismissOnTap: widget._dismissOnTap,
              ),
              NeedsAcknowledgementForm.formViewWidth,
              NeedsAcknowledgementForm.formViewHeight1,
            );

          case _OrganizationProfileState.createOrganization:
            return super.buildContainer(
              CreateForm(
                creationPending: _creationPending,
                goBack: widget._createOrgOnly ? null : _selectOrganization,
                dismissDialog: widget._dismissDialog,
                dismissOnTap: widget._dismissOnTap,
                setAsDefault: organizationState.organizations.isEmpty ||
                    !feature.config.canChangeDefaultOrg,
              ),
              CreateForm.formViewWidth,
              CreateForm.formViewHeight,
            );

          case _OrganizationProfileState.creationPending:
            return super.buildContainer(
              CreationPendingForm(
                orgName: selectedOrganization!.name,
                goBack: widget._createOrgOnly ? null : _selectOrganization,
                dismissDialog: widget._dismissDialog,
                dismissOnTap: widget._dismissOnTap,
              ),
              CreationPendingForm.formViewWidth,
              CreationPendingForm.formViewHeight,
            );

          case _OrganizationProfileState.updateOrganization:
            assert(
              !widget._createOrgOnly,
              'Invalid state ($state) for OrganizationProfile '
              'widget when createOrgOnly is true',
            );
            assert(
              selectedOrganization != null,
              'Invalid state ($state) for OrganizationProfile '
              'selected organization cannot be null',
            );

            final product = feature.config.billing?.productName != null
                ? billingFeature?.getBillingProduct(
                    feature.config.billing!.productName,
                  )
                : null;

            final isOrgAdmin = selectedOrganization!.isAdmin;

            return super.buildContainer(
              UpdateForm(
                organization: selectedOrganization!,
                goBack: feature.config.creationRestricted
                    ? null
                    : _selectOrganization,
                readOnly: !isOrgAdmin,
                selectPlan: isOrgAdmin &&
                        product != null &&
                        selectedOrganization!.subscriptionId != null
                    ? _selectPlan
                    : null,
                dismissDialog: widget._dismissDialog,
                dismissOnTap: widget._dismissOnTap,
              ),
              UpdateForm.formViewWidth,
              UpdateForm.formViewHeight,
            );

          case _OrganizationProfileState.selectPlan:
            final product = billingFeature?.getBillingProduct(
              feature.config.billing!.productName,
            );
            assert(
              product != null,
              'Product not found for product name: '
              '${feature.config.billing!.productName}',
            );
            assert(
              billingFeature != null,
              'Billing feature not registered',
            );

            return super.buildContainer(
              billingFeature!.planSelectionDialog(
                context,
                product!,
                subtitle: feature.getLocalizedValue(
                  context,
                  'orgBillingPlanSubtitle',
                ),
                orgId: selectedOrganization!.orgId,
                currentPlanId: selectedOrganization!.subscriptionPlan,
                currentSubscriptionId: selectedOrganization!.subscriptionId,
                currentPlanStatus: selectedOrganization!.subscriptionStatus,
                planEndDate: selectedOrganization!.subscriptionPeriodEnd,
                waitForUpdates: _waitForSubscriptionUpdates(
                  context,
                  selectedOrganization!,
                  onUpdateDone: (completed) {
                    if (completed) {
                      setState(() {
                        // refresh selected organization
                        selectedOrganization =
                            organizationService.state.organizations.firstWhere(
                          (org) => org.orgId == selectedOrganization!.orgId,
                        );
                      });
                    }
                  },
                ),
                goBack: () {
                  _updateOrganization(selectedOrganization!);
                },
                dismissDialog: widget._dismissDialog,
                dismissOnTap: widget._dismissOnTap,
              ),
              product.formViewWidth,
              product.formViewHeight,
            );
        }
      },
    );
  }

  void _needsAcknowledgement() {
    setState(() {
      state = _OrganizationProfileState.needsAcknowledgement1;
    });
  }

  void _createOrganization() {
    setState(() {
      state = _OrganizationProfileState.createOrganization;
    });
  }

  void _selectOrganization() {
    organizationService.loadOrgs();

    setState(() {
      state = _OrganizationProfileState.selectOrganization;
    });
  }

  void _creationPending(Organization organization) {
    setState(() {
      state = _OrganizationProfileState.creationPending;
      selectedOrganization = organization;
    });
  }

  void _updateOrganization(Organization organization) {
    setState(() {
      state = organization.isVerified
          ? _OrganizationProfileState.updateOrganization
          : _OrganizationProfileState.creationPending;
      selectedOrganization = organization;
    });
  }

  void _selectPlan() {
    setState(() {
      state = _OrganizationProfileState.selectPlan;
    });
  }
}

WaitForUpdates _waitForSubscriptionUpdates(
  BuildContext context,
  Organization organization, {
  Function(bool)? onUpdateDone,
}) {
  final organizationService = context.read<OrganizationService>();
  return ({timeout, statuses, planId, canceled}) async {
    return organizationService
        .waitForSubscriptionUpdates(organization,
            timeout: timeout ?? const Duration(seconds: 30),
            statuses: statuses,
            planIdUpdatedTo: planId,
            cancelAtPeriodEndSet: canceled)
        .then((completed) {
      if (onUpdateDone != null) {
        onUpdateDone(completed);
      }
      return completed;
    });
  };
}

enum _OrganizationProfileState {
  needsAcknowledgement2,
  needsAcknowledgement1,
  selectOrganization,
  createOrganization,
  creationPending,
  updateOrganization,
  selectPlan,
}
