import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

import 'package:ui_widgets_component/ui_widgets.dart';

import '../../l10n/l10n.dart';

class BootstrapNotification extends StatelessWidget {
  static Size dialogSize(double width, double height) => Size(width, height);

  final String _title;
  final String _body;
  final Size _dialogSize;

  final VoidCallback _logout;

  static Widget buildNotification({
    Key? key,
    required String title,
    required String body,
    required Size dialogSize,
    required VoidCallback logout,
  }) {
    return SizedBox(
      width: dialogSize.width + 40, // card margins (4*2) + padding (16*2)
      height: dialogSize.height + 40, // card margins (4*2) + padding (16*2)
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: BootstrapNotification._(
            key: key,
            title: title,
            body: body,
            dialogSize: dialogSize,
            logout: logout,
          ),
        ),
      ),
    );
  }

  const BootstrapNotification._({
    super.key,
    required String title,
    required String body,
    required Size dialogSize,
    required VoidCallback logout,
  })  : _title = title,
        _body = body,
        _dialogSize = dialogSize,
        _logout = logout;

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;

    return DialogForm(
      title: _title,
      formViewHeight: _dialogSize.height,
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(8.0, 8.0, 0.0, 16.0),
            child: MarkdownBody(
              styleSheet: createMarkDownStyleSheet(
                context,
              ),
              data: _body,
            ),
          ),
        ],
      ),
      actions: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 8),
            child: ElevatedButton.icon(
              label: Text(l10n.dismissButton),
              icon: const Icon(Icons.cancel),
              iconAlignment: IconAlignment.end,
              onPressed: _logout,
            ),
          ),
        ],
      ),
    );
  }
}
