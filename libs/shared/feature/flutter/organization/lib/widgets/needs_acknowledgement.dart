import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

import 'package:ui_widgets_component/ui_widgets.dart';

import '../organization_feature.dart';
import '../../l10n/l10n.dart';

class NeedsAcknowledgementForm extends StatefulWidget {
  static const double formViewWidth = 530.0;
  static const double formViewHeight1 = 354.0;
  static const double formViewHeight2 = 244.0;

  final bool _showNotAMemberMessage;

  final VoidCallback _createOrganization;

  final VoidCallback? _goBack;

  final VoidCallback _dismissDialog;
  final bool _dismissOnTap;

  const NeedsAcknowledgementForm({
    super.key,
    bool showNotAMemberMessage = true,
    VoidCallback? goBack,
    required VoidCallback createOrganization,
    required VoidCallback dismissDialog,
    bool dismissOnTap = true,
  })  : _showNotAMemberMessage = showNotAMemberMessage,
        _goBack = goBack,
        _createOrganization = createOrganization,
        _dismissDialog = dismissDialog,
        _dismissOnTap = dismissOnTap;

  @override
  State<NeedsAcknowledgementForm> createState() =>
      _NeedsAcknowledgementFormState();
}

class _NeedsAcknowledgementFormState extends State<NeedsAcknowledgementForm> {
  late final OrganizationFeature _feature;

  // declare a GlobalKey
  final formKey = GlobalKey<FormState>();

  bool isAcknowledgementChecked = false;

  @override
  void initState() {
    super.initState();

    _feature = OrganizationFeature.instance();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = context.l10n;

    return DialogForm(
      formKey: formKey,
      title: _feature.getLocalizedValue(context, 'setupTitle'),
      formViewHeight: widget._showNotAMemberMessage
          ? NeedsAcknowledgementForm.formViewHeight1
          : NeedsAcknowledgementForm.formViewHeight2,
      body: Column(
        children: [
          if (widget._showNotAMemberMessage)
            Padding(
              padding: const EdgeInsets.fromLTRB(8.0, 8.0, 0.0, 0.0),
              child: MarkdownBody(
                styleSheet: createMarkDownStyleSheet(
                  context,
                ),
                data: _feature.getLocalizedValue(
                  context,
                  'notMemberOfOrganizationMessage',
                ),
              ),
            ),
          Padding(
            padding: const EdgeInsets.fromLTRB(8.0, 8.0, 0.0, 16.0),
            child: MarkdownBody(
              styleSheet: createMarkDownStyleSheet(
                context,
              ),
              data: l10n.needsAcknowledgmentMessage(
                _feature.getLocalizedValue(
                  context,
                  'orgTypeText',
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.fromLTRB(30.0, 8.0, 30.0, 8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Checkbox(
                  value: isAcknowledgementChecked,
                  semanticLabel: l10n.semAcknowledgeAmin,
                  onChanged: (bool? value) => setState(
                    () => isAcknowledgementChecked = value!,
                  ),
                ),
                const SizedBox(width: 4.0),
                Flexible(
                  child: Text(
                    l10n.acknowledgmentLabelText,
                    style: theme.textTheme.bodyLarge,
                    softWrap: true,
                    maxLines: 2,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 8),
            child: widget._goBack == null
                ? ElevatedButton.icon(
                    label: Text(l10n.cancelButton),
                    icon: const Icon(Icons.cancel),
                    iconAlignment: IconAlignment.end,
                    onPressed: widget._dismissDialog,
                  )
                : ElevatedButton.icon(
                    label: Text(l10n.backButton),
                    icon: const Icon(Icons.arrow_back),
                    iconAlignment: IconAlignment.end,
                    onPressed: widget._goBack,
                  ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 8),
            child: ElevatedIconButton(
              label: Text(l10n.createButton),
              icon: Icons.create,
              iconAlignment: IconAlignment.end,
              onPressed: isAcknowledgementChecked //
                  ? widget._createOrganization
                  : null,
            ),
          ),
        ],
      ),
      onCloseDialog: widget._dismissOnTap ? null : widget._dismissDialog,
    );
  }
}
