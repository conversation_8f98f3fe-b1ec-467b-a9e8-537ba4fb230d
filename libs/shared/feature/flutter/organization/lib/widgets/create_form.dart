import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logging/logging.dart' as logging;

import 'package:ui_widgets_component/ui_widgets.dart';
import 'package:user_space_model/user_space_model.dart' as model;
import 'package:user_identity_feature/user_identity.dart';
import 'package:organization_service/organization_service.dart';

import '../organization_feature.dart';
import '../../l10n/l10n.dart';

import '../model/address_type.dart';
import '../model/contact_type.dart';

class CreateForm extends StatefulWidget {
  static const double formViewWidth = 588.0;
  static const double formViewHeight = 616.0; // TODO: Make this configurable
  static const double inputWidth = formViewWidth / 2;

  final Function(Organization)? _creationPending;
  final VoidCallback? _goBack;

  final VoidCallback _dismissDialog;
  final bool _dismissOnTap;

  final bool _setAsDefault;

  const CreateForm({
    super.key,
    Function(Organization)? creationPending,
    VoidCallback? goBack,
    required VoidCallback dismissDialog,
    bool dismissOnTap = true,
    bool setAsDefault = false,
  })  : _creationPending = creationPending,
        _goBack = goBack,
        _dismissDialog = dismissDialog,
        _dismissOnTap = dismissOnTap,
        _setAsDefault = setAsDefault;

  @override
  State<CreateForm> createState() => _CreateFormState();
}

class _CreateFormState extends State<CreateForm> {
  final logging.Logger logger = logging.Logger('CreateForm');

  late final OrganizationFeature feature;

  // declare a GlobalKey
  final formKey = GlobalKey<FormState>();

  final TextEditingController orgName = TextEditingController();
  final TextEditingController businessRef = TextEditingController();

  late List<AddressValue<model.AddressType>> addressValues;
  late List<ContactValue<model.ContactType>> contactValues;

  @override
  void initState() {
    super.initState();

    feature = OrganizationFeature.instance();

    addressValues = [
      _createAddressValue(),
    ];

    contactValues = [
      _createContactValue(),
    ];
  }

  AddressValue<model.AddressType> _createAddressValue() {
    return AddressValue.create<model.AddressType>(
      type: model.AddressType.main,
      countryCode: feature.config.defaultCountryCode,
    );
  }

  ContactValue<model.ContactType> _createContactValue() {
    return ContactValue.create<model.ContactType>(
      type: model.ContactType.email,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = context.l10n;

    return BlocBuilder<OrganizationService, OrganizationState>(builder: (
      context,
      organizationState,
    ) {
      final isLoading = organizationState.isLoading(
        [
          createOrgLoading,
        ],
      );
      final inputEnabled = !isLoading;

      return DialogForm(
        formKey: formKey,
        title: feature.getLocalizedValue(context, 'createTitle'),
        formViewHeight: CreateForm.formViewHeight,
        body: Column(
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(8.0, 0.0, 0.0, 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(
                    l10n.namesSectionTitle,
                    style: theme.textTheme.titleLarge,
                  ),
                ],
              ),
            ),
            Wrap(
              children: [
                TextInput(
                  labelText: feature.getLocalizedValue(
                    context,
                    'nameInputLabelName',
                  ),
                  isRequired: true,
                  enabled: inputEnabled,
                  controller: orgName,
                  onChanged: (_, __) {
                    setState(() {});
                  },
                ),
                TextInput(
                  labelText: feature.getLocalizedValue(
                    context,
                    'refInputLabelName',
                  ),
                  enabled: inputEnabled,
                  controller: businessRef,
                ),
              ]
                  .map(
                    (input) => SizedBox(
                      width: CreateForm.inputWidth,
                      child: input,
                    ),
                  )
                  .toList(),
            ),
            const Divider(),
            Padding(
              padding: const EdgeInsets.fromLTRB(8.0, 0.0, 0.0, 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(
                    l10n.addressesSectionSubTitle,
                    style: theme.textTheme.titleLarge,
                  ),
                ],
              ),
            ),
            MultiInput<AddressValue<model.AddressType>>(
              values: addressValues,
              enabled: inputEnabled,
              groupBoxBackground: theme.dialogBackgroundColor,
              groupBoxActionAlignment: MainAxisAlignment.center,
              semanticLabelAddButton: context.l10n.semAddAddress,
              semanticLabelDeleteButton: context.l10n.semDeleteAddress,
              onAdd: () {
                setState(() {
                  addressValues.add(
                    _createAddressValue(),
                  );
                });
              },
              onRemove: (index) {
                setState(() {
                  addressValues.removeAt(index);
                });
              },
              builder: (
                context,
                value,
                index,
                focusNode,
                focusHandler,
              ) {
                final config = feature.config.addressInput;
                final addressLabels = AddressTypeLabels.labels(
                  context,
                  localizedValue: feature.getLocalizedValue,
                  enabled: (type) {
                    // Only allow one main or billing address type
                    return type == model.AddressType.branch ||
                        !addressValues.any(
                          (value) =>
                              value != addressValues[index] &&
                              value.type == type,
                        );
                  },
                  countryCode: value.countryCode,
                );

                return AddressInput<model.AddressType>(
                  key: value.key,
                  value: value,
                  labels: addressLabels,
                  enabled: inputEnabled,
                  showDescriptionField: config.showDescriptionField,
                  showCountyField: config.showCountyField,
                  showCountryField: config.showCountryField,
                  focusNode: focusNode,
                  onFocus: focusHandler,
                  onChanged: (value) {
                    setState(() {
                      addressValues[index] = value;
                    });
                  },
                );
              },
            ),
            const Divider(),
            Padding(
              padding: const EdgeInsets.fromLTRB(8.0, 0.0, 0.0, 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(
                    l10n.contactsSectionSubTitle,
                    style: theme.textTheme.titleLarge,
                  ),
                ],
              ),
            ),
            MultiInput<ContactValue<model.ContactType>>(
              values: contactValues,
              enabled: inputEnabled,
              groupBoxBackground: theme.dialogBackgroundColor,
              groupBoxActionAlignment: MainAxisAlignment.center,
              semanticLabelAddButton: l10n.semAddContact,
              semanticLabelDeleteButton: l10n.semDeleteContact,
              onAdd: () {
                setState(() {
                  contactValues.add(
                    _createContactValue(),
                  );
                });
              },
              onRemove: (index) {
                setState(() {
                  contactValues.removeAt(index);
                });
              },
              builder: (
                context,
                value,
                index,
                focusNode,
                focusHandler,
              ) {
                final config = feature.config.contactInput;
                final contactLabels = ContactTypeLabels.labels(
                  context,
                  localizedValue: feature.getLocalizedValue,
                );

                return ContactInput<model.ContactType>(
                  key: value.key,
                  value: value,
                  labels: contactLabels,
                  enabled: inputEnabled,
                  showDescriptionField: config.showDescriptionField,
                  showTypeSelectionField: config.showTypeSelection,
                  showCountryForPhoneNumbers: config.showCountryForPhoneNumbers,
                  enableCountrySelectionForPhoneNumbers:
                      config.enableCountrySelection,
                  defaultCountryCode: feature.config.defaultCountryCode,
                  focusNode: focusNode,
                  emailValidator: feature.config.emailValidator,
                  onFocus: focusHandler,
                  onChanged: (value) {
                    setState(() {
                      contactValues[index] = value;
                    });
                  },
                );
              },
            ),
            const SizedBox(height: 8),
          ],
        ),
        actions: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 8),
              child: widget._goBack == null
                  ? ElevatedButton.icon(
                      label: Text(l10n.cancelButton),
                      icon: const Icon(Icons.cancel),
                      iconAlignment: IconAlignment.end,
                      onPressed: inputEnabled ? widget._dismissDialog : null,
                    )
                  : ElevatedButton.icon(
                      label: Text(l10n.backButton),
                      icon: const Icon(Icons.arrow_back),
                      iconAlignment: IconAlignment.end,
                      onPressed: inputEnabled ? widget._goBack : null,
                    ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 8),
              child: ElevatedIconButton(
                label: Text(l10n.createButton),
                icon: Icons.save,
                iconAlignment: IconAlignment.end,
                isLoading: isLoading,
                onPressed: inputEnabled &&
                        orgName.text.isNotEmpty &&
                        FormValues.isDirty(addressValues, const []) &&
                        FormValues.isDirty(contactValues, const [])
                    ? _createOrganization
                    : null,
              ),
            ),
          ],
        ),
        onCloseDialog: widget._dismissOnTap ? null : widget._dismissDialog,
      );
    });
  }

  void _createOrganization() {
    final organizationService = context.read<OrganizationService>();
    organizationService
        .createOrganization(
            orgName.text,
            contactValues.map((value) => value.toContactInput()).toList(),
            addressValues.map((value) => value.toAddressInput()).toList(),
            businessRef.text)
        .then(
      (organization) {
        if (organization != null) {
          logger.info('Created organization: $organization');

          if (widget._setAsDefault) {
            UserIdentityFeature.setDefaultOrg(organization.orgId);
          }

          if (!organization.isVerified && widget._creationPending != null) {
            widget._creationPending!(organization);
            return;
          } else if (widget._goBack != null) {
            widget._goBack!();
            return;
          }
        }
        widget._dismissDialog();
      },
    ).onError(
      (error, stackTrace) {
        logger.severe(
          'Failed to create organization',
          error,
          stackTrace,
        );
        widget._dismissDialog;
      },
    );
  }
}
