import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

import 'package:ui_widgets_component/ui_widgets.dart';

import '../organization_feature.dart';
import '../../l10n/l10n.dart';

class CreationPendingForm extends StatefulWidget {
  static const double formViewWidth = 480.0;
  static const double formViewHeight = 209.0;

  final String orgName;

  final VoidCallback? _goBack;

  final VoidCallback _dismissDialog;
  final bool _dismissOnTap;

  const CreationPendingForm({
    super.key,
    required this.orgName,
    VoidCallback? goBack,
    required VoidCallback dismissDialog,
    bool dismissOnTap = true,
  })  : _goBack = goBack,
        _dismissDialog = dismissDialog,
        _dismissOnTap = dismissOnTap;

  @override
  State<CreationPendingForm> createState() => _CreationPendingFormState();
}

class _CreationPendingFormState extends State<CreationPendingForm> {
  late final OrganizationFeature _feature;

  // declare a GlobalKey
  final formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();

    _feature = OrganizationFeature.instance();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;

    return DialogForm(
      formKey: formKey,
      title: _feature.getLocalizedValue(context, 'setupTitle'),
      formViewHeight: CreationPendingForm.formViewHeight,
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(8.0, 8.0, 0.0, 16.0),
            child: MarkdownBody(
              styleSheet: createMarkDownStyleSheet(
                context,
              ),
              data: l10n.creationPending(
                _feature.getLocalizedValue(context, 'orgTypeText'),
                widget.orgName,
              ),
            ),
          ),
        ],
      ),
      actions: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          if (widget._goBack != null)
            Padding(
              padding: const EdgeInsets.only(left: 8),
              child: ElevatedButton.icon(
                label: Text(l10n.backButton),
                icon: const Icon(Icons.arrow_back),
                iconAlignment: IconAlignment.end,
                onPressed: widget._goBack,
              ),
            ),
          Padding(
            padding: const EdgeInsets.only(left: 8),
            child: ElevatedButton.icon(
              label: Text(l10n.dismissButton),
              icon: const Icon(Icons.cancel),
              iconAlignment: IconAlignment.end,
              onPressed: widget._dismissDialog,
            ),
          ),
        ],
      ),
      onCloseDialog: widget._dismissOnTap ? null : widget._dismissDialog,
    );
  }
}
