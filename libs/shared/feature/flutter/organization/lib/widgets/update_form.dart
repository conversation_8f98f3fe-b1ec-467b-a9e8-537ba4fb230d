import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logging/logging.dart' as logging;

import 'package:ui_widgets_component/ui_widgets.dart';
import 'package:user_space_model/user_space_model.dart' as model;
import 'package:organization_service/organization_service.dart';

import '../organization_feature.dart';
import '../../l10n/l10n.dart';

import '../model/member_value.dart';
import '../model/address_type.dart';
import '../model/contact_type.dart';

class UpdateForm extends StatefulWidget {
  static const double formViewWidth = 588.0;
  static const double formViewHeight = 616.0; // TODO: Make this configurable
  static const double inputWidth = formViewWidth / 2;

  final Organization organization;

  final VoidCallback? _selectPlan;
  final VoidCallback? _goBack;

  final bool _readOnly;

  final VoidCallback _dismissDialog;
  final bool _dismissOnTap;

  const UpdateForm({
    super.key,
    required this.organization,
    VoidCallback? selectPlan,
    VoidCallback? goBack,
    bool readOnly = false,
    required VoidCallback dismissDialog,
    bool dismissOnTap = true,
  })  : _selectPlan = selectPlan,
        _goBack = goBack,
        _readOnly = readOnly,
        _dismissDialog = dismissDialog,
        _dismissOnTap = dismissOnTap;

  @override
  State<UpdateForm> createState() => _UpdateFormState();
}

class _UpdateFormState extends State<UpdateForm> {
  final logging.Logger logger = logging.Logger('UpdateForm');

  late final OrganizationFeature feature;

  // declare a GlobalKey
  final formKey = GlobalKey<FormState>();

  late final String orgId;

  final TextEditingController orgName = TextEditingController();
  final TextEditingController businessRef = TextEditingController();

  late List<MemberValue> memberValues;
  late List<AddressValue<model.AddressType>> addressValues;
  late List<ContactValue<model.ContactType>> contactValues;

  late List<MemberValue> savedMemberValues;
  late List<AddressValue<model.AddressType>> savedAddressValues;
  late List<ContactValue<model.ContactType>> savedContactValues;

  double tabContentHeight = 500;
  // if the form is read only, the tab offset
  // is 1 as we don't show the members tab
  late int tabOffset;

  @override
  void initState() {
    super.initState();

    feature = OrganizationFeature.instance();

    final organizationService = context.read<OrganizationService>();
    final organizationState = organizationService.state;

    assert(
      organizationState.selectedOrgData != null,
      'No selected organization data to update',
    );

    orgId = organizationState.selectedOrgData!.orgId;
    orgName.text = organizationState.selectedOrgData!.orgName;
    businessRef.text = organizationState.selectedOrgData!.businessRef ?? '';

    _setValues(organizationState);

    tabOffset = widget._readOnly ? 1 : 0;
    tabContentHeight = _getTabContentHeight(tabOffset + 0);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = context.l10n;

    return BlocListener<OrganizationService, OrganizationState>(
      listenWhen: (previous, current) {
        return memberValues.any(
              (value) => value.status == MemberStatus.resendInvitation,
            ) ||
            previous.selectedOrgData != current.selectedOrgData;
      },
      listener: (
        context,
        organizationState,
      ) {
        setState(() {
          _setValues(organizationState);
        });
      },
      child: BlocBuilder<OrganizationService, OrganizationState>(builder: (
        context,
        organizationState,
      ) {
        final isLoading = organizationState.isLoading(
          [
            updateOrgLoading,
            updateOrgUsersLoading,
          ],
        );
        final inputEnabled = !widget._readOnly && !isLoading;

        return DialogForm(
          formKey: formKey,
          title: feature.getLocalizedValue(context, 'updateTitle'),
          formViewHeight: UpdateForm.formViewHeight,
          body: Column(
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(8.0, 0.0, 0.0, 16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      l10n.namesSectionTitle,
                      style: theme.textTheme.titleLarge,
                    ),
                  ],
                ),
              ),
              Wrap(
                children: [
                  TextInput(
                    labelText: feature.getLocalizedValue(
                      context,
                      'nameInputLabelName',
                    ),
                    readOnly: true,
                    controller: orgName,
                  ),
                  TextInput(
                    labelText: feature.getLocalizedValue(
                      context,
                      'refInputLabelName',
                    ),
                    readOnly: true,
                    controller: businessRef,
                  ),
                ]
                    .map(
                      (input) => SizedBox(
                        width: UpdateForm.inputWidth,
                        child: input,
                      ),
                    )
                    .toList(),
              ),
              Container(
                padding: const EdgeInsets.fromLTRB(8.0, 8.0, 8.0, 8.0),
                height: tabContentHeight,
                child: TabbedFolders(
                  tabs: [
                    if (!widget._readOnly)
                      FolderTab(
                        title: l10n.updateMembersTab,
                        iconData: Icons.people,
                        content: _buildMembersTabContent(
                          context,
                          inputEnabled,
                        ),
                      ),
                    FolderTab(
                      title: l10n.updateAddressesTab,
                      iconData: Icons.business,
                      content: _buildAddressesTabContent(
                        context,
                        inputEnabled,
                      ),
                    ),
                    FolderTab(
                      title: l10n.updateContactsTab,
                      iconData: Icons.contacts,
                      content: _buildContactsTabContent(
                        context,
                        inputEnabled,
                      ),
                    ),
                  ],
                  indicatorHeight: 1,
                  alignment: TabAxisAlignment.center,
                  showContentBorder: false,
                  disableContentAnimation: true,
                  onTabChanged: (index) => setState(() {
                    tabContentHeight = _getTabContentHeight(tabOffset + index);
                  }),
                ),
              ),
              const SizedBox(height: 8),
            ],
          ),
          actions: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              if (widget._selectPlan != null) ...[
                ElevatedButton.icon(
                  label: Text(l10n.subscriptionsButton),
                  icon: const Icon(Icons.add_shopping_cart),
                  iconAlignment: IconAlignment.end,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.tertiary,
                  ),
                  onPressed: widget._selectPlan!,
                ),
                Spacer(),
              ],
              Padding(
                padding: const EdgeInsets.only(left: 8),
                child: widget._goBack == null
                    ? ElevatedButton.icon(
                        label: Text(l10n.cancelButton),
                        icon: const Icon(Icons.cancel),
                        iconAlignment: IconAlignment.end,
                        onPressed: widget._readOnly || inputEnabled
                            ? widget._dismissDialog
                            : null,
                      )
                    : ElevatedButton.icon(
                        label: Text(l10n.backButton),
                        icon: const Icon(Icons.arrow_back),
                        iconAlignment: IconAlignment.end,
                        onPressed: widget._readOnly || inputEnabled
                            ? widget._goBack
                            : null,
                      ),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 8),
                child: ElevatedIconButton(
                  label: Text(l10n.updateButton),
                  icon: Icons.save,
                  iconAlignment: IconAlignment.end,
                  isLoading: isLoading,
                  onPressed: inputEnabled &&
                          (FormValues.isDirty(
                                memberValues,
                                savedMemberValues,
                              ) ||
                              FormValues.isDirty(
                                addressValues,
                                savedAddressValues,
                              ) ||
                              FormValues.isDirty(
                                contactValues,
                                savedContactValues,
                              ))
                      ? _updateOrganization
                      : null,
                ),
              ),
            ],
          ),
          onCloseDialog: widget._dismissOnTap ? null : widget._dismissDialog,
        );
      }),
    );
  }

  double _getTabContentHeight(int index) {
    const tabHeaderHeight = 59;

    switch (index) {
      case 0:
        return tabHeaderHeight + memberValues.length * 85;

      case 1:
        final config = feature.config.addressInput;
        return tabHeaderHeight +
            addressValues.length *
                (AddressInput.calculateHeight(
                  showDescriptionField: config.showDescriptionField,
                  showCountyField: config.showCountyField,
                  showCountryField: config.showCountryField,
                ));

      case 2:
        final config = feature.config.contactInput;
        return tabHeaderHeight +
            contactValues.length *
                (ContactInput.calculateHeight(
                  showDescriptionField: config.showDescriptionField,
                  showTypeSelectionField: config.showTypeSelection,
                ));

      default:
        throw StateError('Invalid tab index: $index');
    }
  }

  Widget _buildMembersTabContent(
    BuildContext context,
    bool inputEnabled,
  ) {
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: MultiInput<MemberValue>(
        values: memberValues,
        enabled: inputEnabled,
        groubBox: false,
        actionsPadding: const EdgeInsets.only(bottom: 16),
        semanticLabelAddButton: context.l10n.semAddMember,
        semanticLabelDeleteButton: context.l10n.semDeleteMember,
        onAdd: () {
          setState(() {
            memberValues.add(
              MemberValue.create(),
            );
            tabContentHeight = _getTabContentHeight(0);
          });
        },
        onRemove: (index) {
          setState(() {
            memberValues.removeAt(index);
            tabContentHeight = _getTabContentHeight(0);
          });
        },
        builder: (
          context,
          value,
          index,
          focusNode,
          focusHandler,
        ) {
          // Add focus listener to update the focus state
          focusNode.addListener(() {
            focusHandler(focusNode.hasFocus);
          });

          return Row(
            key: value.key,
            children: [
              Expanded(
                child: _buildMemberField(
                  context,
                  value,
                  index,
                  focusNode,
                  inputEnabled,
                ),
              )
            ],
          );
        },
      ),
    );
  }

  Widget _buildMemberField(
    BuildContext context,
    MemberValue value,
    int index,
    FocusNode focusNode,
    bool inputEnabled,
  ) {
    final l10n = context.l10n;
    const inputPadding = EdgeInsets.only(top: 8.0, right: 8.0);

    switch (value.status) {
      case MemberStatus.toBeInvited:
        return EmailAddressInput(
          labelText: l10n.memberEmailNew,
          initialValue: value.value,
          enabled: inputEnabled,
          iconTooltip: l10n.memberEmailNewTooltip,
          focusNode: focusNode,
          padding: inputPadding,
          validator: feature.config.emailValidator,
          onChanged: (value, isValid) {
            setState(() {
              memberValues[index] = memberValues[index].copyWith(
                value: value,
                isFieldValid: ('value', isValid),
              );
            });
          },
        );
      case MemberStatus.resendInvitation:
        return TextInput(
          labelText: l10n.memberEmailResend,
          initialValue: value.value,
          readOnly: true,
          trailingIcon: InputIcon(
            icon: Icons.schedule_send,
            iconColor: Colors.blue,
            iconTooltip: l10n.memberEmailResendTooltip,
            onIconTap: () {
              setState(() {
                memberValues[index] = memberValues[index].copyWith(
                  status: MemberStatus.invited,
                );
              });
            },
          ),
          padding: inputPadding,
        );
      case MemberStatus.invited:
        return TextInput(
          labelText: l10n.memberEmailSent,
          initialValue: value.value,
          readOnly: true,
          trailingIcon: InputIcon(
            icon: Icons.send,
            iconColor: Colors.blue,
            iconTooltip: l10n.memberEmailSentTooltip,
            onIconTap: () {
              setState(() {
                memberValues[index] = memberValues[index].copyWith(
                  resetUserId: true,
                  status: MemberStatus.resendInvitation,
                );
              });
            },
          ),
          padding: inputPadding,
        );
      case MemberStatus.active:
        return TextInput(
          labelText: l10n.memberActive,
          initialValue: value.value,
          readOnly: true,
          trailingIcon: const InputIcon(
            icon: Icons.person,
            iconColor: Colors.green,
          ),
          padding: inputPadding,
        );
      case MemberStatus.inactive:
        return TextInput(
          labelText: l10n.memberInactive,
          initialValue: value.value,
          readOnly: true,
          trailingIcon: const InputIcon(
            icon: Icons.person_off_outlined,
            iconColor: Colors.red,
          ),
          padding: inputPadding,
        );
      case MemberStatus.owner:
        return TextInput(
          labelText: l10n.memberOwner,
          initialValue: value.value,
          readOnly: true,
          trailingIcon: const InputIcon(
            icon: Icons.admin_panel_settings,
            iconColor: Colors.green,
          ),
          padding: inputPadding,
        );
    }
  }

  Widget _buildAddressesTabContent(
    BuildContext context,
    bool inputEnabled,
  ) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: MultiInput<AddressValue<model.AddressType>>(
        values: addressValues,
        enabled: inputEnabled,
        groupBoxBackground: theme.dialogBackgroundColor,
        groupBoxMargin: const EdgeInsets.only(top: 8.0, bottom: 8.0),
        groupBoxActionAlignment: MainAxisAlignment.center,
        semanticLabelAddButton: context.l10n.semAddAddress,
        semanticLabelDeleteButton: context.l10n.semDeleteAddress,
        onAdd: () {
          setState(() {
            addressValues.add(
              AddressValue.create<model.AddressType>(
                countryCode: feature.config.defaultCountryCode,
              ),
            );
            tabContentHeight = _getTabContentHeight(1);
          });
        },
        onRemove: (index) {
          setState(() {
            addressValues.removeAt(index);
            tabContentHeight = _getTabContentHeight(1);
          });
        },
        builder: (
          context,
          value,
          index,
          focusNode,
          focusHandler,
        ) {
          final config = feature.config.addressInput;
          final addressLabels = AddressTypeLabels.labels(
            context,
            localizedValue: feature.getLocalizedValue,
            enabled: (type) {
              // Only allow one main or billing address type
              return type == model.AddressType.branch ||
                  !addressValues.any(
                    (value) =>
                        value != addressValues[index] && value.type == type,
                  );
            },
            countryCode: value.countryCode,
          );

          return AddressInput<model.AddressType>(
            key: value.key,
            value: value,
            labels: addressLabels,
            enabled: inputEnabled,
            showDescriptionField: config.showDescriptionField,
            showCountyField: config.showCountyField,
            showCountryField: config.showCountryField,
            focusNode: focusNode,
            onFocus: focusHandler,
            onChanged: (value) {
              setState(() {
                addressValues[index] = value;
              });
            },
          );
        },
      ),
    );
  }

  Widget _buildContactsTabContent(
    BuildContext context,
    bool inputEnabled,
  ) {
    final theme = Theme.of(context);
    final l10n = context.l10n;

    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: MultiInput<ContactValue<model.ContactType>>(
        values: contactValues,
        enabled: inputEnabled,
        groupBoxBackground: theme.dialogBackgroundColor,
        groupBoxMargin: const EdgeInsets.only(top: 8.0, bottom: 8.0),
        groupBoxActionAlignment: MainAxisAlignment.center,
        semanticLabelAddButton: l10n.semAddContact,
        semanticLabelDeleteButton: l10n.semDeleteContact,
        onAdd: () {
          setState(() {
            contactValues.add(
              ContactValue.create<model.ContactType>(),
            );
            tabContentHeight = _getTabContentHeight(2);
          });
        },
        onRemove: (index) {
          setState(() {
            contactValues.removeAt(index);
            tabContentHeight = _getTabContentHeight(2);
          });
        },
        builder: (
          context,
          value,
          index,
          focusNode,
          focusHandler,
        ) {
          final config = feature.config.contactInput;
          final contactLabels = ContactTypeLabels.labels(
            context,
            localizedValue: feature.getLocalizedValue,
          );

          return ContactInput<model.ContactType>(
            key: value.key,
            value: value,
            labels: contactLabels,
            enabled: inputEnabled,
            showDescriptionField: config.showDescriptionField,
            showTypeSelectionField: config.showTypeSelection,
            showCountryForPhoneNumbers: config.showCountryForPhoneNumbers,
            enableCountrySelectionForPhoneNumbers:
                config.enableCountrySelection,
            defaultCountryCode: feature.config.defaultCountryCode,
            emailValidator: feature.config.emailValidator,
            focusNode: focusNode,
            onFocus: focusHandler,
            onChanged: (value) {
              setState(() {
                contactValues[index] = value;
              });
            },
          );
        },
      ),
    );
  }

  void _setValues(OrganizationState organizationState) {
    savedMemberValues = organizationState.selectedOrgData!.users!.orgUsers!
        .map<MemberValue>(
          (user) => MemberValue.create().fromOrgUser(user!),
        )
        .toList();
    memberValues = savedMemberValues.isEmpty
        ? [
            MemberValue.create(),
          ]
        : List.from(savedMemberValues);

    savedAddressValues = organizationState.selectedOrgData!.addresses!
        .map<AddressValue<model.AddressType>>(
          (address) =>
              AddressValue.create<model.AddressType>().fromAddress(address!),
        )
        .toList();
    addressValues = List.from(savedAddressValues);

    savedContactValues = organizationState.selectedOrgData!.contacts!
        .map<ContactValue<model.ContactType>>(
          (contact) =>
              ContactValue.create<model.ContactType>().fromContact(contact!),
        )
        .toList();
    contactValues = List.from(savedContactValues);
  }

  void _updateOrganization() {
    final organizationService = context.read<OrganizationService>();

    if (FormValues.isDirty(
          addressValues,
          savedAddressValues,
        ) ||
        FormValues.isDirty(
          contactValues,
          savedContactValues,
        )) {
      organizationService
          .updateOrganization(orgId,
              contacts: contactValues
                  .where((value) => value.isNotEmpty)
                  .map(
                    (value) => value.toContactInput(),
                  )
                  .toList(),
              addresses: addressValues
                  .where((value) => value.isNotEmpty)
                  .map(
                    (value) => value.toAddressInput(),
                  )
                  .toList())
          .then(
        (_) {
          _updateOrgUsers();
        },
      ).onError(
        (error, stackTrace) {
          logger.severe(
            'Failed to updated organization',
            error,
            stackTrace,
          );
        },
      );
    } else {
      _updateOrgUsers();
    }
  }

  void _updateOrgUsers() {
    if (FormValues.isDirty(
      memberValues,
      savedMemberValues,
    )) {
      final organizationService = context.read<OrganizationService>();

      final memberUserIDs = Set.from(
        memberValues
            .where(
              (m) => m.userId != null,
            )
            .map(
              (m) => m.userId,
            ),
      );
      final removeUserIDs = savedMemberValues
          .where((m) => !memberUserIDs.contains(m.userId))
          .map((m) => m.userId!)
          .toList();
      final addUserIds = memberValues
          .where((m) => m.userId == null)
          .map((m) => m.value)
          .toList();

      organizationService.updateOrgUsers(
        widget.organization,
        add: addUserIds,
        remove: removeUserIDs,
      );
    }
  }
}
