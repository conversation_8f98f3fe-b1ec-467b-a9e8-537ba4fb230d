// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'organization_feature_config.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrganizationFeatureConfig _$OrganizationFeatureConfigFromJson(
        Map<String, dynamic> json) =>
    OrganizationFeatureConfig(
      addressInput: json['address-input'] == null
          ? const AddressInput()
          : AddressInput.fromJson(
              json['address-input'] as Map<String, dynamic>),
      contactInput: json['contact-input'] == null
          ? const ContactInput()
          : ContactInput.fromJson(
              json['contact-input'] as Map<String, dynamic>),
      billing: json['billing'] == null
          ? null
          : Billing.fromJson(json['billing'] as Map<String, dynamic>),
      optionSubtitle: json['option-subtitle'] as String?,
      membershipRequired: json['membership-required'] as bool? ?? false,
      creationRestricted: json['creation-restricted'] as bool? ?? false,
      showDefaultOrgInStatus:
          json['show-default-org-in-status'] as bool? ?? false,
      canChangeDefaultOrg: json['can-change-default-org'] as bool? ?? false,
      quotasToShowInStatus: (json['quotas-to-show-in-status'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      allowedEmailDomains: json['allowed-email-domains'] as String?,
      allowedEmailExample: json['allowed-email-example'] as String?,
      defaultCountryCode: json['default-country-code'] as String? ?? 'US',
    );

AddressInput _$AddressInputFromJson(Map<String, dynamic> json) => AddressInput(
      showTypeSelection: json['show-type-selection'] as bool? ?? true,
      showDescriptionField: json['show-description-field'] as bool? ?? true,
      showCountyField: json['show-county-field'] as bool? ?? true,
      showCountryField: json['show-country-field'] as bool? ?? true,
    );

ContactInput _$ContactInputFromJson(Map<String, dynamic> json) => ContactInput(
      showTypeSelection: json['show-type-selection'] as bool? ?? true,
      showDescriptionField: json['show-description-field'] as bool? ?? true,
      showCountryForPhoneNumbers:
          json['show-country-for-phone-numbers'] as bool? ?? true,
      enableCountrySelection: json['enable-country-selection'] as bool? ?? true,
    );

Billing _$BillingFromJson(Map<String, dynamic> json) => Billing(
      productName: json['product-name'] as String,
      billingKey: json['billing-key'] as String,
      subscriptionKey: json['subscription-key'] as String,
      subscriptionPlanKey: json['subscription-plan-key'] as String,
      subscriptionStatusKey: json['subscription-status-key'] as String,
      subscriptionPeriodEndKey: json['subscription-period-end-key'] as String,
      activeStatusPattern: json['active-status-pattern'] as String?,
    );
