import 'package:json_annotation/json_annotation.dart';

import 'package:ui_widgets_component/ui_widgets.dart';
import 'package:app_framework_component/app_framework.dart';

part 'organization_feature_config.g.dart';

@JsonSerializable(
  fieldRename: FieldRename.kebab,
  createToJson: false,
)
class OrganizationFeatureConfig extends FeatureConfig {
  final String? optionSubtitle;

  /// if true then user should either be a
  /// member of an organization or needs to
  /// create one in order to use the app
  final bool membershipRequired;

  /// if true then user can only create a
  /// single organization
  final bool creationRestricted;

  /// if true then the default organization
  /// should be shown in the status bar
  final bool showDefaultOrgInStatus;

  /// if true then user can change the
  /// default organization if they have
  /// more than one and it is shown
  /// in the status bar
  final bool canChangeDefaultOrg;

  // List of names of the quotas and their
  // current usage values that should be
  // shown in the status bar
  final List<String> quotasToShowInStatus;

  final String? allowedEmailDomains;
  final String? allowedEmailExample;

  final AddressInput addressInput;
  final ContactInput contactInput;

  /// The billing configuration for
  /// an organization
  final Billing? billing;

  final String defaultCountryCode;

  /// Returns a validator that checks that
  /// email is valid and that it matches
  /// the allowed email domains if provided.
  Validator get emailValidator {
    if (allowedEmailDomains == null || allowedEmailDomains!.isEmpty) {
      return EmailAddressValidator();
    } else {
      return ChainedValidator(
        [
          EmailAddressValidator(),
          RegexValidator(
            regex: RegExp(allowedEmailDomains!),
            examples: allowedEmailExample != null //
                ? [allowedEmailExample!]
                : [],
          ),
        ],
      );
    }
  }

  const OrganizationFeatureConfig({
    this.addressInput = const AddressInput(),
    this.contactInput = const ContactInput(),
    this.billing,
    this.optionSubtitle,
    this.membershipRequired = false,
    this.creationRestricted = false,
    this.showDefaultOrgInStatus = false,
    this.canChangeDefaultOrg = false,
    this.quotasToShowInStatus = const [],
    this.allowedEmailDomains,
    this.allowedEmailExample,
    this.defaultCountryCode = 'US',
  });

  factory OrganizationFeatureConfig.fromJson(Map<String, dynamic> json) =>
      _$OrganizationFeatureConfigFromJson(json);
}

@JsonSerializable(
  fieldRename: FieldRename.kebab,
  createToJson: false,
)
class AddressInput extends FeatureConfig {
  final bool showTypeSelection;
  final bool showDescriptionField;
  final bool showCountyField;
  final bool showCountryField;

  const AddressInput({
    this.showTypeSelection = true,
    this.showDescriptionField = true,
    this.showCountyField = true,
    this.showCountryField = true,
  });

  factory AddressInput.fromJson(Map<String, dynamic> json) =>
      _$AddressInputFromJson(json);
}

@JsonSerializable(
  fieldRename: FieldRename.kebab,
  createToJson: false,
)
class ContactInput extends FeatureConfig {
  final bool showTypeSelection;
  final bool showDescriptionField;
  final bool showCountryForPhoneNumbers;
  final bool enableCountrySelection;

  const ContactInput({
    this.showTypeSelection = true,
    this.showDescriptionField = true,
    this.showCountryForPhoneNumbers = true,
    this.enableCountrySelection = true,
  });

  factory ContactInput.fromJson(Map<String, dynamic> json) =>
      _$ContactInputFromJson(json);
}

@JsonSerializable(
  fieldRename: FieldRename.kebab,
  createToJson: false,
)
class Billing extends FeatureConfig {
  /// The name of the product that is used
  /// to determine the product to show in
  /// the plan selection dialog
  final String productName;

  /// The key of the customer specific
  ///  to billing system integration
  final String billingKey;

  /// The key of the subscription specific
  /// to the billing system integration
  final String subscriptionKey;

  /// The key of the subscription plan
  /// that is used to determine if the
  /// organization has an active subscription
  final String subscriptionPlanKey;

  /// The key of the subscription status
  /// that is used to determine if the
  /// organization has an active subscription
  final String subscriptionStatusKey;

  /// The key of the subscription period
  /// end once a subscription is cancelled
  final String subscriptionPeriodEndKey;

  /// If set then the organization must
  /// have billing account with a valid
  /// subscription in order for the user
  /// to coninue using the app
  final String? activeStatusPattern;

  const Billing({
    required this.productName,
    required this.billingKey,
    required this.subscriptionKey,
    required this.subscriptionPlanKey,
    required this.subscriptionStatusKey,
    required this.subscriptionPeriodEndKey,
    this.activeStatusPattern,
  });

  factory Billing.fromJson(Map<String, dynamic> json) =>
      _$BillingFromJson(json);
}
