import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import 'package:ui_widgets_component/ui_widgets.dart' as ui;
import 'package:nav_layouts_component/nav_layouts.dart' as nav;
import 'package:organization_service/organization_service.dart' as service;
import 'package:user_space_model/user_space_model.dart' as model;

class QuotaStatusTool extends nav.AppStatusToolAnchor {
  final BuildContext _context;

  final String _orgId;
  final String _quotaName;

  late final service.OrganizationService _orgService;

  final _OrgQuotaNotifer _orgQuotaNotifier = _OrgQuotaNotifer(
    const _OrgQuotaParam(null),
  );

  StreamSubscription<service.OrganizationState>? _sub;

  @override
  ui.Tool get statusTool {
    return ui.TextButtonTool.statusbar(
      toolValue: _orgQuotaNotifier,
      icon: (param) {
        if (param!.isNotNull) {
          final quota = param.value!;
          if (quota.used >= quota.limit) {
            if (quota.softLimit) {
              return const Icon(
                Icons.warning,
                color: Colors.red,
                size: 16,
              );
            } else {
              return const Icon(
                Icons.block,
                color: Colors.red,
                size: 16,
              );
            }
          } else if (quota.used > quota.limit * 0.8) {
            return const Icon(
              Icons.warning,
              color: Colors.amber,
              size: 16,
            );
          } else {
            return const Icon(
              Icons.check,
              color: Colors.green,
              size: 16,
            );
          }
        } else {
          return null;
        }
      },
      text: (param) {
        if (param!.isNotNull) {
          final quota = param.value!;
          return '${quota.description}: ${quota.used}/${quota.limit}';
        } else {
          return '< --quota-- >';
        }
      },
      tooltip: (param) {
        if (param!.isNotNull) {
          final quota = param.value!;
          if (quota.used >= quota.limit) {
            if (quota.softLimit) {
              return 'quota limit has been exceeded for the organization, overage charges may be applied';
            } else {
              return 'quota limit has been reached for the organization, no further resources can be allocated';
            }
          } else if (quota.used > quota.limit * 0.8) {
            return 'quota usage is approaching the limit for the organization';
          }
        }
        return null;
      },
      onInit: () => _sub = _orgService.stream.listen(
        (state) {
          _updateQuotaStatus();
        },
      ),
      onDispose: () => _sub?.cancel(),
    );
  }

  QuotaStatusTool({
    super.hookName,
    required super.order,
    required BuildContext context,
    required String orgId,
    required String quotaName,
    super.alignment = nav.AppStatusToolAlignment.left,
    super.divider = nav.AppStatusToolDivider.none,
  })  : _context = context,
        _orgId = orgId,
        _quotaName = quotaName {
    _orgService = _context.read<service.OrganizationService>();
    _updateQuotaStatus();
  }

  void _updateQuotaStatus() {
    final org = _orgService.state.organizations.firstWhere(
      (org) => org.orgId == _orgId,
      orElse: () => service.Organization.invalid(),
    );
    if (org.valid) {
      final quotas = org.quotas;
      for (var quota in quotas) {
        if (quota.quotaName == _quotaName) {
          final quotaParam = _OrgQuotaParam(quota);
          if (quotaParam != _orgQuotaNotifier.value) {
            _orgQuotaNotifier.value = quotaParam;
          }
        }
      }
    }
  }
}

typedef _OrgQuotaNotifer = ValueNotifier<_OrgQuotaParam>;

class _OrgQuotaParam extends Equatable {
  final model.OrgQuota? value;

  bool get isNotNull => value != null;

  const _OrgQuotaParam(
    this.value,
  );

  @override
  List<Object?> get props => [
        value?.quotaName,
        value?.description,
        value?.period,
        value?.softLimit,
        value?.limit,
        value?.used,
      ];
}
