import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import 'package:ui_widgets_component/ui_widgets.dart' as ui;
import 'package:nav_layouts_component/nav_layouts.dart' as nav;
import 'package:organization_service/organization_service.dart' as service;
import 'package:user_identity_feature/user_identity.dart' as identity;

import '../l10n/l10n.dart';
import '../widgets/organization_profile.dart';

class OrgSelectorStatusTool extends nav.AppStatusToolAnchor {
  final BuildContext _context;

  final String _orgId;
  final bool _canChangeDefaultOrg;

  late final service.OrganizationService _orgService;

  final _OrgNameNotifer _orgNameNotifier = _OrgNameNotifer(
    const _OrgNameParam(),
  );

  StreamSubscription<service.OrganizationState>? _sub;

  Timer? _orgRefreshTimer;

  @override
  ui.Tool get statusTool {
    final l10n = _context.l10n;

    if (_canChangeDefaultOrg) {
      return ui.TextButtonTool.statusbar(
        toolValue: _orgNameNotifier,
        iconData: (_) => Icons.business,
        text: (param) => param.toString(),
        onPressed: (_) {
          OrganizationProfile.asModal(
            _context,
            dismissOnTap: true,
          );
        },
        options: (param) => param?.orgNames,
        onOption: _changeDefaultOrganization,
        buttonSemanticsLabel: l10n.semStatusOrgProfile,
        optionsSemanticsLabel: l10n.semStatusChangeOrg,
        onInit: () => _sub = _orgService.stream.listen(
          (state) {
            _updateOrganizations();
          },
        ),
        onDispose: () => _sub?.cancel(),
      );
    } else {
      return ui.TextButtonTool.statusbar(
        iconData: (_) => Icons.business,
        toolValue: _orgNameNotifier,
        text: (param) => param.toString(),
        onPressed: (_) {
          OrganizationProfile.asModal(
            _context,
            dismissOnTap: true,
          );
        },
        buttonSemanticsLabel: l10n.semStatusOrgProfile,
        onInit: () => _sub = _orgService.stream.listen(
          (state) {
            _updateOrganizations();
          },
        ),
        onDispose: () => {
          _orgRefreshTimer?.cancel(),
          _sub?.cancel(),
        },
      );
    }
  }

  OrgSelectorStatusTool({
    super.hookName,
    required super.order,
    required BuildContext context,
    required String orgId,
    bool canChangeDefaultOrg = true,
    super.alignment = nav.AppStatusToolAlignment.left,
    super.divider = nav.AppStatusToolDivider.none,
  })  : _context = context,
        _orgId = orgId,
        _canChangeDefaultOrg = canChangeDefaultOrg {
    _orgService = _context.read<service.OrganizationService>();
    _updateOrganizations();
    _startOrgRefreshTimer();
  }

  void _startOrgRefreshTimer() {
    identity.UserIdentityFeature.registerSignOutHook(
      () {
        _orgRefreshTimer?.cancel();
        _orgRefreshTimer = null;
      },
    );

    _orgRefreshTimer = Timer.periodic(
      const Duration(seconds: 10),
      (timer) {
        identity.UserIdentityFeature.isSessionValid().then(
          (valid) {
            if (valid) {
              _orgService.loadOrgs(
                notifyOnError: false,
              );
            } else {
              timer.cancel();
              _orgRefreshTimer = null;
            }
          },
        );
      },
    );
  }

  void _updateOrganizations() {
    final orgIds = {
      for (var org in _orgService.state.organizations) org.name: org.orgId
    };
    if (!mapEquals(orgIds, _orgNameNotifier.value.orgIds)) {
      _orgNameNotifier.value = _orgNameNotifier.value.copyWith(
        value: orgIds.keys.firstWhere(
          (key) => orgIds[key] == _orgId,
          orElse: () => '< --- >',
        ),
        orgIds: orgIds,
      );
    }
  }

  void _changeDefaultOrganization(String orgName, _) {
    if (orgName != _orgNameNotifier.value.value) {
      final l10n = _context.l10n;

      ui.AlertDialogHelper.showWarning(
        context: _context,
        markdownText: l10n.changeOrganizationConfirmation,
        textAlignment: TextAlign.center,
        cancelText: l10n.cancelButton,
        confirmText: l10n.okButton,
        onConfirm: () {
          identity.UserIdentityFeature.setDefaultOrg(
            _orgNameNotifier.value.orgIds[orgName]!,
            logout: true,
          );
        },
      );
    }
  }
}

typedef _OrgNameNotifer = ValueNotifier<_OrgNameParam>;

class _OrgNameParam extends Equatable {
  List<String> get orgNames => orgIds.keys.toList();

  final String value;
  final Map<String, String> orgIds;

  const _OrgNameParam({
    this.value = '',
    this.orgIds = const {},
  });

  _OrgNameParam copyWith({
    String? value,
    Map<String, String>? orgIds,
  }) {
    return _OrgNameParam(
      value: value ?? this.value,
      orgIds: orgIds ?? this.orgIds,
    );
  }

  @override
  String toString() => value;

  @override
  List<Object> get props => [
        value,
        orgIds,
      ];
}
