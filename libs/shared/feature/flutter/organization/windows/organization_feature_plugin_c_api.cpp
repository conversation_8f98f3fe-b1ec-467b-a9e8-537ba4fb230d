#include "include/organization_feature/organization_feature_plugin_c_api.h"

#include <flutter/plugin_registrar_windows.h>

#include "organization_feature_plugin.h"

void OrganizationFeaturePluginCApiRegisterWithRegistrar(
    FlutterDesktopPluginRegistrarRef registrar) {
  organization_feature::OrganizationFeaturePlugin::RegisterWithRegistrar(
      flutter::PluginRegistrarManager::GetInstance()
          ->GetRegistrar<flutter::PluginRegistrarWindows>(registrar));
}
