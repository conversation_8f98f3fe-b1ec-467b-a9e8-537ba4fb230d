#ifndef FLUTTER_PLUGIN_ORGANIZATION_FEATURE_PLUGIN_H_
#define FLUTTER_PLUGIN_ORGANIZATION_FEATURE_PLUGIN_H_

#include <flutter/method_channel.h>
#include <flutter/plugin_registrar_windows.h>

#include <memory>

namespace organization_feature {

class OrganizationFeaturePlugin : public flutter::Plugin {
 public:
  static void RegisterWithRegistrar(flutter::PluginRegistrarWindows *registrar);

  OrganizationFeaturePlugin();

  virtual ~OrganizationFeaturePlugin();

  // Disallow copy and assign.
  OrganizationFeaturePlugin(const OrganizationFeaturePlugin&) = delete;
  OrganizationFeaturePlugin& operator=(const OrganizationFeaturePlugin&) = delete;

  // Called when a method is called on this plugin's channel from Dart.
  void HandleMethodCall(
      const flutter::MethodCall<flutter::EncodableValue> &method_call,
      std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result);
};

}  // namespace organization_feature

#endif  // FLUTTER_PLUGIN_ORGANIZATION_FEATURE_PLUGIN_H_
