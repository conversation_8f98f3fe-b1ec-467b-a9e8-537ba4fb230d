# Organization feature

MyCS organization management feature module

## Organization Membership Rules

Organization membership rules are managed by the following organization feature config flags:

* membership-required:

User should not be able to login if they are not a member of an organization and a default organization is set for the user.

* creation-restricted:

User can create and be owner of only one organization but can be a member of more than one.

* show-default-org-in-status:

Shows the user's default organization in the status bar (empty if not set). Clicking on the status will open the create org dialog. If the `creation-restricted` is true then the user can only update the organization he/she owns.

* "can-change-default-org:

If the user is a member of multiple organizations then the user can change the default organization in context.

## Bootstrap Scenarios

On bootstrap the membership flags are used to determine if user login can continue based on the following scenarios.

### Unrestricted membership.

Organization Config:
* membership-required = false
* can-change-default-org = true

1. If user is not a member of any organization user should be taken to the home page.
2. If user has a default organization set then user should be taken to the home page only if it is verified.
3. If user has a default organization set which is not verified then the "unverified dialog" should be shown, the default organization reset to empty and user logged out.
4. If user's default organization is verified but requires an active subscription and one does not exist, then the "no active subscription dialog" should be shown, the default organization reset to empty and user logged out.
