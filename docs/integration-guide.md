# NovAssist Framework Integration Guide

> 📖 **Navigation**: [← Back to Framework Overview](README.md) | [📋 Documentation Summary](summary.md)

## Quick Start Guide

This guide provides step-by-step instructions for integrating the NovAssist framework into a new Flutter application.

## Prerequisites

1. **Flutter SDK**: Version 3.0 or higher
2. **Dart SDK**: Version 2.17 or higher
3. **Dependencies**: Add the following to your `pubspec.yaml`:

```yaml
dependencies:
  flutter:
    sdk: flutter
  
  # Framework Components
  app_framework_component: ^1.0.0
  nav_layouts_component: ^1.0.0
  ui_widgets_component: ^1.0.0
  platform_utilities_component: ^1.0.0
  
  # Features
  user_identity_feature: ^1.0.0
  organization_feature: ^1.0.0
  billing_feature: ^1.0.0
  
  # Services
  identity_service: ^1.0.0
  organization_service: ^1.0.0
  billing_service: ^1.0.0
  
  # Utilities
  utilities_ab: ^1.0.0
  
  # Third-party Dependencies
  flutter_bloc: ^8.1.0
  hydrated_bloc: ^9.1.0
  get_it: ^7.6.0
  dio: ^5.0.0
  logging: ^1.1.0
  amplify_flutter: ^1.0.0
  amplify_auth_cognito: ^1.0.0
  amplify_api: ^1.0.0
```

## Step 1: Application Setup

### 1.1 Initialize Main Application

```dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:logging/logging.dart';

import 'package:utilities_ab/utilities.dart';
import 'package:platform_utilities_component/platform_utilities.dart';
import 'package:app_framework_component/app_framework.dart' as app;
import 'package:nav_layouts_component/nav_layouts.dart' as nav;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize logging
  initLogging(
    Level.ALL,
    logToConsole: true,
  );

  // Initialize platform utilities
  await AppPlatform.init();

  // Initialize HydratedBloc storage
  HydratedBloc.storage = await HydratedStorage.build(
    storageDirectory: kIsWeb
        ? HydratedStorageDirectory.web
        : HydratedStorageDirectory('${AppPlatform.appDataPath}/state'),
  );

  // Initialize feature registry
  app.FeatureRegistry.intialize(featureConfigLoader);

  runApp(MyApp());
}
```

### 1.2 Create Configuration Loader

```dart
Future<Map<String, dynamic>> featureConfigLoader(String featureName) async {
  return jsonDecode(
    await rootBundle.loadString('assets/${featureName}_feature.json'),
  );
}
```

## Step 2: Service Registration

### 2.1 Register Service Providers

```dart
class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return nav.StatefulWrapper(
      onInit: (_) async {
        // Register identity service
        GetIt.instance.registerLazySingleton<IdentityProvider>(
          () => AWSCognitoService(),
        );

        // Register organization service
        GetIt.instance.registerLazySingleton<OrgProvider>(
          () => UserSpaceOrgService(),
        );

        // Register billing service
        GetIt.instance.registerLazySingleton<BillingProvider>(
          () => UserSpaceBillingService(
            UserSpaceBillingApi(_createDioClient(userSpaceApiEndpoint)),
          ),
        );

        // Register features
        await UserIdentityFeature.register();
        await OrganizationFeature.register();
        await BillingFeature.register();
      },
      onDispose: () async {
        GetIt.instance.unregister<IdentityProvider>(
          disposingFunction: (service) async => await service.dispose(),
        );
        app.FeatureRegistry.instance().dispose();
      },
      child: MainApp(),
    );
  }
}
```

### 2.2 Create HTTP Client

```dart
Dio _createDioClient(String endpoint) {
  final authProvider = GetIt.instance.get<IdentityProvider>();
  
  final dio = Dio(BaseOptions(
    baseUrl: endpoint,
    connectTimeout: Duration(seconds: 30),
    receiveTimeout: Duration(seconds: 30),
  ));
  
  dio.interceptors.add(authProvider.authInterceptor);
  return dio;
}
```

## Step 3: Navigation Setup

### 3.1 Create Authentication Router

```dart
class MainApp extends StatelessWidget {
  late final AuthRouter _authRouter;

  MainApp({super.key}) {
    _authRouter = AuthRouter(
      navigatorKey: nav.GlobalNavigator.key,
      initialLocation: '/',
      authRoute: LoginViewFlow(),
      publicRoutes: [
        const HomePage().route(),
      ],
      privateRoutes: [
        AppNavLayout().route(),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final featureRegistry = app.FeatureRegistry.instance();
    
    return featureRegistry.scope(
      context,
      child: MaterialApp.router(
        title: 'My Application',
        routerConfig: _authRouter.config,
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.indigo),
          useMaterial3: false,
        ),
      ),
    );
  }
}
```

### 3.2 Create Navigation Layout

```dart
class AppNavLayout extends nav.AppNavLayout {
  @override
  List<nav.NavTarget> get navTargets => [
    nav.NavTarget(
      body: const DashboardPage(),
      destBuilder: (_) => nav.NavDest(
        iconData: Icons.dashboard,
        label: 'Dashboard',
      ),
    ),
    nav.NavTarget(
      body: const SettingsPage(),
      destBuilder: (_) => nav.NavDest(
        iconData: Icons.settings,
        label: 'Settings',
      ),
    ),
  ];

  @override
  List<Widget>? buildTitleBarActions(BuildContext context) {
    return [
      const ProfileMenu(
        defaultAvatarImage: AssetImage('assets/default_avatar.png'),
      ),
    ];
  }

  AppNavLayout() : super('My Application');
}
```

## Step 4: Feature Configuration

### 4.1 Create Feature Configuration Files

Create the following JSON files in your `assets/` directory:

**user_identity_feature.json**:
```json
{
  "showLogoutOnTitleBar": true,
  "enableProfileManagement": true,
  "defaultAvatarImage": "assets/default_avatar.png"
}
```

**organization_feature.json**:
```json
{
  "enableOrganizationSwitching": true,
  "showOrganizationSelector": true
}
```

**billing_feature.json**:
```json
{
  "enableSubscriptionManagement": true,
  "showBillingStatus": true
}
```

### 4.2 Update pubspec.yaml Assets

```yaml
flutter:
  assets:
    - assets/user_identity_feature.json
    - assets/organization_feature.json
    - assets/billing_feature.json
    - assets/default_avatar.png
```

## Step 5: Create Application Pages

### 5.1 Home Page (Public)

```dart
class HomePage extends StatelessWidget {
  static const String name = '/';

  const HomePage({super.key});

  Route<void> route() {
    return MaterialPageRoute(
      settings: const RouteSettings(name: name),
      builder: (_) => this,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Welcome'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('Welcome to My Application'),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () => nav.GlobalNavigator.goTo('/login'),
              child: const Text('Login'),
            ),
          ],
        ),
      ),
    );
  }
}
```

### 5.2 Dashboard Page (Private)

```dart
class DashboardPage extends StatelessWidget {
  const DashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('Dashboard'),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () => UserIdentityFeature.logout(),
              child: const Text('Logout'),
            ),
          ],
        ),
      ),
    );
  }
}
```

### 5.3 Settings Page (Private)

```dart
class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('Settings'),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () => _showProfileDialog(context),
              child: const Text('Edit Profile'),
            ),
          ],
        ),
      ),
    );
  }

  void _showProfileDialog(BuildContext context) {
    UserProfile.asModal(
      context,
      dismissOnTap: true,
    );
  }
}
```

## Step 6: Authentication Flow

### 6.1 Create Login Flow

```dart
class LoginViewFlow extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: FormInputGroup(
            inputs: [
              EmailInput(
                label: 'Email',
                onChanged: (value) => setState(() => email = value),
              ),
              PasswordInput(
                label: 'Password',
                onChanged: (value) => setState(() => password = value),
              ),
            ],
            onSubmit: () => _handleLogin(),
          ),
        ),
      ),
    );
  }

  Future<void> _handleLogin() async {
    try {
      final identityService = GetIt.instance.get<IdentityService>();
      await identityService.signIn(email, password);
      nav.GlobalNavigator.goTo('/dashboard');
    } catch (e) {
      // Handle login error
      print('Login failed: $e');
    }
  }
}
```

## Step 7: Feature Integration

### 7.1 Use Feature Services

```dart
class DashboardPage extends StatelessWidget {
  const DashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<IdentityService, IdentityState>(
      builder: (context, state) {
        if (state.isLoggedIn) {
          return _buildDashboard(context, state.user);
        } else {
          return _buildLoading();
        }
      },
    );
  }

  Widget _buildDashboard(BuildContext context, User user) {
    return Scaffold(
      body: Column(
        children: [
          // User profile section
          ListTile(
            leading: CircleAvatar(
              backgroundImage: AssetImage('assets/default_avatar.png'),
            ),
            title: Text(user.name),
            subtitle: Text(user.email),
            trailing: IconButton(
              icon: const Icon(Icons.logout),
              onPressed: () => UserIdentityFeature.logout(),
            ),
          ),
          
          // Organization selector
          if (OrganizationFeature.isEnabled)
            OrganizationSelector(
              onOrganizationChanged: (org) {
                OrganizationFeature.setCurrentOrganization(org.id);
              },
            ),
          
          // Billing status
          if (BillingFeature.isEnabled)
            BillingStatusCard(),
        ],
      ),
    );
  }

  Widget _buildLoading() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }
}
```

## Step 8: Error Handling

### 8.1 Global Error Handler

```dart
class ErrorHandler {
  static void handleError(BuildContext context, dynamic error) {
    final logger = Logger('ErrorHandler');
    
    if (error is NetworkException) {
      logger.severe('Network error: $error');
      _showNetworkErrorDialog(context);
    } else if (error is ValidationException) {
      logger.warning('Validation error: $error');
      _showValidationError(context, error.message);
    } else {
      logger.severe('Unexpected error: $error');
      _showGenericErrorDialog(context);
    }
  }

  static void _showNetworkErrorDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Network Error'),
        content: const Text('Please check your internet connection and try again.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  static void _showValidationError(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  static void _showGenericErrorDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: const Text('An unexpected error occurred. Please try again.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
```

## Step 9: Testing

### 9.1 Unit Tests

```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

void main() {
  group('Feature Integration Tests', () {
    test('UserIdentityFeature registration', () async {
      await UserIdentityFeature.register();
      expect(FeatureRegistry.isRegistered('user_identity'), isTrue);
    });

    test('Service injection', () {
      final identityService = GetIt.instance.get<IdentityService>();
      expect(identityService, isNotNull);
    });
  });
}
```

### 9.2 Widget Tests

```dart
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Navigation Tests', () {
    testWidgets('Navigation to dashboard', (tester) async {
      await tester.pumpWidget(MyApp());
      
      // Navigate to dashboard
      await tester.tap(find.text('Login'));
      await tester.pumpAndSettle();
      
      expect(find.text('Dashboard'), findsOneWidget);
    });
  });
}
```

## Best Practices

### 1. Feature Organization
- Keep features self-contained
- Use clear naming conventions
- Document feature dependencies

### 2. Service Management
- Register services as lazy singletons
- Implement proper disposal methods
- Use dependency injection consistently

### 3. State Management
- Use BLoC pattern for complex state
- Implement proper error handling
- Cache state appropriately

### 4. Navigation
- Use named routes consistently
- Implement proper authentication guards
- Handle deep linking appropriately

### 5. Error Handling
- Log errors with context
- Provide user-friendly error messages
- Implement retry mechanisms

### 6. Performance
- Use lazy loading for features
- Implement proper caching strategies
- Monitor memory usage

## Troubleshooting

### Common Issues

1. **Feature Registration Fails**
   - Check configuration files exist in assets
   - Verify JSON format is valid
   - Ensure feature dependencies are registered

2. **Service Injection Errors**
   - Verify services are registered before use
   - Check for circular dependencies
   - Ensure proper disposal

3. **Navigation Issues**
   - Check route definitions
   - Verify authentication state
   - Ensure proper navigator keys

4. **State Persistence Problems**
   - Check HydratedBloc configuration
   - Verify storage permissions
   - Test state serialization

### Debug Tips

1. **Enable Debug Logging**
```dart
initLogging(Level.ALL, logToConsole: true);
```

2. **Check Feature Registry**
```dart
final registry = FeatureRegistry.instance();
print('Registered features: ${registry.getFeatures()}');
```

3. **Monitor Service Registration**
```dart
print('Registered services: ${GetIt.instance.getAll()}');
```

## Next Steps

1. **Custom Features**: Create your own features following the established patterns
2. **Custom Services**: Implement additional service providers
3. **Custom Widgets**: Extend the UI widgets component
4. **Performance Optimization**: Implement caching and lazy loading
5. **Testing**: Add comprehensive test coverage
6. **Documentation**: Document your custom components

---

> 📖 **Navigation**: [← Back to Framework Overview](README.md) | [📋 Documentation Summary](summary.md) 