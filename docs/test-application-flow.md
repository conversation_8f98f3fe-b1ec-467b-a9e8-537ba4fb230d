# Test Application Flow Documentation

> 📖 **Navigation**: [← Back to Framework Overview](README.md) | [📋 Documentation Summary](summary.md)

## Overview

The test application at `apps/tests/mycs_feature_test` demonstrates how all the framework components work together to create a complete, functional application. This document provides a detailed walkthrough of the application startup and runtime flow.

## Application Startup Sequence

```mermaid
sequenceDiagram
    participant Main as Main Function
    participant Platform as AppPlatform
    participant Amplify as AWS Amplify
    participant Storage as HydratedStorage
    participant Registry as FeatureRegistry
    participant Services as Service Registration
    participant Features as Feature Registration
    participant App as MaterialApp

    Main->>Platform: Initialize AppPlatform
    Platform-->>Main: Platform ready

    Main->>Amplify: Configure Amplify
    Amplify-->>Main: Amplify configured

    Main->>Storage: Initialize HydratedStorage
    Storage-->>Main: Storage ready

    Main->>Registry: Initialize FeatureRegistry
    Registry-->>Main: Registry ready

    Main->>Services: Register Service Providers
    Services-->>Main: Services registered

    Main->>Features: Register Features
    Features->>Registry: Register UserIdentity
    Features->>Registry: Register Organization
    Features->>Registry: Register Billing
    Registry-->>Features: Features registered
    Features-->>Main: Features ready

    Main->>App: Create MaterialApp with AuthRouter
    App-->>Main: App created

    Main->>App: Run App
    App-->>Main: App running
```

## Authentication Flow

```mermaid
sequenceDiagram
    participant User as User
    participant AuthRouter as AuthRouter
    participant Identity as IdentityService
    participant Cognito as AWS Cognito
    participant App as App Navigation

    User->>AuthRouter: Access Protected Route
    AuthRouter->>Identity: Check Session Valid
    Identity->>Cognito: Validate Token
    Cognito-->>Identity: Token Valid/Invalid
    Identity-->>AuthRouter: Session Status

    alt Session Valid
        AuthRouter->>App: Navigate to Protected Route
        App-->>User: Show Protected Content
    else Session Invalid
        AuthRouter->>User: Redirect to Login
        User->>AuthRouter: Submit Credentials
        AuthRouter->>Identity: Authenticate User
        Identity->>Cognito: Sign In
        Cognito-->>Identity: Authentication Result
        Identity-->>AuthRouter: Auth Success/Failure
        
        alt Auth Success
            AuthRouter->>App: Navigate to Protected Route
            App-->>User: Show Protected Content
        else Auth Failure
            AuthRouter->>User: Show Error Message
        end
    end
```

## Feature Integration Flow

```mermaid
sequenceDiagram
    participant App as Application
    participant Registry as FeatureRegistry
    participant UIF as UserIdentityFeature
    participant ORGF as OrganizationFeature
    participant BILLF as BillingFeature
    participant Services as Service Layer

    App->>Registry: Get Feature Services
    Registry->>UIF: Provide Identity Service
    Registry->>ORGF: Provide Organization Service
    Registry->>BILLF: Provide Billing Service

    App->>UIF: User Profile Action
    UIF->>Services: Get User Data
    Services-->>UIF: User Data
    UIF-->>App: Update UI

    App->>ORGF: Organization Switch
    ORGF->>Services: Switch Organization
    Services-->>ORGF: Organization Data
    ORGF-->>App: Update Navigation

    App->>BILLF: Billing Action
    BILLF->>Services: Process Payment
    Services-->>BILLF: Payment Result
    BILLF-->>App: Update Billing UI
```

## Navigation and Layout Flow

```mermaid
sequenceDiagram
    participant User as User
    participant Nav as Navigation System
    participant Layout as AppNavLayout
    participant Tools as Title/Status Tools
    participant Features as Feature Anchors

    User->>Nav: Navigate to Route
    Nav->>Layout: Build Navigation Layout
    Layout->>Tools: Build Title Bar
    Layout->>Tools: Build Status Bar
    Tools->>Features: Get Feature Anchors
    Features-->>Tools: Feature Tools
    Tools-->>Layout: Complete Tool Bars
    Layout-->>Nav: Navigation Layout Ready
    Nav-->>User: Display Navigation

    User->>Tools: Interact with Tool
    Tools->>Features: Execute Feature Action
    Features-->>Tools: Action Result
    Tools-->>User: Update UI
```

## State Management Flow

```mermaid
sequenceDiagram
    participant UI as UI Layer
    participant BLoC as BLoC Layer
    participant Service as Service Layer
    participant Storage as HydratedStorage
    participant API as Backend API

    UI->>BLoC: User Action
    BLoC->>Service: Process Action
    Service->>API: API Call
    API-->>Service: Response
    Service-->>BLoC: Update State
    BLoC->>Storage: Persist State
    Storage-->>BLoC: State Saved
    BLoC-->>UI: Emit New State
    UI-->>User: Update UI

    Note over UI,Storage: State Restoration on App Restart
    UI->>Storage: Load Saved State
    Storage-->>UI: Restored State
    UI-->>User: Restore UI State
```

## Configuration Loading Flow

```mermaid
sequenceDiagram
    participant App as Application
    participant Registry as FeatureRegistry
    participant Loader as ConfigLoader
    participant Assets as Asset Bundle
    participant Feature as Feature

    App->>Registry: Initialize with ConfigLoader
    App->>Registry: Register Feature
    Registry->>Loader: Load Feature Config
    Loader->>Assets: Load JSON File
    Assets-->>Loader: JSON Content
    Loader->>Feature: Parse Configuration
    Feature-->>Loader: Config Object
    Loader-->>Registry: Feature Configuration
    Registry->>Feature: Initialize with Config
    Feature-->>Registry: Feature Ready
    Registry-->>App: Feature Registered
```

## Error Handling Flow

```mermaid
sequenceDiagram
    participant UI as UI Layer
    participant Service as Service Layer
    participant Logger as Logger
    participant ErrorHandler as ErrorHandler
    participant User as User

    UI->>Service: Service Call
    Service->>Logger: Log Operation
    Service->>ErrorHandler: Handle Potential Error
    
    alt Success
        Service-->>UI: Success Response
        UI-->>User: Update UI
    else Network Error
        ErrorHandler->>Logger: Log Network Error
        ErrorHandler-->>UI: Network Error
        UI-->>User: Show Network Error Dialog
    else Validation Error
        ErrorHandler->>Logger: Log Validation Error
        ErrorHandler-->>UI: Validation Error
        UI-->>User: Show Validation Error
    else Unexpected Error
        ErrorHandler->>Logger: Log Unexpected Error
        ErrorHandler-->>UI: Generic Error
        UI-->>User: Show Generic Error Dialog
    end
```

## Key Integration Points

### 1. Feature Registration
The application demonstrates how to register multiple features:
```dart
// Register the user identity feature
await UserIdentityFeature.register();
// Register the organization feature
await OrganizationFeature.register();
// Register the billing feature
await BillingFeature.register();
```

### 2. Service Injection
Services are injected using GetIt dependency injection:
```dart
GetIt.instance.registerLazySingleton<IdentityProvider>(
  () => AWSCognitoService(),
);
GetIt.instance.registerLazySingleton<BillingProvider>(
  () => UserSpaceBillingService(
    UserSpaceBillingApi(_createDioClient(userSpaceApiEndpoint)),
  ),
);
```

### 3. Navigation Setup
Authentication-based navigation is configured:
```dart
final authRouter = AuthRouter(
  navigatorKey: GlobalNavigator.key,
  initialLocation: '/',
  authRoute: LoginViewFlow(),
  publicRoutes: [const HomePage().route()],
  privateRoutes: [AppNavLayout().route()],
);
```

### 4. State Management
Persistent state management is initialized:
```dart
HydratedBloc.storage = await HydratedStorage.build(
  storageDirectory: kIsWeb
      ? HydratedStorageDirectory.web
      : HydratedStorageDirectory('${AppPlatform.appDataPath}/state'),
);
```

### 5. Feature Integration
Features are integrated into the application scope:
```dart
return featureRegistry.scope(
  context,
  child: MaterialApp.router(
    // ... app configuration
  ),
);
```

## Testing the Integration

The test application provides several ways to test the framework integration:

1. **Authentication Testing**: Login/logout flows with AWS Cognito
2. **Navigation Testing**: Multi-screen navigation with authentication
3. **Feature Testing**: Organization switching and billing operations
4. **State Persistence**: App restart with state restoration
5. **Error Handling**: Network errors and validation failures
6. **Responsive Design**: Different screen sizes and orientations

## Performance Considerations

The test application demonstrates several performance optimizations:

1. **Lazy Loading**: Services are registered as lazy singletons
2. **Caching**: HydratedBloc provides automatic state caching
3. **Async Operations**: Non-blocking UI with proper async handling
4. **Memory Management**: Proper disposal of resources and services
5. **Network Optimization**: HTTP client with timeouts and retry logic

---

> 📖 **Navigation**: [← Back to Framework Overview](README.md) | [📋 Documentation Summary](summary.md) 