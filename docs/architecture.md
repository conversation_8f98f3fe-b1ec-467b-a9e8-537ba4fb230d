# NovAssist Application Framework Architecture

> 📖 **Navigation**: [← Back to Framework Overview](README.md) | [📋 Documentation Summary](summary.md)

## High-Level Architecture

```mermaid
graph TB
    subgraph "Application Layer"
        App[Main Application]
        TestApp[Test Application]
    end
    
    subgraph "Feature Layer"
        UI[User Identity Feature]
        ORG[Organization Feature]
        BILL[Billing Feature]
    end
    
    subgraph "Component Layer"
        AF[App Framework Component]
        NAV[Navigation Layouts Component]
        UIW[UI Widgets Component]
        PU[Platform Utilities Component]
    end
    
    subgraph "Service Layer"
        IS[Identity Service]
        OS[Organization Service]
        BS[Billing Service]
    end
    
    subgraph "Utility Layer"
        UTIL[Utilities]
        FFI[FFI Helper]
        ASYNC[Async Helper]
    end
    
    subgraph "Platform Layer"
        FLUTTER[Flutter Framework]
        AWS[AWS Services]
        API[Backend APIs]
    end
    
    App --> UI
    App --> ORG
    App --> BILL
    TestApp --> UI
    TestApp --> ORG
    TestApp --> BILL
    
    UI --> AF
    ORG --> AF
    BILL --> AF
    
    AF --> NAV
    AF --> UIW
    AF --> PU
    
    UI --> IS
    ORG --> OS
    BILL --> BS
    
    AF --> UTIL
    PU --> UTIL
    UIW --> UTIL
    
    UTIL --> FFI
    UTIL --> ASYNC
    
    IS --> AWS
    OS --> API
    BS --> API
    
    AF --> FLUTTER
    NAV --> FLUTTER
    UIW --> FLUTTER
```

## Feature Registry Architecture

```mermaid
graph TB
    subgraph "Feature Registry"
        FR[FeatureRegistry]
        FC[Feature Container]
        FS[Feature Services]
    end
    
    subgraph "Features"
        F1[User Identity Feature]
        F2[Organization Feature]
        F3[Billing Feature]
    end
    
    subgraph "Services"
        S1[Identity Service]
        S2[Organization Service]
        S3[Billing Service]
    end
    
    subgraph "Configuration"
        C1[User Identity Config]
        C2[Organization Config]
        C3[Billing Config]
    end
    
    FR --> FC
    FC --> F1
    FC --> F2
    FC --> F3
    
    F1 --> S1
    F2 --> S2
    F3 --> S3
    
    F1 --> C1
    F2 --> C2
    F3 --> C3
    
    FS --> S1
    FS --> S2
    FS --> S3
```

## Navigation Architecture

```mermaid
graph TB
    subgraph "Navigation System"
        RN[Root Navigator]
        AN[Auth Router]
        GN[Global Navigator]
    end
    
    subgraph "Layouts"
        RNL[Root Nav Layout]
        ANL[App Nav Layout]
        NL[Nav Layout]
    end
    
    subgraph "Views"
        HV[Home View]
        AV[Auth View]
        PV[Private Views]
    end
    
    subgraph "Tools"
        TTB[Title Tool Bar]
        STB[Status Tool Bar]
        AT[App Tools]
    end
    
    RN --> AN
    AN --> GN
    
    RN --> RNL
    AN --> ANL
    ANL --> NL
    
    RNL --> HV
    ANL --> PV
    AN --> AV
    
    ANL --> TTB
    ANL --> STB
    TTB --> AT
    STB --> AT
```

## State Management Architecture

```mermaid
graph TB
    subgraph "State Management"
        HB[Hydrated Bloc]
        BC[Bloc Container]
        BS[Bloc Services]
    end
    
    subgraph "Features"
        UIF[User Identity Feature]
        ORGF[Organization Feature]
        BILLF[Billing Feature]
    end
    
    subgraph "Services"
        IS[Identity Service]
        OS[Organization Service]
        BS[Billing Service]
    end
    
    subgraph "Storage"
        LS[Local Storage]
        SS[Secure Storage]
        CS[Cache Storage]
    end
    
    HB --> BC
    BC --> BS
    
    BS --> UIF
    BS --> ORGF
    BS --> BILLF
    
    UIF --> IS
    ORGF --> OS
    BILLF --> BS
    
    IS --> LS
    IS --> SS
    OS --> CS
    BS --> CS
```

## Component Dependencies

```mermaid
graph LR
    subgraph "Core Dependencies"
        AF[App Framework]
        NAV[Navigation Layouts]
        UIW[UI Widgets]
        PU[Platform Utilities]
    end
    
    subgraph "Feature Dependencies"
        UI[User Identity]
        ORG[Organization]
        BILL[Billing]
    end
    
    subgraph "Service Dependencies"
        IS[Identity Service]
        OS[Organization Service]
        BS[Billing Service]
    end
    
    subgraph "Utility Dependencies"
        UTIL[Utilities]
        FFI[FFI Helper]
        ASYNC[Async Helper]
    end
    
    UI --> AF
    ORG --> AF
    BILL --> AF
    
    AF --> NAV
    AF --> UIW
    AF --> PU
    
    UI --> IS
    ORG --> OS
    BILL --> BS
    
    AF --> UTIL
    NAV --> UTIL
    UIW --> UTIL
    PU --> UTIL
    
    UTIL --> FFI
    UTIL --> ASYNC
```

## Data Flow Architecture

```mermaid
sequenceDiagram
    participant App as Application
    participant FR as Feature Registry
    participant F as Feature
    participant S as Service
    participant API as Backend API
    
    App->>FR: Register Features
    FR->>F: Initialize Features
    F->>S: Initialize Services
    
    App->>F: User Action
    F->>S: Process Request
    S->>API: API Call
    API->>S: Response
    S->>F: Update State
    F->>App: UI Update
    
    App->>FR: Get Feature Services
    FR->>F: Provide Services
    F->>App: Service Access
```

## Configuration Flow

```mermaid
sequenceDiagram
    participant App as Application
    participant FR as Feature Registry
    participant F as Feature
    participant Config as Configuration Loader
    
    App->>FR: Initialize with Config Loader
    App->>FR: Register Feature
    FR->>Config: Load Feature Config
    Config->>FR: Return Config JSON
    FR->>F: Initialize with Config
    F->>FR: Feature Ready
    FR->>App: Feature Registered
```

## Error Handling Architecture

```mermaid
graph TB
    subgraph "Error Handling"
        EH[Error Handler]
        EL[Error Logger]
        ER[Error Recovery]
    end
    
    subgraph "Application Layers"
        UI[UI Layer]
        BL[Business Logic]
        SL[Service Layer]
    end
    
    subgraph "Error Types"
        VE[Validation Errors]
        NE[Network Errors]
        SE[Service Errors]
        UE[User Errors]
    end
    
    UI --> EH
    BL --> EH
    SL --> EH
    
    EH --> EL
    EH --> ER
    
    VE --> EH
    NE --> EH
    SE --> EH
    UE --> EH
```

---

> 📖 **Navigation**: [← Back to Framework Overview](README.md) | [📋 Documentation Summary](summary.md) 