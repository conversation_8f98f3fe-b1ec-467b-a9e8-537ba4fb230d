# Non-Functional Capabilities Documentation

> 📖 **Navigation**: [← Back to Framework Overview](README.md) | [📋 Documentation Summary](summary.md)

## 1. Application Feature Management

### Overview
The Application Feature Management system provides a plugin-based architecture for managing functional capabilities. It uses the `FeatureRegistry` to register, configure, and manage features throughout the application lifecycle.

### Key Components

#### Feature Registry
- **Purpose**: Central registry for all application features
- **Location**: `libs/component/flutter/app_framework/lib/ux/feature_registry.dart`
- **Responsibilities**:
  - Feature registration and initialization
  - Configuration loading and management
  - Service dependency injection
  - Localization delegate aggregation

#### Feature Interface
```dart
abstract class Feature {
  String get name;
  List<LocalizationsDelegate> get localizationsDelegates;
  List<FeatureAnchor> anchors(BuildContext? context);
  Future<void> initialize(FeatureConfig? config, Map<String, Feature> registeredFeatures);
  Widget scope(BuildContext context, {Key? key, required Widget child});
  void dispose();
}
```

### Integration Example

```dart
// 1. Initialize Feature Registry
FeatureRegistry.intialize(featureConfigLoader);

// 2. Register Features
await UserIdentityFeature.register();
await OrganizationFeature.register();
await BillingFeature.register();

// 3. Use Feature Registry in App
final featureRegistry = FeatureRegistry.instance();
return featureRegistry.scope(
  context,
  child: MaterialApp.router(...),
);
```

### Configuration Management
Features are configured via JSON files loaded from assets:
```json
{
  "showLogoutOnTitleBar": true,
  "enableProfileManagement": true,
  "defaultAvatarImage": "assets/default_avatar.png"
}
```

## 2. Application Navigation

### Overview
The Navigation system provides a comprehensive routing and layout management solution with support for authentication, responsive layouts, and feature integration.

### Key Components

#### Auth Router
- **Purpose**: Handles authentication-based routing
- **Location**: `libs/component/flutter/nav_layouts/lib/navigation/root_nav_router.dart`
- **Features**:
  - Public and private route management
  - Authentication state checking
  - Automatic redirects

#### Navigation Layouts
- **Root Nav Layout**: Base layout for all navigation
- **App Nav Layout**: Application-specific navigation with title bar and status bar
- **Nav Layout**: Generic navigation container

### Integration Example

```dart
// 1. Create Auth Router
final authRouter = AuthRouter(
  navigatorKey: GlobalNavigator.key,
  initialLocation: '/',
  authRoute: LoginViewFlow(),
  publicRoutes: [const HomePage().route()],
  privateRoutes: [AppNavLayout().route()],
);

// 2. Create App Navigation Layout
class AppNavLayout extends nav.AppNavLayout {
  @override
  List<nav.NavTarget> get navTargets => [
    nav.NavTarget(
      body: const ApplicationsPage(),
      destBuilder: (_) => nav.NavDest(
        iconData: Icons.apps,
        label: 'Applications',
      ),
    ),
  ];

  @override
  List<Widget>? buildTitleBarActions(BuildContext context) {
    return [const ProfileMenu()];
  }
}
```

### Responsive Design
The navigation system automatically adapts to different screen sizes:
- **Mobile**: Bottom navigation with drawer
- **Desktop**: Side navigation with title bar
- **Tablet**: Adaptive layout based on screen size

## 3. Application State Management

### Overview
The state management system uses BLoC (Business Logic Component) pattern with HydratedBloc for persistent state storage.

### Key Components

#### Hydrated Bloc Storage
- **Purpose**: Persistent state management across app restarts
- **Location**: `libs/component/flutter/app_framework/lib/bloc/`
- **Features**:
  - Automatic state serialization
  - Cross-platform storage
  - Secure storage options

#### Service Layer
- **Identity Service**: Manages user authentication state
- **Organization Service**: Manages organization membership state
- **Billing Service**: Manages subscription and payment state

### Integration Example

```dart
// 1. Initialize Hydrated Storage
HydratedBloc.storage = await HydratedStorage.build(
  storageDirectory: kIsWeb
      ? HydratedStorageDirectory.web
      : HydratedStorageDirectory('${AppPlatform.appDataPath}/state'),
);

// 2. Register Services
GetIt.instance.registerLazySingleton<IdentityService>(
  () => IdentityService(authProvider),
);

// 3. Use in Features
class UserIdentityScope extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance.get<IdentityService>(),
      child: child,
    );
  }
}
```

## 4. Data Access Service Scaffolding

### Overview
The data access layer provides abstractions for backend service integration with support for authentication, caching, and error handling.

### Key Components

#### Service Providers
- **Identity Provider**: AWS Cognito integration
- **Organization Provider**: REST API integration
- **Billing Provider**: Payment API integration

#### HTTP Client Configuration
```dart
Dio _createDioClient(String endpoint) {
  final authProvider = GetIt.instance.get<IdentityProvider>();
  
  final dio = Dio(BaseOptions(
    baseUrl: endpoint,
    connectTimeout: Duration(seconds: 30),
    receiveTimeout: Duration(seconds: 30),
  ));
  
  dio.interceptors.add(authProvider.authInterceptor);
  return dio;
}
```

### Integration Example

```dart
// 1. Register Service Providers
GetIt.instance.registerLazySingleton<IdentityProvider>(
  () => AWSCognitoService(),
);

GetIt.instance.registerLazySingleton<BillingProvider>(
  () => UserSpaceBillingService(
    UserSpaceBillingApi(_createDioClient(userSpaceApiEndpoint)),
  ),
);

// 2. Use in Features
final billingService = GetIt.instance.get<BillingProvider>();
final subscription = await billingService.getCurrentSubscription();
```

## 5. UI Widgets for Various Use Cases

### Overview
The UI Widgets component provides a comprehensive set of reusable widgets for common application patterns.

### Key Widget Categories

#### Form Components
- **Text Input**: Validated text input with error handling
- **Password Input**: Secure password entry with visibility toggle
- **Email Input**: Email validation with format checking
- **Phone Input**: Phone number formatting and validation
- **Selection List**: Dropdown and multi-select components

#### Dialog Components
- **Alert Dialog**: Standard alert dialogs with actions
- **Form Dialog**: Modal forms with validation
- **Avatar Button**: User profile buttons with menus

#### Navigation Components
- **Tools App Bar**: Application toolbar with actions
- **Status View Bar**: Status bar with tools and indicators
- **Text Button Tool**: Toolbar buttons with text and icons

### Integration Example

```dart
// 1. Form with Validation
FormInputGroup(
  inputs: [
    TextInput(
      label: 'Email',
      validator: EmailAddressValidator(),
      onChanged: (value) => setState(() => email = value),
    ),
    PasswordInput(
      label: 'Password',
      validator: PasswordValidator(),
      onChanged: (value) => setState(() => password = value),
    ),
  ],
  onSubmit: () => _handleSubmit(),
);

// 2. Dialog with Form
AlertDialog(
  title: Text('Confirm Action'),
  content: Text('Are you sure you want to proceed?'),
  actions: [
    TextButton(
      onPressed: () => Navigator.pop(context),
      child: Text('Cancel'),
    ),
    ElevatedButton(
      onPressed: () => _handleConfirm(),
      child: Text('Confirm'),
    ),
  ],
);

// 3. Status Bar with Tools
StatusViewBar(
  toolsLeft: [
    TextButtonTool.statusbar(
      iconData: (_) => Icons.person,
      text: (_) => user.name,
      onPressed: (_) => _showProfile(),
    ),
  ],
);
```

## 6. Common Utilities for Asynchronous Process and FFI

### Overview
The utilities provide cross-cutting concerns for async operations, foreign function integration, and common application patterns.

### Key Utilities

#### Async Helper
- **Purpose**: Simplified async operation management
- **Location**: `libs/commons/dart/async_helper/`
- **Features**:
  - Async operation chaining
  - Error handling and retry logic
  - Progress tracking

#### FFI Helper
- **Purpose**: Foreign Function Interface utilities
- **Location**: `libs/commons/dart/ffi_helper/`
- **Features**:
  - Native code integration
  - Memory management
  - Platform-specific implementations

#### Cache Management
- **Purpose**: In-memory and persistent caching
- **Location**: `libs/commons/dart/utilities/lib/cache/`
- **Features**:
  - LRU eviction
  - TTL support
  - Multiple storage backends

### Integration Example

```dart
// 1. Async Operations
final result = await AsyncHelper.chain([
  () => _loadUserData(),
  (data) => _processUserData(data),
  (processed) => _saveUserData(processed),
]);

// 2. FFI Integration
final nativeLibrary = FFIHelper.loadLibrary('native_lib');
final nativeFunction = nativeLibrary.lookupFunction<
  Int32 Function(Int32),
  int Function(int)
>('native_function');

// 3. Caching
final cache = Cache<String, UserData>(
  storage: InMemoryStorage(),
  evictionAlgorithm: LRUEviction(),
  maxSize: 100,
);

final userData = await cache.get('user:123', 
  loader: () => _fetchUserData('123')
);
```

## 7. Logging and Error Handling Framework

### Overview
The logging and error handling framework provides comprehensive error management, logging, and debugging capabilities.

### Key Components

#### Logging System
- **Purpose**: Structured logging across the application
- **Location**: `libs/commons/dart/utilities/lib/logging/`
- **Features**:
  - Multiple log levels (DEBUG, INFO, WARNING, ERROR, SEVERE)
  - Console and file output
  - Structured logging with context

#### Error Handling
- **Purpose**: Centralized error management
- **Location**: `libs/commons/dart/utilities/lib/error/`
- **Features**:
  - Exception categorization
  - Error recovery strategies
  - User-friendly error messages

### Integration Example

```dart
// 1. Initialize Logging
initLogging(
  Level.ALL,
  logToConsole: true,
  logToFile: true,
  logDirectory: AppPlatform.appDataPath,
);

// 2. Use Logging
final logger = Logger('UserIdentityFeature');
logger.info('User logged in successfully', {'userId': user.id});

// 3. Error Handling
try {
  await _performRiskyOperation();
} on NetworkException catch (e) {
  logger.severe('Network error occurred', e);
  _showNetworkErrorDialog();
} on ValidationException catch (e) {
  logger.warning('Validation failed', e);
  _showValidationError(e.message);
} catch (e, stackTrace) {
  logger.severe('Unexpected error', e, stackTrace);
  _showGenericErrorDialog();
}
```

## Integration Patterns

### Feature Integration Pattern
1. **Register Feature**: Add to FeatureRegistry
2. **Configure Services**: Inject dependencies
3. **Setup Navigation**: Add routes and layouts
4. **Initialize State**: Setup BLoC providers
5. **Add UI Components**: Integrate widgets

### Service Integration Pattern
1. **Define Interface**: Create service abstraction
2. **Implement Provider**: Create concrete implementation
3. **Register Service**: Add to dependency injection
4. **Use in Features**: Access via GetIt

### Widget Integration Pattern
1. **Import Widgets**: Add to pubspec.yaml
2. **Use in Layouts**: Integrate into navigation
3. **Configure Styling**: Apply theme and styling
4. **Add Interactions**: Connect to business logic

---

> 📖 **Navigation**: [← Back to Framework Overview](README.md) | [📋 Documentation Summary](summary.md) 