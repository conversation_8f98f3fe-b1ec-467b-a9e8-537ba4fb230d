name: novassist_doctor
description: Novassist Doctor App - AI-Powered Patient Profile and Healthcare Chat Interface
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  
  # UI & Navigation
  cupertino_icons: ^1.0.6
  go_router: ^12.1.3
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  
  # State Management
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.3
  
  # HTTP & API
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1
  
  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  shared_preferences: ^2.2.2
  
  # Date & Time
  intl: ^0.19.0
  timeago: ^3.6.0
  
  # Charts & Visualization
  fl_chart: ^0.65.0
  
  # Chat & Messaging
  flutter_chat_ui: ^1.6.10
  uuid: ^4.2.1
  
  # Utils
  equatable: ^2.0.5
  freezed_annotation: ^2.4.1
  
  # Healthcare specific
  fhir: ^0.10.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  # Code Generation
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  riverpod_generator: ^2.3.9
  freezed: ^2.4.6
  retrofit_generator: ^8.0.4
  hive_generator: ^2.0.1
  
  # Linting
  flutter_lints: ^3.0.1
  very_good_analysis: ^5.1.0

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
  
  fonts:
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter-Regular.ttf
        - asset: assets/fonts/Inter-Medium.ttf
          weight: 500
        - asset: assets/fonts/Inter-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Inter-Bold.ttf
          weight: 700
