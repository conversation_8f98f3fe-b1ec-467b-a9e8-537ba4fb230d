import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/chat_message.dart';
import '../services/chat_service.dart';
import '../../patient/providers/patient_provider.dart';

// Chat service provider
final chatServiceProvider = Provider<ChatService>((ref) {
  return ChatService();
});

// Chat session providers
final chatSessionProvider =
    FutureProvider.family<ChatSession?, String>((ref, sessionId) async {
  final chatService = ref.watch(chatServiceProvider);
  return await chatService.getChatSession(sessionId);
});

final chatSessionsProvider = FutureProvider<List<ChatSession>>((ref) async {
  final chatService = ref.watch(chatServiceProvider);
  return await chatService.getAllChatSessions();
});

final patientChatSessionsProvider =
    FutureProvider.family<List<ChatSession>, String>((ref, patientId) async {
  final chatService = ref.watch(chatServiceProvider);
  return await chatService.getChatSessionsForPatient(patientId);
});

// Chat state management
final chatStateProvider = StateNotifierProvider<ChatNotifier, ChatState>((ref) {
  return ChatNotifier(ref);
});

class ChatNotifier extends StateNotifier<ChatState> {
  final Ref ref;

  ChatNotifier(this.ref) : super(const ChatState());

  Future<void> createSession(String sessionId, {String? patientId}) async {
    state = state.copyWith(isLoading: true);

    try {
      final chatService = ref.read(chatServiceProvider);

      // Get patient context if patientId is provided
      PatientContext? patientContext;
      if (patientId != null) {
        patientContext = await _buildPatientContext(patientId);
      }

      final session = ChatSession(
        id: sessionId,
        title: patientContext != null
            ? 'Chat with ${patientContext.patientName}'
            : 'General Healthcare Chat',
        patientId: patientId,
        createdAt: DateTime.now(),
        messages: [],
        status: SessionStatus.active,
      );

      await chatService.saveChatSession(session);

      // Add welcome message
      if (patientContext != null) {
        final welcomeMessage = ChatMessage(
          id: _generateId(),
          content:
              'Hello! I\'m your AI healthcare assistant. I have access to ${patientContext.patientName}\'s medical records and can help you with questions about their care. What would you like to know?',
          type: MessageType.system,
          sender: MessageSender.ai,
          timestamp: DateTime.now(),
          patientId: patientId,
          patientContext: patientContext,
          suggestedActions: _getInitialSuggestedActions(patientId!),
        );

        await sendMessage(sessionId, welcomeMessage);
      }

      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  Future<void> sendMessage(String sessionId, ChatMessage message) async {
    state = state.copyWith(isLoading: true);

    try {
      final chatService = ref.read(chatServiceProvider);

      // Add user message to session
      await chatService.addMessageToSession(sessionId, message);

      // If it's a user message, generate AI response
      if (message.sender == MessageSender.user) {
        await _generateAIResponse(sessionId, message);
      }

      // Refresh the session
      ref.invalidate(chatSessionProvider(sessionId));

      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  Future<void> _generateAIResponse(
      String sessionId, ChatMessage userMessage) async {
    final chatService = ref.read(chatServiceProvider);

    // Create thinking message
    final thinkingMessage = ChatMessage(
      id: _generateId(),
      content: 'Thinking...',
      type: MessageType.response,
      sender: MessageSender.ai,
      timestamp: DateTime.now(),
      patientId: userMessage.patientId,
      status: MessageStatus.processing,
    );

    await chatService.addMessageToSession(sessionId, thinkingMessage);
    ref.invalidate(chatSessionProvider(sessionId));

    try {
      // Get patient context for AI
      PatientContext? patientContext;
      if (userMessage.patientId != null) {
        patientContext = await _buildPatientContext(userMessage.patientId!);
      }

      // Generate AI response
      final aiResponse = await chatService.generateAIResponse(
        userMessage.content,
        patientContext: patientContext,
        conversationHistory: await _getConversationHistory(sessionId),
      );

      // Replace thinking message with actual response
      final responseMessage = ChatMessage(
        id: thinkingMessage.id,
        content: aiResponse.content,
        type: MessageType.response,
        sender: MessageSender.ai,
        timestamp: DateTime.now(),
        patientId: userMessage.patientId,
        patientContext: patientContext,
        suggestedActions: aiResponse.suggestedActions,
        status: MessageStatus.delivered,
      );

      await chatService.updateMessageInSession(sessionId, responseMessage);
      ref.invalidate(chatSessionProvider(sessionId));
    } catch (e) {
      // Replace thinking message with error
      final errorMessage = ChatMessage(
        id: thinkingMessage.id,
        content:
            'I apologize, but I encountered an error while processing your request. Please try again.',
        type: MessageType.error,
        sender: MessageSender.ai,
        timestamp: DateTime.now(),
        patientId: userMessage.patientId,
        status: MessageStatus.failed,
      );

      await chatService.updateMessageInSession(sessionId, errorMessage);
      ref.invalidate(chatSessionProvider(sessionId));
    }
  }

  Future<PatientContext> _buildPatientContext(String patientId) async {
    final patient = await ref.read(patientProvider(patientId).future);
    final labResults =
        await ref.read(recentLabResultsProvider(patientId).future);
    final medications =
        await ref.read(activeMedicationsProvider(patientId).future);

    if (patient == null) {
      throw Exception('Patient not found');
    }

    return PatientContext(
      patientId: patientId,
      patientName: patient.fullName,
      age: patient.age,
      gender: patient.gender,
      allergies: patient.allergies,
      chronicConditions: patient.chronicConditions,
      lastVisit: patient.lastVisit,
      currentMedications: medications.map((m) => m.displayName).toList(),
      recentLabResults:
          labResults.map((r) => '${r.testName}: ${r.displayValue}').toList(),
    );
  }

  Future<List<ChatMessage>> _getConversationHistory(String sessionId) async {
    final session = await ref.read(chatSessionProvider(sessionId).future);
    return session?.messages ?? [];
  }

  List<ChatAction> _getInitialSuggestedActions(String patientId) {
    return [
      ChatAction(
        id: _generateId(),
        label: 'Show recent lab results',
        type: ActionType.viewLabResults,
        icon: 'science',
      ),
      ChatAction(
        id: _generateId(),
        label: 'List current medications',
        type: ActionType.viewMedications,
        icon: 'medication',
      ),
      ChatAction(
        id: _generateId(),
        label: 'Summarize medical history',
        type: ActionType.viewNotes,
        icon: 'note_alt',
      ),
      ChatAction(
        id: _generateId(),
        label: 'Check for interactions',
        type: ActionType.searchSimilarCases,
        icon: 'warning',
      ),
    ];
  }

  Future<void> clearSession(String sessionId) async {
    state = state.copyWith(isLoading: true);

    try {
      final chatService = ref.read(chatServiceProvider);
      await chatService.clearChatSession(sessionId);
      ref.invalidate(chatSessionProvider(sessionId));
      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }
}

class ChatState {
  final bool isLoading;
  final String? error;

  const ChatState({
    this.isLoading = false,
    this.error,
  });

  ChatState copyWith({
    bool? isLoading,
    String? error,
  }) {
    return ChatState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// AI Response model
class AIResponse {
  final String content;
  final List<ChatAction>? suggestedActions;
  final Map<String, dynamic>? metadata;

  const AIResponse({
    required this.content,
    this.suggestedActions,
    this.metadata,
  });
}

// Convenience provider for accessing chat notifier
final chatProvider = StateNotifierProvider<ChatNotifier, ChatState>((ref) {
  return ChatNotifier(ref);
});
