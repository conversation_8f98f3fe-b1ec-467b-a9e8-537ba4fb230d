import 'package:flutter/material.dart';
import '../../../core/config/app_theme.dart';
import '../models/chat_message.dart';

class SuggestedActionsWidget extends StatelessWidget {
  final String patientId;
  final Function(ChatAction) onActionTap;
  
  const SuggestedActionsWidget({
    super.key,
    required this.patientId,
    required this.onActionTap,
  });

  @override
  Widget build(BuildContext context) {
    final actions = _getDefaultActions();
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(
          top: BorderSide(color: Colors.grey.shade300),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Actions',
            style: Theme.of(context).textTheme.labelMedium?.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: actions.map((action) {
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: ActionChip(
                    label: Text(action.label),
                    onPressed: () => onActionTap(action),
                    backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
                    labelStyle: TextStyle(
                      color: AppTheme.primaryColor,
                      fontSize: 12,
                    ),
                    avatar: Icon(
                      _getActionIcon(action.type),
                      size: 16,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }
  
  List<ChatAction> _getDefaultActions() {
    return [
      ChatAction(
        id: '1',
        label: 'Lab Results',
        type: ActionType.viewLabResults,
      ),
      ChatAction(
        id: '2',
        label: 'Medications',
        type: ActionType.viewMedications,
      ),
      ChatAction(
        id: '3',
        label: 'Medical Notes',
        type: ActionType.viewNotes,
      ),
      ChatAction(
        id: '4',
        label: 'Patient Profile',
        type: ActionType.viewPatientProfile,
      ),
      ChatAction(
        id: '5',
        label: 'Similar Cases',
        type: ActionType.searchSimilarCases,
      ),
    ];
  }
  
  IconData _getActionIcon(ActionType type) {
    switch (type) {
      case ActionType.viewLabResults:
        return Icons.science;
      case ActionType.viewMedications:
        return Icons.medication;
      case ActionType.viewNotes:
        return Icons.note_alt;
      case ActionType.scheduleAppointment:
        return Icons.calendar_today;
      case ActionType.orderLab:
        return Icons.add_task;
      case ActionType.prescribeMedication:
        return Icons.local_pharmacy;
      case ActionType.viewPatientProfile:
        return Icons.person;
      case ActionType.searchSimilarCases:
        return Icons.search;
      case ActionType.generateReport:
        return Icons.description;
    }
  }
}
