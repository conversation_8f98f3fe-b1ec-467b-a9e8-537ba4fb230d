import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/config/app_theme.dart';
import '../../patient/providers/patient_provider.dart';

class PatientContextWidget extends ConsumerWidget {
  final String patientId;
  final bool isExpanded;
  final ScrollController? scrollController;
  
  const PatientContextWidget({
    super.key,
    required this.patientId,
    this.isExpanded = false,
    this.scrollController,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final patientAsync = ref.watch(patientProvider(patientId));
    
    return patientAsync.when(
      data: (patient) => patient != null
          ? _buildPatientContext(context, patient)
          : _buildError(context, 'Patient not found'),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => _buildError(context, error.toString()),
    );
  }
  
  Widget _buildPatientContext(BuildContext context, patient) {
    if (isExpanded) {
      return _buildExpandedContext(context, patient);
    } else {
      return _buildCompactContext(context, patient);
    }
  }
  
  Widget _buildCompactContext(BuildContext context, patient) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withOpacity(0.1),
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300),
        ),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 16,
            backgroundColor: AppTheme.primaryColor,
            child: Text(
              '${patient.firstName[0]}${patient.lastName[0]}',
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  patient.fullName,
                  style: Theme.of(context).textTheme.labelLarge,
                ),
                Text(
                  'Age: ${patient.age} • ${patient.gender}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
          Icon(
            Icons.info_outline,
            color: AppTheme.primaryColor,
            size: 16,
          ),
        ],
      ),
    );
  }
  
  Widget _buildExpandedContext(BuildContext context, patient) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
        controller: scrollController,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Patient Context',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            
            // Basic info
            _buildInfoSection(
              context,
              'Basic Information',
              [
                'Name: ${patient.fullName}',
                'Age: ${patient.age}',
                'Gender: ${patient.gender}',
                'MRN: ${patient.mrn}',
                if (patient.bloodType != null) 'Blood Type: ${patient.bloodType}',
              ],
            ),
            
            // Allergies
            if (patient.allergies?.isNotEmpty == true)
              _buildInfoSection(
                context,
                'Allergies',
                patient.allergies!,
                isWarning: true,
              ),
            
            // Chronic conditions
            if (patient.chronicConditions.isNotEmpty)
              _buildInfoSection(
                context,
                'Chronic Conditions',
                patient.chronicConditions,
              ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildInfoSection(
    BuildContext context,
    String title,
    List<String> items, {
    bool isWarning = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            if (isWarning)
              Icon(
                Icons.warning,
                color: AppTheme.errorColor,
                size: 16,
              ),
            if (isWarning) const SizedBox(width: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.labelLarge?.copyWith(
                color: isWarning ? AppTheme.errorColor : null,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ...items.map((item) => Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Text(
            '• $item',
            style: Theme.of(context).textTheme.bodySmall,
          ),
        )),
        const SizedBox(height: 16),
      ],
    );
  }
  
  Widget _buildError(BuildContext context, String error) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Text(
        'Error: $error',
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: AppTheme.errorColor,
        ),
      ),
    );
  }
}
