import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/config/app_theme.dart';
import '../models/chat_message.dart';
import '../providers/chat_provider.dart';
import '../widgets/chat_message_widget.dart';
import '../widgets/chat_input_widget.dart';
import '../widgets/patient_context_widget.dart';
import '../widgets/suggested_actions_widget.dart';
import '../../patient/providers/patient_provider.dart';

class ChatScreen extends ConsumerStatefulWidget {
  final String? patientId;
  final String? sessionId;

  const ChatScreen({
    super.key,
    this.patientId,
    this.sessionId,
  });

  @override
  ConsumerState<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends ConsumerState<ChatScreen> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _messageController = TextEditingController();
  late String _currentSessionId;

  @override
  void initState() {
    super.initState();
    _currentSessionId =
        widget.sessionId ?? DateTime.now().millisecondsSinceEpoch.toString();

    // Initialize chat session if it doesn't exist
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.sessionId == null) {
        ref.read(chatProvider.notifier).createSession(
              _currentSessionId,
              patientId: widget.patientId,
            );
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final chatSession = ref.watch(chatSessionProvider(_currentSessionId));
    final patient = widget.patientId != null
        ? ref.watch(patientProvider(widget.patientId!))
        : null;

    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('AI Assistant'),
            if (patient?.hasValue == true && patient!.value != null)
              Text(
                patient.value!.fullName,
                style: Theme.of(context).textTheme.bodySmall,
              ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () => _showPatientContext(context),
          ),
          PopupMenuButton<String>(
            onSelected: (value) => _handleMenuAction(value),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'clear',
                child: Text('Clear Chat'),
              ),
              const PopupMenuItem(
                value: 'export',
                child: Text('Export Chat'),
              ),
              const PopupMenuItem(
                value: 'settings',
                child: Text('Settings'),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Patient context banner
          if (widget.patientId != null)
            PatientContextWidget(patientId: widget.patientId!),

          // Chat messages
          Expanded(
            child: chatSession.when(
              data: (session) => session != null
                  ? _buildChatMessages(session.messages)
                  : _buildEmptyChat(),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => _buildErrorState(error.toString()),
            ),
          ),

          // Suggested actions
          if (widget.patientId != null)
            SuggestedActionsWidget(
              patientId: widget.patientId!,
              onActionTap: _handleSuggestedAction,
            ),

          // Chat input
          ChatInputWidget(
            controller: _messageController,
            onSendMessage: _sendMessage,
            onAttachFile: _attachFile,
            isLoading: ref.watch(chatProvider).isLoading,
          ),
        ],
      ),
    );
  }

  Widget _buildChatMessages(List<ChatMessage> messages) {
    if (messages.isEmpty) {
      return _buildEmptyChat();
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: messages.length,
      itemBuilder: (context, index) {
        final message = messages[index];
        return ChatMessageWidget(
          message: message,
          onActionTap: _handleMessageAction,
        );
      },
    );
  }

  Widget _buildEmptyChat() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.smart_toy,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'AI Healthcare Assistant',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const SizedBox(height: 8),
          Text(
            widget.patientId != null
                ? 'Ask me anything about this patient\'s medical history,\nlab results, medications, or treatment plans.'
                : 'Ask me anything about healthcare, medical conditions,\ntreatments, or general medical questions.',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          _buildQuickStartButtons(),
        ],
      ),
    );
  }

  Widget _buildQuickStartButtons() {
    final quickActions = widget.patientId != null
        ? [
            'Show recent lab results',
            'List current medications',
            'Summarize medical history',
            'Check for drug interactions',
          ]
        : [
            'Explain a medical condition',
            'Drug information lookup',
            'Treatment guidelines',
            'Diagnostic criteria',
          ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: quickActions.map((action) {
        return ActionChip(
          label: Text(action),
          onPressed: () => _sendQuickAction(action),
          backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
          labelStyle: TextStyle(color: AppTheme.primaryColor),
        );
      }).toList(),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppTheme.errorColor,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading chat',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () =>
                ref.refresh(chatSessionProvider(_currentSessionId)),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _sendMessage(String content) {
    if (content.trim().isEmpty) return;

    final message = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content.trim(),
      type: MessageType.query,
      sender: MessageSender.user,
      timestamp: DateTime.now(),
      patientId: widget.patientId,
      status: MessageStatus.sending,
    );

    ref.read(chatProvider.notifier).sendMessage(_currentSessionId, message);
    _messageController.clear();
    _scrollToBottom();
  }

  void _sendQuickAction(String action) {
    _messageController.text = action;
    _sendMessage(action);
  }

  void _attachFile() {
    // TODO: Implement file attachment
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('File attachment coming soon')),
    );
  }

  void _handleSuggestedAction(ChatAction action) {
    switch (action.type) {
      case ActionType.viewLabResults:
        _sendQuickAction('Show recent lab results for this patient');
        break;
      case ActionType.viewMedications:
        _sendQuickAction('List current medications for this patient');
        break;
      case ActionType.viewNotes:
        _sendQuickAction('Summarize recent medical notes');
        break;
      case ActionType.searchSimilarCases:
        _sendQuickAction('Find similar cases to this patient');
        break;
      default:
        _sendQuickAction(action.label);
    }
  }

  void _handleMessageAction(ChatAction action) {
    // Handle actions from chat messages
    _handleSuggestedAction(action);
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'clear':
        _clearChat();
        break;
      case 'export':
        _exportChat();
        break;
      case 'settings':
        _showSettings();
        break;
    }
  }

  void _clearChat() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Chat'),
        content: const Text(
            'Are you sure you want to clear this chat? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              ref.read(chatProvider.notifier).clearSession(_currentSessionId);
              Navigator.of(context).pop();
            },
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _exportChat() {
    // TODO: Implement chat export
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Chat export coming soon')),
    );
  }

  void _showSettings() {
    // TODO: Implement chat settings
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Chat settings coming soon')),
    );
  }

  void _showPatientContext(BuildContext context) {
    if (widget.patientId == null) return;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => PatientContextWidget(
          patientId: widget.patientId!,
          isExpanded: true,
          scrollController: scrollController,
        ),
      ),
    );
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }
}
