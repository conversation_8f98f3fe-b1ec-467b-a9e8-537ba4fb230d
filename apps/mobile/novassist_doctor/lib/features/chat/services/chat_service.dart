import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:uuid/uuid.dart';

import '../../../core/config/app_config.dart';
import '../../../core/services/storage_service.dart';
import '../models/chat_message.dart';
import '../providers/chat_provider.dart';

class ChatService {
  late final Dio _dio;
  
  ChatService() {
    _dio = Dio(BaseOptions(
      baseUrl: AppConfig.chatApiUrl,
      connectTimeout: AppConfig.apiTimeout,
      receiveTimeout: AppConfig.apiTimeout,
      headers: {
        'Authorization': 'Bearer ${AppConfig.apiKey}',
        'Content-Type': 'application/json',
      },
    ));
    
    _dio.interceptors.add(LogInterceptor(
      requestBody: AppConfig.isDebugMode,
      responseBody: AppConfig.isDebugMode,
    ));
  }
  
  // Chat session operations
  Future<ChatSession?> getChatSession(String sessionId) async {
    return StorageService.getChatSession(sessionId);
  }
  
  Future<List<ChatSession>> getAllChatSessions() async {
    return StorageService.getAllChatSessions();
  }
  
  Future<List<ChatSession>> getChatSessionsForPatient(String patientId) async {
    return StorageService.getChatSessionsForPatient(patientId);
  }
  
  Future<void> saveChatSession(ChatSession session) async {
    await StorageService.saveChatSession(session);
  }
  
  Future<void> addMessageToSession(String sessionId, ChatMessage message) async {
    final session = await getChatSession(sessionId);
    if (session != null) {
      final updatedMessages = [...session.messages, message];
      final updatedSession = session.copyWith(
        messages: updatedMessages,
        updatedAt: DateTime.now(),
      );
      await saveChatSession(updatedSession);
    }
  }
  
  Future<void> updateMessageInSession(String sessionId, ChatMessage message) async {
    final session = await getChatSession(sessionId);
    if (session != null) {
      final updatedMessages = session.messages.map((m) {
        return m.id == message.id ? message : m;
      }).toList();
      
      final updatedSession = session.copyWith(
        messages: updatedMessages,
        updatedAt: DateTime.now(),
      );
      await saveChatSession(updatedSession);
    }
  }
  
  Future<void> clearChatSession(String sessionId) async {
    final session = await getChatSession(sessionId);
    if (session != null) {
      final clearedSession = session.copyWith(
        messages: [],
        updatedAt: DateTime.now(),
      );
      await saveChatSession(clearedSession);
    }
  }
  
  // AI Response generation
  Future<AIResponse> generateAIResponse(
    String userMessage, {
    PatientContext? patientContext,
    List<ChatMessage>? conversationHistory,
  }) async {
    try {
      // Build the context for the AI
      final context = _buildAIContext(userMessage, patientContext, conversationHistory);
      
      // Call the AI API
      final response = await _dio.post(
        AppConfig.chatEndpoint,
        data: {
          'message': userMessage,
          'context': context,
          'patient_id': patientContext?.patientId,
          'conversation_history': conversationHistory?.map((m) => {
            'role': m.sender == MessageSender.user ? 'user' : 'assistant',
            'content': m.content,
            'timestamp': m.timestamp.toIso8601String(),
          }).toList(),
        },
      );
      
      if (response.statusCode == 200) {
        final data = response.data;
        return AIResponse(
          content: data['response'] ?? 'I apologize, but I couldn\'t generate a response.',
          suggestedActions: _parseSuggestedActions(data['suggested_actions']),
          metadata: data['metadata'],
        );
      }
      
      throw Exception('Failed to generate AI response');
    } catch (e) {
      // Fallback to local AI simulation
      return _generateLocalAIResponse(userMessage, patientContext);
    }
  }
  
  Map<String, dynamic> _buildAIContext(
    String userMessage,
    PatientContext? patientContext,
    List<ChatMessage>? conversationHistory,
  ) {
    final context = <String, dynamic>{
      'user_role': 'doctor',
      'system_prompt': '''You are a healthcare AI assistant helping doctors with patient care. 
You have access to patient medical records and should provide accurate, evidence-based information.
Always prioritize patient safety and recommend consulting with specialists when appropriate.
Be concise but thorough in your responses.''',
    };
    
    if (patientContext != null) {
      context['patient'] = {
        'name': patientContext.patientName,
        'age': patientContext.age,
        'gender': patientContext.gender,
        'allergies': patientContext.allergies,
        'chronic_conditions': patientContext.chronicConditions,
        'current_medications': patientContext.currentMedications,
        'recent_lab_results': patientContext.recentLabResults,
        'last_visit': patientContext.lastVisit?.toIso8601String(),
      };
    }
    
    return context;
  }
  
  List<ChatAction>? _parseSuggestedActions(dynamic actionsData) {
    if (actionsData == null) return null;
    
    try {
      final List<dynamic> actions = actionsData is List ? actionsData : [];
      return actions.map((action) {
        return ChatAction(
          id: const Uuid().v4(),
          label: action['label'] ?? '',
          type: _parseActionType(action['type']),
          data: action['data'],
          icon: action['icon'],
        );
      }).toList();
    } catch (e) {
      return null;
    }
  }
  
  ActionType _parseActionType(String? type) {
    switch (type) {
      case 'view_lab_results':
        return ActionType.viewLabResults;
      case 'view_medications':
        return ActionType.viewMedications;
      case 'view_notes':
        return ActionType.viewNotes;
      case 'schedule_appointment':
        return ActionType.scheduleAppointment;
      case 'order_lab':
        return ActionType.orderLab;
      case 'prescribe_medication':
        return ActionType.prescribeMedication;
      case 'view_patient_profile':
        return ActionType.viewPatientProfile;
      case 'search_similar_cases':
        return ActionType.searchSimilarCases;
      case 'generate_report':
        return ActionType.generateReport;
      default:
        return ActionType.viewPatientProfile;
    }
  }
  
  // Local AI simulation for offline/fallback scenarios
  AIResponse _generateLocalAIResponse(String userMessage, PatientContext? patientContext) {
    final lowercaseMessage = userMessage.toLowerCase();
    
    // Simple pattern matching for common queries
    if (lowercaseMessage.contains('lab result') || lowercaseMessage.contains('test result')) {
      return _generateLabResultsResponse(patientContext);
    } else if (lowercaseMessage.contains('medication') || lowercaseMessage.contains('drug')) {
      return _generateMedicationsResponse(patientContext);
    } else if (lowercaseMessage.contains('history') || lowercaseMessage.contains('summary')) {
      return _generateHistoryResponse(patientContext);
    } else if (lowercaseMessage.contains('interaction') || lowercaseMessage.contains('contraindication')) {
      return _generateInteractionResponse(patientContext);
    } else {
      return _generateGenericResponse(patientContext);
    }
  }
  
  AIResponse _generateLabResultsResponse(PatientContext? patientContext) {
    if (patientContext?.recentLabResults?.isNotEmpty == true) {
      final results = patientContext!.recentLabResults!.take(5).join('\n• ');
      return AIResponse(
        content: 'Here are the recent lab results for ${patientContext.patientName}:\n\n• $results\n\nWould you like me to analyze any specific values or trends?',
        suggestedActions: [
          ChatAction(
            id: const Uuid().v4(),
            label: 'Analyze trends',
            type: ActionType.viewLabResults,
          ),
          ChatAction(
            id: const Uuid().v4(),
            label: 'Check reference ranges',
            type: ActionType.viewLabResults,
          ),
        ],
      );
    } else {
      return const AIResponse(
        content: 'I don\'t see any recent lab results for this patient. Would you like me to help you order new tests?',
      );
    }
  }
  
  AIResponse _generateMedicationsResponse(PatientContext? patientContext) {
    if (patientContext?.currentMedications?.isNotEmpty == true) {
      final medications = patientContext!.currentMedications!.join('\n• ');
      return AIResponse(
        content: 'Current medications for ${patientContext.patientName}:\n\n• $medications\n\nWould you like me to check for interactions or side effects?',
        suggestedActions: [
          ChatAction(
            id: const Uuid().v4(),
            label: 'Check interactions',
            type: ActionType.searchSimilarCases,
          ),
          ChatAction(
            id: const Uuid().v4(),
            label: 'Review dosages',
            type: ActionType.viewMedications,
          ),
        ],
      );
    } else {
      return const AIResponse(
        content: 'This patient doesn\'t appear to have any active medications on record. Would you like to add a new prescription?',
      );
    }
  }
  
  AIResponse _generateHistoryResponse(PatientContext? patientContext) {
    if (patientContext != null) {
      final summary = '''
Medical Summary for ${patientContext.patientName}:

• Age: ${patientContext.age} years old
• Gender: ${patientContext.gender}
• Allergies: ${patientContext.allergies?.join(', ') ?? 'None documented'}
• Chronic Conditions: ${patientContext.chronicConditions?.join(', ') ?? 'None documented'}
• Last Visit: ${patientContext.lastVisit != null ? _formatDate(patientContext.lastVisit!) : 'No recent visits'}

Would you like me to provide more details about any specific aspect?
''';
      
      return AIResponse(
        content: summary,
        suggestedActions: [
          ChatAction(
            id: const Uuid().v4(),
            label: 'View detailed notes',
            type: ActionType.viewNotes,
          ),
          ChatAction(
            id: const Uuid().v4(),
            label: 'Check recent visits',
            type: ActionType.viewPatientProfile,
          ),
        ],
      );
    } else {
      return const AIResponse(
        content: 'I need a patient context to provide a medical history summary. Please select a patient first.',
      );
    }
  }
  
  AIResponse _generateInteractionResponse(PatientContext? patientContext) {
    return const AIResponse(
      content: 'I can help you check for drug interactions. Please note that this is for informational purposes only and should not replace clinical judgment. Always consult drug interaction databases and consider patient-specific factors.',
    );
  }
  
  AIResponse _generateGenericResponse(PatientContext? patientContext) {
    final patientName = patientContext?.patientName ?? 'the patient';
    return AIResponse(
      content: 'I\'m here to help you with $patientName\'s care. I can assist with reviewing lab results, medications, medical history, and answering clinical questions. What would you like to know?',
      suggestedActions: [
        ChatAction(
          id: const Uuid().v4(),
          label: 'Show lab results',
          type: ActionType.viewLabResults,
        ),
        ChatAction(
          id: const Uuid().v4(),
          label: 'List medications',
          type: ActionType.viewMedications,
        ),
        ChatAction(
          id: const Uuid().v4(),
          label: 'Medical summary',
          type: ActionType.viewNotes,
        ),
      ],
    );
  }
  
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
