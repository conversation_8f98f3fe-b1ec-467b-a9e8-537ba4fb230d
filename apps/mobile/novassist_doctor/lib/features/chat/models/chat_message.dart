import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive/hive.dart';

part 'chat_message.freezed.dart';
part 'chat_message.g.dart';

@freezed
@HiveType(typeId: 14)
class ChatMessage with _$ChatMessage {
  const factory ChatMessage({
    @HiveField(0) required String id,
    @HiveField(1) required String content,
    @HiveField(2) required MessageType type,
    @HiveField(3) required MessageSender sender,
    @HiveField(4) required DateTime timestamp,
    @HiveField(5) String? patientId,
    @HiveField(6) List<String>? attachments,
    @HiveField(7) Map<String, dynamic>? metadata,
    @HiveField(8) String? replyToId,
    @HiveField(9) MessageStatus? status,
    @HiveField(10) List<ChatAction>? suggestedActions,
    @HiveField(11) PatientContext? patientContext,
  }) = _ChatMessage;

  factory ChatMessage.fromJson(Map<String, dynamic> json) => _$ChatMessageFromJson(json);
}

@HiveType(typeId: 15)
enum MessageType {
  @HiveField(0)
  text,
  @HiveField(1)
  query,
  @HiveField(2)
  response,
  @HiveField(3)
  system,
  @HiveField(4)
  error,
  @HiveField(5)
  suggestion,
  @HiveField(6)
  attachment,
}

@HiveType(typeId: 16)
enum MessageSender {
  @HiveField(0)
  user,
  @HiveField(1)
  ai,
  @HiveField(2)
  system,
}

@HiveType(typeId: 17)
enum MessageStatus {
  @HiveField(0)
  sending,
  @HiveField(1)
  sent,
  @HiveField(2)
  delivered,
  @HiveField(3)
  failed,
  @HiveField(4)
  processing,
}

@freezed
@HiveType(typeId: 18)
class ChatAction with _$ChatAction {
  const factory ChatAction({
    @HiveField(0) required String id,
    @HiveField(1) required String label,
    @HiveField(2) required ActionType type,
    @HiveField(3) Map<String, dynamic>? data,
    @HiveField(4) String? icon,
  }) = _ChatAction;

  factory ChatAction.fromJson(Map<String, dynamic> json) => _$ChatActionFromJson(json);
}

@HiveType(typeId: 19)
enum ActionType {
  @HiveField(0)
  viewLabResults,
  @HiveField(1)
  viewMedications,
  @HiveField(2)
  viewNotes,
  @HiveField(3)
  scheduleAppointment,
  @HiveField(4)
  orderLab,
  @HiveField(5)
  prescribeMedication,
  @HiveField(6)
  viewPatientProfile,
  @HiveField(7)
  searchSimilarCases,
  @HiveField(8)
  generateReport,
}

@freezed
@HiveType(typeId: 20)
class PatientContext with _$PatientContext {
  const factory PatientContext({
    @HiveField(0) required String patientId,
    @HiveField(1) required String patientName,
    @HiveField(2) int? age,
    @HiveField(3) String? gender,
    @HiveField(4) List<String>? recentDiagnoses,
    @HiveField(5) List<String>? currentMedications,
    @HiveField(6) List<String>? allergies,
    @HiveField(7) List<String>? chronicConditions,
    @HiveField(8) DateTime? lastVisit,
    @HiveField(9) List<String>? recentLabResults,
  }) = _PatientContext;

  factory PatientContext.fromJson(Map<String, dynamic> json) => _$PatientContextFromJson(json);
}

@freezed
@HiveType(typeId: 21)
class ChatSession with _$ChatSession {
  const factory ChatSession({
    @HiveField(0) required String id,
    @HiveField(1) required String title,
    @HiveField(2) String? patientId,
    @HiveField(3) required DateTime createdAt,
    @HiveField(4) DateTime? updatedAt,
    @HiveField(5) required List<ChatMessage> messages,
    @HiveField(6) SessionStatus? status,
    @HiveField(7) Map<String, dynamic>? metadata,
    @HiveField(8) List<String>? tags,
  }) = _ChatSession;

  factory ChatSession.fromJson(Map<String, dynamic> json) => _$ChatSessionFromJson(json);
}

@HiveType(typeId: 22)
enum SessionStatus {
  @HiveField(0)
  active,
  @HiveField(1)
  archived,
  @HiveField(2)
  deleted,
}

extension ChatMessageExtensions on ChatMessage {
  bool get isFromUser => sender == MessageSender.user;
  
  bool get isFromAI => sender == MessageSender.ai;
  
  bool get isSystemMessage => sender == MessageSender.system;
  
  bool get hasAttachments => attachments?.isNotEmpty ?? false;
  
  bool get hasSuggestedActions => suggestedActions?.isNotEmpty ?? false;
  
  bool get isError => type == MessageType.error;
  
  bool get isProcessing => status == MessageStatus.processing;
  
  String get displayTime {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}
