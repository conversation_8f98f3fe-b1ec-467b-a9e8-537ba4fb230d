enum MessageType {
  text,
  query,
  response,
  system,
  error,
  suggestion,
  attachment,
}

enum MessageSender {
  user,
  ai,
  system,
}

enum MessageStatus {
  sending,
  sent,
  delivered,
  failed,
  processing,
}

enum ActionType {
  viewLabResults,
  viewMedications,
  viewNotes,
  scheduleAppointment,
  orderLab,
  prescribeMedication,
  viewPatientProfile,
  searchSimilarCases,
  generateReport,
}

enum SessionStatus {
  active,
  archived,
  deleted,
}

class ChatMessage {
  final String id;
  final String content;
  final MessageType type;
  final MessageSender sender;
  final DateTime timestamp;
  final String? patientId;
  final List<String>? attachments;
  final Map<String, dynamic>? metadata;
  final String? replyToId;
  final MessageStatus? status;
  final List<ChatAction>? suggestedActions;
  final PatientContext? patientContext;

  const ChatMessage({
    required this.id,
    required this.content,
    required this.type,
    required this.sender,
    required this.timestamp,
    this.patientId,
    this.attachments,
    this.metadata,
    this.replyToId,
    this.status,
    this.suggestedActions,
    this.patientContext,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id'] as String,
      content: json['content'] as String,
      type: MessageType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => MessageType.text,
      ),
      sender: MessageSender.values.firstWhere(
        (e) => e.name == json['sender'],
        orElse: () => MessageSender.user,
      ),
      timestamp: DateTime.parse(json['timestamp'] as String),
      patientId: json['patientId'] as String?,
      attachments: (json['attachments'] as List<dynamic>?)?.cast<String>(),
      metadata: json['metadata'] as Map<String, dynamic>?,
      replyToId: json['replyToId'] as String?,
      status: json['status'] != null
          ? MessageStatus.values.firstWhere(
              (e) => e.name == json['status'],
              orElse: () => MessageStatus.sent,
            )
          : null,
      suggestedActions: (json['suggestedActions'] as List<dynamic>?)
          ?.map((e) => ChatAction.fromJson(e as Map<String, dynamic>))
          .toList(),
      patientContext: json['patientContext'] != null
          ? PatientContext.fromJson(
              json['patientContext'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'type': type.name,
      'sender': sender.name,
      'timestamp': timestamp.toIso8601String(),
      'patientId': patientId,
      'attachments': attachments,
      'metadata': metadata,
      'replyToId': replyToId,
      'status': status?.name,
      'suggestedActions': suggestedActions?.map((e) => e.toJson()).toList(),
      'patientContext': patientContext?.toJson(),
    };
  }

  bool get isFromUser => sender == MessageSender.user;

  bool get isFromAI => sender == MessageSender.ai;

  bool get isSystemMessage => sender == MessageSender.system;

  bool get hasAttachments => attachments?.isNotEmpty ?? false;

  bool get hasSuggestedActions => suggestedActions?.isNotEmpty ?? false;

  bool get isError => type == MessageType.error;

  bool get isProcessing => status == MessageStatus.processing;

  String get displayTime {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}

class ChatAction {
  final String id;
  final String label;
  final ActionType type;
  final Map<String, dynamic>? data;
  final String? icon;

  const ChatAction({
    required this.id,
    required this.label,
    required this.type,
    this.data,
    this.icon,
  });

  factory ChatAction.fromJson(Map<String, dynamic> json) {
    return ChatAction(
      id: json['id'] as String,
      label: json['label'] as String,
      type: ActionType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ActionType.viewPatientProfile,
      ),
      data: json['data'] as Map<String, dynamic>?,
      icon: json['icon'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'label': label,
      'type': type.name,
      'data': data,
      'icon': icon,
    };
  }
}

class PatientContext {
  final String patientId;
  final String patientName;
  final int? age;
  final String? gender;
  final List<String>? recentDiagnoses;
  final List<String>? currentMedications;
  final List<String>? allergies;
  final List<String>? chronicConditions;
  final DateTime? lastVisit;
  final List<String>? recentLabResults;

  const PatientContext({
    required this.patientId,
    required this.patientName,
    this.age,
    this.gender,
    this.recentDiagnoses,
    this.currentMedications,
    this.allergies,
    this.chronicConditions,
    this.lastVisit,
    this.recentLabResults,
  });

  factory PatientContext.fromJson(Map<String, dynamic> json) {
    return PatientContext(
      patientId: json['patientId'] as String,
      patientName: json['patientName'] as String,
      age: json['age'] as int?,
      gender: json['gender'] as String?,
      recentDiagnoses:
          (json['recentDiagnoses'] as List<dynamic>?)?.cast<String>(),
      currentMedications:
          (json['currentMedications'] as List<dynamic>?)?.cast<String>(),
      allergies: (json['allergies'] as List<dynamic>?)?.cast<String>(),
      chronicConditions:
          (json['chronicConditions'] as List<dynamic>?)?.cast<String>(),
      lastVisit: json['lastVisit'] != null
          ? DateTime.parse(json['lastVisit'] as String)
          : null,
      recentLabResults:
          (json['recentLabResults'] as List<dynamic>?)?.cast<String>(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'patientId': patientId,
      'patientName': patientName,
      'age': age,
      'gender': gender,
      'recentDiagnoses': recentDiagnoses,
      'currentMedications': currentMedications,
      'allergies': allergies,
      'chronicConditions': chronicConditions,
      'lastVisit': lastVisit?.toIso8601String(),
      'recentLabResults': recentLabResults,
    };
  }
}

class ChatSession {
  final String id;
  final String title;
  final String? patientId;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final List<ChatMessage> messages;
  final SessionStatus? status;
  final Map<String, dynamic>? metadata;
  final List<String>? tags;

  const ChatSession({
    required this.id,
    required this.title,
    this.patientId,
    required this.createdAt,
    this.updatedAt,
    required this.messages,
    this.status,
    this.metadata,
    this.tags,
  });

  factory ChatSession.fromJson(Map<String, dynamic> json) {
    return ChatSession(
      id: json['id'] as String,
      title: json['title'] as String,
      patientId: json['patientId'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
      messages: (json['messages'] as List<dynamic>)
          .map((e) => ChatMessage.fromJson(e as Map<String, dynamic>))
          .toList(),
      status: json['status'] != null
          ? SessionStatus.values.firstWhere(
              (e) => e.name == json['status'],
              orElse: () => SessionStatus.active,
            )
          : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
      tags: (json['tags'] as List<dynamic>?)?.cast<String>(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'patientId': patientId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'messages': messages.map((e) => e.toJson()).toList(),
      'status': status?.name,
      'metadata': metadata,
      'tags': tags,
    };
  }

  ChatSession copyWith({
    String? id,
    String? title,
    String? patientId,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<ChatMessage>? messages,
    SessionStatus? status,
    Map<String, dynamic>? metadata,
    List<String>? tags,
  }) {
    return ChatSession(
      id: id ?? this.id,
      title: title ?? this.title,
      patientId: patientId ?? this.patientId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      messages: messages ?? this.messages,
      status: status ?? this.status,
      metadata: metadata ?? this.metadata,
      tags: tags ?? this.tags,
    );
  }
}
