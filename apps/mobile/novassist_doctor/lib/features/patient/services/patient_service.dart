import 'dart:convert';
import 'package:dio/dio.dart';

import '../../../core/config/app_config.dart';
import '../../../core/services/storage_service.dart';
import '../models/patient.dart';
import '../models/lab_result.dart';
import '../models/medication.dart';
import '../models/medical_note.dart';

class PatientService {
  late final Dio _dio;
  
  PatientService() {
    _dio = Dio(BaseOptions(
      baseUrl: AppConfig.baseUrl,
      connectTimeout: AppConfig.apiTimeout,
      receiveTimeout: AppConfig.apiTimeout,
      headers: {
        'Authorization': 'Bearer ${AppConfig.apiKey}',
        'Content-Type': 'application/json',
      },
    ));
    
    // Add interceptors for logging and error handling
    _dio.interceptors.add(LogInterceptor(
      requestBody: AppConfig.isDebugMode,
      responseBody: AppConfig.isDebugMode,
    ));
  }
  
  // Patient operations
  Future<Patient?> getPatient(String patientId) async {
    try {
      // First check local storage
      final localPatient = StorageService.getPatient(patientId);
      if (localPatient != null) {
        return localPatient;
      }
      
      // If not found locally, fetch from API
      final response = await _dio.get('${AppConfig.patientsEndpoint}/$patientId');
      if (response.statusCode == 200) {
        final patient = Patient.fromJson(response.data);
        await StorageService.savePatient(patient);
        return patient;
      }
      return null;
    } catch (e) {
      // Return local data if API fails
      return StorageService.getPatient(patientId);
    }
  }
  
  Future<List<Patient>> getAllPatients() async {
    try {
      // Get local patients first
      final localPatients = StorageService.getAllPatients();
      
      // Try to sync with API
      final response = await _dio.get(AppConfig.patientsEndpoint);
      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['patients'] ?? response.data;
        final apiPatients = data.map((json) => Patient.fromJson(json)).toList();
        
        // Save to local storage
        for (final patient in apiPatients) {
          await StorageService.savePatient(patient);
        }
        
        return apiPatients;
      }
      
      return localPatients;
    } catch (e) {
      // Return local data if API fails
      return StorageService.getAllPatients();
    }
  }
  
  Future<Patient> createPatient(Patient patient) async {
    try {
      final response = await _dio.post(
        AppConfig.patientsEndpoint,
        data: patient.toJson(),
      );
      
      if (response.statusCode == 201) {
        final createdPatient = Patient.fromJson(response.data);
        await StorageService.savePatient(createdPatient);
        return createdPatient;
      }
      throw Exception('Failed to create patient');
    } catch (e) {
      // Save locally if API fails
      await StorageService.savePatient(patient);
      return patient;
    }
  }
  
  Future<Patient> updatePatient(Patient patient) async {
    try {
      final response = await _dio.put(
        '${AppConfig.patientsEndpoint}/${patient.id}',
        data: patient.toJson(),
      );
      
      if (response.statusCode == 200) {
        final updatedPatient = Patient.fromJson(response.data);
        await StorageService.savePatient(updatedPatient);
        return updatedPatient;
      }
      throw Exception('Failed to update patient');
    } catch (e) {
      // Save locally if API fails
      await StorageService.savePatient(patient);
      return patient;
    }
  }
  
  // Lab results operations
  Future<List<LabResult>> getLabResults(String patientId) async {
    try {
      // Get local results first
      final localResults = StorageService.getLabResultsForPatient(patientId);
      
      // Try to sync with API
      final response = await _dio.get(
        AppConfig.labResultsEndpoint,
        queryParameters: {'patientId': patientId},
      );
      
      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['results'] ?? response.data;
        final apiResults = data.map((json) => LabResult.fromJson(json)).toList();
        
        // Save to local storage
        for (final result in apiResults) {
          await StorageService.saveLabResult(result);
        }
        
        return apiResults;
      }
      
      return localResults;
    } catch (e) {
      return StorageService.getLabResultsForPatient(patientId);
    }
  }
  
  // Medication operations
  Future<List<Medication>> getMedications(String patientId) async {
    try {
      // Get local medications first
      final localMedications = StorageService.getMedicationsForPatient(patientId);
      
      // Try to sync with API
      final response = await _dio.get(
        AppConfig.medicationsEndpoint,
        queryParameters: {'patientId': patientId},
      );
      
      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['medications'] ?? response.data;
        final apiMedications = data.map((json) => Medication.fromJson(json)).toList();
        
        // Save to local storage
        for (final medication in apiMedications) {
          await StorageService.saveMedication(medication);
        }
        
        return apiMedications;
      }
      
      return localMedications;
    } catch (e) {
      return StorageService.getMedicationsForPatient(patientId);
    }
  }
  
  // Medical notes operations
  Future<List<MedicalNote>> getMedicalNotes(String patientId) async {
    try {
      // Get local notes first
      final localNotes = StorageService.getNotesForPatient(patientId);
      
      // Try to sync with API
      final response = await _dio.get(
        AppConfig.notesEndpoint,
        queryParameters: {'patientId': patientId},
      );
      
      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['notes'] ?? response.data;
        final apiNotes = data.map((json) => MedicalNote.fromJson(json)).toList();
        
        // Save to local storage
        for (final note in apiNotes) {
          await StorageService.saveMedicalNote(note);
        }
        
        return apiNotes;
      }
      
      return localNotes;
    } catch (e) {
      return StorageService.getNotesForPatient(patientId);
    }
  }
  
  // Search operations
  Future<List<Patient>> searchPatients(String query) async {
    try {
      final response = await _dio.get(
        '${AppConfig.patientsEndpoint}/search',
        queryParameters: {'q': query},
      );
      
      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['patients'] ?? response.data;
        return data.map((json) => Patient.fromJson(json)).toList();
      }
      
      // Fallback to local search
      final allPatients = StorageService.getAllPatients();
      final lowercaseQuery = query.toLowerCase();
      
      return allPatients.where((patient) {
        return patient.fullName.toLowerCase().contains(lowercaseQuery) ||
               patient.mrn.toLowerCase().contains(lowercaseQuery) ||
               (patient.email?.toLowerCase().contains(lowercaseQuery) ?? false);
      }).toList();
    } catch (e) {
      // Local search fallback
      final allPatients = StorageService.getAllPatients();
      final lowercaseQuery = query.toLowerCase();
      
      return allPatients.where((patient) {
        return patient.fullName.toLowerCase().contains(lowercaseQuery) ||
               patient.mrn.toLowerCase().contains(lowercaseQuery) ||
               (patient.email?.toLowerCase().contains(lowercaseQuery) ?? false);
      }).toList();
    }
  }
}
