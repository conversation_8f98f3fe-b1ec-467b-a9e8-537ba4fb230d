import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/config/app_theme.dart';
import '../models/medication.dart';
import '../providers/patient_provider.dart';

class MedicationsScreen extends ConsumerWidget {
  final String patientId;
  
  const MedicationsScreen({
    super.key,
    required this.patientId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final medicationsAsync = ref.watch(patientMedicationsProvider(patientId));
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Medications'),
      ),
      body: medicationsAsync.when(
        data: (medications) => medications.isNotEmpty
            ? _buildMedications(context, medications)
            : _buildEmptyState(context),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => _buildError(context, error.toString()),
      ),
    );
  }
  
  Widget _buildMedications(BuildContext context, List<Medication> medications) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: medications.length,
      itemBuilder: (context, index) {
        final medication = medications[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: _getStatusColor(medication.status),
              child: Icon(
                Icons.medication,
                color: Colors.white,
                size: 20,
              ),
            ),
            title: Text(medication.displayName),
            subtitle: Text('${medication.dosageInfo} • ${medication.statusDisplayName}'),
            trailing: medication.needsRefill
                ? Icon(Icons.warning, color: AppTheme.warningColor)
                : null,
          ),
        );
      },
    );
  }
  
  Widget _buildEmptyState(BuildContext context) {
    return const Center(
      child: Text('No medications found'),
    );
  }
  
  Widget _buildError(BuildContext context, String error) {
    return Center(
      child: Text('Error: $error'),
    );
  }
  
  Color _getStatusColor(MedicationStatus status) {
    switch (status) {
      case MedicationStatus.active:
        return AppTheme.normalColor;
      case MedicationStatus.discontinued:
        return Colors.grey;
      case MedicationStatus.completed:
        return AppTheme.primaryColor;
      case MedicationStatus.onHold:
        return AppTheme.warningColor;
      case MedicationStatus.cancelled:
        return AppTheme.errorColor;
    }
  }
}
