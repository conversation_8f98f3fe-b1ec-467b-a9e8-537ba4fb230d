import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/config/app_theme.dart';
import '../models/patient.dart';
import '../providers/patient_provider.dart';
import '../widgets/patient_header.dart';
import '../widgets/patient_demographics.dart';
import '../widgets/patient_vitals.dart';
import '../widgets/patient_allergies.dart';
import '../widgets/patient_conditions.dart';

class PatientProfileScreen extends ConsumerWidget {
  final String patientId;
  
  const PatientProfileScreen({
    super.key,
    required this.patientId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final patientAsync = ref.watch(patientProvider(patientId));
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Patient Profile'),
        actions: [
          IconButton(
            icon: const Icon(Icons.chat),
            onPressed: () => context.go('/patients/$patientId/chat'),
          ),
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              // TODO: Navigate to edit patient screen
            },
          ),
        ],
      ),
      body: patientAsync.when(
        data: (patient) => patient != null 
            ? _buildPatientProfile(context, patient)
            : _buildPatientNotFound(context),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => _buildError(context, error.toString()),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => context.go('/patients/$patientId/chat'),
        icon: const Icon(Icons.smart_toy),
        label: const Text('Ask AI'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }
  
  Widget _buildPatientProfile(BuildContext context, Patient patient) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Patient Header with photo and basic info
          PatientHeader(patient: patient),
          
          const SizedBox(height: 24),
          
          // Quick Actions
          _buildQuickActions(context, patient),
          
          const SizedBox(height: 24),
          
          // Demographics
          PatientDemographics(patient: patient),
          
          const SizedBox(height: 16),
          
          // Vital Signs
          PatientVitals(patient: patient),
          
          const SizedBox(height: 16),
          
          // Allergies
          PatientAllergies(patient: patient),
          
          const SizedBox(height: 16),
          
          // Chronic Conditions
          PatientConditions(patient: patient),
          
          const SizedBox(height: 16),
          
          // Recent Activity
          _buildRecentActivity(context, patient),
          
          const SizedBox(height: 80), // Space for FAB
        ],
      ),
    );
  }
  
  Widget _buildQuickActions(BuildContext context, Patient patient) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildActionButton(
                    context,
                    icon: Icons.science,
                    label: 'Lab Results',
                    onTap: () => context.go('/patients/${patient.id}/lab-results'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildActionButton(
                    context,
                    icon: Icons.medication,
                    label: 'Medications',
                    onTap: () => context.go('/patients/${patient.id}/medications'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildActionButton(
                    context,
                    icon: Icons.note_alt,
                    label: 'Notes',
                    onTap: () => context.go('/patients/${patient.id}/notes'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildActionButton(
                    context,
                    icon: Icons.chat,
                    label: 'AI Chat',
                    onTap: () => context.go('/patients/${patient.id}/chat'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Icon(icon, size: 32, color: AppTheme.primaryColor),
            const SizedBox(height: 8),
            Text(
              label,
              style: Theme.of(context).textTheme.labelMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildRecentActivity(BuildContext context, Patient patient) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recent Activity',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            _buildActivityItem(
              context,
              icon: Icons.calendar_today,
              title: 'Last Visit',
              subtitle: patient.lastVisit != null 
                  ? _formatDate(patient.lastVisit!)
                  : 'No recent visits',
              color: AppTheme.primaryColor,
            ),
            const Divider(),
            _buildActivityItem(
              context,
              icon: Icons.science,
              title: 'Recent Lab Results',
              subtitle: 'View latest test results',
              color: AppTheme.accentColor,
              onTap: () => context.go('/patients/${patient.id}/lab-results'),
            ),
            const Divider(),
            _buildActivityItem(
              context,
              icon: Icons.medication,
              title: 'Current Medications',
              subtitle: 'Review active prescriptions',
              color: AppTheme.warningColor,
              onTap: () => context.go('/patients/${patient.id}/medications'),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildActivityItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.labelLarge,
                  ),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ),
            if (onTap != null)
              Icon(Icons.chevron_right, color: Colors.grey.shade400),
          ],
        ),
      ),
    );
  }
  
  Widget _buildPatientNotFound(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person_off,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'Patient not found',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'The requested patient could not be found.',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }
  
  Widget _buildError(BuildContext context, String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppTheme.errorColor,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading patient',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
  
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
