import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/config/app_theme.dart';
import '../models/patient.dart';
import '../providers/patient_provider.dart';

class PatientDetailScreen extends ConsumerWidget {
  final String patientId;
  
  const PatientDetailScreen({
    super.key,
    required this.patientId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final patientAsync = ref.watch(patientProvider(patientId));
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Patient Details'),
        actions: [
          IconButton(
            icon: const Icon(Icons.chat),
            onPressed: () => context.go('/patients/$patientId/chat'),
          ),
        ],
      ),
      body: patientAsync.when(
        data: (patient) => patient != null 
            ? _buildPatientDetail(context, patient)
            : _buildPatientNotFound(context),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => _buildError(context, error.toString()),
      ),
    );
  }
  
  Widget _buildPatientDetail(BuildContext context, Patient patient) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Patient header
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: AppTheme.primaryColor,
                    child: Text(
                      '${patient.firstName[0]}${patient.lastName[0]}',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          patient.fullName,
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                        Text(
                          'Age: ${patient.age} • ${patient.gender}',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        Text(
                          'MRN: ${patient.mrn}',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Quick actions
          Row(
            children: [
              Expanded(
                child: _buildActionCard(
                  context,
                  'Profile',
                  Icons.person,
                  () => context.go('/patients/$patientId/profile'),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildActionCard(
                  context,
                  'Lab Results',
                  Icons.science,
                  () => context.go('/patients/$patientId/lab-results'),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          Row(
            children: [
              Expanded(
                child: _buildActionCard(
                  context,
                  'Medications',
                  Icons.medication,
                  () => context.go('/patients/$patientId/medications'),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildActionCard(
                  context,
                  'Notes',
                  Icons.note_alt,
                  () => context.go('/patients/$patientId/notes'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  Widget _buildActionCard(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(icon, size: 32, color: AppTheme.primaryColor),
              const SizedBox(height: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.labelMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildPatientNotFound(BuildContext context) {
    return const Center(
      child: Text('Patient not found'),
    );
  }
  
  Widget _buildError(BuildContext context, String error) {
    return Center(
      child: Text('Error: $error'),
    );
  }
}
