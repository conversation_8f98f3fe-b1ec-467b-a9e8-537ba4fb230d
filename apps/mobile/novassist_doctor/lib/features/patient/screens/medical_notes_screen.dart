import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/config/app_theme.dart';
import '../models/medical_note.dart';
import '../providers/patient_provider.dart';

class MedicalNotesScreen extends ConsumerWidget {
  final String patientId;
  
  const MedicalNotesScreen({
    super.key,
    required this.patientId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notesAsync = ref.watch(patientNotesProvider(patientId));
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Medical Notes'),
      ),
      body: notesAsync.when(
        data: (notes) => notes.isNotEmpty
            ? _buildNotes(context, notes)
            : _buildEmptyState(context),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => _buildError(context, error.toString()),
      ),
    );
  }
  
  Widget _buildNotes(BuildContext context, List<MedicalNote> notes) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: notes.length,
      itemBuilder: (context, index) {
        final note = notes[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: AppTheme.primaryColor,
              child: Icon(
                _getNoteIcon(note.type),
                color: Colors.white,
                size: 20,
              ),
            ),
            title: Text(note.title),
            subtitle: Text('${note.typeDisplayName} • ${_formatDate(note.createdAt)}'),
            onTap: () => _showNoteDetail(context, note),
          ),
        );
      },
    );
  }
  
  Widget _buildEmptyState(BuildContext context) {
    return const Center(
      child: Text('No medical notes found'),
    );
  }
  
  Widget _buildError(BuildContext context, String error) {
    return Center(
      child: Text('Error: $error'),
    );
  }
  
  IconData _getNoteIcon(NoteType type) {
    switch (type) {
      case NoteType.progress:
        return Icons.trending_up;
      case NoteType.admission:
        return Icons.login;
      case NoteType.discharge:
        return Icons.logout;
      case NoteType.consultation:
        return Icons.chat;
      case NoteType.procedure:
        return Icons.medical_services;
      case NoteType.nursing:
        return Icons.local_hospital;
      case NoteType.therapy:
        return Icons.healing;
      case NoteType.radiology:
        return Icons.camera_alt;
      case NoteType.pathology:
        return Icons.science;
      case NoteType.emergency:
        return Icons.emergency;
      case NoteType.followUp:
        return Icons.schedule;
      case NoteType.assessment:
        return Icons.assessment;
      case NoteType.plan:
        return Icons.assignment;
    }
  }
  
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
  
  void _showNoteDetail(BuildContext context, MedicalNote note) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(note.title),
        content: SingleChildScrollView(
          child: Text(note.content),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
