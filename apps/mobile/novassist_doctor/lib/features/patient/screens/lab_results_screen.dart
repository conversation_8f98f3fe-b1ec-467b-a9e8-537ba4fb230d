import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/config/app_theme.dart';
import '../models/lab_result.dart';
import '../providers/patient_provider.dart';

class LabResultsScreen extends ConsumerWidget {
  final String patientId;
  
  const LabResultsScreen({
    super.key,
    required this.patientId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final labResultsAsync = ref.watch(patientLabResultsProvider(patientId));
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Lab Results'),
      ),
      body: labResultsAsync.when(
        data: (results) => results.isNotEmpty
            ? _buildLabResults(context, results)
            : _buildEmptyState(context),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => _buildError(context, error.toString()),
      ),
    );
  }
  
  Widget _buildLabResults(BuildContext context, List<LabResult> results) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: results.length,
      itemBuilder: (context, index) {
        final result = results[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: _getStatusColor(result.status),
              child: Icon(
                _getStatusIcon(result.status),
                color: Colors.white,
                size: 20,
              ),
            ),
            title: Text(result.testName),
            subtitle: Text('${result.displayValue} • ${_formatDate(result.resultDate)}'),
            trailing: result.isAbnormal
                ? Icon(Icons.warning, color: AppTheme.warningColor)
                : null,
          ),
        );
      },
    );
  }
  
  Widget _buildEmptyState(BuildContext context) {
    return const Center(
      child: Text('No lab results found'),
    );
  }
  
  Widget _buildError(BuildContext context, String error) {
    return Center(
      child: Text('Error: $error'),
    );
  }
  
  Color _getStatusColor(LabResultStatus status) {
    switch (status) {
      case LabResultStatus.normal:
        return AppTheme.normalColor;
      case LabResultStatus.abnormal:
        return AppTheme.abnormalColor;
      case LabResultStatus.critical:
        return AppTheme.criticalColor;
      case LabResultStatus.pending:
        return Colors.grey;
      case LabResultStatus.cancelled:
        return Colors.grey;
    }
  }
  
  IconData _getStatusIcon(LabResultStatus status) {
    switch (status) {
      case LabResultStatus.normal:
        return Icons.check;
      case LabResultStatus.abnormal:
        return Icons.warning;
      case LabResultStatus.critical:
        return Icons.error;
      case LabResultStatus.pending:
        return Icons.schedule;
      case LabResultStatus.cancelled:
        return Icons.cancel;
    }
  }
  
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
