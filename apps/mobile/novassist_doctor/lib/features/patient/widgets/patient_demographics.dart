import 'package:flutter/material.dart';
import '../models/patient.dart';

class PatientDemographics extends StatelessWidget {
  final Patient patient;
  
  const PatientDemographics({
    super.key,
    required this.patient,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Demographics',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            _buildInfoRow(context, 'Date of Birth', _formatDate(patient.dateOfBirth)),
            _buildInfoRow(context, 'Gender', patient.gender),
            if (patient.email != null)
              _buildInfoRow(context, 'Email', patient.email!),
            if (patient.phone != null)
              _buildInfoRow(context, 'Phone', patient.phone!),
            if (patient.address != null)
              _buildInfoRow(context, 'Address', _formatAddress(patient.address!)),
            if (patient.emergencyContact != null)
              _buildInfoRow(context, 'Emergency Contact', patient.emergencyContact!),
            if (patient.emergencyContactPhone != null)
              _buildInfoRow(context, 'Emergency Phone', patient.emergencyContactPhone!),
            if (patient.insuranceProvider != null)
              _buildInfoRow(context, 'Insurance', patient.insuranceProvider!),
          ],
        ),
      ),
    );
  }
  
  Widget _buildInfoRow(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.labelMedium,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }
  
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
  
  String _formatAddress(Address address) {
    return '${address.street}, ${address.city}, ${address.state} ${address.zipCode}';
  }
}
