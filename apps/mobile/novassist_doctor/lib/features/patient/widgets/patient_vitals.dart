import 'package:flutter/material.dart';
import '../../../core/config/app_theme.dart';
import '../models/patient.dart';

class PatientVitals extends StatelessWidget {
  final Patient patient;
  
  const PatientVitals({
    super.key,
    required this.patient,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Vital Information',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildVitalCard(
                    context,
                    'Height',
                    patient.height != null ? '${patient.height!.toInt()} cm' : 'Not recorded',
                    Icons.height,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildVitalCard(
                    context,
                    'Weight',
                    patient.weight != null ? '${patient.weight!.toInt()} kg' : 'Not recorded',
                    Icons.monitor_weight,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildVitalCard(
                    context,
                    'BMI',
                    patient.bmi != null ? '${patient.bmi!.toStringAsFixed(1)}' : 'Not available',
                    Icons.analytics,
                    subtitle: patient.bmiCategory,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildVitalCard(
                    context,
                    'Blood Type',
                    patient.bloodType ?? 'Unknown',
                    Icons.bloodtype,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildVitalCard(
    BuildContext context,
    String title,
    String value,
    IconData icon, {
    String? subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: AppTheme.primaryColor),
              const SizedBox(width: 4),
              Text(
                title,
                style: Theme.of(context).textTheme.labelSmall,
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.labelLarge,
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 2),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: _getBMIColor(subtitle),
              ),
            ),
          ],
        ],
      ),
    );
  }
  
  Color _getBMIColor(String category) {
    switch (category.toLowerCase()) {
      case 'normal':
        return AppTheme.normalColor;
      case 'overweight':
        return AppTheme.warningColor;
      case 'obese':
        return AppTheme.criticalColor;
      case 'underweight':
        return AppTheme.abnormalColor;
      default:
        return Colors.grey;
    }
  }
}
