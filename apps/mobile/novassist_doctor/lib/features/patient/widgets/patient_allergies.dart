import 'package:flutter/material.dart';
import '../../../core/config/app_theme.dart';
import '../models/patient.dart';

class PatientAllergies extends StatelessWidget {
  final Patient patient;
  
  const PatientAllergies({
    super.key,
    required this.patient,
  });

  @override
  Widget build(BuildContext context) {
    final allergies = patient.allergies ?? [];
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.warning,
                  color: allergies.isNotEmpty ? AppTheme.errorColor : Colors.grey,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Allergies',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (allergies.isEmpty)
              Text(
                'No known allergies',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey.shade600,
                ),
              )
            else
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: allergies.map((allergy) {
                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: AppTheme.errorColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(color: AppTheme.errorColor.withOpacity(0.3)),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.warning,
                          size: 14,
                          color: AppTheme.errorColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          allergy,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppTheme.errorColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),
          ],
        ),
      ),
    );
  }
}
