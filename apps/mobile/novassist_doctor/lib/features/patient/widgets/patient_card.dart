import 'package:flutter/material.dart';
import '../../../core/config/app_theme.dart';
import '../models/patient.dart';

class PatientCard extends StatelessWidget {
  final Patient patient;
  final VoidCallback? onTap;
  final VoidCallback? onChatTap;
  
  const PatientCard({
    super.key,
    required this.patient,
    this.onTap,
    this.onChatTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Patient avatar
              CircleAvatar(
                radius: 24,
                backgroundColor: AppTheme.primaryColor,
                backgroundImage: patient.profileImageUrl != null
                    ? NetworkImage(patient.profileImageUrl!)
                    : null,
                child: patient.profileImageUrl == null
                    ? Text(
                        '${patient.firstName[0]}${patient.lastName[0]}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      )
                    : null,
              ),
              
              const SizedBox(width: 16),
              
              // Patient info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      patient.fullName,
                      style: Theme.of(context).textTheme.labelLarge,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Age: ${patient.age} • ${patient.gender}',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      'MRN: ${patient.mrn}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    if (patient.lastVisit != null) ...[
                      const SizedBox(height: 2),
                      Text(
                        'Last visit: ${_formatDate(patient.lastVisit!)}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              
              // Action buttons
              Column(
                children: [
                  IconButton(
                    icon: const Icon(Icons.chat),
                    onPressed: onChatTap,
                    color: AppTheme.primaryColor,
                    tooltip: 'Chat with AI',
                  ),
                  // Status indicators
                  if (patient.allergies?.isNotEmpty == true)
                    Icon(
                      Icons.warning,
                      size: 16,
                      color: AppTheme.errorColor,
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
