import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/services/storage_service.dart';
import '../models/patient.dart';
import '../models/lab_result.dart';
import '../models/medication.dart';
import '../models/medical_note.dart';
import '../services/patient_service.dart';

part 'patient_provider.g.dart';

// Patient providers
@riverpod
Future<Patient?> patient(PatientRef ref, String patientId) async {
  final patientService = ref.watch(patientServiceProvider);
  return await patientService.getPatient(patientId);
}

@riverpod
Future<List<Patient>> patients(PatientsRef ref) async {
  final patientService = ref.watch(patientServiceProvider);
  return await patientService.getAllPatients();
}

// Lab results providers
@riverpod
Future<List<LabResult>> patientLabResults(PatientLabResultsRef ref, String patientId) async {
  final patientService = ref.watch(patientServiceProvider);
  return await patientService.getLabResults(patientId);
}

@riverpod
Future<List<LabResult>> recentLabResults(RecentLabResultsRef ref, String patientId, {int limit = 5}) async {
  final allResults = await ref.watch(patientLabResultsProvider(patientId).future);
  return allResults.take(limit).toList();
}

@riverpod
Future<List<LabResult>> abnormalLabResults(AbnormalLabResultsRef ref, String patientId) async {
  final allResults = await ref.watch(patientLabResultsProvider(patientId).future);
  return allResults.where((result) => result.isAbnormal).toList();
}

// Medication providers
@riverpod
Future<List<Medication>> patientMedications(PatientMedicationsRef ref, String patientId) async {
  final patientService = ref.watch(patientServiceProvider);
  return await patientService.getMedications(patientId);
}

@riverpod
Future<List<Medication>> activeMedications(ActiveMedicationsRef ref, String patientId) async {
  final allMedications = await ref.watch(patientMedicationsProvider(patientId).future);
  return allMedications.where((med) => med.isActive).toList();
}

@riverpod
Future<List<Medication>> medicationsNeedingRefill(MedicationsNeedingRefillRef ref, String patientId) async {
  final activeMeds = await ref.watch(activeMedicationsProvider(patientId).future);
  return activeMeds.where((med) => med.needsRefill).toList();
}

// Medical notes providers
@riverpod
Future<List<MedicalNote>> patientNotes(PatientNotesRef ref, String patientId) async {
  final patientService = ref.watch(patientServiceProvider);
  return await patientService.getMedicalNotes(patientId);
}

@riverpod
Future<List<MedicalNote>> recentNotes(RecentNotesRef ref, String patientId, {int limit = 5}) async {
  final allNotes = await ref.watch(patientNotesProvider(patientId).future);
  return allNotes.take(limit).toList();
}

@riverpod
Future<List<MedicalNote>> notesByType(NotesByTypeRef ref, String patientId, NoteType type) async {
  final allNotes = await ref.watch(patientNotesProvider(patientId).future);
  return allNotes.where((note) => note.type == type).toList();
}

// Search and filter providers
@riverpod
Future<List<Patient>> searchPatients(SearchPatientsRef ref, String query) async {
  if (query.isEmpty) {
    return await ref.watch(patientsProvider.future);
  }
  
  final allPatients = await ref.watch(patientsProvider.future);
  final lowercaseQuery = query.toLowerCase();
  
  return allPatients.where((patient) {
    return patient.fullName.toLowerCase().contains(lowercaseQuery) ||
           patient.mrn.toLowerCase().contains(lowercaseQuery) ||
           (patient.email?.toLowerCase().contains(lowercaseQuery) ?? false);
  }).toList();
}

// Patient service provider
@riverpod
PatientService patientService(PatientServiceRef ref) {
  return PatientService();
}

// State management for UI
@riverpod
class PatientSelection extends _$PatientSelection {
  @override
  String? build() => null;
  
  void selectPatient(String patientId) {
    state = patientId;
  }
  
  void clearSelection() {
    state = null;
  }
}

@riverpod
class PatientSearchQuery extends _$PatientSearchQuery {
  @override
  String build() => '';
  
  void updateQuery(String query) {
    state = query;
  }
  
  void clearQuery() {
    state = '';
  }
}

// Computed providers for patient statistics
@riverpod
Future<PatientStats> patientStats(PatientStatsRef ref, String patientId) async {
  final patient = await ref.watch(patientProvider(patientId).future);
  final labResults = await ref.watch(patientLabResultsProvider(patientId).future);
  final medications = await ref.watch(patientMedicationsProvider(patientId).future);
  final notes = await ref.watch(patientNotesProvider(patientId).future);
  
  if (patient == null) {
    return const PatientStats(
      totalLabResults: 0,
      abnormalLabResults: 0,
      activeMedications: 0,
      totalNotes: 0,
      lastVisit: null,
    );
  }
  
  return PatientStats(
    totalLabResults: labResults.length,
    abnormalLabResults: labResults.where((r) => r.isAbnormal).length,
    activeMedications: medications.where((m) => m.isActive).length,
    totalNotes: notes.length,
    lastVisit: patient.lastVisit,
  );
}

class PatientStats {
  final int totalLabResults;
  final int abnormalLabResults;
  final int activeMedications;
  final int totalNotes;
  final DateTime? lastVisit;
  
  const PatientStats({
    required this.totalLabResults,
    required this.abnormalLabResults,
    required this.activeMedications,
    required this.totalNotes,
    required this.lastVisit,
  });
}
