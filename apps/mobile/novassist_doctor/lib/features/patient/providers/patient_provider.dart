import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/patient.dart';
import '../models/lab_result.dart';
import '../models/medication.dart';
import '../models/medical_note.dart';
import '../services/patient_service.dart';

// Patient service provider
final patientServiceProvider = Provider<PatientService>((ref) {
  return PatientService();
});

// Patient providers
final patientProvider =
    FutureProvider.family<Patient?, String>((ref, patientId) async {
  final patientService = ref.watch(patientServiceProvider);
  return await patientService.getPatient(patientId);
});

final patientsProvider = FutureProvider<List<Patient>>((ref) async {
  final patientService = ref.watch(patientServiceProvider);
  return await patientService.getAllPatients();
});

// Lab results providers
final patientLabResultsProvider =
    FutureProvider.family<List<LabResult>, String>((ref, patientId) async {
  final patientService = ref.watch(patientServiceProvider);
  return await patientService.getLabResults(patientId);
});

final recentLabResultsProvider =
    FutureProvider.family<List<LabResult>, String>((ref, patientId) async {
  final allResults =
      await ref.watch(patientLabResultsProvider(patientId).future);
  return allResults.take(5).toList();
});

final abnormalLabResultsProvider =
    FutureProvider.family<List<LabResult>, String>((ref, patientId) async {
  final allResults =
      await ref.watch(patientLabResultsProvider(patientId).future);
  return allResults.where((result) => result.isAbnormal).toList();
});

// Medication providers
final patientMedicationsProvider =
    FutureProvider.family<List<Medication>, String>((ref, patientId) async {
  final patientService = ref.watch(patientServiceProvider);
  return await patientService.getMedications(patientId);
});

final activeMedicationsProvider =
    FutureProvider.family<List<Medication>, String>((ref, patientId) async {
  final allMedications =
      await ref.watch(patientMedicationsProvider(patientId).future);
  return allMedications.where((med) => med.isActive).toList();
});

final medicationsNeedingRefillProvider =
    FutureProvider.family<List<Medication>, String>((ref, patientId) async {
  final activeMeds =
      await ref.watch(activeMedicationsProvider(patientId).future);
  return activeMeds.where((med) => med.needsRefill).toList();
});

// Medical notes providers
final patientNotesProvider =
    FutureProvider.family<List<MedicalNote>, String>((ref, patientId) async {
  final patientService = ref.watch(patientServiceProvider);
  return await patientService.getMedicalNotes(patientId);
});

final recentNotesProvider =
    FutureProvider.family<List<MedicalNote>, String>((ref, patientId) async {
  final allNotes = await ref.watch(patientNotesProvider(patientId).future);
  return allNotes.take(5).toList();
});

// Search and filter providers
final searchPatientsProvider =
    FutureProvider.family<List<Patient>, String>((ref, query) async {
  if (query.isEmpty) {
    return await ref.watch(patientsProvider.future);
  }

  final allPatients = await ref.watch(patientsProvider.future);
  final lowercaseQuery = query.toLowerCase();

  return allPatients.where((patient) {
    return patient.fullName.toLowerCase().contains(lowercaseQuery) ||
        patient.mrn.toLowerCase().contains(lowercaseQuery) ||
        (patient.email?.toLowerCase().contains(lowercaseQuery) ?? false);
  }).toList();
});

// State management for UI
final patientSelectionProvider = StateProvider<String?>((ref) => null);
final patientSearchQueryProvider = StateProvider<String>((ref) => '');

// Computed providers for patient statistics
final patientStatsProvider =
    FutureProvider.family<PatientStats, String>((ref, patientId) async {
  final patient = await ref.watch(patientProvider(patientId).future);
  final labResults =
      await ref.watch(patientLabResultsProvider(patientId).future);
  final medications =
      await ref.watch(patientMedicationsProvider(patientId).future);
  final notes = await ref.watch(patientNotesProvider(patientId).future);

  if (patient == null) {
    return const PatientStats(
      totalLabResults: 0,
      abnormalLabResults: 0,
      activeMedications: 0,
      totalNotes: 0,
      lastVisit: null,
    );
  }

  return PatientStats(
    totalLabResults: labResults.length,
    abnormalLabResults: labResults.where((r) => r.isAbnormal).length,
    activeMedications: medications.where((m) => m.isActive).length,
    totalNotes: notes.length,
    lastVisit: patient.lastVisit,
  );
});

class PatientStats {
  final int totalLabResults;
  final int abnormalLabResults;
  final int activeMedications;
  final int totalNotes;
  final DateTime? lastVisit;

  const PatientStats({
    required this.totalLabResults,
    required this.abnormalLabResults,
    required this.activeMedications,
    required this.totalNotes,
    required this.lastVisit,
  });
}
