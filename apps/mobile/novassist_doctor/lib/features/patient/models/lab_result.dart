import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive/hive.dart';

part 'lab_result.freezed.dart';
part 'lab_result.g.dart';

@freezed
@HiveType(typeId: 2)
class LabResult with _$LabResult {
  const factory LabResult({
    @HiveField(0) required String id,
    @HiveField(1) required String patientId,
    @HiveField(2) required String testName,
    @HiveField(3) required String testCode,
    @HiveField(4) required String value,
    @HiveField(5) required String unit,
    @HiveField(6) String? referenceRange,
    @HiveField(7) required LabResultStatus status,
    @HiveField(8) required DateTime collectionDate,
    @HiveField(9) required DateTime resultDate,
    @HiveField(10) String? orderingPhysician,
    @HiveField(11) String? laboratoryName,
    @HiveField(12) String? notes,
    @HiveField(13) String? category, // e.g., "Chemistry", "Hematology", "Microbiology"
    @HiveField(14) bool? isCritical,
    @HiveField(15) String? interpretation,
    @HiveField(16) List<String>? flags, // e.g., ["HIGH", "ABNORMAL"]
  }) = _LabResult;

  factory LabResult.fromJson(Map<String, dynamic> json) => _$LabResultFromJson(json);
}

@HiveType(typeId: 3)
enum LabResultStatus {
  @HiveField(0)
  normal,
  @HiveField(1)
  abnormal,
  @HiveField(2)
  critical,
  @HiveField(3)
  pending,
  @HiveField(4)
  cancelled,
}

@freezed
@HiveType(typeId: 4)
class LabPanel with _$LabPanel {
  const factory LabPanel({
    @HiveField(0) required String id,
    @HiveField(1) required String patientId,
    @HiveField(2) required String panelName,
    @HiveField(3) required List<LabResult> results,
    @HiveField(4) required DateTime orderDate,
    @HiveField(5) DateTime? completedDate,
    @HiveField(6) String? orderingPhysician,
    @HiveField(7) String? indication,
    @HiveField(8) String? notes,
  }) = _LabPanel;

  factory LabPanel.fromJson(Map<String, dynamic> json) => _$LabPanelFromJson(json);
}

extension LabResultExtensions on LabResult {
  bool get isAbnormal => status == LabResultStatus.abnormal || status == LabResultStatus.critical;
  
  bool get isHigh => flags?.contains('HIGH') ?? false;
  
  bool get isLow => flags?.contains('LOW') ?? false;
  
  String get statusDisplayName {
    switch (status) {
      case LabResultStatus.normal:
        return 'Normal';
      case LabResultStatus.abnormal:
        return 'Abnormal';
      case LabResultStatus.critical:
        return 'Critical';
      case LabResultStatus.pending:
        return 'Pending';
      case LabResultStatus.cancelled:
        return 'Cancelled';
    }
  }
  
  String get displayValue {
    if (unit.isNotEmpty) {
      return '$value $unit';
    }
    return value;
  }
}
