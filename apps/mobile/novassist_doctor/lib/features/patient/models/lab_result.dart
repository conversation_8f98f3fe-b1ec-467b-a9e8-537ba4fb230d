enum LabResultStatus {
  normal,
  abnormal,
  critical,
  pending,
  cancelled,
}

class LabResult {
  final String id;
  final String patientId;
  final String testName;
  final String testCode;
  final String value;
  final String unit;
  final String? referenceRange;
  final LabResultStatus status;
  final DateTime collectionDate;
  final DateTime resultDate;
  final String? orderingPhysician;
  final String? laboratoryName;
  final String? notes;
  final String? category;
  final bool? isCritical;
  final String? interpretation;
  final List<String>? flags;

  const LabResult({
    required this.id,
    required this.patientId,
    required this.testName,
    required this.testCode,
    required this.value,
    required this.unit,
    this.referenceRange,
    required this.status,
    required this.collectionDate,
    required this.resultDate,
    this.orderingPhysician,
    this.laboratoryName,
    this.notes,
    this.category,
    this.isCritical,
    this.interpretation,
    this.flags,
  });

  factory LabResult.fromJson(Map<String, dynamic> json) {
    return LabResult(
      id: json['id'] as String,
      patientId: json['patientId'] as String,
      testName: json['testName'] as String,
      testCode: json['testCode'] as String,
      value: json['value'] as String,
      unit: json['unit'] as String,
      referenceRange: json['referenceRange'] as String?,
      status: LabResultStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => LabResultStatus.normal,
      ),
      collectionDate: DateTime.parse(json['collectionDate'] as String),
      resultDate: DateTime.parse(json['resultDate'] as String),
      orderingPhysician: json['orderingPhysician'] as String?,
      laboratoryName: json['laboratoryName'] as String?,
      notes: json['notes'] as String?,
      category: json['category'] as String?,
      isCritical: json['isCritical'] as bool?,
      interpretation: json['interpretation'] as String?,
      flags: (json['flags'] as List<dynamic>?)?.cast<String>(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'patientId': patientId,
      'testName': testName,
      'testCode': testCode,
      'value': value,
      'unit': unit,
      'referenceRange': referenceRange,
      'status': status.name,
      'collectionDate': collectionDate.toIso8601String(),
      'resultDate': resultDate.toIso8601String(),
      'orderingPhysician': orderingPhysician,
      'laboratoryName': laboratoryName,
      'notes': notes,
      'category': category,
      'isCritical': isCritical,
      'interpretation': interpretation,
      'flags': flags,
    };
  }

  bool get isAbnormal => status == LabResultStatus.abnormal || status == LabResultStatus.critical;
  
  bool get isHigh => flags?.contains('HIGH') ?? false;
  
  bool get isLow => flags?.contains('LOW') ?? false;
  
  String get statusDisplayName {
    switch (status) {
      case LabResultStatus.normal:
        return 'Normal';
      case LabResultStatus.abnormal:
        return 'Abnormal';
      case LabResultStatus.critical:
        return 'Critical';
      case LabResultStatus.pending:
        return 'Pending';
      case LabResultStatus.cancelled:
        return 'Cancelled';
    }
  }
  
  String get displayValue {
    if (unit.isNotEmpty) {
      return '$value $unit';
    }
    return value;
  }
}

class LabPanel {
  final String id;
  final String patientId;
  final String panelName;
  final List<LabResult> results;
  final DateTime orderDate;
  final DateTime? completedDate;
  final String? orderingPhysician;
  final String? indication;
  final String? notes;

  const LabPanel({
    required this.id,
    required this.patientId,
    required this.panelName,
    required this.results,
    required this.orderDate,
    this.completedDate,
    this.orderingPhysician,
    this.indication,
    this.notes,
  });

  factory LabPanel.fromJson(Map<String, dynamic> json) {
    return LabPanel(
      id: json['id'] as String,
      patientId: json['patientId'] as String,
      panelName: json['panelName'] as String,
      results: (json['results'] as List<dynamic>)
          .map((e) => LabResult.fromJson(e as Map<String, dynamic>))
          .toList(),
      orderDate: DateTime.parse(json['orderDate'] as String),
      completedDate: json['completedDate'] != null 
          ? DateTime.parse(json['completedDate'] as String) 
          : null,
      orderingPhysician: json['orderingPhysician'] as String?,
      indication: json['indication'] as String?,
      notes: json['notes'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'patientId': patientId,
      'panelName': panelName,
      'results': results.map((e) => e.toJson()).toList(),
      'orderDate': orderDate.toIso8601String(),
      'completedDate': completedDate?.toIso8601String(),
      'orderingPhysician': orderingPhysician,
      'indication': indication,
      'notes': notes,
    };
  }
}
