import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive/hive.dart';

part 'patient.freezed.dart';
part 'patient.g.dart';

@freezed
@HiveType(typeId: 0)
class Patient with _$Patient {
  const factory Patient({
    @HiveField(0) required String id,
    @HiveField(1) required String firstName,
    @HiveField(2) required String lastName,
    @HiveField(3) required DateTime dateOfBirth,
    @HiveField(4) required String gender,
    @HiveField(5) required String mrn, // Medical Record Number
    @HiveField(6) String? email,
    @HiveField(7) String? phone,
    @HiveField(8) Address? address,
    @HiveField(9) String? emergencyContact,
    @HiveField(10) String? emergencyContactPhone,
    @HiveField(11) List<String>? allergies,
    @HiveField(12) String? bloodType,
    @HiveField(13) double? height, // in cm
    @HiveField(14) double? weight, // in kg
    @HiveField(15) String? primaryPhysician,
    @HiveField(16) String? insuranceProvider,
    @HiveField(17) String? insuranceNumber,
    @HiveField(18) DateTime? lastVisit,
    @HiveField(19) String? profileImageUrl,
    @HiveField(20) @Default([]) List<String> chronicConditions,
    @HiveField(21) DateTime? createdAt,
    @HiveField(22) DateTime? updatedAt,
  }) = _Patient;

  factory Patient.fromJson(Map<String, dynamic> json) => _$PatientFromJson(json);
}

@freezed
@HiveType(typeId: 1)
class Address with _$Address {
  const factory Address({
    @HiveField(0) required String street,
    @HiveField(1) required String city,
    @HiveField(2) required String state,
    @HiveField(3) required String zipCode,
    @HiveField(4) String? country,
  }) = _Address;

  factory Address.fromJson(Map<String, dynamic> json) => _$AddressFromJson(json);
}

extension PatientExtensions on Patient {
  String get fullName => '$firstName $lastName';
  
  int get age {
    final now = DateTime.now();
    int age = now.year - dateOfBirth.year;
    if (now.month < dateOfBirth.month || 
        (now.month == dateOfBirth.month && now.day < dateOfBirth.day)) {
      age--;
    }
    return age;
  }
  
  double? get bmi {
    if (height == null || weight == null) return null;
    final heightInMeters = height! / 100;
    return weight! / (heightInMeters * heightInMeters);
  }
  
  String get bmiCategory {
    final bmiValue = bmi;
    if (bmiValue == null) return 'Unknown';
    
    if (bmiValue < 18.5) return 'Underweight';
    if (bmiValue < 25) return 'Normal';
    if (bmiValue < 30) return 'Overweight';
    return 'Obese';
  }
}
