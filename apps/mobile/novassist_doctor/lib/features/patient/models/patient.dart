class Patient {
  final String id;
  final String firstName;
  final String lastName;
  final DateTime dateOfBirth;
  final String gender;
  final String mrn;
  final String? email;
  final String? phone;
  final Address? address;
  final String? emergencyContact;
  final String? emergencyContactPhone;
  final List<String>? allergies;
  final String? bloodType;
  final double? height;
  final double? weight;
  final String? primaryPhysician;
  final String? insuranceProvider;
  final String? insuranceNumber;
  final DateTime? lastVisit;
  final String? profileImageUrl;
  final List<String> chronicConditions;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const Patient({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.dateOfBirth,
    required this.gender,
    required this.mrn,
    this.email,
    this.phone,
    this.address,
    this.emergencyContact,
    this.emergencyContactPhone,
    this.allergies,
    this.bloodType,
    this.height,
    this.weight,
    this.primaryPhysician,
    this.insuranceProvider,
    this.insuranceNumber,
    this.lastVisit,
    this.profileImageUrl,
    this.chronicConditions = const [],
    this.createdAt,
    this.updatedAt,
  });

  factory Patient.fromJson(Map<String, dynamic> json) {
    return Patient(
      id: json['id'] as String,
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      dateOfBirth: DateTime.parse(json['dateOfBirth'] as String),
      gender: json['gender'] as String,
      mrn: json['mrn'] as String,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      address: json['address'] != null ? Address.fromJson(json['address']) : null,
      emergencyContact: json['emergencyContact'] as String?,
      emergencyContactPhone: json['emergencyContactPhone'] as String?,
      allergies: (json['allergies'] as List<dynamic>?)?.cast<String>(),
      bloodType: json['bloodType'] as String?,
      height: (json['height'] as num?)?.toDouble(),
      weight: (json['weight'] as num?)?.toDouble(),
      primaryPhysician: json['primaryPhysician'] as String?,
      insuranceProvider: json['insuranceProvider'] as String?,
      insuranceNumber: json['insuranceNumber'] as String?,
      lastVisit: json['lastVisit'] != null ? DateTime.parse(json['lastVisit']) : null,
      profileImageUrl: json['profileImageUrl'] as String?,
      chronicConditions: (json['chronicConditions'] as List<dynamic>?)?.cast<String>() ?? [],
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'firstName': firstName,
      'lastName': lastName,
      'dateOfBirth': dateOfBirth.toIso8601String(),
      'gender': gender,
      'mrn': mrn,
      'email': email,
      'phone': phone,
      'address': address?.toJson(),
      'emergencyContact': emergencyContact,
      'emergencyContactPhone': emergencyContactPhone,
      'allergies': allergies,
      'bloodType': bloodType,
      'height': height,
      'weight': weight,
      'primaryPhysician': primaryPhysician,
      'insuranceProvider': insuranceProvider,
      'insuranceNumber': insuranceNumber,
      'lastVisit': lastVisit?.toIso8601String(),
      'profileImageUrl': profileImageUrl,
      'chronicConditions': chronicConditions,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  String get fullName => '$firstName $lastName';
  
  int get age {
    final now = DateTime.now();
    int age = now.year - dateOfBirth.year;
    if (now.month < dateOfBirth.month || 
        (now.month == dateOfBirth.month && now.day < dateOfBirth.day)) {
      age--;
    }
    return age;
  }
  
  double? get bmi {
    if (height == null || weight == null) return null;
    final heightInMeters = height! / 100;
    return weight! / (heightInMeters * heightInMeters);
  }
  
  String get bmiCategory {
    final bmiValue = bmi;
    if (bmiValue == null) return 'Unknown';
    
    if (bmiValue < 18.5) return 'Underweight';
    if (bmiValue < 25) return 'Normal';
    if (bmiValue < 30) return 'Overweight';
    return 'Obese';
  }
}

class Address {
  final String street;
  final String city;
  final String state;
  final String zipCode;
  final String? country;

  const Address({
    required this.street,
    required this.city,
    required this.state,
    required this.zipCode,
    this.country,
  });

  factory Address.fromJson(Map<String, dynamic> json) {
    return Address(
      street: json['street'] as String,
      city: json['city'] as String,
      state: json['state'] as String,
      zipCode: json['zipCode'] as String,
      country: json['country'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'street': street,
      'city': city,
      'state': state,
      'zipCode': zipCode,
      'country': country,
    };
  }
}
