import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive/hive.dart';

part 'medical_note.freezed.dart';
part 'medical_note.g.dart';

@freezed
@HiveType(typeId: 8)
class MedicalNote with _$MedicalNote {
  const factory MedicalNote({
    @HiveField(0) required String id,
    @HiveField(1) required String patientId,
    @HiveField(2) required String title,
    @HiveField(3) required String content,
    @HiveField(4) required NoteType type,
    @HiveField(5) required DateTime createdAt,
    @HiveField(6) required String createdBy,
    @HiveField(7) DateTime? updatedAt,
    @HiveField(8) String? updatedBy,
    @HiveField(9) String? visitId,
    @HiveField(10) List<String>? tags,
    @HiveField(11) bool? isConfidential,
    @HiveField(12) String? department,
    @HiveField(13) List<String>? attachments,
    @HiveField(14) String? templateId,
    @HiveField(15) Map<String, dynamic>? structuredData,
  }) = _MedicalNote;

  factory MedicalNote.fromJson(Map<String, dynamic> json) => _$MedicalNoteFromJson(json);
}

@HiveType(typeId: 9)
enum NoteType {
  @HiveField(0)
  progress,
  @HiveField(1)
  admission,
  @HiveField(2)
  discharge,
  @HiveField(3)
  consultation,
  @HiveField(4)
  procedure,
  @HiveField(5)
  nursing,
  @HiveField(6)
  therapy,
  @HiveField(7)
  radiology,
  @HiveField(8)
  pathology,
  @HiveField(9)
  emergency,
  @HiveField(10)
  followUp,
  @HiveField(11)
  assessment,
  @HiveField(12)
  plan,
}

@freezed
@HiveType(typeId: 10)
class Visit with _$Visit {
  const factory Visit({
    @HiveField(0) required String id,
    @HiveField(1) required String patientId,
    @HiveField(2) required DateTime visitDate,
    @HiveField(3) required VisitType type,
    @HiveField(4) required String provider,
    @HiveField(5) String? department,
    @HiveField(6) String? chiefComplaint,
    @HiveField(7) VitalSigns? vitalSigns,
    @HiveField(8) List<String>? diagnoses,
    @HiveField(9) List<String>? procedures,
    @HiveField(10) List<MedicalNote>? notes,
    @HiveField(11) VisitStatus? status,
    @HiveField(12) DateTime? checkInTime,
    @HiveField(13) DateTime? checkOutTime,
    @HiveField(14) String? location,
    @HiveField(15) double? copay,
  }) = _Visit;

  factory Visit.fromJson(Map<String, dynamic> json) => _$VisitFromJson(json);
}

@HiveType(typeId: 11)
enum VisitType {
  @HiveField(0)
  routine,
  @HiveField(1)
  followUp,
  @HiveField(2)
  emergency,
  @HiveField(3)
  consultation,
  @HiveField(4)
  procedure,
  @HiveField(5)
  telehealth,
  @HiveField(6)
  urgent,
}

@HiveType(typeId: 12)
enum VisitStatus {
  @HiveField(0)
  scheduled,
  @HiveField(1)
  checkedIn,
  @HiveField(2)
  inProgress,
  @HiveField(3)
  completed,
  @HiveField(4)
  cancelled,
  @HiveField(5)
  noShow,
}

@freezed
@HiveType(typeId: 13)
class VitalSigns with _$VitalSigns {
  const factory VitalSigns({
    @HiveField(0) double? temperature, // Celsius
    @HiveField(1) int? systolicBP,
    @HiveField(2) int? diastolicBP,
    @HiveField(3) int? heartRate,
    @HiveField(4) int? respiratoryRate,
    @HiveField(5) double? oxygenSaturation,
    @HiveField(6) double? height, // cm
    @HiveField(7) double? weight, // kg
    @HiveField(8) double? bmi,
    @HiveField(9) DateTime? measuredAt,
    @HiveField(10) String? measuredBy,
  }) = _VitalSigns;

  factory VitalSigns.fromJson(Map<String, dynamic> json) => _$VitalSignsFromJson(json);
}

extension MedicalNoteExtensions on MedicalNote {
  String get typeDisplayName {
    switch (type) {
      case NoteType.progress:
        return 'Progress Note';
      case NoteType.admission:
        return 'Admission Note';
      case NoteType.discharge:
        return 'Discharge Note';
      case NoteType.consultation:
        return 'Consultation';
      case NoteType.procedure:
        return 'Procedure Note';
      case NoteType.nursing:
        return 'Nursing Note';
      case NoteType.therapy:
        return 'Therapy Note';
      case NoteType.radiology:
        return 'Radiology Report';
      case NoteType.pathology:
        return 'Pathology Report';
      case NoteType.emergency:
        return 'Emergency Note';
      case NoteType.followUp:
        return 'Follow-up Note';
      case NoteType.assessment:
        return 'Assessment';
      case NoteType.plan:
        return 'Treatment Plan';
    }
  }
  
  bool get hasAttachments => attachments?.isNotEmpty ?? false;
  
  String get summary {
    if (content.length <= 100) return content;
    return '${content.substring(0, 100)}...';
  }
}
