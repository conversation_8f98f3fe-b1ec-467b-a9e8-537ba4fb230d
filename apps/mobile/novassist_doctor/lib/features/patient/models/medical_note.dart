enum NoteType {
  progress,
  admission,
  discharge,
  consultation,
  procedure,
  nursing,
  therapy,
  radiology,
  pathology,
  emergency,
  followUp,
  assessment,
  plan,
}

enum VisitType {
  routine,
  followUp,
  emergency,
  consultation,
  procedure,
  telehealth,
  urgent,
}

enum VisitStatus {
  scheduled,
  checkedIn,
  inProgress,
  completed,
  cancelled,
  noShow,
}

class MedicalNote {
  final String id;
  final String patientId;
  final String title;
  final String content;
  final NoteType type;
  final DateTime createdAt;
  final String createdBy;
  final DateTime? updatedAt;
  final String? updatedBy;
  final String? visitId;
  final List<String>? tags;
  final bool? isConfidential;
  final String? department;
  final List<String>? attachments;
  final String? templateId;
  final Map<String, dynamic>? structuredData;

  const MedicalNote({
    required this.id,
    required this.patientId,
    required this.title,
    required this.content,
    required this.type,
    required this.createdAt,
    required this.createdBy,
    this.updatedAt,
    this.updatedBy,
    this.visitId,
    this.tags,
    this.isConfidential,
    this.department,
    this.attachments,
    this.templateId,
    this.structuredData,
  });

  factory MedicalNote.fromJson(Map<String, dynamic> json) {
    return MedicalNote(
      id: json['id'] as String,
      patientId: json['patientId'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      type: NoteType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => NoteType.progress,
      ),
      createdAt: DateTime.parse(json['createdAt'] as String),
      createdBy: json['createdBy'] as String,
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt'] as String) 
          : null,
      updatedBy: json['updatedBy'] as String?,
      visitId: json['visitId'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.cast<String>(),
      isConfidential: json['isConfidential'] as bool?,
      department: json['department'] as String?,
      attachments: (json['attachments'] as List<dynamic>?)?.cast<String>(),
      templateId: json['templateId'] as String?,
      structuredData: json['structuredData'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'patientId': patientId,
      'title': title,
      'content': content,
      'type': type.name,
      'createdAt': createdAt.toIso8601String(),
      'createdBy': createdBy,
      'updatedAt': updatedAt?.toIso8601String(),
      'updatedBy': updatedBy,
      'visitId': visitId,
      'tags': tags,
      'isConfidential': isConfidential,
      'department': department,
      'attachments': attachments,
      'templateId': templateId,
      'structuredData': structuredData,
    };
  }

  String get typeDisplayName {
    switch (type) {
      case NoteType.progress:
        return 'Progress Note';
      case NoteType.admission:
        return 'Admission Note';
      case NoteType.discharge:
        return 'Discharge Note';
      case NoteType.consultation:
        return 'Consultation';
      case NoteType.procedure:
        return 'Procedure Note';
      case NoteType.nursing:
        return 'Nursing Note';
      case NoteType.therapy:
        return 'Therapy Note';
      case NoteType.radiology:
        return 'Radiology Report';
      case NoteType.pathology:
        return 'Pathology Report';
      case NoteType.emergency:
        return 'Emergency Note';
      case NoteType.followUp:
        return 'Follow-up Note';
      case NoteType.assessment:
        return 'Assessment';
      case NoteType.plan:
        return 'Treatment Plan';
    }
  }
  
  bool get hasAttachments => attachments?.isNotEmpty ?? false;
  
  String get summary {
    if (content.length <= 100) return content;
    return '${content.substring(0, 100)}...';
  }
}

class Visit {
  final String id;
  final String patientId;
  final DateTime visitDate;
  final VisitType type;
  final String provider;
  final String? department;
  final String? chiefComplaint;
  final VitalSigns? vitalSigns;
  final List<String>? diagnoses;
  final List<String>? procedures;
  final List<MedicalNote>? notes;
  final VisitStatus? status;
  final DateTime? checkInTime;
  final DateTime? checkOutTime;
  final String? location;
  final double? copay;

  const Visit({
    required this.id,
    required this.patientId,
    required this.visitDate,
    required this.type,
    required this.provider,
    this.department,
    this.chiefComplaint,
    this.vitalSigns,
    this.diagnoses,
    this.procedures,
    this.notes,
    this.status,
    this.checkInTime,
    this.checkOutTime,
    this.location,
    this.copay,
  });

  factory Visit.fromJson(Map<String, dynamic> json) {
    return Visit(
      id: json['id'] as String,
      patientId: json['patientId'] as String,
      visitDate: DateTime.parse(json['visitDate'] as String),
      type: VisitType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => VisitType.routine,
      ),
      provider: json['provider'] as String,
      department: json['department'] as String?,
      chiefComplaint: json['chiefComplaint'] as String?,
      vitalSigns: json['vitalSigns'] != null 
          ? VitalSigns.fromJson(json['vitalSigns'] as Map<String, dynamic>) 
          : null,
      diagnoses: (json['diagnoses'] as List<dynamic>?)?.cast<String>(),
      procedures: (json['procedures'] as List<dynamic>?)?.cast<String>(),
      notes: (json['notes'] as List<dynamic>?)
          ?.map((e) => MedicalNote.fromJson(e as Map<String, dynamic>))
          .toList(),
      status: json['status'] != null 
          ? VisitStatus.values.firstWhere(
              (e) => e.name == json['status'],
              orElse: () => VisitStatus.scheduled,
            )
          : null,
      checkInTime: json['checkInTime'] != null 
          ? DateTime.parse(json['checkInTime'] as String) 
          : null,
      checkOutTime: json['checkOutTime'] != null 
          ? DateTime.parse(json['checkOutTime'] as String) 
          : null,
      location: json['location'] as String?,
      copay: (json['copay'] as num?)?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'patientId': patientId,
      'visitDate': visitDate.toIso8601String(),
      'type': type.name,
      'provider': provider,
      'department': department,
      'chiefComplaint': chiefComplaint,
      'vitalSigns': vitalSigns?.toJson(),
      'diagnoses': diagnoses,
      'procedures': procedures,
      'notes': notes?.map((e) => e.toJson()).toList(),
      'status': status?.name,
      'checkInTime': checkInTime?.toIso8601String(),
      'checkOutTime': checkOutTime?.toIso8601String(),
      'location': location,
      'copay': copay,
    };
  }
}

class VitalSigns {
  final double? temperature;
  final int? systolicBP;
  final int? diastolicBP;
  final int? heartRate;
  final int? respiratoryRate;
  final double? oxygenSaturation;
  final double? height;
  final double? weight;
  final double? bmi;
  final DateTime? measuredAt;
  final String? measuredBy;

  const VitalSigns({
    this.temperature,
    this.systolicBP,
    this.diastolicBP,
    this.heartRate,
    this.respiratoryRate,
    this.oxygenSaturation,
    this.height,
    this.weight,
    this.bmi,
    this.measuredAt,
    this.measuredBy,
  });

  factory VitalSigns.fromJson(Map<String, dynamic> json) {
    return VitalSigns(
      temperature: (json['temperature'] as num?)?.toDouble(),
      systolicBP: json['systolicBP'] as int?,
      diastolicBP: json['diastolicBP'] as int?,
      heartRate: json['heartRate'] as int?,
      respiratoryRate: json['respiratoryRate'] as int?,
      oxygenSaturation: (json['oxygenSaturation'] as num?)?.toDouble(),
      height: (json['height'] as num?)?.toDouble(),
      weight: (json['weight'] as num?)?.toDouble(),
      bmi: (json['bmi'] as num?)?.toDouble(),
      measuredAt: json['measuredAt'] != null 
          ? DateTime.parse(json['measuredAt'] as String) 
          : null,
      measuredBy: json['measuredBy'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'temperature': temperature,
      'systolicBP': systolicBP,
      'diastolicBP': diastolicBP,
      'heartRate': heartRate,
      'respiratoryRate': respiratoryRate,
      'oxygenSaturation': oxygenSaturation,
      'height': height,
      'weight': weight,
      'bmi': bmi,
      'measuredAt': measuredAt?.toIso8601String(),
      'measuredBy': measuredBy,
    };
  }
}
