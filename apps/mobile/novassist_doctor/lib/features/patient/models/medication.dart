enum MedicationStatus {
  active,
  discontinued,
  completed,
  onHold,
  cancelled,
}

class Medication {
  final String id;
  final String patientId;
  final String name;
  final String genericName;
  final String dosage;
  final String frequency;
  final String route;
  final DateTime startDate;
  final DateTime? endDate;
  final String prescribedBy;
  final String? indication;
  final String? instructions;
  final MedicationStatus status;
  final int? refillsRemaining;
  final DateTime? lastRefillDate;
  final String? pharmacy;
  final List<String>? sideEffects;
  final List<String>? interactions;
  final String? notes;
  final bool? isControlledSubstance;
  final String? ndc;

  const Medication({
    required this.id,
    required this.patientId,
    required this.name,
    required this.genericName,
    required this.dosage,
    required this.frequency,
    required this.route,
    required this.startDate,
    this.endDate,
    required this.prescribedBy,
    this.indication,
    this.instructions,
    required this.status,
    this.refillsRemaining,
    this.lastRefillDate,
    this.pharmacy,
    this.sideEffects,
    this.interactions,
    this.notes,
    this.isControlledSubstance,
    this.ndc,
  });

  factory Medication.fromJson(Map<String, dynamic> json) {
    return Medication(
      id: json['id'] as String,
      patientId: json['patientId'] as String,
      name: json['name'] as String,
      genericName: json['genericName'] as String,
      dosage: json['dosage'] as String,
      frequency: json['frequency'] as String,
      route: json['route'] as String,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] != null ? DateTime.parse(json['endDate'] as String) : null,
      prescribedBy: json['prescribedBy'] as String,
      indication: json['indication'] as String?,
      instructions: json['instructions'] as String?,
      status: MedicationStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => MedicationStatus.active,
      ),
      refillsRemaining: json['refillsRemaining'] as int?,
      lastRefillDate: json['lastRefillDate'] != null 
          ? DateTime.parse(json['lastRefillDate'] as String) 
          : null,
      pharmacy: json['pharmacy'] as String?,
      sideEffects: (json['sideEffects'] as List<dynamic>?)?.cast<String>(),
      interactions: (json['interactions'] as List<dynamic>?)?.cast<String>(),
      notes: json['notes'] as String?,
      isControlledSubstance: json['isControlledSubstance'] as bool?,
      ndc: json['ndc'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'patientId': patientId,
      'name': name,
      'genericName': genericName,
      'dosage': dosage,
      'frequency': frequency,
      'route': route,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'prescribedBy': prescribedBy,
      'indication': indication,
      'instructions': instructions,
      'status': status.name,
      'refillsRemaining': refillsRemaining,
      'lastRefillDate': lastRefillDate?.toIso8601String(),
      'pharmacy': pharmacy,
      'sideEffects': sideEffects,
      'interactions': interactions,
      'notes': notes,
      'isControlledSubstance': isControlledSubstance,
      'ndc': ndc,
    };
  }

  bool get isActive => status == MedicationStatus.active;
  
  bool get isDiscontinued => status == MedicationStatus.discontinued;
  
  bool get needsRefill => refillsRemaining != null && refillsRemaining! <= 1;
  
  String get statusDisplayName {
    switch (status) {
      case MedicationStatus.active:
        return 'Active';
      case MedicationStatus.discontinued:
        return 'Discontinued';
      case MedicationStatus.completed:
        return 'Completed';
      case MedicationStatus.onHold:
        return 'On Hold';
      case MedicationStatus.cancelled:
        return 'Cancelled';
    }
  }
  
  String get displayName => name.isNotEmpty ? name : genericName;
  
  String get dosageInfo => '$dosage, $frequency';
  
  Duration? get duration {
    if (endDate == null) return null;
    return endDate!.difference(startDate);
  }
  
  bool get isLongTerm {
    final dur = duration;
    if (dur == null) return true; // Ongoing medication
    return dur.inDays > 90; // More than 3 months
  }
}

class MedicationHistory {
  final String id;
  final String medicationId;
  final String patientId;
  final DateTime changeDate;
  final String changeType;
  final String changedBy;
  final String? previousValue;
  final String? newValue;
  final String? reason;
  final String? notes;

  const MedicationHistory({
    required this.id,
    required this.medicationId,
    required this.patientId,
    required this.changeDate,
    required this.changeType,
    required this.changedBy,
    this.previousValue,
    this.newValue,
    this.reason,
    this.notes,
  });

  factory MedicationHistory.fromJson(Map<String, dynamic> json) {
    return MedicationHistory(
      id: json['id'] as String,
      medicationId: json['medicationId'] as String,
      patientId: json['patientId'] as String,
      changeDate: DateTime.parse(json['changeDate'] as String),
      changeType: json['changeType'] as String,
      changedBy: json['changedBy'] as String,
      previousValue: json['previousValue'] as String?,
      newValue: json['newValue'] as String?,
      reason: json['reason'] as String?,
      notes: json['notes'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'medicationId': medicationId,
      'patientId': patientId,
      'changeDate': changeDate.toIso8601String(),
      'changeType': changeType,
      'changedBy': changedBy,
      'previousValue': previousValue,
      'newValue': newValue,
      'reason': reason,
      'notes': notes,
    };
  }
}
