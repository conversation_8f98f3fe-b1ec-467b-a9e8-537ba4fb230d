import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive/hive.dart';

part 'medication.freezed.dart';
part 'medication.g.dart';

@freezed
@HiveType(typeId: 5)
class Medication with _$Medication {
  const factory Medication({
    @HiveField(0) required String id,
    @HiveField(1) required String patientId,
    @HiveField(2) required String name,
    @HiveField(3) required String genericName,
    @HiveField(4) required String dosage,
    @HiveField(5) required String frequency,
    @HiveField(6) required String route, // e.g., "Oral", "IV", "Topical"
    @HiveField(7) required DateTime startDate,
    @HiveField(8) DateTime? endDate,
    @HiveField(9) required String prescribedBy,
    @HiveField(10) String? indication,
    @HiveField(11) String? instructions,
    @HiveField(12) required MedicationStatus status,
    @HiveField(13) int? refillsRemaining,
    @HiveField(14) DateTime? lastRefillDate,
    @HiveField(15) String? pharmacy,
    @HiveField(16) List<String>? sideEffects,
    @HiveField(17) List<String>? interactions,
    @HiveField(18) String? notes,
    @HiveField(19) bool? isControlledSubstance,
    @HiveField(20) String? ndc, // National Drug Code
  }) = _Medication;

  factory Medication.fromJson(Map<String, dynamic> json) => _$MedicationFromJson(json);
}

@HiveType(typeId: 6)
enum MedicationStatus {
  @HiveField(0)
  active,
  @HiveField(1)
  discontinued,
  @HiveField(2)
  completed,
  @HiveField(3)
  onHold,
  @HiveField(4)
  cancelled,
}

@freezed
@HiveType(typeId: 7)
class MedicationHistory with _$MedicationHistory {
  const factory MedicationHistory({
    @HiveField(0) required String id,
    @HiveField(1) required String medicationId,
    @HiveField(2) required String patientId,
    @HiveField(3) required DateTime changeDate,
    @HiveField(4) required String changeType, // e.g., "Started", "Stopped", "Dose Changed"
    @HiveField(5) required String changedBy,
    @HiveField(6) String? previousValue,
    @HiveField(7) String? newValue,
    @HiveField(8) String? reason,
    @HiveField(9) String? notes,
  }) = _MedicationHistory;

  factory MedicationHistory.fromJson(Map<String, dynamic> json) => _$MedicationHistoryFromJson(json);
}

extension MedicationExtensions on Medication {
  bool get isActive => status == MedicationStatus.active;
  
  bool get isDiscontinued => status == MedicationStatus.discontinued;
  
  bool get needsRefill => refillsRemaining != null && refillsRemaining! <= 1;
  
  String get statusDisplayName {
    switch (status) {
      case MedicationStatus.active:
        return 'Active';
      case MedicationStatus.discontinued:
        return 'Discontinued';
      case MedicationStatus.completed:
        return 'Completed';
      case MedicationStatus.onHold:
        return 'On Hold';
      case MedicationStatus.cancelled:
        return 'Cancelled';
    }
  }
  
  String get displayName => name.isNotEmpty ? name : genericName;
  
  String get dosageInfo => '$dosage, $frequency';
  
  Duration? get duration {
    if (endDate == null) return null;
    return endDate!.difference(startDate);
  }
  
  bool get isLongTerm {
    final dur = duration;
    if (dur == null) return true; // Ongoing medication
    return dur.inDays > 90; // More than 3 months
  }
}
