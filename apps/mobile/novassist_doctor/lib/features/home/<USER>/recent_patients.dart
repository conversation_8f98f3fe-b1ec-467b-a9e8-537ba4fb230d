import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/config/app_theme.dart';
import '../../patient/providers/patient_provider.dart';
import '../../patient/models/patient.dart';

class RecentPatients extends ConsumerWidget {
  const RecentPatients({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final patientsAsync = ref.watch(patientsProvider);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Recent Patients',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => context.go('/patients'),
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            patientsAsync.when(
              data: (patients) => patients.isNotEmpty
                  ? _buildPatientsList(context, patients.take(3).toList())
                  : _buildEmptyState(context),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => _buildError(context, error.toString()),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPatientsList(BuildContext context, List<Patient> patients) {
    return Column(
      children: patients.map((patient) {
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            contentPadding: EdgeInsets.zero,
            leading: CircleAvatar(
              backgroundColor: AppTheme.primaryColor,
              child: Text(
                '${patient.firstName[0]}${patient.lastName[0]}',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            title: Text(patient.fullName),
            subtitle: Text('Age: ${patient.age} • ${patient.gender}'),
            trailing: IconButton(
              icon: const Icon(Icons.chat),
              onPressed: () => context.go('/patients/${patient.id}/chat'),
            ),
            onTap: () => context.go('/patients/${patient.id}'),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        children: [
          Icon(
            Icons.people_outline,
            size: 48,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 8),
          Text(
            'No patients yet',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey.shade600,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildError(BuildContext context, String error) {
    return Center(
      child: Text(
        'Error loading patients',
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.errorColor,
            ),
      ),
    );
  }
}
