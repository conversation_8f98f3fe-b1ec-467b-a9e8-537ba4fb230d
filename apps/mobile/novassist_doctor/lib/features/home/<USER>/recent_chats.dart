import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/config/app_theme.dart';
import '../../chat/providers/chat_provider.dart';
import '../../chat/models/chat_message.dart';

class RecentChats extends ConsumerWidget {
  const RecentChats({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final chatSessionsAsync = ref.watch(chatSessionsProvider);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Recent Chats',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => context.go('/chat'),
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            chatSessionsAsync.when(
              data: (sessions) => sessions.isNotEmpty
                  ? _buildChatsList(context, sessions.take(3).toList())
                  : _buildEmptyState(context),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => _buildError(context, error.toString()),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChatsList(BuildContext context, List<ChatSession> sessions) {
    return Column(
      children: sessions.map((session) {
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            contentPadding: EdgeInsets.zero,
            leading: CircleAvatar(
              backgroundColor: AppTheme.secondaryColor,
              child: const Icon(
                Icons.chat,
                color: Colors.white,
                size: 20,
              ),
            ),
            title: Text(session.title),
            subtitle: Text(_getLastMessagePreview(session)),
            trailing: Text(_formatDate(session.updatedAt ?? session.createdAt)),
            onTap: () => context.go('/chat/${session.id}'),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 48,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 8),
          Text(
            'No chat sessions yet',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey.shade600,
                ),
          ),
          const SizedBox(height: 8),
          TextButton(
            onPressed: () => context.go('/chat'),
            child: const Text('Start your first chat'),
          ),
        ],
      ),
    );
  }

  Widget _buildError(BuildContext context, String error) {
    return Center(
      child: Text(
        'Error loading chats',
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.errorColor,
            ),
      ),
    );
  }

  String _getLastMessagePreview(ChatSession session) {
    if (session.messages.isEmpty) {
      return 'No messages yet';
    }
    final lastMessage = session.messages.last;
    return lastMessage.content.length > 30
        ? '${lastMessage.content.substring(0, 30)}...'
        : lastMessage.content;
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return '${date.hour}:${date.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else {
      return '${date.day}/${date.month}';
    }
  }
}
