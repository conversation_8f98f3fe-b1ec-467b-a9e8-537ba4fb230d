import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/config/app_theme.dart';
import '../../patient/providers/patient_provider.dart';
import '../../chat/providers/chat_provider.dart';
import '../widgets/dashboard_stats.dart';
import '../widgets/recent_patients.dart';
import '../widgets/quick_actions.dart';
import '../widgets/recent_chats.dart';

class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Novassist Doctor'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearch(context),
          ),
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () => _showNotifications(context),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () => _refreshData(ref),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Welcome message
              _buildWelcomeCard(context),
              
              const SizedBox(height: 24),
              
              // Dashboard stats
              const DashboardStats(),
              
              const SizedBox(height: 24),
              
              // Quick actions
              const QuickActions(),
              
              const SizedBox(height: 24),
              
              // Recent patients
              const RecentPatients(),
              
              const SizedBox(height: 24),
              
              // Recent chats
              const RecentChats(),
              
              const SizedBox(height: 80), // Space for bottom navigation
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => context.go('/chat'),
        icon: const Icon(Icons.smart_toy),
        label: const Text('New Chat'),
        backgroundColor: AppTheme.primaryColor,
      ),
      bottomNavigationBar: _buildBottomNavigation(context),
    );
  }
  
  Widget _buildWelcomeCard(BuildContext context) {
    final hour = DateTime.now().hour;
    String greeting;
    
    if (hour < 12) {
      greeting = 'Good morning';
    } else if (hour < 17) {
      greeting = 'Good afternoon';
    } else {
      greeting = 'Good evening';
    }
    
    return Card(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [
              AppTheme.primaryColor,
              AppTheme.secondaryColor,
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '$greeting, Doctor',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Ready to provide excellent patient care with AI assistance',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.white.withOpacity(0.9),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildBottomNavigation(BuildContext context) {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      selectedItemColor: AppTheme.primaryColor,
      unselectedItemColor: Colors.grey,
      currentIndex: 0,
      onTap: (index) => _handleBottomNavTap(context, index),
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.home),
          label: 'Home',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.people),
          label: 'Patients',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.chat),
          label: 'Chat',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.analytics),
          label: 'Analytics',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.person),
          label: 'Profile',
        ),
      ],
    );
  }
  
  void _handleBottomNavTap(BuildContext context, int index) {
    switch (index) {
      case 0:
        // Already on home
        break;
      case 1:
        context.go('/patients');
        break;
      case 2:
        context.go('/chat');
        break;
      case 3:
        // TODO: Navigate to analytics
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Analytics coming soon')),
        );
        break;
      case 4:
        // TODO: Navigate to profile
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Profile coming soon')),
        );
        break;
    }
  }
  
  void _showSearch(BuildContext context) {
    showSearch(
      context: context,
      delegate: PatientSearchDelegate(),
    );
  }
  
  void _showNotifications(BuildContext context) {
    // TODO: Implement notifications
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Notifications coming soon')),
    );
  }
  
  Future<void> _refreshData(WidgetRef ref) async {
    // Refresh all data
    ref.invalidate(patientsProvider);
    ref.invalidate(chatSessionsProvider);
    
    // Wait for data to load
    await Future.wait([
      ref.read(patientsProvider.future),
      ref.read(chatSessionsProvider.future),
    ]);
  }
}

class PatientSearchDelegate extends SearchDelegate<String> {
  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () => query = '',
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () => close(context, ''),
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return _buildSearchResults(context);
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return _buildSearchResults(context);
  }
  
  Widget _buildSearchResults(BuildContext context) {
    if (query.isEmpty) {
      return const Center(
        child: Text('Enter patient name or MRN to search'),
      );
    }
    
    return Consumer(
      builder: (context, ref, child) {
        final searchResults = ref.watch(searchPatientsProvider(query));
        
        return searchResults.when(
          data: (patients) {
            if (patients.isEmpty) {
              return const Center(
                child: Text('No patients found'),
              );
            }
            
            return ListView.builder(
              itemCount: patients.length,
              itemBuilder: (context, index) {
                final patient = patients[index];
                return ListTile(
                  leading: CircleAvatar(
                    child: Text(patient.firstName[0] + patient.lastName[0]),
                  ),
                  title: Text(patient.fullName),
                  subtitle: Text('MRN: ${patient.mrn}'),
                  onTap: () {
                    close(context, patient.id);
                    context.go('/patients/${patient.id}');
                  },
                );
              },
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(
            child: Text('Error: $error'),
          ),
        );
      },
    );
  }
}
