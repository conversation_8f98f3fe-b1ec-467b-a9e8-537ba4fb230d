import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../features/auth/screens/login_screen.dart';
import '../../features/home/<USER>/home_screen.dart';
import '../../features/patient/screens/patient_list_screen.dart';
import '../../features/patient/screens/patient_detail_screen.dart';
import '../../features/patient/screens/patient_profile_screen.dart';
import '../../features/patient/screens/lab_results_screen.dart';
import '../../features/patient/screens/medications_screen.dart';
import '../../features/patient/screens/medical_notes_screen.dart';
import '../../features/chat/screens/chat_screen.dart';
import '../../features/chat/screens/chat_list_screen.dart';

final appRouterProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: '/login',
    routes: [
      // Authentication
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      
      // Main Navigation
      GoRoute(
        path: '/home',
        name: 'home',
        builder: (context, state) => const HomeScreen(),
      ),
      
      // Patients
      GoRoute(
        path: '/patients',
        name: 'patients',
        builder: (context, state) => const PatientListScreen(),
        routes: [
          GoRoute(
            path: '/:patientId',
            name: 'patient-detail',
            builder: (context, state) {
              final patientId = state.pathParameters['patientId']!;
              return PatientDetailScreen(patientId: patientId);
            },
            routes: [
              GoRoute(
                path: '/profile',
                name: 'patient-profile',
                builder: (context, state) {
                  final patientId = state.pathParameters['patientId']!;
                  return PatientProfileScreen(patientId: patientId);
                },
              ),
              GoRoute(
                path: '/lab-results',
                name: 'lab-results',
                builder: (context, state) {
                  final patientId = state.pathParameters['patientId']!;
                  return LabResultsScreen(patientId: patientId);
                },
              ),
              GoRoute(
                path: '/medications',
                name: 'medications',
                builder: (context, state) {
                  final patientId = state.pathParameters['patientId']!;
                  return MedicationsScreen(patientId: patientId);
                },
              ),
              GoRoute(
                path: '/notes',
                name: 'medical-notes',
                builder: (context, state) {
                  final patientId = state.pathParameters['patientId']!;
                  return MedicalNotesScreen(patientId: patientId);
                },
              ),
              GoRoute(
                path: '/chat',
                name: 'patient-chat',
                builder: (context, state) {
                  final patientId = state.pathParameters['patientId']!;
                  return ChatScreen(patientId: patientId);
                },
              ),
            ],
          ),
        ],
      ),
      
      // Chat
      GoRoute(
        path: '/chat',
        name: 'chat-list',
        builder: (context, state) => const ChatListScreen(),
        routes: [
          GoRoute(
            path: '/:sessionId',
            name: 'chat-session',
            builder: (context, state) {
              final sessionId = state.pathParameters['sessionId']!;
              return ChatScreen(sessionId: sessionId);
            },
          ),
        ],
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Page not found',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 8),
            Text(
              state.error.toString(),
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go('/home'),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    ),
  );
});

// Navigation helpers
extension AppRouterExtension on GoRouter {
  void goToPatientDetail(String patientId) {
    go('/patients/$patientId');
  }
  
  void goToPatientProfile(String patientId) {
    go('/patients/$patientId/profile');
  }
  
  void goToLabResults(String patientId) {
    go('/patients/$patientId/lab-results');
  }
  
  void goToMedications(String patientId) {
    go('/patients/$patientId/medications');
  }
  
  void goToMedicalNotes(String patientId) {
    go('/patients/$patientId/notes');
  }
  
  void goToPatientChat(String patientId) {
    go('/patients/$patientId/chat');
  }
  
  void goToChatSession(String sessionId) {
    go('/chat/$sessionId');
  }
}
