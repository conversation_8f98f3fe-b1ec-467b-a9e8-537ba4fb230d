import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../features/patient/models/patient.dart';
import '../../features/patient/models/lab_result.dart';
import '../../features/patient/models/medication.dart';
import '../../features/patient/models/medical_note.dart';
import '../../features/chat/models/chat_message.dart';

class StorageService {
  static late Box<Patient> _patientsBox;
  static late Box<LabResult> _labResultsBox;
  static late Box<Medication> _medicationsBox;
  static late Box<MedicalNote> _notesBox;
  static late Box<ChatSession> _chatSessionsBox;
  static late SharedPreferences _prefs;
  
  static Future<void> init() async {
    // Register Hive adapters
    _registerAdapters();
    
    // Open boxes
    _patientsBox = await Hive.openBox<Patient>('patients');
    _labResultsBox = await Hive.openBox<LabResult>('lab_results');
    _medicationsBox = await Hive.openBox<Medication>('medications');
    _notesBox = await Hive.openBox<MedicalNote>('medical_notes');
    _chatSessionsBox = await Hive.openBox<ChatSession>('chat_sessions');
    
    // Initialize SharedPreferences
    _prefs = await SharedPreferences.getInstance();
  }
  
  static void _registerAdapters() {
    // Register all Hive type adapters
    if (!Hive.isAdapterRegistered(0)) {
      Hive.registerAdapter(PatientAdapter());
      Hive.registerAdapter(AddressAdapter());
      Hive.registerAdapter(LabResultAdapter());
      Hive.registerAdapter(LabResultStatusAdapter());
      Hive.registerAdapter(LabPanelAdapter());
      Hive.registerAdapter(MedicationAdapter());
      Hive.registerAdapter(MedicationStatusAdapter());
      Hive.registerAdapter(MedicationHistoryAdapter());
      Hive.registerAdapter(MedicalNoteAdapter());
      Hive.registerAdapter(NoteTypeAdapter());
      Hive.registerAdapter(VisitAdapter());
      Hive.registerAdapter(VisitTypeAdapter());
      Hive.registerAdapter(VisitStatusAdapter());
      Hive.registerAdapter(VitalSignsAdapter());
      Hive.registerAdapter(ChatMessageAdapter());
      Hive.registerAdapter(MessageTypeAdapter());
      Hive.registerAdapter(MessageSenderAdapter());
      Hive.registerAdapter(MessageStatusAdapter());
      Hive.registerAdapter(ChatActionAdapter());
      Hive.registerAdapter(ActionTypeAdapter());
      Hive.registerAdapter(PatientContextAdapter());
      Hive.registerAdapter(ChatSessionAdapter());
      Hive.registerAdapter(SessionStatusAdapter());
    }
  }
  
  // Patient operations
  static Future<void> savePatient(Patient patient) async {
    await _patientsBox.put(patient.id, patient);
  }
  
  static Patient? getPatient(String id) {
    return _patientsBox.get(id);
  }
  
  static List<Patient> getAllPatients() {
    return _patientsBox.values.toList();
  }
  
  static Future<void> deletePatient(String id) async {
    await _patientsBox.delete(id);
  }
  
  // Lab results operations
  static Future<void> saveLabResult(LabResult labResult) async {
    await _labResultsBox.put(labResult.id, labResult);
  }
  
  static List<LabResult> getLabResultsForPatient(String patientId) {
    return _labResultsBox.values
        .where((result) => result.patientId == patientId)
        .toList()
      ..sort((a, b) => b.resultDate.compareTo(a.resultDate));
  }
  
  // Medication operations
  static Future<void> saveMedication(Medication medication) async {
    await _medicationsBox.put(medication.id, medication);
  }
  
  static List<Medication> getMedicationsForPatient(String patientId) {
    return _medicationsBox.values
        .where((med) => med.patientId == patientId)
        .toList()
      ..sort((a, b) => b.startDate.compareTo(a.startDate));
  }
  
  // Medical notes operations
  static Future<void> saveMedicalNote(MedicalNote note) async {
    await _notesBox.put(note.id, note);
  }
  
  static List<MedicalNote> getNotesForPatient(String patientId) {
    return _notesBox.values
        .where((note) => note.patientId == patientId)
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }
  
  // Chat operations
  static Future<void> saveChatSession(ChatSession session) async {
    await _chatSessionsBox.put(session.id, session);
  }
  
  static ChatSession? getChatSession(String id) {
    return _chatSessionsBox.get(id);
  }
  
  static List<ChatSession> getAllChatSessions() {
    return _chatSessionsBox.values.toList()
      ..sort((a, b) => b.updatedAt?.compareTo(a.updatedAt ?? a.createdAt) ?? 0);
  }
  
  static List<ChatSession> getChatSessionsForPatient(String patientId) {
    return _chatSessionsBox.values
        .where((session) => session.patientId == patientId)
        .toList()
      ..sort((a, b) => b.updatedAt?.compareTo(a.updatedAt ?? a.createdAt) ?? 0);
  }
  
  // Preferences operations
  static Future<void> setString(String key, String value) async {
    await _prefs.setString(key, value);
  }
  
  static String? getString(String key) {
    return _prefs.getString(key);
  }
  
  static Future<void> setBool(String key, bool value) async {
    await _prefs.setBool(key, value);
  }
  
  static bool? getBool(String key) {
    return _prefs.getBool(key);
  }
  
  static Future<void> setInt(String key, int value) async {
    await _prefs.setInt(key, value);
  }
  
  static int? getInt(String key) {
    return _prefs.getInt(key);
  }
  
  // Clear all data
  static Future<void> clearAll() async {
    await _patientsBox.clear();
    await _labResultsBox.clear();
    await _medicationsBox.clear();
    await _notesBox.clear();
    await _chatSessionsBox.clear();
    await _prefs.clear();
  }
}
