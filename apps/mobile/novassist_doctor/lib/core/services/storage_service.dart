import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import '../../features/patient/models/patient.dart';
import '../../features/patient/models/lab_result.dart';
import '../../features/patient/models/medication.dart';
import '../../features/patient/models/medical_note.dart';
import '../../features/chat/models/chat_message.dart';

class StorageService {
  static late SharedPreferences _prefs;
  
  // In-memory storage for demo purposes
  static final Map<String, Patient> _patients = {};
  static final Map<String, List<LabResult>> _labResults = {};
  static final Map<String, List<Medication>> _medications = {};
  static final Map<String, List<MedicalNote>> _notes = {};
  static final Map<String, ChatSession> _chatSessions = {};
  
  static Future<void> init() async {
    // Initialize SharedPreferences
    _prefs = await SharedPreferences.getInstance();
    
    // Load demo data
    await _loadDemoData();
  }
  
  static Future<void> _loadDemoData() async {
    // Create demo patients
    final demoPatients = [
      Patient(
        id: '1',
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: DateTime(1980, 5, 15),
        gender: 'Male',
        mrn: 'MRN001',
        email: '<EMAIL>',
        phone: '******-0123',
        allergies: ['Penicillin', 'Shellfish'],
        bloodType: 'O+',
        height: 175.0,
        weight: 80.0,
        chronicConditions: ['Hypertension', 'Type 2 Diabetes'],
        lastVisit: DateTime.now().subtract(const Duration(days: 7)),
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
      Patient(
        id: '2',
        firstName: 'Jane',
        lastName: 'Smith',
        dateOfBirth: DateTime(1975, 8, 22),
        gender: 'Female',
        mrn: 'MRN002',
        email: '<EMAIL>',
        phone: '******-0124',
        allergies: ['Latex'],
        bloodType: 'A+',
        height: 165.0,
        weight: 65.0,
        chronicConditions: ['Asthma'],
        lastVisit: DateTime.now().subtract(const Duration(days: 3)),
        createdAt: DateTime.now().subtract(const Duration(days: 60)),
      ),
      Patient(
        id: '3',
        firstName: 'Robert',
        lastName: 'Johnson',
        dateOfBirth: DateTime(1990, 12, 10),
        gender: 'Male',
        mrn: 'MRN003',
        email: '<EMAIL>',
        phone: '******-0125',
        bloodType: 'B+',
        height: 180.0,
        weight: 75.0,
        lastVisit: DateTime.now().subtract(const Duration(days: 14)),
        createdAt: DateTime.now().subtract(const Duration(days: 90)),
      ),
    ];
    
    for (final patient in demoPatients) {
      _patients[patient.id] = patient;
    }
    
    // Create demo lab results
    _labResults['1'] = [
      LabResult(
        id: 'lab1',
        patientId: '1',
        testName: 'Glucose',
        testCode: 'GLU',
        value: '180',
        unit: 'mg/dL',
        referenceRange: '70-100',
        status: LabResultStatus.abnormal,
        collectionDate: DateTime.now().subtract(const Duration(days: 1)),
        resultDate: DateTime.now(),
        category: 'Chemistry',
        flags: ['HIGH'],
      ),
      LabResult(
        id: 'lab2',
        patientId: '1',
        testName: 'HbA1c',
        testCode: 'HBA1C',
        value: '8.5',
        unit: '%',
        referenceRange: '<7.0',
        status: LabResultStatus.abnormal,
        collectionDate: DateTime.now().subtract(const Duration(days: 7)),
        resultDate: DateTime.now().subtract(const Duration(days: 6)),
        category: 'Chemistry',
        flags: ['HIGH'],
      ),
    ];
    
    // Create demo medications
    _medications['1'] = [
      Medication(
        id: 'med1',
        patientId: '1',
        name: 'Metformin',
        genericName: 'Metformin HCl',
        dosage: '500mg',
        frequency: 'Twice daily',
        route: 'Oral',
        startDate: DateTime.now().subtract(const Duration(days: 90)),
        prescribedBy: 'Dr. Smith',
        indication: 'Type 2 Diabetes',
        status: MedicationStatus.active,
        refillsRemaining: 2,
      ),
      Medication(
        id: 'med2',
        patientId: '1',
        name: 'Lisinopril',
        genericName: 'Lisinopril',
        dosage: '10mg',
        frequency: 'Once daily',
        route: 'Oral',
        startDate: DateTime.now().subtract(const Duration(days: 120)),
        prescribedBy: 'Dr. Smith',
        indication: 'Hypertension',
        status: MedicationStatus.active,
        refillsRemaining: 1,
      ),
    ];
    
    // Create demo medical notes
    _notes['1'] = [
      MedicalNote(
        id: 'note1',
        patientId: '1',
        title: 'Follow-up Visit',
        content: 'Patient reports good adherence to medications. Blood pressure well controlled. Glucose levels still elevated, considering dose adjustment.',
        type: NoteType.followUp,
        createdAt: DateTime.now().subtract(const Duration(days: 7)),
        createdBy: 'Dr. Smith',
        department: 'Internal Medicine',
      ),
      MedicalNote(
        id: 'note2',
        patientId: '1',
        title: 'Lab Review',
        content: 'HbA1c remains elevated at 8.5%. Discussed lifestyle modifications and potential medication adjustment with patient.',
        type: NoteType.assessment,
        createdAt: DateTime.now().subtract(const Duration(days: 6)),
        createdBy: 'Dr. Smith',
        department: 'Internal Medicine',
      ),
    ];
  }
  
  // Patient operations
  static Future<void> savePatient(Patient patient) async {
    _patients[patient.id] = patient;
  }
  
  static Patient? getPatient(String id) {
    return _patients[id];
  }
  
  static List<Patient> getAllPatients() {
    return _patients.values.toList();
  }
  
  static Future<void> deletePatient(String id) async {
    _patients.remove(id);
  }
  
  // Lab results operations
  static Future<void> saveLabResult(LabResult labResult) async {
    _labResults[labResult.patientId] ??= [];
    _labResults[labResult.patientId]!.add(labResult);
  }
  
  static List<LabResult> getLabResultsForPatient(String patientId) {
    final results = _labResults[patientId] ?? [];
    results.sort((a, b) => b.resultDate.compareTo(a.resultDate));
    return results;
  }
  
  // Medication operations
  static Future<void> saveMedication(Medication medication) async {
    _medications[medication.patientId] ??= [];
    _medications[medication.patientId]!.add(medication);
  }
  
  static List<Medication> getMedicationsForPatient(String patientId) {
    final meds = _medications[patientId] ?? [];
    meds.sort((a, b) => b.startDate.compareTo(a.startDate));
    return meds;
  }
  
  // Medical notes operations
  static Future<void> saveMedicalNote(MedicalNote note) async {
    _notes[note.patientId] ??= [];
    _notes[note.patientId]!.add(note);
  }
  
  static List<MedicalNote> getNotesForPatient(String patientId) {
    final notes = _notes[patientId] ?? [];
    notes.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return notes;
  }
  
  // Chat operations
  static Future<void> saveChatSession(ChatSession session) async {
    _chatSessions[session.id] = session;
  }
  
  static ChatSession? getChatSession(String id) {
    return _chatSessions[id];
  }
  
  static List<ChatSession> getAllChatSessions() {
    final sessions = _chatSessions.values.toList();
    sessions.sort((a, b) => (b.updatedAt ?? b.createdAt).compareTo(a.updatedAt ?? a.createdAt));
    return sessions;
  }
  
  static List<ChatSession> getChatSessionsForPatient(String patientId) {
    final sessions = _chatSessions.values.where((session) => session.patientId == patientId).toList();
    sessions.sort((a, b) => (b.updatedAt ?? b.createdAt).compareTo(a.updatedAt ?? a.createdAt));
    return sessions;
  }
  
  // Preferences operations
  static Future<void> setString(String key, String value) async {
    await _prefs.setString(key, value);
  }
  
  static String? getString(String key) {
    return _prefs.getString(key);
  }
  
  static Future<void> setBool(String key, bool value) async {
    await _prefs.setBool(key, value);
  }
  
  static bool? getBool(String key) {
    return _prefs.getBool(key);
  }
  
  static Future<void> setInt(String key, int value) async {
    await _prefs.setInt(key, value);
  }
  
  static int? getInt(String key) {
    return _prefs.getInt(key);
  }
  
  // Clear all data
  static Future<void> clearAll() async {
    _patients.clear();
    _labResults.clear();
    _medications.clear();
    _notes.clear();
    _chatSessions.clear();
    await _prefs.clear();
  }
}
