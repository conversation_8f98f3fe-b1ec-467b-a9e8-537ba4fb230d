class AppConfig {
  static late String baseUrl;
  static late String apiKey;
  static late String chatApiUrl;
  static late bool isDebugMode;
  
  static Future<void> init() async {
    // In a real app, these would come from environment variables or remote config
    baseUrl = 'https://api.novassist.ai/v1';
    chatApiUrl = 'https://chat.novassist.ai/v1';
    apiKey = 'your-api-key-here';
    isDebugMode = true; // Set based on build mode
  }
  
  // API Endpoints
  static String get patientsEndpoint => '$baseUrl/patients';
  static String get labResultsEndpoint => '$baseUrl/lab-results';
  static String get medicationsEndpoint => '$baseUrl/medications';
  static String get notesEndpoint => '$baseUrl/notes';
  static String get chatEndpoint => '$chatApiUrl/chat';
  
  // App Constants
  static const String appName = 'Novassist Doctor';
  static const String appVersion = '1.0.0';
  static const Duration apiTimeout = Duration(seconds: 30);
  static const int maxChatHistory = 100;
}
