# Novassist Doctor App

A comprehensive Flutter application for healthcare professionals to manage patient profiles and interact with AI-powered healthcare assistance.

## 🏥 Features

### 📋 Patient Management
- **Complete Patient Profiles**: View comprehensive patient demographics, medical history, and vital signs
- **Lab Results Tracking**: Access and analyze patient lab results with trend analysis
- **Medication Management**: Track current medications, dosages, and potential interactions
- **Medical Notes**: Review and search through patient medical notes and visit history

### 🤖 AI-Powered Chat Interface
- **Context-Aware AI**: Healthcare-trained LLM with access to patient data
- **Smart Queries**: Ask questions about patient history, lab results, medications, and treatment plans
- **Suggested Actions**: AI provides relevant action suggestions based on patient context
- **Medical Knowledge**: General healthcare information and clinical decision support

### 🔍 Advanced Search & Analytics
- **Patient Search**: Quick search by name, MRN, or other identifiers
- **Data Visualization**: Charts and graphs for lab trends and vital signs
- **Clinical Insights**: AI-powered analysis of patient data patterns

## 🛠️ Technical Stack

- **Framework**: Flutter 3.10+
- **State Management**: Riverpod with code generation
- **Navigation**: GoRouter for declarative routing
- **Local Storage**: Hive for offline data persistence
- **HTTP Client**: Dio with Retrofit for API integration
- **UI Components**: Material Design 3 with custom healthcare theme

## 📱 App Structure

```
lib/
├── core/
│   ├── app.dart                 # Main app widget
│   ├── config/
│   │   ├── app_config.dart      # App configuration
│   │   └── app_theme.dart       # Theme and styling
│   ├── router/
│   │   └── app_router.dart      # Navigation setup
│   └── services/
│       └── storage_service.dart # Local data management
├── features/
│   ├── auth/
│   │   └── screens/
│   │       └── login_screen.dart
│   ├── home/
│   │   └── screens/
│   │       └── home_screen.dart
│   ├── patient/
│   │   ├── models/              # Patient data models
│   │   ├── providers/           # State management
│   │   ├── services/            # API services
│   │   ├── screens/             # UI screens
│   │   └── widgets/             # Reusable components
│   └── chat/
│       ├── models/              # Chat data models
│       ├── providers/           # Chat state management
│       ├── services/            # AI chat services
│       ├── screens/             # Chat UI
│       └── widgets/             # Chat components
└── main.dart                    # App entry point
```

## 🚀 Getting Started

### Prerequisites
- Flutter SDK 3.10.0 or higher
- Dart SDK 3.0.0 or higher
- Android Studio / VS Code with Flutter extensions

### Installation

1. **Navigate to the Flutter app directory**
   ```bash
   cd apps/mobile/novassist_doctor
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Generate code (for models and providers)**
   ```bash
   flutter packages pub run build_runner build
   ```

4. **Run the app**
   ```bash
   flutter run
   ```

### Demo Login
Use the demo credentials to explore the app:
- **Email**: <EMAIL>
- **Password**: demo123

## 🏗️ Key Components

### Patient Models
- `Patient`: Complete patient demographics and medical information
- `LabResult`: Laboratory test results with reference ranges and status
- `Medication`: Current and historical medication records
- `MedicalNote`: Clinical notes and visit documentation

### Chat System
- `ChatMessage`: Individual chat messages with AI context
- `ChatSession`: Conversation sessions with patient context
- `PatientContext`: Patient data available to AI for context-aware responses

### State Management
- **Riverpod Providers**: Reactive state management for all app data
- **Code Generation**: Automatic provider generation for type safety
- **Local Storage**: Offline-first approach with Hive database

## 🔧 Configuration

### API Configuration
Update `lib/core/config/app_config.dart` with your API endpoints:

```dart
static String baseUrl = 'https://api.novassist.ai/v1';
static String chatApiUrl = 'https://chat.novassist.ai/v1';
static String apiKey = 'your-api-key-here';
```

### Theme Customization
Modify `lib/core/config/app_theme.dart` to customize the app appearance:

```dart
static const Color primaryColor = Color(0xFF4F46E5); // Indigo
static const Color secondaryColor = Color(0xFF7C3AED); // Purple
static const Color accentColor = Color(0xFF10B981); // Green
```

## 📊 Features in Detail

### Patient Profile Screen
- Complete patient demographics
- Medical history summary
- Quick access to lab results, medications, and notes
- Direct AI chat integration

### AI Chat Interface
- Context-aware conversations about patient care
- Access to patient medical records during chat
- Suggested actions based on patient data
- Healthcare knowledge base integration

### Lab Results Visualization
- Trend analysis with charts
- Reference range comparisons
- Abnormal value highlighting
- Historical result tracking

### Medication Management
- Active medication tracking
- Drug interaction checking
- Dosage and frequency monitoring
- Prescription history

## 🔒 Security & Privacy

- **Local Data Encryption**: Sensitive data encrypted in local storage
- **API Security**: Secure API communication with authentication
- **HIPAA Compliance**: Designed with healthcare privacy standards in mind
- **Offline Capability**: Core functionality available without internet

## 🧪 Testing

Run tests with:
```bash
flutter test
```

For integration tests:
```bash
flutter test integration_test/
```

## 📱 Platform Support

- **iOS**: iOS 12.0+
- **Android**: Android API 21+ (Android 5.0)
- **Web**: Modern browsers (Chrome, Firefox, Safari, Edge)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is proprietary and confidential. All rights reserved by Novassist.

## 📞 Support

For support and questions:
- **Email**: <EMAIL>
- **Documentation**: [docs.novassist.ai](https://docs.novassist.ai)
- **Issues**: GitHub Issues

---

**Built with ❤️ for better healthcare**
