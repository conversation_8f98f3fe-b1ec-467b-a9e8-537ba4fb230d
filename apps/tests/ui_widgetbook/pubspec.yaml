name: ui_widgetbook
description: "WidgetBook custom widget scenario testing application"
publish_to: 'none'
version: 0.1.0

environment:
  sdk: '>=3.4.3 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  intl: any

  logging: ^1.2.0
  equatable: ^2.0.7
  provider: ^6.1.2
  widgetbook: ^3.14.3
  widgetbook_annotation: ^3.5.0
  dotted_border: ^2.1.0
  re_editor: ^0.7.0
  re_highlight: ^0.0.3
  country_pickers: ^3.0.1

  utilities_ab:
    path: ../../../libs/commons/dart/utilities
  ui_widgets_component:
    path: ../../../libs/component/flutter/ui_widgets

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  widgetbook_generator: ^3.8.0
  build_runner: ^2.4.11

flutter:
  generate: true
  uses-material-design: true
  
  assets:
    - assets/