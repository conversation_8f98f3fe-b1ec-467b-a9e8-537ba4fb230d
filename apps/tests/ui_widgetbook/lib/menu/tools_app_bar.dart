import 'package:flutter/material.dart';
import 'package:widgetbook/widgetbook.dart';
import 'package:widgetbook_annotation/widgetbook_annotation.dart' as widgetbook;

import 'package:ui_widgets_component/ui_widgets.dart';

import '../knobs/double_slider.dart';

@widgetbook.UseCase(name: 'Tools App Bar', type: ToolsAppBar)
Widget buildToolsAppBarUseCase(BuildContext context) {
  bool unconstrainedWidth = context.knobs.boolean(
    label: 'Unconstrained Content Area Width',
    description: 'Allow the view to take up as much width as possible.',
    initialValue: false,
  );
  double? width = context.knobs.doubleSlider(
    label: 'Content Area Width',
    description: 'The width of the display area for the view. '
        'Has no effect if the unconstrained width is enabled.',
    initialValue: 600.0,
    minValue: 300.0,
    maxValue: 800.0,
  );
  if (unconstrainedWidth) {
    width = null;
  }

  bool unconstrainedHeight = context.knobs.boolean(
    label: 'Unconstrained Content Area Height',
    description: 'Allow the view to take up as much height as possible.',
    initialValue: false,
  );
  double? height = context.knobs.doubleSlider(
    label: 'Content Area Height',
    description: 'The height of the display area for the view. '
        'Has no effect if the unconstrained height is enabled.',
    initialValue: 400.0,
    minValue: 300.0,
    maxValue: 800.0,
  );
  if (unconstrainedHeight) {
    height = null;
  }

  double leftIndent = context.knobs.doubleSlider(
    label: 'Left Toolbar Indent',
    description: 'Space between the left edge of the AppBar and '
        'the left toolbar. This adds room for a leading widget.',
    initialValue: 0.0,
    minValue: 0.0,
    maxValue: 128.0,
  );

  double rightIndent = context.knobs.doubleSlider(
    label: 'Right Toolbar Indent',
    description: 'Space between the right edge of the AppBar and '
        'the right toolbar. This adds room for a action widgets.',
    initialValue: 0.0,
    minValue: 0.0,
    maxValue: 128.0,
  );

  return SizedBox(
    width: width,
    height: height,
    child: Scaffold(
      appBar: ToolsAppBar(
        context: context,
        leftIndent: leftIndent,
        leftToolBar: ToolBar(
          tools: [
            ToolIconButton(
              title: 'Print Open Document',
              tooltip: 'print open document',
              iconData: Icons.print_outlined,
              onPressed: (_) {},
            ),
            ToolIconButton(
              title: 'Upload Document Folder',
              tooltip: 'upload documents in folder',
              iconData: Icons.drive_folder_upload,
              onPressed: (_) {},
            ),
            ToolIconButton(
              title: 'Upload Document',
              tooltip: 'upload single document',
              iconData: Icons.file_upload_outlined,
              onPressed: (_) {},
            ),
          ],
        ),
        rightIndent: rightIndent,
        rightToolBar: ToolBar(
          tools: [
            ToolIconButton(
              title: 'Sign out',
              tooltip: 'log out of application',
              iconData: Icons.logout,
              onPressed: (_) {},
            ),
          ],
        ),
        title: 'Tools Menu Bar',
        centerTitle: true,
      ),
      body: LayoutBuilder(
        builder: (context, constraints) {
          return Center(
            child: Text('${constraints.maxWidth} x ${constraints.maxHeight}'),
          );
        },
      ),
    ),
  );
}
