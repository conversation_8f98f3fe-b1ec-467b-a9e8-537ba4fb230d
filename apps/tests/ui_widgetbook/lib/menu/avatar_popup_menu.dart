import 'package:flutter/material.dart';
import 'package:widgetbook/widgetbook.dart';
import 'package:widgetbook_annotation/widgetbook_annotation.dart' as widgetbook;

// Import the widget from your app
import 'package:ui_widgets_component/menu/avatar_popup_menu.dart';

@widgetbook.UseCase(name: 'Avatar Popup Menu', type: AvatarPopupMenu)
Widget buildAvatarPopupMenuUseCase(BuildContext context) {
  final avatarRadius = context.knobs.double.input(
    label: 'Avatar Size',
    description: 'The radius of the CircleAvatar.',
    initialValue: 64.0,
  );
  final avatarInitials = context.knobs.string(
    label: 'Avatar Initials',
    description: 'Initials displayed in the avatar when no image url is set.',
    initialValue: 'AB',
  );
  final avatarImgUrl = context.knobs.string(
    label: 'Avatar Image Url',
    description: 'The the avatar image loaded via url.',
    initialValue: 'https://gravatar.com/avatar/'
        'a907f68d34b138fda4755adec497d8a5?s=400&d=robohash&r=x',
  );

  final ImageProvider avatarBgImage;
  if (avatarImgUrl.isEmpty) {
    avatarBgImage = const AssetImage('assets/doctor.png');
  } else {
    avatarBgImage = NetworkImage(avatarImgUrl);
  }

  return AvatarPopupMenu<Menu>(
    avatarRadius: avatarRadius,
    avatarBgImage: avatarBgImage,
    avatarInitials: avatarImgUrl.isEmpty ? avatarInitials : null,
    offset: const Offset(-90, 5),
    menuItems: const <PopupMenuEntry<Menu>>[
      PopupMenuItem<Menu>(
        value: Menu.preview,
        child: ListTile(
          leading: Icon(Icons.visibility_outlined),
          title: Text('Preview'),
        ),
      ),
      PopupMenuItem<Menu>(
        value: Menu.share,
        child: ListTile(
          leading: Icon(Icons.share_outlined),
          title: Text('Share'),
        ),
      ),
      PopupMenuItem<Menu>(
        value: Menu.getLink,
        child: ListTile(
          leading: Icon(Icons.link_outlined),
          title: Text('Get link'),
        ),
      ),
      PopupMenuDivider(),
      PopupMenuItem<Menu>(
        value: Menu.remove,
        child: ListTile(
          leading: Icon(Icons.delete_outline),
          title: Text('Remove'),
        ),
      ),
      PopupMenuItem<Menu>(
        value: Menu.download,
        child: ListTile(
          leading: Icon(Icons.download_outlined),
          title: Text('Download'),
        ),
      ),
    ],
    onSelected: (result) {
      switch (result) {
        case Menu.preview:
          print('Preview');
          break;
        case Menu.share:
          print('Share');
          break;
        case Menu.getLink:
          print('Get link');
          break;
        case Menu.remove:
          print('Remove');
          break;
        case Menu.download:
          print('Download');
          break;
      }
    },
  );
}

enum Menu { preview, share, getLink, remove, download }
