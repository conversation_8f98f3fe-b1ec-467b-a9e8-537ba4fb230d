import 'package:flutter/material.dart';
import 'package:equatable/equatable.dart';
import 'package:ui_widgetbook/knobs/int_slider.dart';
import 'package:widgetbook/widgetbook.dart';
import 'package:widgetbook_annotation/widgetbook_annotation.dart' as widgetbook;

import 'package:ui_widgets_component/ui_widgets.dart';

import '../knobs/double_slider.dart';

@widgetbook.UseCase(name: 'Tools Scaffold', type: ToolsScaffold)
Widget buildToolsScaffoldUseCase(BuildContext context) {
  bool unconstrainedWidth = context.knobs.boolean(
    label: 'Unconstrained Content Area Width',
    description: 'Allow the view to take up as much width as possible.',
    initialValue: false,
  );
  double? width = context.knobs.doubleSlider(
    label: 'Content Area Width',
    description: 'The width of the display area for the view. '
        'Has no effect if the unconstrained width is enabled.',
    initialValue: 850.0,
    minValue: 600.0,
    maxValue: 1200.0,
  );
  if (unconstrainedWidth) {
    width = null;
  }

  bool unconstrainedHeight = context.knobs.boolean(
    label: 'Unconstrained Content Area Height',
    description: 'Allow the view to take up as much height as possible.',
    initialValue: false,
  );
  double? height = context.knobs.doubleSlider(
    label: 'Content Area Height',
    description: 'The height of the display area for the view. '
        'Has no effect if the unconstrained height is enabled.',
    initialValue: 800.0,
    minValue: 300.0,
    maxValue: 800.0,
  );
  if (unconstrainedHeight) {
    height = null;
  }

  int toolbarToolsAlignment = context.knobs.intSlider(
    label: 'Alignment of Tools on Toolbar',
    description: 'Alignment of tools where 1-left, 2-center, 3-right.',
    initialValue: 2,
    minValue: 1,
    maxValue: 3,
  );

  bool floatToolsLocation = context.knobs.boolean(
    label: 'Float tools location',
    description: 'Floats tools either to the left or right within '
        'the content view when the view is in compact mode.',
    initialValue: false,
  );

  bool showStatusBar = context.knobs.boolean(
    label: 'Show status bar',
    description: 'Show a status bar with various status tools.',
    initialValue: false,
  );

  ValueNotifier<_PageNumberParams> pageNumberParams =
      ValueNotifier<_PageNumberParams>(
    const _PageNumberParams(
      currPage: 0,
      lastPage: 9,
    ),
  );
  ValueNotifier<_PageZoomParams> pageZoomParams =
      ValueNotifier<_PageZoomParams>(
    const _PageZoomParams(
      singlPageMode: false,
      tiledPageMode: false,
      currZoom: 1.0,
      fitWidth: false,
      fitPage: false,
    ),
  );

  return SizedBox(
    width: width,
    height: height,
    child: ToolsScaffold(
      toolsAlignment: toolbarToolsAlignment == 1
          ? ToolsAlignment.left
          : toolbarToolsAlignment == 2
              ? ToolsAlignment.center
              : ToolsAlignment.right,
      tools: [
        ToolGroup<_PageNumberParams>(
          toolValue: pageNumberParams,
          tools: [
            ButtonTool<_PageNumberParams>(
              iconData: (_) => Icons.first_page,
              tooltip: (_) => 'First Page',
              enabled: (params) => params!.currPage != 0,
              onPressed: (params) {
                pageNumberParams.value = params!.copyWith(
                  currPage: 0,
                );
              },
            ),
            ButtonTool<_PageNumberParams>(
              iconData: (_) => Icons.arrow_back,
              tooltip: (_) => 'Previous Page',
              enabled: (params) => params!.currPage != 0,
              onPressed: (params) {
                pageNumberParams.value = params!.copyWith(
                  currPage: params.currPage - 1,
                );
              },
            ),
            TextButtonTool(
              text: (params) => 'Page ${params!.currPage + 1}',
              tooltip: (_) => 'Page',
              width: 110,
              options: (_) => [
                'Page 1',
                'Page 2',
                'Page 3',
                'Page 4',
                'Page 5',
                'Page 6',
                'Page 7',
                'Page 8',
                'Page 9',
                'Page 10',
              ],
              onOption: (option, index) {
                pageNumberParams.value = pageNumberParams.value.copyWith(
                  currPage: index,
                );
                print('Selected: $option / $index');
              },
            ),
            ButtonTool<_PageNumberParams>(
              iconData: (_) => Icons.arrow_forward,
              tooltip: (_) => 'Next Page',
              enabled: (params) => params!.currPage != 9,
              onPressed: (params) {
                pageNumberParams.value = params!.copyWith(
                  currPage: params.currPage + 1,
                );
              },
            ),
            ButtonTool<_PageNumberParams>(
              iconData: (_) => Icons.last_page,
              tooltip: (_) => 'Last Page',
              enabled: (params) => params!.currPage != 9,
              onPressed: (params) {
                pageNumberParams.value = params!.copyWith(
                  currPage: params.lastPage,
                );
              },
            ),
          ],
        ),
        const DividerTool(),
        ToolGroup<_PageZoomParams>(
          toolValue: pageZoomParams,
          tools: [
            ButtonTool<_PageZoomParams>(
              icon: (params) => params!.singlPageMode
                  ? const ImageIcon(
                      AssetImage(
                        'multi-page.png',
                      ),
                    )
                  : const ImageIcon(
                      AssetImage(
                        'single-page.png',
                      ),
                    ),
              tooltip: (params) => params!.singlPageMode //
                  ? 'Multi Page View'
                  : 'Single Page View',
              onPressed: (params) {
                pageZoomParams.value = params!.copyWith(
                  singlPageMode: !params.singlPageMode,
                );
              },
            ),
            ButtonTool<_PageZoomParams>(
              icon: (_) => const ImageIcon(
                AssetImage(
                  'fit-width.png',
                ),
              ),
              tooltip: (_) => 'Fit Width',
              onPressed: (params) {
                final fitWidth = !params!.fitWidth;
                pageZoomParams.value = params.copyWith(
                  fitWidth: fitWidth,
                  fitPage: fitWidth ? false : null,
                );
              },
              isPressed: (param) => param!.fitWidth,
            ),
            ButtonTool<_PageZoomParams>(
              iconData: (_) => Icons.zoom_out,
              tooltip: (_) => 'Zoom Out',
              enabled: (params) => params!.currZoom > 0,
              onPressed: (params) {
                pageZoomParams.value = params!.copyWith(
                  currZoom: params.currZoom - 1,
                  fitWidth: false,
                  fitPage: false,
                );
              },
            ),
            StatusTextTool<_PageZoomParams>(
              width: 150,
              text: (value) => 'Zoom ${(value!.currZoom * 100).toInt()}%',
              tooltip: (_) => 'Zoom',
            ),
            ButtonTool<_PageZoomParams>(
              iconData: (_) => Icons.zoom_in,
              tooltip: (_) => 'Zoom In',
              enabled: (params) => params!.currZoom < 20,
              onPressed: (params) {
                pageZoomParams.value = params!.copyWith(
                  currZoom: params.currZoom + 1,
                  fitWidth: false,
                  fitPage: false,
                );
              },
            ),
            ButtonTool<_PageZoomParams>(
              icon: (_) => const ImageIcon(
                AssetImage(
                  'fit-page.png',
                ),
              ),
              tooltip: (_) => 'Fit Page',
              onPressed: (params) {
                final fitPage = !params!.fitPage;
                pageZoomParams.value = params.copyWith(
                  fitWidth: fitPage ? false : null,
                  fitPage: !params.fitPage,
                );
              },
              isPressed: (param) => param!.fitPage,
            ),
            ButtonTool<_PageZoomParams>(
              icon: (params) {
                return params!.tiledPageMode
                    ? const ImageIcon(
                        AssetImage(
                          'tiled-pages.png',
                        ),
                      )
                    : const ImageIcon(
                        AssetImage(
                          'single-column-pages.png',
                        ),
                      );
              },
              tooltip: (params) => params!.tiledPageMode //
                  ? 'Tiled Page View'
                  : 'Single Column Page View',
              enabled: (params) => !params!.singlPageMode,
              onPressed: (params) {
                pageZoomParams.value = params!.copyWith(
                  tiledPageMode: !params.tiledPageMode,
                );
              },
            ),
          ],
        ),
      ],
      statusItems: showStatusBar
          ? [
              TextButtonTool.statusbar(
                iconData: (_) => Icons.person,
                text: (_) => 'John Doe',
              ),
              TextButtonTool.statusbar(
                iconData: (_) => Icons.business,
                text: (_) => 'Acme Organization',
                onPressed: (_) {},
                options: (_) => [
                  'Acme Organization',
                  'Beta Organization',
                  'Theta Organization',
                ],
                onOption: (option, _) {
                  print('Selected: $option');
                },
              ),
              const DividerTool(),
              const SpacerTool(),
              TextButtonTool.statusbar(
                text: (_) => '© Appbricks, Inc.',
                textShadow: true,
              ),
              const SpacerTool(),
              const DividerTool(),
              TextButtonTool.statusbar(
                text: (_) => 'Charges: \$222',
              ),
              const DividerTool(),
              TextButtonTool.statusbar(
                text: (_) => 'Usage: 10 GB',
              ),
            ]
          : null,
      floatAlignment: floatToolsLocation
          ? FloatToolsAlignment.right
          : FloatToolsAlignment.left,
      body: LayoutBuilder(
        builder: (context, constraints) {
          return Center(
            child: Text('${constraints.maxWidth} x ${constraints.maxHeight}'),
          );
        },
      ),
    ),
  );
}

// View parameters for the document image viewer
class _PageNumberParams extends Equatable {
  final int currPage;
  final int lastPage;

  const _PageNumberParams({
    required this.currPage,
    required this.lastPage,
  });

  _PageNumberParams copyWith({
    int? currPage,
    int? lastPage,
  }) {
    return _PageNumberParams(
      currPage: currPage ?? this.currPage,
      lastPage: lastPage ?? this.lastPage,
    );
  }

  @override
  List<Object?> get props => [
        currPage,
        lastPage,
      ];
}

// View parameters for the document image viewer
class _PageZoomParams extends Equatable {
  final bool singlPageMode;
  final bool tiledPageMode;

  final double currZoom;

  final bool fitWidth;
  final bool fitPage;

  const _PageZoomParams({
    required this.singlPageMode,
    required this.tiledPageMode,
    required this.currZoom,
    required this.fitWidth,
    required this.fitPage,
  });

  _PageZoomParams copyWith({
    bool? singlPageMode,
    bool? tiledPageMode,
    double? currZoom,
    bool? fitWidth,
    bool? fitPage,
  }) {
    return _PageZoomParams(
      // ignore: prefer_if_null_operators
      singlPageMode: singlPageMode == null ? this.singlPageMode : singlPageMode,
      // ignore: prefer_if_null_operators
      tiledPageMode: tiledPageMode == null ? this.tiledPageMode : tiledPageMode,
      currZoom: currZoom ?? this.currZoom,
      // ignore: prefer_if_null_operators
      fitWidth: fitWidth == null ? this.fitWidth : fitWidth,
      // ignore: prefer_if_null_operators
      fitPage: fitPage == null ? this.fitPage : fitPage,
    );
  }

  @override
  List<Object?> get props => [
        singlPageMode,
        tiledPageMode,
        currZoom,
        fitWidth,
        fitPage,
      ];
}
