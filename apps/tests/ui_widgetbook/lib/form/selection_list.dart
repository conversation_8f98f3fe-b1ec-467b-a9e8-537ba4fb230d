import 'package:flutter/material.dart';
import 'package:ui_widgetbook/knobs/int_slider.dart';
import 'package:widgetbook/widgetbook.dart';
import 'package:widgetbook_annotation/widgetbook_annotation.dart' as widgetbook;

// Import the widget from your app
import 'package:ui_widgets_component/form/selection_list.dart';

@widgetbook.UseCase(name: 'Selection List', type: SelectionList)
Widget buildSelectionListUseCase(BuildContext context) {
  final itemHeight = context.knobs.intSlider(
    label: 'Item Height',
    description: 'Height of each item in the list',
    minValue: 60,
    maxValue: 120,
    initialValue: 80,
  );

  final borderRadius = context.knobs.intSlider(
    label: 'Border Radius',
    description: 'Border radius for the rounded corners',
    minValue: 4,
    maxValue: 20,
    initialValue: 12,
  );

  final spacing = context.knobs.intSlider(
    label: 'Spacing',
    description: 'Space between items',
    minValue: 4,
    maxValue: 16,
    initialValue: 8,
  );

  final iconSize = context.knobs.intSlider(
    label: 'Icon Size',
    description: 'Size of the icons',
    minValue: 16,
    maxValue: 32,
    initialValue: 24,
  );

  final showIcons = context.knobs.boolean(
    label: 'Show Icons',
    description: 'Whether to display icons for each item',
    initialValue: true,
  );

  final showSubtitles = context.knobs.boolean(
    label: 'Show Subtitles',
    description: 'Whether to display subtitles for each item',
    initialValue: true,
  );

  final showTrailingIconsOnlyIfSelected = context.knobs.boolean(
    label: 'Show Trailing Icons Only If Selected',
    description: 'Show trailing icons only for the selected item',
    initialValue: false,
  );

  final initialSelectedIndex = context.knobs.intSlider(
    label: 'Initial Selected Index',
    description: 'Index of the initially selected item (0-4)',
    minValue: 0,
    maxValue: 4,
    initialValue: 1,
  );

  // Sample data
  final items = [
    SelectedListItem(
      value: 'option1',
      title: 'Personal Account',
      subtitle: showSubtitles ? 'Individual user account' : null,
      icon: showIcons ? Icons.person : null,
      iconColor: Colors.blue,
      trailingIcons: [
        TrailingIcon<String>(
          icon: Icons.info_outline,
          tooltip: 'Info',
          onTap: (value) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Info for $value')),
            );
          },
        ),
      ],
    ),
    SelectedListItem(
      value: 'option2',
      title: 'Business Account',
      subtitle: showSubtitles ? 'Company or organization account' : null,
      icon: showIcons ? Icons.business : null,
      iconColor: Colors.green,
      trailingIcons: [
        TrailingIcon<String>(
          icon: Icons.edit,
          tooltip: 'Edit',
          onTap: (value) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Edit $value')),
            );
          },
        ),
        TrailingIcon<String>(
          icon: Icons.delete_outline,
          tooltip: 'Delete',
          onTap: (value) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Delete $value')),
            );
          },
        ),
      ],
    ),
    SelectedListItem(
      value: 'option3',
      title: 'Developer Account',
      subtitle: showSubtitles ? 'For developers and technical users' : null,
      icon: showIcons ? Icons.code : null,
      iconColor: Colors.orange,
      trailingIcons: [
        TrailingIcon<String>(
          icon: Icons.bug_report,
          tooltip: 'Report Bug',
          onTap: (value) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Report bug for $value')),
            );
          },
        ),
      ],
    ),
    SelectedListItem(
      value: 'option4',
      title: 'Enterprise Account',
      subtitle:
          showSubtitles ? 'Large organization with advanced features' : null,
      icon: showIcons ? Icons.business : null,
      iconColor: Colors.purple,
      trailingIcons: [
        TrailingIcon<String>(
          icon: Icons.settings,
          tooltip: 'Settings',
          onTap: (value) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Settings for $value')),
            );
          },
        ),
      ],
    ),
    SelectedListItem(
      value: 'option5',
      title: 'Student Account',
      subtitle: showSubtitles ? 'Educational institution account' : null,
      icon: showIcons ? Icons.school : null,
      iconColor: Colors.red,
      trailingIcons: [
        TrailingIcon<String>(
          icon: Icons.star,
          tooltip: 'Favorite',
          onTap: (value) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Marked $value as favorite')),
            );
          },
        ),
      ],
    ),
  ];

  final initialSelectedValue = initialSelectedIndex < items.length
      ? items[initialSelectedIndex].value
      : null;

  return _SelectionListDemo(
    items: items,
    initialSelectedValue: initialSelectedValue,
    itemHeight: itemHeight.toDouble(),
    borderRadius: borderRadius.toDouble(),
    spacing: spacing.toDouble(),
    iconSize: iconSize.toDouble(),
    showTrailingIconsOnlyIfSelected: showTrailingIconsOnlyIfSelected,
  );
}

@widgetbook.UseCase(name: 'Selection List - Custom Colors', type: SelectionList)
Widget buildSelectionListCustomColorsUseCase(BuildContext context) {
  final selectedBorderColor = context.knobs.color(
    label: 'Selected Border Color',
    description: 'Color of the border when item is selected',
    initialValue: Colors.blue,
  );

  final selectedBackgroundColor = context.knobs.color(
    label: 'Selected Background Color',
    description: 'Background color when item is selected',
    initialValue: Colors.blue.withValues(alpha: 0.1),
  );

  final hoverBorderColor = context.knobs.color(
    label: 'Hover Border Color',
    description: 'Color of the border on hover',
    initialValue: Colors.blue.withValues(alpha: 0.5),
  );

  final hoverBackgroundColor = context.knobs.color(
    label: 'Hover Background Color',
    description: 'Background color on hover',
    initialValue: Colors.grey.withValues(alpha: 0.1),
  );

  final items = [
    SelectedListItem(
      value: 'basic',
      title: 'Basic Plan',
      subtitle: 'Essential features for individuals',
      icon: Icons.star_outline,
    ),
    SelectedListItem(
      value: 'pro',
      title: 'Pro Plan',
      subtitle: 'Advanced features for professionals',
      icon: Icons.star_half,
    ),
    SelectedListItem(
      value: 'enterprise',
      title: 'Enterprise Plan',
      subtitle: 'Full features for large organizations',
      icon: Icons.star,
    ),
  ];

  return _SelectionListDemo(
    items: items,
    initialSelectedValue: 'pro',
    selectedBorderColor: selectedBorderColor,
    selectedBackgroundColor: selectedBackgroundColor,
    hoverBorderColor: hoverBorderColor,
    hoverBackgroundColor: hoverBackgroundColor,
  );
}

class _SelectionListDemo extends StatefulWidget {
  final List<SelectedListItem<String>> items;
  final String? initialSelectedValue;
  final double? itemHeight;
  final double? borderRadius;
  final double? spacing;
  final double? iconSize;
  final Color? selectedBorderColor;
  final Color? selectedBackgroundColor;
  final Color? hoverBorderColor;
  final Color? hoverBackgroundColor;
  final bool? showTrailingIconsOnlyIfSelected;

  const _SelectionListDemo({
    required this.items,
    this.initialSelectedValue,
    this.itemHeight,
    this.borderRadius,
    this.spacing,
    this.iconSize,
    this.selectedBorderColor,
    this.selectedBackgroundColor,
    this.hoverBorderColor,
    this.hoverBackgroundColor,
    this.showTrailingIconsOnlyIfSelected,
  });

  @override
  State<_SelectionListDemo> createState() => _SelectionListDemoState();
}

class _SelectionListDemoState extends State<_SelectionListDemo> {
  String? selectedValue;

  @override
  void initState() {
    super.initState();
    selectedValue = widget.initialSelectedValue;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 400),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SelectionList<String>(
                items: widget.items,
                selectedValue: selectedValue,
                itemHeight: widget.itemHeight ?? 80.0,
                borderRadius: widget.borderRadius ?? 12.0,
                spacing: widget.spacing ?? 8.0,
                iconSize: widget.iconSize ?? 24.0,
                selectedBorderColor: widget.selectedBorderColor,
                selectedBackgroundColor: widget.selectedBackgroundColor,
                hoverBorderColor: widget.hoverBorderColor,
                hoverBackgroundColor: widget.hoverBackgroundColor,
                showTrailingIconsOnlyIfSelected:
                    widget.showTrailingIconsOnlyIfSelected ?? false,
                onSelectionChanged: (value) {
                  setState(() {
                    selectedValue = value;
                  });
                  print('Selected: $value');
                },
              ),
              const SizedBox(height: 24),
              if (selectedValue != null) ...[
                Text(
                  'Selected: $selectedValue',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  'You selected the ${selectedValue!.toUpperCase()} option.',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
