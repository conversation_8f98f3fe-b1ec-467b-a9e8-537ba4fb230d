import 'package:flutter/material.dart';
import 'package:ui_widgetbook/knobs/int_slider.dart';
import 'package:widgetbook/widgetbook.dart';
import 'package:widgetbook_annotation/widgetbook_annotation.dart' as widgetbook;

// Import the widget from your app
import 'package:ui_widgets_component/form/text_input.dart';

@widgetbook.UseCase(name: 'Multi-Line Text Input', type: TextInput)
Widget buildAvatarButtonUseCase(BuildContext context) {
  final labelText = context.knobs.string(
    label: 'Label Text',
    description: 'The text displayed when the input is empty.',
    initialValue: 'Enter your text here',
  );

  final maxCharsInLine = context.knobs.intSlider(
    label: 'Max Characters in a Line',
    description: 'The maximum number characters an input line can have.',
    initialValue: 50,
    minValue: 10,
    maxValue: 100,
  );

  final maxLines = context.knobs.intSlider(
    label: 'Max Lines',
    description: 'The maximum number of lines the input can have.',
    initialValue: 5,
    minValue: 1,
    maxValue: 20,
  );

  final vExpandable = context.knobs.boolean(
    label: 'Vertically Expandable',
    description: 'Whether the input box can be resized vertically.',
    initialValue: false,
  );

  final hExpandable = context.knobs.boolean(
    label: 'Horizontally Expandable',
    description: 'Whether the input box can be resized vertically.',
    initialValue: false,
  );

  final initialText = context.knobs.string(
    label: 'Initial Text',
    description: 'The initial text displayed in the input.',
    initialValue: 'Hello World',
  );

  return Center(
    child: Container(
      color: Colors.black26,
      child: TextInput(
        labelText: labelText,
        maxCharsInLine: maxCharsInLine,
        maxLines: maxLines,
        hExpandable: hExpandable,
        vExpandable: vExpandable,
        controller: TextEditingController(
          text: initialText,
        ),
      ),
    ),
  );
}
