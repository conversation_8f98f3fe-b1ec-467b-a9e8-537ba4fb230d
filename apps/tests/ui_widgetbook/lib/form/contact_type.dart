import 'package:flutter/material.dart';
import 'package:ui_widgets_component/ui_widgets.dart';

enum ContactType {
  email,
  tollFree,
  voiceAndSMS,
  voiceOnly,
  smsOnly,
  fax;

  static ContactLabels<ContactType> labels(
    BuildContext context,
  ) {
    return ContactLabels.fromLocalization<ContactType>(
      context,
    ).copyWith(
      contactTypes: [
        const ContactTypeSelection<ContactType>(
          value: ContactType.email,
          label: 'Email',
          icon: Icon(Icons.contact_mail),
          inputType: ContactInputType.emailAddress,
        ),
        const ContactTypeSelection<ContactType>(
          value: ContactType.tollFree,
          label: 'Toll Free',
          icon: Icon(Icons.contact_phone),
          inputType: ContactInputType.phoneNumber,
        ),
        const ContactTypeSelection<ContactType>(
          value: ContactType.voiceAndSMS,
          label: 'Voice & SMS',
          icon: Icon(Icons.perm_phone_msg),
          inputType: ContactInputType.phoneNumber,
        ),
        const ContactTypeSelection<ContactType>(
          value: ContactType.voiceOnly,
          label: 'Voice Only',
          icon: Icon(Icons.phone),
          inputType: ContactInputType.phoneNumber,
        ),
        const ContactTypeSelection<ContactType>(
          value: ContactType.smsOnly,
          label: 'SMS Only',
          icon: Icon(Icons.sms),
          inputType: ContactInputType.phoneNumber,
        ),
        const ContactTypeSelection<ContactType>(
          value: ContactType.fax,
          label: 'Fax',
          icon: Icon(Icons.fax),
          inputType: ContactInputType.phoneNumber,
        ),
      ],
    );
  }
}
