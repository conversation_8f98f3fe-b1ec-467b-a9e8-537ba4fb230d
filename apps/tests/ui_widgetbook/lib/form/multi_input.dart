import 'dart:core';
import 'package:flutter/material.dart';
import 'package:widgetbook/widgetbook.dart';
import 'package:widgetbook_annotation/widgetbook_annotation.dart' as widgetbook;

import 'package:ui_widgets_component/ui_widgets.dart';

import '../knobs/double_slider.dart';

@widgetbook.UseCase(name: 'Multi Input', type: MultiInput<InputValue>)
Widget buildAddressInputdUseCase(BuildContext context) {
  final formViewWidth = _displayAreaWidthKnob(context);
  final formViewHeight = _displayAreaHeightKnob(context);

  final showInputGroupBox = _showInputGroupBox(context);

  return Column(
    mainAxisAlignment: MainAxisAlignment.center,
    children: [
      SizedBox(
        width: formViewWidth,
        height: formViewHeight,
        child: MultiInputTest(
          formViewHeight: formViewHeight,
          showInputGroupBox: showInputGroupBox,
        ),
      ),
    ],
  );
}

double _displayAreaWidthKnob(BuildContext context) {
  return context.knobs.doubleSlider(
    label: 'Display Area Width',
    description: 'The width of the display area for the form.',
    initialValue: 500.0,
    minValue: 300.0,
    maxValue: 850.0,
  );
}

double _displayAreaHeightKnob(BuildContext context) {
  return context.knobs.doubleSlider(
    label: 'Display Area Height',
    description: 'The height of the display area for the form.',
    initialValue: 500.0,
    minValue: 300.0,
    maxValue: 850.0,
  );
}

bool _showInputGroupBox(BuildContext context) {
  return context.knobs.boolean(
    label: 'Enclose Input in Group Box',
    initialValue: true,
  );
}

class MultiInputTest extends StatefulWidget {
  final double formViewHeight;

  final bool showInputGroupBox;

  const MultiInputTest({
    super.key,
    required this.formViewHeight,
    required this.showInputGroupBox,
  });

  @override
  State<MultiInputTest> createState() => _MultiInputTestState();
}

class _MultiInputTestState extends State<MultiInputTest> {
  late List<InputValue> inputValues;

  @override
  void initState() {
    super.initState();

    inputValues = [
      InputValue.create(),
    ];
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: DialogForm(
          title: 'Multi Input Test',
          formViewHeight: widget.formViewHeight,
          body: MultiInput<InputValue>(
            values: inputValues,
            groubBox: widget.showInputGroupBox,
            groupBoxBackground: theme.dialogBackgroundColor,
            actionsPadding: const EdgeInsets.only(bottom: 16),
            onAdd: () {
              setState(() {
                inputValues.add(
                  InputValue.create(),
                );
              });
            },
            onRemove: (index) {
              setState(() {
                inputValues.removeAt(index);
              });
            },
            builder: (
              context,
              value,
              index,
              focusNode,
              focusHandler,
            ) {
              return EmailAddressInput(
                labelText: 'Email Address',
                initialValue: value.value,
                focusNode: focusNode,
                validator: EmailAddressValidator(),
                onChanged: (value, isValid) {
                  setState(() {
                    inputValues[index] = inputValues[index].copyWith(
                      value: value,
                      isFieldValid: ('value', isValid),
                    );
                  });
                },
              );
            },
          ),
          actions: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 8),
                child: ElevatedButton.icon(
                  label: const Text('Cancel'),
                  icon: const Icon(Icons.cancel),
                  iconAlignment: IconAlignment.end,
                  onPressed: () {},
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 8),
                child: ElevatedButton.icon(
                  label: const Text('Save'),
                  icon: const Icon(Icons.save),
                  iconAlignment: IconAlignment.end,
                  onPressed: FormValues.isDirty(
                    inputValues,
                    [],
                  )
                      ? () {}
                      : null,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
