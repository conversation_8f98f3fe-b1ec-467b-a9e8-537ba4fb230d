import 'package:flutter/material.dart';
import 'package:widgetbook/widgetbook.dart';
import 'package:widgetbook_annotation/widgetbook_annotation.dart' as widgetbook;

import 'package:ui_widgets_component/ui_widgets.dart';

import '../knobs/double_slider.dart';

import 'addess_type.dart';
import 'address_input_test.dart';

@widgetbook.UseCase(name: 'Address Input', type: AddressInput)
Widget buildAddressInputUseCase(BuildContext context) {
  final formViewWidth = _displayAreaWidthKnob(context);
  final formViewHeight = _displayAreaHeightKnob(context, 639);

  final usAddressFormat = _usAddressFieldKnob(context);
  final showAddressTypeField = _showAddressTypeFieldKnob(context);
  final showDescriptionField = _showDescriptionFieldKnob(context);
  final showCountyField = _showCountyFieldKnob(context);
  final showCountryField = _showCountryFieldKnob(context);

  late AddressValue<AddressType> addressInitial;
  if (usAddressFormat) {
    addressInitial = AddressValue.create<AddressType>(
      type: AddressType.main,
      number: '999',
      street: 'Main St',
      other: 'Suite 100',
      municipality: 'Framingham',
      province: 'MA',
      postalCode: '01721',
      countryCode: 'USA',
    );
  } else {
    addressInitial = AddressValue.create<AddressType>();
  }

  return Column(
    mainAxisAlignment: MainAxisAlignment.center,
    children: [
      SizedBox(
        width: formViewWidth,
        height: formViewHeight,
        child: AddressInputTest(
          formViewHeight: formViewHeight,
          initialValue: addressInitial,
          showAddressTypeField: showAddressTypeField,
          showDescriptionField: showDescriptionField,
          showCountyField: showCountyField,
          showCountryField: !usAddressFormat && showCountryField,
        ),
      ),
    ],
  );
}

@widgetbook.UseCase(name: 'Multi Address Input', type: AddressInput)
Widget buildMultiAddressInputUseCase(BuildContext context) {
  final formViewWidth = _displayAreaWidthKnob(context);
  final formViewHeight = _displayAreaHeightKnob(context, 680);

  final usAddressFormat = _usAddressFieldKnob(context);
  final showAddressTypeField = _showAddressTypeFieldKnob(context);
  final showDescriptionField = _showDescriptionFieldKnob(context);
  final showCountyField = _showCountyFieldKnob(context);
  final showCountryField = _showCountryFieldKnob(context);

  List<AddressValue<AddressType>> initialValues = [];
  if (usAddressFormat) {
    initialValues = [
      AddressValue.create<AddressType>(
        type: AddressType.main,
        number: '41543',
        street: 'Oberbrunner Ridges',
        other: 'Suite #544',
        municipality: 'South Ramonachester',
        province: 'AL',
        postalCode: '41541-5348',
        countryCode: 'US',
      ),
      AddressValue.create<AddressType>(
        type: AddressType.branch,
        number: '965',
        street: 'Charlene Row',
        other: 'Suite #2',
        municipality: 'Violatown',
        province: 'CA',
        postalCode: '09945-7962',
        countryCode: 'US',
      )
    ];
  }

  return Column(
    mainAxisAlignment: MainAxisAlignment.center,
    children: [
      SizedBox(
        width: formViewWidth,
        height: formViewHeight,
        child: MultiAddressInputTest(
          formViewHeight: formViewHeight,
          initialValues: initialValues,
          showAddressTypeField: showAddressTypeField,
          showDescriptionField: showDescriptionField,
          showCountyField: showCountyField,
          showCountryField: showCountryField,
        ),
      ),
    ],
  );
}

double _displayAreaWidthKnob(BuildContext context) {
  return context.knobs.doubleSlider(
    label: 'Display Area Width',
    description: 'The width of the display area for the form.',
    initialValue: 500.0,
    minValue: 300.0,
    maxValue: 850.0,
  );
}

double _displayAreaHeightKnob(BuildContext context, double initialValue) {
  return context.knobs.doubleSlider(
    label: 'Display Area Height',
    description: 'The height of the display area for the form.',
    initialValue: initialValue,
    minValue: 300.0,
    maxValue: 850.0,
  );
}

bool _usAddressFieldKnob(BuildContext context) {
  return context.knobs.boolean(
    label: 'US Address Format Toggle',
    initialValue: true,
  );
}

bool _showAddressTypeFieldKnob(BuildContext context) {
  return context.knobs.boolean(
    label: 'Show Address Type Toggle',
    initialValue: true,
  );
}

bool _showDescriptionFieldKnob(BuildContext context) {
  return context.knobs.boolean(
    label: 'Show Description Field',
    initialValue: true,
  );
}

bool _showCountyFieldKnob(BuildContext context) {
  return context.knobs.boolean(
    label: 'Show County Field',
    initialValue: true,
  );
}

bool _showCountryFieldKnob(BuildContext context) {
  return context.knobs.boolean(
    label: 'Show Country Field',
    initialValue: true,
  );
}
