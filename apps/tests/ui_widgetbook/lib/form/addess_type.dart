import 'package:flutter/material.dart';
import 'package:ui_widgets_component/ui_widgets.dart';

enum AddressType {
  main,
  branch,
  billing;

  static AddressLabels<AddressType> labelsWithTypes(
    BuildContext context,
    AddressValue<AddressType> addressValue, {
    List<AddressValue<AddressType>>? addressValues,
  }) {
    return AddressLabels.fromLocalization<AddressType>(
      context,
      countryCode: addressValue.countryCode,
    ).copyWith(
      addressTypes: [
        AddressTypeSelection<AddressType>(
          value: AddressType.main,
          label: 'Main',
          icon: const Icon(Icons.business),
          enabled: addressValues == null ||
              !addressValues.any(
                (value) =>
                    value != addressValue && value.type == AddressType.main,
              ),
        ),
        const AddressTypeSelection<AddressType>(
          value: AddressType.branch,
          label: 'Branch',
          icon: Icon(Icons.maps_home_work),
          enabled: true,
        ),
        AddressTypeSelection<AddressType>(
          value: AddressType.billing,
          label: 'Billing',
          tooltip: 'Defaults to main address if '
              'a billing address is not provided',
          icon: const Icon(Icons.payment),
          enabled: addressValues == null ||
              !addressValues.any(
                (value) =>
                    value != addressValue && value.type == AddressType.billing,
              ),
        ),
      ],
    );
  }
}
