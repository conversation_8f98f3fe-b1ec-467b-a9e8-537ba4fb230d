import 'package:flutter/material.dart';

import 'package:ui_widgets_component/ui_widgets.dart';

import 'contact_type.dart';

class ContactInputTest extends StatefulWidget {
  final double formViewHeight;

  final ContactValue<ContactType> initialValue;
  final ContactLabels<ContactType> contactLabels;

  final bool showContactTypeField;
  final bool showDescriptionField;
  final bool showCountryForPhoneNumber;
  final bool showCountrySelection;

  const ContactInputTest({
    super.key,
    required this.formViewHeight,
    required this.initialValue,
    required this.contactLabels,
    required this.showContactTypeField,
    required this.showDescriptionField,
    required this.showCountryForPhoneNumber,
    required this.showCountrySelection,
  });

  @override
  State<ContactInputTest> createState() => _ContactInputTestState();
}

class _ContactInputTestState extends State<ContactInputTest> {
  late ContactValue<ContactType> contactValue;

  @override
  void initState() {
    super.initState();

    contactValue = widget.initialValue.copyWith();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: DialogForm(
          title: 'Contact Input Test',
          formViewHeight: widget.formViewHeight,
          body: ContactInput<ContactType>(
            value: contactValue,
            labels: widget.contactLabels,
            showDescriptionField: widget.showDescriptionField,
            showTypeSelectionField: widget.showContactTypeField,
            showCountryForPhoneNumbers: widget.showCountryForPhoneNumber,
            enableCountrySelectionForPhoneNumbers: widget.showCountrySelection,
            onChanged: (value) {
              print(
                'Value: $value, '
                'isValid: ${value.isValid}'
                'isRequiredNotEmpty: ${value.isRequiredNotEmpty}, ',
              );
              setState(() {
                contactValue = value;
              });
            },
          ),
          actions: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 8),
                child: ElevatedButton.icon(
                  label: const Text('Cancel'),
                  icon: const Icon(Icons.cancel),
                  iconAlignment: IconAlignment.end,
                  onPressed: () {},
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 8),
                child: ElevatedButton.icon(
                  label: const Text('Save'),
                  icon: const Icon(Icons.save),
                  iconAlignment: IconAlignment.end,
                  onPressed: contactValue.isRequiredNotEmpty &&
                          contactValue.isValid &&
                          contactValue != widget.initialValue //
                      ? () {}
                      : null,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class MultiContactInputTest extends StatefulWidget {
  final double formViewHeight;

  final List<ContactValue<ContactType>> initialValues;
  final ContactLabels<ContactType> contactLabels;

  final bool showContactTypeField;
  final bool showDescriptionField;
  final bool showCountryForPhoneNumber;
  final bool showCountrySelection;

  const MultiContactInputTest({
    super.key,
    required this.formViewHeight,
    required this.initialValues,
    required this.contactLabels,
    required this.showContactTypeField,
    required this.showDescriptionField,
    required this.showCountryForPhoneNumber,
    required this.showCountrySelection,
  });

  @override
  State<MultiContactInputTest> createState() => _MultiContactInputTestState();
}

class _MultiContactInputTestState extends State<MultiContactInputTest> {
  late List<ContactValue<ContactType>> contactValues;

  @override
  void initState() {
    super.initState();

    if (widget.initialValues.isEmpty) {
      contactValues = [
        ContactValue.create<ContactType>(),
      ];
    } else {
      contactValues = [...widget.initialValues];
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    final bool isDark = theme.colorScheme.brightness == Brightness.dark;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: DialogForm(
          title: 'Multi Contact Input Test',
          formViewHeight: widget.formViewHeight,
          body: MultiInput<ContactValue<ContactType>>(
            values: contactValues,
            groupBoxBackground: isDark ? Colors.grey[800]! : Colors.white,
            groupBoxActionAlignment: MainAxisAlignment.center,
            onAdd: () {
              setState(() {
                contactValues.add(
                  ContactValue.create<ContactType>(),
                );
              });
            },
            onRemove: (index) {
              setState(() {
                contactValues.removeAt(index);
              });
            },
            builder: (
              context,
              value,
              index,
              focusNode,
              focusHandler,
            ) {
              return ContactInput<ContactType>(
                key: value.key,
                value: value,
                labels: widget.contactLabels,
                showDescriptionField: widget.showDescriptionField,
                showTypeSelectionField: widget.showContactTypeField,
                showCountryForPhoneNumbers: widget.showCountryForPhoneNumber,
                enableCountrySelectionForPhoneNumbers:
                    widget.showCountrySelection,
                focusNode: focusNode,
                onFocus: focusHandler,
                onChanged: (value) {
                  print(
                    'Value: $value, '
                    'isValid: ${value.isValid}'
                    'isRequiredNotEmpty: ${value.isRequiredNotEmpty}, ',
                  );
                  setState(() {
                    contactValues[index] = value;
                  });
                },
              );
            },
          ),
          actions: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 8),
                child: ElevatedButton.icon(
                  label: const Text('Cancel'),
                  icon: const Icon(Icons.cancel),
                  iconAlignment: IconAlignment.end,
                  onPressed: () {},
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 8),
                child: ElevatedButton.icon(
                  label: const Text('Save'),
                  icon: const Icon(Icons.save),
                  iconAlignment: IconAlignment.end,
                  onPressed: FormValues.isDirty(
                    contactValues,
                    widget.initialValues,
                  )
                      ? () {}
                      : null,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
