import 'package:flutter/material.dart';

import 'package:ui_widgets_component/ui_widgets.dart';

import 'addess_type.dart';

class AddressInputTest extends StatefulWidget {
  final double formViewHeight;

  final AddressValue<AddressType> initialValue;

  final bool showAddressTypeField;
  final bool showDescriptionField;
  final bool showCountyField;
  final bool showCountryField;

  const AddressInputTest({
    super.key,
    required this.formViewHeight,
    required this.initialValue,
    this.showAddressTypeField = true,
    this.showDescriptionField = true,
    this.showCountyField = true,
    this.showCountryField = true,
  });

  @override
  State<AddressInputTest> createState() => _AddressInputTestState();
}

class _AddressInputTestState extends State<AddressInputTest> {
  late AddressValue<AddressType> addressValue;

  @override
  void initState() {
    super.initState();

    addressValue = widget.initialValue.copyWith();
  }

  @override
  Widget build(BuildContext context) {
    final addresLabels = widget.showAddressTypeField
        ? AddressType.labelsWithTypes(
            context,
            addressValue,
          )
        : AddressLabels.fromLocalization<AddressType>(
            context,
            countryCode: addressValue.countryCode,
          );

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: DialogForm(
          title: 'Address Input Test',
          formViewHeight: widget.formViewHeight,
          body: AddressInput<AddressType>(
            value: addressValue,
            labels: addresLabels,
            showDescriptionField: widget.showDescriptionField,
            showCountyField: widget.showCountyField,
            showCountryField: widget.showCountryField,
            onChanged: (value) {
              print(
                'Value: $value, '
                'isValid: ${value.isValid}'
                'isRequiredNotEmpty: ${value.isRequiredNotEmpty}, ',
              );
              setState(() {
                addressValue = value;
              });
            },
          ),
          actions: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 8),
                child: ElevatedButton.icon(
                  label: const Text('Cancel'),
                  icon: const Icon(Icons.cancel),
                  iconAlignment: IconAlignment.end,
                  onPressed: () {},
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 8),
                child: ElevatedButton.icon(
                  label: const Text('Save'),
                  icon: const Icon(Icons.save),
                  iconAlignment: IconAlignment.end,
                  onPressed: addressValue.isRequiredNotEmpty &&
                          addressValue.isValid &&
                          addressValue != widget.initialValue //
                      ? () {}
                      : null,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class MultiAddressInputTest extends StatefulWidget {
  final double formViewHeight;

  final List<AddressValue<AddressType>> initialValues;
  final bool showAddressTypeField;
  final bool showDescriptionField;
  final bool showCountyField;
  final bool showCountryField;

  const MultiAddressInputTest({
    super.key,
    required this.formViewHeight,
    required this.initialValues,
    this.showAddressTypeField = true,
    this.showDescriptionField = true,
    this.showCountyField = true,
    this.showCountryField = true,
  });

  @override
  State<MultiAddressInputTest> createState() => _MultiAddressInputTestState();
}

class _MultiAddressInputTestState extends State<MultiAddressInputTest> {
  late List<AddressValue<AddressType>> addressValues;

  @override
  void initState() {
    super.initState();

    if (widget.initialValues.isEmpty) {
      addressValues = [
        AddressValue.create<AddressType>(),
      ];
    } else {
      addressValues = [...widget.initialValues];
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    final bool isDark = theme.colorScheme.brightness == Brightness.dark;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: DialogForm(
          title: 'Multi Address Input Test',
          formViewHeight: widget.formViewHeight,
          body: MultiInput<AddressValue<AddressType>>(
            values: addressValues,
            groupBoxBackground: isDark ? Colors.grey[800]! : Colors.white,
            groupBoxActionAlignment: MainAxisAlignment.center,
            onAdd: () {
              setState(() {
                addressValues.add(
                  AddressValue.create<AddressType>(),
                );
              });
            },
            onRemove: (index) {
              setState(() {
                addressValues.removeAt(index);
              });
            },
            builder: (
              context,
              value,
              index,
              focusNode,
              focusHandler,
            ) {
              final addressLabels = widget.showAddressTypeField
                  ? AddressType.labelsWithTypes(
                      context,
                      addressValues[index],
                      addressValues: addressValues,
                    )
                  : AddressLabels.fromLocalization<AddressType>(
                      context,
                      countryCode: addressValues[index].countryCode,
                    );

              return AddressInput<AddressType>(
                key: value.key,
                value: value,
                labels: addressLabels,
                showDescriptionField: widget.showDescriptionField,
                showCountyField: widget.showCountyField,
                showCountryField: widget.showCountryField,
                focusNode: focusNode,
                onFocus: focusHandler,
                onChanged: (value) {
                  print(
                    'Value: $value, '
                    'isValid: ${value.isValid}'
                    'isRequiredNotEmpty: ${value.isRequiredNotEmpty}, ',
                  );
                  setState(() {
                    addressValues[index] = value;
                  });
                },
              );
            },
          ),
          actions: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 8),
                child: ElevatedButton.icon(
                  label: const Text('Cancel'),
                  icon: const Icon(Icons.cancel),
                  iconAlignment: IconAlignment.end,
                  onPressed: () {},
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 8),
                child: ElevatedButton.icon(
                  label: const Text('Save'),
                  icon: const Icon(Icons.save),
                  iconAlignment: IconAlignment.end,
                  onPressed: FormValues.isDirty(
                    addressValues,
                    widget.initialValues,
                  )
                      ? () {}
                      : null,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
