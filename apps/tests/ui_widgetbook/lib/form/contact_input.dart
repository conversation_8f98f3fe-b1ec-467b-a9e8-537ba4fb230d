import 'package:flutter/material.dart';
import 'package:widgetbook/widgetbook.dart';
import 'package:widgetbook_annotation/widgetbook_annotation.dart' as widgetbook;

import 'package:ui_widgets_component/ui_widgets.dart';

import '../knobs/double_slider.dart';

import 'contact_type.dart';
import 'contact_input_test.dart';

@widgetbook.UseCase(name: 'Contact Input', type: ContactInput)
Widget buildContactInputUseCase(BuildContext context) {
  final formViewWidth = _displayAreaWidthKnob(context);
  final formViewHeight = _displayAreaHeightKnob(context, 332);

  final fillContactValue = _fillContactValue(context);
  final showContactTypeField = _showContactTypeFieldKnob(context);
  final showDescriptionField = _showDescriptionFieldKnob(context);
  final showCountryForPhoneNumber = _showCountryForPhoneNumber(context);
  final showCountrySelection = _showCountrySelectionKnob(context);

  final contactLabels = ContactType.labels(context);
  ContactValue<ContactType> contactInitial = ContactValue.create<ContactType>();

  if (fillContactValue) {
    contactInitial = ContactValue.create<ContactType>(
      type: ContactType.tollFree,
      description: 'This is a toll free number',
      value: '+18005551212',
    );
  }

  return Column(
    mainAxisAlignment: MainAxisAlignment.center,
    children: [
      SizedBox(
        width: formViewWidth,
        height: formViewHeight,
        child: ContactInputTest(
          formViewHeight: formViewHeight,
          initialValue: contactInitial,
          contactLabels: contactLabels,
          showContactTypeField: showContactTypeField,
          showDescriptionField: showDescriptionField,
          showCountryForPhoneNumber: showCountryForPhoneNumber,
          showCountrySelection: showCountrySelection,
        ),
      ),
    ],
  );
}

@widgetbook.UseCase(name: 'Multi Contact Input', type: ContactInput)
Widget buildMultiContactInputUseCase(BuildContext context) {
  final formViewWidth = _displayAreaWidthKnob(context);
  final formViewHeight = _displayAreaHeightKnob(context, 375);

  final fillContactValues = _fillContactValues(context);
  final showContactTypeField = _showContactTypeFieldKnob(context);
  final showDescriptionField = _showDescriptionFieldKnob(context);
  final showCountryForPhoneNumber = _showCountryForPhoneNumber(context);
  final showCountrySelection = _showCountrySelectionKnob(context);

  final contactLabels = ContactType.labels(context);
  List<ContactValue<ContactType>> initialValues = [];

  if (fillContactValues) {
    initialValues = [
      ContactValue.create<ContactType>(
        type: ContactType.email,
        description: 'This is an email address',
        value: '<EMAIL>',
      ),
      ContactValue.create<ContactType>(
        type: ContactType.tollFree,
        description: 'This is a toll free number',
        value: '+18005551212',
      ),
      ContactValue.create<ContactType>(
        type: ContactType.voiceAndSMS,
        description: 'This is a UK phone number',
        value: '+447896876544',
      )
    ];
  }

  return Column(
    mainAxisAlignment: MainAxisAlignment.center,
    children: [
      SizedBox(
        width: formViewWidth,
        height: formViewHeight,
        child: MultiContactInputTest(
          formViewHeight: formViewHeight,
          initialValues: initialValues,
          contactLabels: contactLabels,
          showContactTypeField: showContactTypeField,
          showDescriptionField: showDescriptionField,
          showCountryForPhoneNumber: showCountryForPhoneNumber,
          showCountrySelection: showCountrySelection,
        ),
      ),
    ],
  );
}

double _displayAreaWidthKnob(BuildContext context) {
  return context.knobs.doubleSlider(
    label: 'Display Area Width',
    description: 'The width of the display area for the form.',
    initialValue: 500.0,
    minValue: 300.0,
    maxValue: 850.0,
  );
}

double _displayAreaHeightKnob(BuildContext context, double initialValue) {
  return context.knobs.doubleSlider(
    label: 'Display Area Height',
    description: 'The height of the display area for the form.',
    initialValue: initialValue,
    minValue: 300.0,
    maxValue: 850.0,
  );
}

bool _fillContactValue(BuildContext context) {
  return context.knobs.boolean(
    label: 'Pre-populate contact data',
    initialValue: true,
  );
}

bool _showContactTypeFieldKnob(BuildContext context) {
  return context.knobs.boolean(
    label: 'Show Contact Type Selection',
    initialValue: true,
  );
}

bool _showDescriptionFieldKnob(BuildContext context) {
  return context.knobs.boolean(
    label: 'Show Description Field',
    initialValue: true,
  );
}

bool _showCountryForPhoneNumber(BuildContext context) {
  return context.knobs.boolean(
    label: 'Show Country for Phone Number',
    description: 'Show the country flag and international '
        'dial code for phone number inputs.',
    initialValue: true,
  );
}

bool _showCountrySelectionKnob(BuildContext context) {
  return context.knobs.boolean(
    label: 'Show Country Selection for Phone Number',
    description: 'Enable changing the country for '
        'phone number inputs. This flag has effect '
        'only if the "Show Country for Phone Number" '
        'flag is enabled.',
    initialValue: true,
  );
}

bool _fillContactValues(BuildContext context) {
  return context.knobs.boolean(
    label: 'Pre-populate contact data',
    initialValue: true,
  );
}
