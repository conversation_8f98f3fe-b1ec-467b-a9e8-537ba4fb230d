import 'package:widgetbook/widgetbook.dart';

class IntSliderKnob extends Knob<int> {
  final int minValue;
  final int maxValue;

  IntSliderKnob({
    required super.label,
    super.description,
    required super.initialValue,
    required this.minValue,
    required this.maxValue,
  });

  @override
  List<Field> get fields => [
        IntSliderField(
          name: label,
          initialValue: initialValue,
          min: minValue,
          max: maxValue,
        ),
      ];

  @override
  int valueFromQueryGroup(Map<String, String> group) {
    return valueOf(label, group);
  }
}

extension IntSliderKnobBuilder on KnobsBuilder {
  int intSlider({
    required String label,
    required String description,
    required int initialValue,
    required int minValue,
    required int maxValue,
  }) =>
      onKnobAdded(
        IntSliderKnob(
          label: label,
          description: description,
          initialValue: initialValue,
          minValue: minValue,
          maxValue: maxValue,
        ),
      )!;
}
