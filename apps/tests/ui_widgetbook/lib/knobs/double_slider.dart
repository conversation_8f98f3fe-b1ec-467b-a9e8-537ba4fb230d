import 'package:widgetbook/widgetbook.dart';

class Double<PERSON><PERSON>r<PERSON>nob extends Knob<double> {
  final double minValue;
  final double maxValue;

  DoubleSliderKnob({
    required super.label,
    super.description,
    required super.initialValue,
    required this.minValue,
    required this.maxValue,
  });

  @override
  List<Field> get fields => [
        DoubleSliderField(
          name: label,
          initialValue: initialValue,
          min: minValue,
          max: maxValue,
        ),
      ];

  @override
  double valueFromQueryGroup(Map<String, String> group) {
    return valueOf(label, group);
  }
}

extension DoubleSliderKnobBuilder on KnobsBuilder {
  double doubleSlider({
    required String label,
    required String description,
    required double initialValue,
    required double minValue,
    required double maxValue,
  }) =>
      onKnobAdded(
        DoubleSliderKnob(
          label: label,
          description: description,
          initialValue: initialValue,
          minValue: minValue,
          maxValue: maxValue,
        ),
      )!;
}
