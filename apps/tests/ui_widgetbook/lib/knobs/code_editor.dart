import 'package:flutter/material.dart';
import 'package:widgetbook/widgetbook.dart';

import 'package:re_editor/re_editor.dart' as re_editor;
import 'package:re_highlight/languages/json.dart';
import 'package:re_highlight/styles/atom-one-light.dart';

import 'find.dart';

class CodeEditor extends Knob<String> {
  CodeEditor({
    required super.label,
    super.description,
    required super.initialValue,
  });

  @override
  List<Field> get fields => [
        CodeEditorField(
          name: label,
          initialValue: initialValue,
        ),
      ];

  @override
  String valueFromQueryGroup(Map<String, String> group) {
    return valueOf(label, group);
  }
}

class CodeEditorField extends StringField {
  final re_editor.CodeLineEditingController _controller =
      re_editor.CodeLineEditingController();

  CodeEditorField({
    required super.name,
    required super.initialValue,
  }) {
    _controller.text = initialValue ?? '{}';
  }

  @override
  Widget toWidget(BuildContext context, String group, String? value) {
    return LayoutBuilder(builder: (context, constraints) {
      final theme = Theme.of(context);

      final ButtonStyle buttonStyle = ElevatedButton.styleFrom(
        backgroundColor: theme.colorScheme.primaryContainer,
      );

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: constraints.maxWidth,
            height: 400,
            child: re_editor.CodeEditor(
              border: Border.all(color: theme.colorScheme.primary),
              borderRadius: BorderRadius.circular(4),
              sperator: Container(
                width: 1,
                color: theme.colorScheme.primary,
              ),
              style: re_editor.CodeEditorStyle(
                codeTheme: re_editor.CodeHighlightTheme(
                  languages: {
                    'json': re_editor.CodeHighlightThemeMode(mode: langJson),
                  },
                  theme: atomOneLightTheme,
                ),
              ),
              controller: _controller,
              wordWrap: false,
              indicatorBuilder: (
                context,
                editingController,
                chunkController,
                notifier,
              ) {
                return Row(
                  children: [
                    re_editor.DefaultCodeLineNumber(
                      controller: editingController,
                      notifier: notifier,
                    ),
                    re_editor.DefaultCodeChunkIndicator(
                      width: 20,
                      controller: chunkController,
                      notifier: notifier,
                    )
                  ],
                );
              },
              findBuilder: (context, controller, readOnly) => CodeFindPanelView(
                controller: controller,
                readOnly: readOnly,
              ),
            ),
          ),
          const SizedBox(
            height: 16.0,
          ),
          ElevatedButton(
            style: buttonStyle,
            onPressed: () => updateField(
              context,
              group,
              _controller.text,
            ),
            child: const Text('Apply'),
          ),
        ],
      );
    });
  }
}

extension CodeEditorBuilder on KnobsBuilder {
  String codeEditor({
    required String label,
    required String description,
    required String initialValue,
  }) =>
      onKnobAdded(
        CodeEditor(
          label: label,
          description: description,
          initialValue: initialValue,
        ),
      )!;
}
