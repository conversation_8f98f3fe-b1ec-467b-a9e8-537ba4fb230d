// dart format width=80
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_import, prefer_relative_imports, directives_ordering

// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AppGenerator
// **************************************************************************

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:ui_widgetbook/dialog/alert_dialog.dart' as _i2;
import 'package:ui_widgetbook/dialog/avatar_button.dart' as _i3;
import 'package:ui_widgetbook/dialog/dialog_form.dart' as _i4;
import 'package:ui_widgetbook/dynamic/form.dart' as _i5;
import 'package:ui_widgetbook/form/address_input.dart' as _i6;
import 'package:ui_widgetbook/form/contact_input.dart' as _i7;
import 'package:ui_widgetbook/form/multi_input.dart' as _i8;
import 'package:ui_widgetbook/form/selection_list.dart' as _i9;
import 'package:ui_widgetbook/form/text_input.dart' as _i10;
import 'package:ui_widgetbook/menu/avatar_popup_menu.dart' as _i11;
import 'package:ui_widgetbook/menu/tools_app_bar.dart' as _i12;
import 'package:ui_widgetbook/menu/tools_scaffold.dart' as _i13;
import 'package:ui_widgetbook/view/folder_tab.dart' as _i14;
import 'package:ui_widgetbook/view/group_box.dart' as _i15;
import 'package:ui_widgetbook/view/tabbed_folders.dart' as _i16;
import 'package:widgetbook/widgetbook.dart' as _i1;

final directories = <_i1.WidgetbookNode>[
  _i1.WidgetbookFolder(
    name: 'dialog',
    children: [
      _i1.WidgetbookComponent(
        name: 'AlertDialogHelper',
        useCases: [
          _i1.WidgetbookUseCase(
            name: 'Auto-Size Dialog',
            builder: _i2.buildAutoSizeDialogUseCase,
          ),
          _i1.WidgetbookUseCase(
            name: 'Centered Text Dialog',
            builder: _i2.buildCenteredTextDialogUseCase,
          ),
          _i1.WidgetbookUseCase(
            name: 'Compact Dialog',
            builder: _i2.buildCompactDialogUseCase,
          ),
          _i1.WidgetbookUseCase(
            name: 'Confirm Only Dialog',
            builder: _i2.buildConfirmOnlyDialogUseCase,
          ),
          _i1.WidgetbookUseCase(
            name: 'Custom Dialog',
            builder: _i2.buildCustomDialogUseCase,
          ),
          _i1.WidgetbookUseCase(
            name: 'Dismiss Only Dialog',
            builder: _i2.buildDismissOnlyDialogUseCase,
          ),
          _i1.WidgetbookUseCase(
            name: 'Error Dialog',
            builder: _i2.buildErrorDialogUseCase,
          ),
          _i1.WidgetbookUseCase(
            name: 'Full Width Dialog',
            builder: _i2.buildFullWidthDialogUseCase,
          ),
          _i1.WidgetbookUseCase(
            name: 'Info Dialog',
            builder: _i2.buildInfoDialogUseCase,
          ),
          _i1.WidgetbookUseCase(
            name: 'Warning Dialog',
            builder: _i2.buildWarningDialogUseCase,
          ),
        ],
      ),
      _i1.WidgetbookLeafComponent(
        name: 'AvatarButton',
        useCase: _i1.WidgetbookUseCase(
          name: 'Avatar Button',
          builder: _i3.buildAvatarButtonUseCase,
        ),
      ),
      _i1.WidgetbookComponent(
        name: 'DialogForm',
        useCases: [
          _i1.WidgetbookUseCase(
            name: 'Dialog Form within a Card',
            builder: _i4.buildDialogFormCardUseCase,
          ),
          _i1.WidgetbookUseCase(
            name: 'Modal Material Dialog Form',
            builder: _i4.buildModalMaterialDialogFormUseCase,
          ),
        ],
      ),
    ],
  ),
  _i1.WidgetbookFolder(
    name: 'dynamic',
    children: [
      _i1.WidgetbookLeafComponent(
        name: 'FormInputGroup',
        useCase: _i1.WidgetbookUseCase(
          name: 'Dynamic Form Input Group',
          builder: _i5.buildDialogFormCardUseCase,
        ),
      )
    ],
  ),
  _i1.WidgetbookFolder(
    name: 'form',
    children: [
      _i1.WidgetbookComponent(
        name: 'AddressInput',
        useCases: [
          _i1.WidgetbookUseCase(
            name: 'Address Input',
            builder: _i6.buildAddressInputUseCase,
          ),
          _i1.WidgetbookUseCase(
            name: 'Multi Address Input',
            builder: _i6.buildMultiAddressInputUseCase,
          ),
        ],
      ),
      _i1.WidgetbookComponent(
        name: 'ContactInput',
        useCases: [
          _i1.WidgetbookUseCase(
            name: 'Contact Input',
            builder: _i7.buildContactInputUseCase,
          ),
          _i1.WidgetbookUseCase(
            name: 'Multi Contact Input',
            builder: _i7.buildMultiContactInputUseCase,
          ),
        ],
      ),
      _i1.WidgetbookLeafComponent(
        name: 'MultiInput<InputValue>',
        useCase: _i1.WidgetbookUseCase(
          name: 'Multi Input',
          builder: _i8.buildAddressInputdUseCase,
        ),
      ),
      _i1.WidgetbookComponent(
        name: 'SelectionList',
        useCases: [
          _i1.WidgetbookUseCase(
            name: 'Selection List',
            builder: _i9.buildSelectionListUseCase,
          ),
          _i1.WidgetbookUseCase(
            name: 'Selection List - Custom Colors',
            builder: _i9.buildSelectionListCustomColorsUseCase,
          ),
        ],
      ),
      _i1.WidgetbookLeafComponent(
        name: 'TextInput',
        useCase: _i1.WidgetbookUseCase(
          name: 'Multi-Line Text Input',
          builder: _i10.buildAvatarButtonUseCase,
        ),
      ),
    ],
  ),
  _i1.WidgetbookFolder(
    name: 'menu',
    children: [
      _i1.WidgetbookLeafComponent(
        name: 'AvatarPopupMenu',
        useCase: _i1.WidgetbookUseCase(
          name: 'Avatar Popup Menu',
          builder: _i11.buildAvatarPopupMenuUseCase,
        ),
      ),
      _i1.WidgetbookLeafComponent(
        name: 'ToolsAppBar',
        useCase: _i1.WidgetbookUseCase(
          name: 'Tools App Bar',
          builder: _i12.buildToolsAppBarUseCase,
        ),
      ),
      _i1.WidgetbookLeafComponent(
        name: 'ToolsScaffold',
        useCase: _i1.WidgetbookUseCase(
          name: 'Tools Scaffold',
          builder: _i13.buildToolsScaffoldUseCase,
        ),
      ),
    ],
  ),
  _i1.WidgetbookFolder(
    name: 'view',
    children: [
      _i1.WidgetbookLeafComponent(
        name: 'FolderTabBar',
        useCase: _i1.WidgetbookUseCase(
          name: 'Folder Tab Bar',
          builder: _i14.buildFolderTabBarUseCase,
        ),
      ),
      _i1.WidgetbookLeafComponent(
        name: 'GroupBox',
        useCase: _i1.WidgetbookUseCase(
          name: 'Group Box',
          builder: _i15.buildAvatarButtonUseCase,
        ),
      ),
      _i1.WidgetbookLeafComponent(
        name: 'TabbedFolders',
        useCase: _i1.WidgetbookUseCase(
          name: 'Tabbed Folders',
          builder: _i16.buildTabbedFoldersUseCase,
        ),
      ),
    ],
  ),
];
