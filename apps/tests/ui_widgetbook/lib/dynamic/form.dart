import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:widgetbook/widgetbook.dart';
import 'package:widgetbook_annotation/widgetbook_annotation.dart' as widgetbook;

// Import the widget from your app
import 'package:ui_widgets_component/ui_widgets.dart';

import '../knobs/code_editor.dart';
import '../knobs/double_slider.dart';
import '../knobs/int_slider.dart';

@widgetbook.UseCase(name: 'Dynamic Form Input Group', type: FormInputGroup)
Widget buildDialogFormCardUseCase(BuildContext context) {
  _dynamicFormConfig = context.knobs.codeEditor(
    label: 'Config',
    description: 'The configuration for the form',
    initialValue: _dynamicFormConfig,
  );
  double? formWidth = context.knobs.doubleSlider(
    label: 'Dialog Form Width',
    description:
        'The width of the display area for the form content (this is the shaded area).',
    initialValue: 400.0,
    minValue: 250.0,
    maxValue: 800.0,
  );
  int? numFieldsPerRow = context.knobs.intSlider(
    label: 'Number of Fields Per Row',
    description: 'The maximum number of fields that will be displayed per row.',
    initialValue: 3,
    minValue: 1,
    maxValue: 5,
  );
  double? minFieldWrapWidth = context.knobs.doubleSlider(
    label: 'Min Field Wrap Width',
    description:
        'The minimum field width which when reached will cause fields in the row to wrap.',
    initialValue: 150,
    minValue: 150,
    maxValue: 350,
  );

  return Container(
    decoration: BoxDecoration(
      border: Border.all(
        color: Theme.of(context).colorScheme.onSurface.withOpacity(
              0.5,
            ),
      ),
      borderRadius: BorderRadius.circular(4),
    ),
    child: Builder(
      builder: (context) {
        try {
          final inputConfig = InputGroupConfig.fromJson(
            jsonDecode(
              _dynamicFormConfig,
            ),
          );

          return Form(
            config: inputConfig,
            formWidth: formWidth,
            numFieldsPerRow: numFieldsPerRow,
            minFieldWrapWidth: numFieldsPerRow == 1 ? null : minFieldWrapWidth,
          );
        } catch (e, stackTrace) {
          debugPrint('Error parsing config: $e, $stackTrace');

          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              'Error parsing config: $e',
              style: const TextStyle(fontSize: 16),
            ),
          );
        }
      },
    ),
  );
}

class Form extends StatefulWidget {
  final InputGroupConfig config;

  final double formWidth;
  final int numFieldsPerRow;
  final double? minFieldWrapWidth;

  const Form({
    super.key,
    required this.config,
    required this.formWidth,
    required this.numFieldsPerRow,
    this.minFieldWrapWidth,
  });

  @override
  State<Form> createState() => _FormState();
}

class _FormState extends State<Form> {
  Map<String, String?> values = {};
  bool isInputGroupValid = false;

  @override
  Widget build(BuildContext context) {
    const spacer = SizedBox(height: 16);

    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: Theme.of(context).colorScheme.primary,
        ),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SizedBox(
          width: widget.formWidth,
          height: 500.0,
          child: Column(
            children: [
              Container(
                color: Colors.grey[900],
                child: FormInputGroup(
                  config: widget.config,
                  onChanged: _onChanged,
                  numFieldsPerRow: widget.numFieldsPerRow,
                  minFieldWrapWidth: widget.numFieldsPerRow == 1
                      ? null
                      : widget.minFieldWrapWidth,
                ),
              ),
              spacer,
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    ElevatedButton.icon(
                      label: const Text('Save'),
                      icon: const Icon(Icons.save),
                      iconAlignment: IconAlignment.end,
                      onPressed: isInputGroupValid
                          ? () {
                              showDialog<String>(
                                context: context,
                                builder: (BuildContext context) => AlertDialog(
                                  title: const Text(
                                    'Save Data',
                                  ),
                                  content: Text(
                                    'Form data values:\n\n${values.entries.map(
                                          (e) => '  ${e.key} = ${e.value}',
                                        ).join('\n')}',
                                  ),
                                  actions: <Widget>[
                                    TextButton(
                                      onPressed: () => Navigator.pop(
                                        context,
                                        'Dismiss',
                                      ),
                                      child: const Text(
                                        'Dismiss',
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            }
                          : null,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onChanged({
    required FormDataValue changedValue,
    required bool isInputGroupValid,
  }) {
    debugPrint(
      'changedValue: $changedValue, '
      'isInputGroupValid: $isInputGroupValid',
    );

    setState(() {
      values[changedValue.id] = changedValue.value;
      this.isInputGroupValid = isInputGroupValid;
    });
  }
}

String _dynamicFormConfig = r'''
{
  "heading": "GP Associate Details",
  "inputs": [
    {
      "type": "text",
      "id": "full_name",
      "label": "Full Name",
      "required": true,
      "validators": [
        {
          "type": "regex",
          "pattern": "^((([A-Z][A-Za-z]+)|([A-Z]\\.?)) )+([A-Z][A-Za-z]+)$",
          "examples": [
            "John Doe",
            "Jane H Smith",
            "Jack J. Thompson"
          ]
        }
      ]
    },
    {
      "type": "text",
      "id": "zip_code",
      "label": "Zip Code",
      "max-length": 10,
      "validators": [
        {
          "type": "minLength",
          "length": 5
        },
        {
          "type": "regex",
          "pattern": "^\\d{5}(-\\d{4})?$",
          "examples": [
            "02112",
            "02112-0001"
          ]
        }
      ]
    },
    {
      "type": "text",
      "id": "age",
      "label": "Age",
      "default-value": "25",
      "validators": [
        {
          "type": "number",
          "min": 18,
          "max": 65
        }
      ]
    },
    {
      "id": "gp_associate_role",
      "label": "GP Associate Role",
      "type": "fixedList",
      "options": [
        "Doctor",
        "Nurse",
        "Pharmacist",
        "Pharmacist Technician",
        "Physiotherapist",
        "Administrator",
        "Allied Healthcare Professional",
        "Associate"
      ]
    },
    {
      "id": "registration",
      "type": "text",
      "label": "Registration N/A",
      "conditions": {
        "enable-if": {
          "condition": "gp_associate_role != '' && gp_associate_role != 'Associate' && gp_associate_role != 'Allied Healthcare Professional' && gp_associate_role != 'Administrator'"
        },
        "required-if": {
          "condition": "gp_associate_role == 'Doctor' || gp_associate_role == 'Nurse' || gp_associate_role == 'Pharmacist' || gp_associate_role == 'Pharmacist Technician' || gp_associate_role == 'Physiotherapist'"
        },
        "label-value-if": [
          {
            "condition": "gp_associate_role == 'Doctor'",
            "value": "GMC Registration"
          },
          {
            "condition": "gp_associate_role == 'Nurse'",
            "value": "NMC Pin"
          },
          {
            "condition": "gp_associate_role == 'Pharmacist'",
            "value": "GPhC Number"
          },
          {
            "condition": "gp_associate_role == 'Pharmacist Technician'",
            "value": "GPhC Number"
          },
          {
            "condition": "gp_associate_role == 'Physiotherapist'",
            "value": "PH Registration"
          }
        ]
      }
    }
  ]
}
''';
