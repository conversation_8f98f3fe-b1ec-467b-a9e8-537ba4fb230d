import 'package:flutter/material.dart';
import 'package:widgetbook/widgetbook.dart';
import 'package:widgetbook_annotation/widgetbook_annotation.dart' as widgetbook;

import 'package:ui_widgets_component/ui_widgets.dart';

import '../knobs/double_slider.dart';

@widgetbook.UseCase(name: 'Dialog Form within a Card', type: DialogForm)
Widget buildDialogFormCardUseCase(BuildContext context) {
  final formViewWidth = _displayAreaWidthKnob(context);
  final formViewHeight = _displayAreaHeightKnob(context);

  return Column(
    mainAxisAlignment: MainAxisAlignment.center,
    children: [
      SizedBox(
        width: formViewWidth,
        height: formViewHeight,
        child: Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Container(
              color: Colors.black12,
              child: _buildDialogForm(context, formViewHeight),
            ),
          ),
        ),
      ),
    ],
  );
}

@widgetbook.UseCase(name: 'Modal Material Dialog Form', type: DialogForm)
Widget buildModalMaterialDialogFormUseCase(BuildContext context) {
  final formViewWidth = _displayAreaWidthKnob(context);
  final formViewHeight = _displayAreaHeightKnob(context);
  final dialogDismissable = _dialogDismissableOnTapKnob(context);

  return Column(
    mainAxisAlignment: MainAxisAlignment.center,
    children: <Widget>[
      TextButton(
        onPressed: () => showDialog<void>(
          context: context,
          barrierDismissible: dialogDismissable,
          builder: (dialogContext) {
            return Localizations.override(
              context: dialogContext,
              delegates: const [
                UIWidgetLocalizations.delegate,
              ],
              child: Dialog(
                child: SizedBox(
                  width: formViewWidth,
                  height: formViewHeight,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: _buildDialogForm(
                      dialogContext,
                      formViewHeight,
                      dialogDismissable,
                      () {
                        Navigator.pop(dialogContext);
                      },
                    ),
                  ),
                ),
              ),
            );
          },
        ),
        child: const Text('Show Dialog'),
      ),
      const SizedBox(height: 10),
      TextButton(
        onPressed: () => showDialog<void>(
          context: context,
          barrierDismissible: dialogDismissable,
          builder: (dialogContext) {
            return Localizations.override(
              context: dialogContext,
              delegates: const [
                UIWidgetLocalizations.delegate,
              ],
              child: Dialog.fullscreen(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: _buildDialogForm(
                    dialogContext,
                    formViewHeight,
                    dialogDismissable,
                    () {
                      Navigator.pop(dialogContext);
                    },
                  ),
                ),
              ),
            );
          },
        ),
        child: const Text('Show Fullscreen Dialog'),
      ),
      const SizedBox(height: 10),
      TextButton(
        onPressed: () => showModalBottomSheet<void>(
          context: context,
          isScrollControlled: true,
          isDismissible: dialogDismissable,
          builder: (dialogContext) {
            return SizedBox(
              height: formViewHeight,
              child: Localizations.override(
                context: dialogContext,
                delegates: const [
                  UIWidgetLocalizations.delegate,
                ],
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: _buildDialogForm(
                    dialogContext,
                    formViewHeight,
                    dialogDismissable,
                    () {
                      Navigator.pop(dialogContext);
                    },
                  ),
                ),
              ),
            );
          },
        ),
        child: const Text('Show Bottom Sheet Dialog'),
      ),
    ],
  );
}

double _displayAreaWidthKnob(BuildContext context) {
  return context.knobs.doubleSlider(
    label: 'Display Area Width (dark shade)',
    description: 'The width of the display area for the form.',
    initialValue: 400.0,
    minValue: 300.0,
    maxValue: 800.0,
  );
}

double _displayAreaHeightKnob(BuildContext context) {
  return context.knobs.doubleSlider(
    label: 'Display Area Height (dark shade)',
    description: 'The height of the display area for the form.',
    initialValue: 587.0,
    minValue: 300.0,
    maxValue: 800.0,
  );
}

bool _dialogDismissableOnTapKnob(BuildContext context) {
  return context.knobs.boolean(
    label: 'Dismiss dialog behavior',
    description: 'Whether the dialog can be dismissed by tapping '
        'outside. Otherwise the dialog can only be dismissed via '
        'the close button or actions.',
    initialValue: true,
  );
}

Widget _buildDialogForm(
  BuildContext buildContext,
  double formViewHeight, [
  bool? dismissableOnTap,
  VoidCallback? dismissDialog,
]) {
  return DialogForm(
    title: 'User Profile',
    formViewHeight: formViewHeight,
    onCloseDialog: dismissableOnTap == null || dismissableOnTap //
        ? null
        : dismissDialog,
    body: Container(
      color: Colors.black26,
      child: Column(
        children: [
          UsernameInput(
            isRequired: true,
            validator: MinLengthValidator(
              length: 3,
            ),
            onChanged: (value, _) {},
          ),
          PasswordInput(
            isRequired: true,
            validator: PasswordValidator(),
            onChanged: (value) {
              _password = value;
            },
          ),
          ConfirmPasswordInput(
            isRequired: true,
            validator: ConfirmPasswordValidator(
              value: _password,
            ),
            onChanged: (value) {},
          ),
          EmailAddressInput(
            isRequired: true,
            validator: EmailAddressValidator(),
            onChanged: (value, _) {},
          ),
          PhoneNumberInput(
            onChanged: (value, isValid) {},
          ),
        ],
      ),
    ),
    actions: Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8),
          child: ElevatedButton.icon(
            label: const Text('Cancel'),
            icon: const Icon(Icons.cancel),
            iconAlignment: IconAlignment.end,
            onPressed: dismissDialog ?? () {},
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(left: 8),
          child: ElevatedButton.icon(
            label: const Text('Save'),
            icon: const Icon(Icons.save),
            iconAlignment: IconAlignment.end,
            onPressed: dismissDialog ?? () {},
          ),
        ),
      ],
    ),
  );
}

var _password = '';
