import 'package:flutter/material.dart';
import 'package:widgetbook/widgetbook.dart';
import 'package:widgetbook_annotation/widgetbook_annotation.dart' as widgetbook;

import 'package:ui_widgets_component/ui_widgets.dart' as ui;

import '../knobs/double_slider.dart';

@widgetbook.UseCase(name: 'Info Dialog', type: ui.AlertDialogHelper)
Widget buildInfoDialogUseCase(BuildContext context) {
  final dismissText = _dismissTextKnob(context);
  final barrierDismissible = _barrierDismissibleKnob(context);

  return Column(
    mainAxisAlignment: MainAxisAlignment.center,
    children: <Widget>[
      TextButton(
        onPressed: () => ui.AlertDialogHelper.showInfo(
          context: context,
          markdownText:
              'This is an **information** dialog with just a dismiss button.\n\nYou can use *markdown* formatting.',
          dismissText: dismissText,
          barrierDismissible: barrierDismissible,
          onDismiss: () {
            print('Info dialog dismissed');
          },
        ),
        child: const Text('Show Info Dialog'),
      ),
    ],
  );
}

@widgetbook.UseCase(name: 'Warning Dialog', type: ui.AlertDialogHelper)
Widget buildWarningDialogUseCase(BuildContext context) {
  final cancelText = _cancelTextKnob(context);
  final confirmText = _confirmTextKnob(context);
  final barrierDismissible = _barrierDismissibleKnob(context);

  return Column(
    mainAxisAlignment: MainAxisAlignment.center,
    children: <Widget>[
      TextButton(
        onPressed: () => ui.AlertDialogHelper.showWarning(
          context: context,
          markdownText:
              '**Warning!** This action cannot be undone.\n\nAre you sure you want to proceed?',
          cancelText: cancelText,
          confirmText: confirmText,
          barrierDismissible: barrierDismissible,
          onCancel: () {
            print('Warning dialog cancelled');
          },
          onConfirm: () {
            print('Warning dialog confirmed');
          },
        ),
        child: const Text('Show Warning Dialog'),
      ),
    ],
  );
}

@widgetbook.UseCase(name: 'Error Dialog', type: ui.AlertDialogHelper)
Widget buildErrorDialogUseCase(BuildContext context) {
  final dismissText = _dismissTextKnob(context);
  final barrierDismissible = _barrierDismissibleKnob(context);

  return Column(
    mainAxisAlignment: MainAxisAlignment.center,
    children: <Widget>[
      TextButton(
        onPressed: () => ui.AlertDialogHelper.showError(
          context: context,
          markdownText:
              '**Error occurred!**\n\nSomething went wrong while processing your request.',
          dismissText: dismissText,
          barrierDismissible: barrierDismissible,
          onDismiss: () {
            print('Error dialog dismissed');
          },
        ),
        child: const Text('Show Error Dialog'),
      ),
    ],
  );
}

@widgetbook.UseCase(name: 'Custom Dialog', type: ui.AlertDialogHelper)
Widget buildCustomDialogUseCase(BuildContext context) {
  final icon = _iconKnob(context);
  final iconColor = _iconColorKnob(context);
  final iconSize = _iconSizeKnob(context);
  final cancelText = _cancelTextKnob(context);
  final confirmText = _confirmTextKnob(context);
  final barrierDismissible = _barrierDismissibleKnob(context);
  final minWidth = _minWidthKnob(context);
  final maxWidth = _maxWidthKnob(context);
  final minHeight = _minHeightKnob(context);
  final maxHeight = _maxHeightKnob(context);

  return Column(
    mainAxisAlignment: MainAxisAlignment.center,
    children: <Widget>[
      TextButton(
        onPressed: () => ui.AlertDialogHelper.show(
          context: context,
          icon: icon,
          iconColor: iconColor,
          iconSize: iconSize,
          markdownText: '''
# Custom Dialog

This is a **custom** dialog with:
- Custom icon and color
- Specific text constraints
- Custom button text
- Long paragraph text that demonstrates wrapping behavior

## Long Paragraph Example

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.

Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.
          ''',
          textConstraints: BoxConstraints(
            minWidth: minWidth,
            maxWidth: maxWidth,
            minHeight: minHeight,
            maxHeight: maxHeight,
          ),
          cancelText: cancelText,
          confirmText: confirmText,
          barrierDismissible: barrierDismissible,
          onCancel: () {
            print('Custom dialog cancelled');
          },
          onConfirm: () {
            print('Custom dialog confirmed');
          },
        ),
        child: const Text('Show Custom Dialog'),
      ),
    ],
  );
}

@widgetbook.UseCase(name: 'Dismiss Only Dialog', type: ui.AlertDialogHelper)
Widget buildDismissOnlyDialogUseCase(BuildContext context) {
  final icon = _iconKnob(context);
  final iconColor = _iconColorKnob(context);
  final dismissText = _dismissTextKnob(context);
  final barrierDismissible = _barrierDismissibleKnob(context);

  return Column(
    mainAxisAlignment: MainAxisAlignment.center,
    children: <Widget>[
      TextButton(
        onPressed: () => ui.AlertDialogHelper.show(
          context: context,
          icon: icon,
          iconColor: iconColor,
          markdownText:
              '**Success!**\n\nYour action was completed successfully.',
          dismissText: dismissText,
          barrierDismissible: barrierDismissible,
          onDismiss: () {
            print('Success dialog dismissed');
          },
        ),
        child: const Text('Show Dismiss Only Dialog'),
      ),
    ],
  );
}

@widgetbook.UseCase(name: 'Confirm Only Dialog', type: ui.AlertDialogHelper)
Widget buildConfirmOnlyDialogUseCase(BuildContext context) {
  final icon = _iconKnob(context);
  final iconColor = _iconColorKnob(context);
  final confirmText = _confirmTextKnob(context);
  final barrierDismissible = _barrierDismissibleKnob(context);

  return Column(
    mainAxisAlignment: MainAxisAlignment.center,
    children: <Widget>[
      TextButton(
        onPressed: () => ui.AlertDialogHelper.show(
          context: context,
          icon: icon,
          iconColor: iconColor,
          markdownText: 'Do you want to save your changes?',
          confirmText: confirmText,
          barrierDismissible: barrierDismissible,
          onConfirm: () {
            print('Changes saved');
          },
        ),
        child: const Text('Show Confirm Only Dialog'),
      ),
    ],
  );
}

@widgetbook.UseCase(name: 'Centered Text Dialog', type: ui.AlertDialogHelper)
Widget buildCenteredTextDialogUseCase(BuildContext context) {
  final icon = _iconKnob(context);
  final iconColor = _iconColorKnob(context);
  final dismissText = _dismissTextKnob(context);
  final barrierDismissible = _barrierDismissibleKnob(context);
  final textAlignment = _textAlignmentKnob(context);

  return Column(
    mainAxisAlignment: MainAxisAlignment.center,
    children: <Widget>[
      TextButton(
        onPressed: () => ui.AlertDialogHelper.showInfo(
          context: context,
          markdownText:
              '**Centered Information**\n\nThis text is centered in the dialog.\n\nYou can use *markdown* formatting with centered alignment.',
          icon: icon,
          iconColor: iconColor,
          dismissText: dismissText,
          barrierDismissible: barrierDismissible,
          textAlignment: textAlignment,
          onDismiss: () {
            print('Centered text dialog dismissed');
          },
        ),
        child: const Text('Show Centered Text Dialog'),
      ),
    ],
  );
}

@widgetbook.UseCase(name: 'Full Width Dialog', type: ui.AlertDialogHelper)
Widget buildFullWidthDialogUseCase(BuildContext context) {
  final icon = _iconKnob(context);
  final iconColor = _iconColorKnob(context);
  final cancelText = _cancelTextKnob(context);
  final confirmText = _confirmTextKnob(context);
  final barrierDismissible = _barrierDismissibleKnob(context);
  final textAlignment = _textAlignmentKnob(context);
  final minWidth = _minWidthKnob(context);
  final maxWidth = _maxWidthKnob(context);
  final minHeight = _minHeightKnob(context);
  final maxHeight = _maxHeightKnob(context);

  return Column(
    mainAxisAlignment: MainAxisAlignment.center,
    children: <Widget>[
      TextButton(
        onPressed: () => ui.AlertDialogHelper.showWarning(
          context: context,
          markdownText: '''
# Full Width Dialog

This dialog uses **custom constraints** to fill the available space.

## Features:
- Custom width and height constraints
- Configurable text alignment
- No empty space around the text
- Full markdown support

The text content fills the entire dialog area without leaving blank spaces.
          ''',
          icon: icon,
          iconColor: iconColor,
          cancelText: cancelText,
          confirmText: confirmText,
          barrierDismissible: barrierDismissible,
          textAlignment: textAlignment,
          textConstraints: BoxConstraints(
            minWidth: minWidth,
            maxWidth: maxWidth,
            minHeight: minHeight,
            maxHeight: maxHeight,
          ),
          onCancel: () {
            print('Full width dialog cancelled');
          },
          onConfirm: () {
            print('Full width dialog confirmed');
          },
        ),
        child: const Text('Show Full Width Dialog'),
      ),
    ],
  );
}

@widgetbook.UseCase(name: 'Auto-Size Dialog', type: ui.AlertDialogHelper)
Widget buildAutoSizeDialogUseCase(BuildContext context) {
  final icon = _iconKnob(context);
  final iconColor = _iconColorKnob(context);
  final dismissText = _dismissTextKnob(context);
  final barrierDismissible = _barrierDismissibleKnob(context);
  final textAlignment = _textAlignmentKnob(context);

  return Column(
    mainAxisAlignment: MainAxisAlignment.center,
    children: <Widget>[
      TextButton(
        onPressed: () => ui.AlertDialogHelper.showInfo(
          context: context,
          markdownText:
              '**Auto-Sized Dialog**\n\nThis dialog automatically sizes itself to fit the content.\n\nNo constraints are provided, so the dialog will be as small as possible while still containing all the text.',
          icon: icon,
          iconColor: iconColor,
          dismissText: dismissText,
          barrierDismissible: barrierDismissible,
          textAlignment: textAlignment,
          // No textConstraints provided - dialog will auto-size
          onDismiss: () {
            print('Auto-size dialog dismissed');
          },
        ),
        child: const Text('Show Auto-Size Dialog'),
      ),
    ],
  );
}

@widgetbook.UseCase(name: 'Compact Dialog', type: ui.AlertDialogHelper)
Widget buildCompactDialogUseCase(BuildContext context) {
  final icon = _iconKnob(context);
  final iconColor = _iconColorKnob(context);
  final dismissText = _dismissTextKnob(context);
  final barrierDismissible = _barrierDismissibleKnob(context);
  final textAlignment = _textAlignmentKnob(context);

  return Column(
    mainAxisAlignment: MainAxisAlignment.center,
    children: <Widget>[
      TextButton(
        onPressed: () => ui.AlertDialogHelper.showError(
          context: context,
          markdownText:
              '**Compact Error**\n\nThis is a compact dialog with minimal constraints.',
          icon: icon,
          iconColor: iconColor,
          dismissText: dismissText,
          barrierDismissible: barrierDismissible,
          textAlignment: textAlignment,
          textConstraints: const BoxConstraints(
            minWidth: 200.0,
            maxWidth: 300.0,
            minHeight: 50.0,
            maxHeight: 150.0,
          ),
          onDismiss: () {
            print('Compact dialog dismissed');
          },
        ),
        child: const Text('Show Compact Dialog'),
      ),
    ],
  );
}

// Knob functions for customization
IconData _iconKnob(BuildContext context) {
  final iconOptions = [
    Icons.info,
    Icons.warning,
    Icons.error,
    Icons.help,
    Icons.check_circle,
    Icons.question_mark,
  ];

  final iconNames = [
    'info',
    'warning',
    'error',
    'help',
    'check_circle',
    'question_mark',
  ];

  final selectedIndex = context.knobs.list(
    label: 'Icon',
    description: 'The icon to display in the dialog',
    options: iconNames,
    initialOption: 'warning',
  );

  return iconOptions[iconNames.indexOf(selectedIndex)];
}

Color _iconColorKnob(BuildContext context) {
  final colorOptions = [
    Colors.blue,
    Colors.amber,
    Colors.red,
    Colors.green,
    Colors.orange,
    Colors.purple,
  ];

  final colorNames = [
    'blue',
    'amber',
    'red',
    'green',
    'orange',
    'purple',
  ];

  final selectedIndex = context.knobs.list(
    label: 'Icon Color',
    description: 'The color of the icon',
    options: colorNames,
    initialOption: 'amber',
  );

  return colorOptions[colorNames.indexOf(selectedIndex)];
}

double _iconSizeKnob(BuildContext context) {
  return context.knobs.doubleSlider(
    label: 'Icon Size',
    description: 'The size of the icon',
    initialValue: 64.0,
    minValue: 32.0,
    maxValue: 96.0,
  );
}

String _cancelTextKnob(BuildContext context) {
  return context.knobs.string(
    label: 'Cancel Text',
    description: 'Text for the cancel button',
    initialValue: 'Cancel',
  );
}

String _confirmTextKnob(BuildContext context) {
  return context.knobs.string(
    label: 'Confirm Text',
    description: 'Text for the confirm button',
    initialValue: 'OK',
  );
}

String _dismissTextKnob(BuildContext context) {
  return context.knobs.string(
    label: 'Dismiss Text',
    description: 'Text for the dismiss button',
    initialValue: 'OK',
  );
}

bool _barrierDismissibleKnob(BuildContext context) {
  return context.knobs.boolean(
    label: 'Barrier Dismissible',
    description: 'Whether the dialog can be dismissed by tapping outside',
    initialValue: true,
  );
}

double _minWidthKnob(BuildContext context) {
  return context.knobs.doubleSlider(
    label: 'Min Width',
    description: 'Minimum width of the text content area',
    initialValue: 300.0,
    minValue: 200.0,
    maxValue: 600.0,
  );
}

double _maxWidthKnob(BuildContext context) {
  return context.knobs.doubleSlider(
    label: 'Max Width',
    description: 'Maximum width of the text content area',
    initialValue: 500.0,
    minValue: 400.0,
    maxValue: 800.0,
  );
}

double _minHeightKnob(BuildContext context) {
  return context.knobs.doubleSlider(
    label: 'Min Height',
    description: 'Minimum height of the text content area',
    initialValue: 100.0,
    minValue: 50.0,
    maxValue: 300.0,
  );
}

double _maxHeightKnob(BuildContext context) {
  return context.knobs.doubleSlider(
    label: 'Max Height',
    description: 'Maximum height of the text content area',
    initialValue: 400.0,
    minValue: 200.0,
    maxValue: 600.0,
  );
}

TextAlign _textAlignmentKnob(BuildContext context) {
  final alignmentOptions = [
    'start',
    'center',
    'end',
  ];

  final selectedAlignment = context.knobs.list(
    label: 'Text Alignment',
    description: 'The alignment of the text content',
    options: alignmentOptions,
    initialOption: 'start',
  );

  switch (selectedAlignment) {
    case 'center':
      return TextAlign.center;
    case 'end':
      return TextAlign.end;
    default:
      return TextAlign.start;
  }
}
