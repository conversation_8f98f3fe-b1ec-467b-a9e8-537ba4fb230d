import 'package:flutter/material.dart';
import 'package:widgetbook/widgetbook.dart';
import 'package:widgetbook_annotation/widgetbook_annotation.dart' as widgetbook;

// Import the widget from your app
import 'package:ui_widgets_component/dialog/avatar_button.dart';

@widgetbook.UseCase(name: 'Avatar Button', type: AvatarButton)
Widget buildAvatarButtonUseCase(BuildContext context) {
  const avatarImgUrl = 'https://gravatar.com/avatar/'
      'a907f68d34b138fda4755adec497d8a5?s=400&d=robohash&r=x';

  return AvatarButton(
    radius: context.knobs.double.input(
      label: 'Avatar Size',
      description: 'The radius of the CircleAvatar.',
      initialValue: 64.0,
    ),
    backgroundImage: NetworkImage(
      context.knobs.string(
        label: 'Avatar Image Url',
        description:
            'The backgroundImage of the CircleAvatar loaded via NetworkImage.',
        initialValue: avatarImgUrl,
      ),
    ),
  );
}
