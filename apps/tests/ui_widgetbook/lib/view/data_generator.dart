import 'dart:math';

DateTime generateRandomDate() {
  final random = Random();
  final year = random.nextInt(5) + 2020;
  final month = random.nextInt(12) + 1;
  final day = random.nextInt(28) + 1;
  final hour = random.nextInt(24);
  final minute = random.nextInt(60);

  return DateTime(year, month, day, hour, minute);
}

String generateNHSNumber() {
  final nhsNumber = StringBuffer();
  const numberChars = '123456789';

  nhsNumber.write(
    generateRandomString(3, chars: numberChars),
  );
  nhsNumber.write(' ');
  nhsNumber.write(
    generateRandomString(3, chars: numberChars),
  );
  nhsNumber.write(' ');
  nhsNumber.write(
    generateRandomString(4, chars: numberChars),
  );

  return nhsNumber.toString();
}

int generateRandomNumber(int limit) {
  final random = Random();
  return random.nextInt(limit);
}

String generateRandomString(
  int length, {
  String chars = alphanumericChars,
  bool toUpperCase = false,
}) {
  final random = Random();
  final result = StringBuffer();

  for (int i = 0; i < length; i++) {
    result.write(chars[random.nextInt(chars.length)]);
  }

  if (toUpperCase) {
    return result.toString().toUpperCase();
  }
  return result.toString();
}

String generateRandomName() {
  final random = Random();
  return _names[random.nextInt(_names.length)];
}

String generateRandomHospitalName() {
  final random = Random();
  return _hospitalNames[random.nextInt(_hospitalNames.length)];
}

String generateRandomHospitalDepartment() {
  final random = Random();
  return _hospitalDepartments[random.nextInt(_hospitalDepartments.length)];
}

const numberChars = '123456789';
const letterChars = 'abcdefghijklmnopqrstuvwxyz';
const alphanumericChars = 'abcdefghijklmnopqrstuvwxyz0123456789';

const _names = [
  'Alice Smith',
  'Bob Johnson',
  'Charlie Williams',
  'David Jones',
  'Eve Brown',
  'Frank Davis',
  'Grace Miller',
  'Heidi Wilson',
  'Ivan Moore',
  'Judy Taylor',
  'Kevin Anderson',
  'Linda Thomas',
  'Mallory Jackson',
  'Oscar Harris',
  'Peggy Martin',
  'Quentin Thompson',
  'Romeo Garcia',
  'Sybil Martinez',
  'Trent Robinson',
  'Ursula Clark',
  'Victor Rodriguez',
  'Walter Lewis',
  'Xavier Lee',
  'Yvonne Walker',
  'Zelda Hall',
];

const _hospitalNames = [
  'St. Mary\'s Hospital',
  'St. Thomas\' Hospital',
  'St. George\'s Hospital',
  'St. John\'s Hospital',
  'St. Paul\'s Hospital',
  'St. Peter\'s Hospital',
  'St. James\' Hospital',
  'St. Andrew\'s Hospital',
  'St. Philip\'s Hospital',
  'St. Bartholomew\'s Hospital',
  'St. Matthew\'s Hospital',
  'St. Luke\'s Hospital',
  'St. Jude\'s Hospital',
];

const _hospitalDepartments = [
  'Cardiology',
  'Dermatology',
  'Endocrinology',
  'Gastroenterology',
  'Haematology',
  'Immunology',
  'Nephrology',
  'Neurology',
  'Oncology',
  'Ophthalmology',
  'Orthopaedics',
  'Paediatrics',
  'Psychiatry',
  'Radiology',
  'Rheumatology',
  'Urology',
];
