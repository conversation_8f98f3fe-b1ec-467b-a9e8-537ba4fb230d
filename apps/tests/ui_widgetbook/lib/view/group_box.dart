import 'package:flutter/material.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:widgetbook/widgetbook.dart';
import 'package:widgetbook_annotation/widgetbook_annotation.dart' as widgetbook;

import 'package:ui_widgets_component/ui_widgets.dart';

import '../knobs/double_slider.dart';

@widgetbook.UseCase(name: 'Group Box', type: GroupBox)
Widget buildAvatarButtonUseCase(BuildContext context) {
  double? margin = context.knobs.doubleSlider(
    label: 'Margin',
    description: 'The space from the edge of the parent '
        'widget to the outer edge of the group box border.',
    initialValue: 16.0,
    minValue: 8.0,
    maxValue: 128.0,
  );
  double? padding = context.knobs.doubleSlider(
    label: 'Padding',
    description: 'The space from the innet edge of the '
        'group box border and the inner content container.',
    initialValue: 16.0,
    minValue: 8.0,
    maxValue: 128.0,
  );
  bool showTitle = context.knobs.boolean(
    label: 'Show Group Box Title',
    initialValue: true,
  );

  return Padding(
    padding: const EdgeInsets.all(128),
    child: DottedBorder(
      color: Colors.red.withOpacity(0.5),
      strokeWidth: 1,
      child: GroupBox(
        title: showTitle ? 'Group Box Title' : null,
        margin: EdgeInsets.all(margin),
        padding: EdgeInsets.all(padding),
        child: DottedBorder(
          color: Colors.red.withOpacity(0.5),
          strokeWidth: 1,
          child: const Center(
            child: Text('Group Box Content'),
          ),
        ),
      ),
    ),
  );
}
