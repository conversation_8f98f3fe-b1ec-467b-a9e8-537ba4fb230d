import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:widgetbook/widgetbook.dart';
import 'package:widgetbook_annotation/widgetbook_annotation.dart' as widgetbook;

import 'package:ui_widgets_component/ui_widgets.dart';

import '../knobs/double_slider.dart';

@widgetbook.UseCase(name: 'Tabbed Folders', type: TabbedFolders)
Widget buildTabbedFoldersUseCase(BuildContext context) {
  bool unconstrainedWidth = context.knobs.boolean(
    label: 'Unconstrained Tabbed Folder Content Area Width',
    description: 'Allow the tabbed view to take up as much width as possible.',
    initialValue: false,
  );
  double? width = context.knobs.doubleSlider(
    label: 'Tabbed Folder Content Area Width',
    description: 'The width of the display area for the tabbed view. '
        'Has no effect if the unconstrained width is enabled.',
    initialValue: 600.0,
    minValue: 300.0,
    maxValue: 800.0,
  );
  if (unconstrainedWidth) {
    width = null;
  }

  bool unconstrainedHeight = context.knobs.boolean(
    label: 'Unconstrained Tabbed Folder Content Area Height',
    description: 'Allow the tabbed view to take up as much height as possible.',
    initialValue: false,
  );
  double? height = context.knobs.doubleSlider(
    label: 'Tabbed Folder Content Area Height',
    description: 'The height of the display area for the tabbed view. '
        'Has no effect if the unconstrained height is enabled.',
    initialValue: 400.0,
    minValue: 300.0,
    maxValue: 800.0,
  );
  if (unconstrainedHeight) {
    height = null;
  }

  bool disableContentAnimation = context.knobs.boolean(
    label: 'Disable Animation',
    description: 'Disable animation of content page when tab changes.',
    initialValue: false,
  );

  return SizedBox(
    width: width,
    height: height,
    child: Column(
      children: [
        const SizedBox(height: 8.0),
        Center(
          child: Text(
            '${width ?? 'unconstrained'} x '
            '${height ?? 'unconstrained'}',
          ),
        ),
        const SizedBox(height: 8.0),
        Expanded(
          child: TabbedFolders(
            tabs: [
              FolderTab(
                title: 'Reader',
                icon: const ImageIcon(
                  AssetImage(
                    'assets/read-document.png',
                  ),
                ),
                content: _buildContent(context, 'Reader'),
              ),
              FolderTab(
                title: 'Inbox',
                iconData: Icons.inbox,
                content: _buildContent(context, 'Inbox'),
              ),
              FolderTab(
                title: 'Unread',
                iconData: Icons.markunread_mailbox_outlined,
                content: _buildContent(context, 'Unread'),
              ),
              FolderTab(
                title: 'Drafts',
                iconData: Icons.drafts,
                content: _buildContent(context, 'Drafts'),
              ),
              FolderTab(
                title: 'Inflow',
                iconData: Icons.rebase_edit,
                content: _buildContent(context, 'Inflow'),
              ),
            ],
            disableContentAnimation: disableContentAnimation,
          ),
        ),
      ],
    ),
  );
}

Widget _buildContent(BuildContext context, String title) {
  return Builder(builder: (context) {
    final pageSize = Provider.of<DisplayAreaSize>(context);

    return Center(
      child: Text('$title (${pageSize.width} x ${pageSize.height})'),
    );
  });
}
