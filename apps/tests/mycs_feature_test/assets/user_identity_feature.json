{"app-name": "User Identity Example App", "auth-timeout": 15, "show-logout-on-title-bar": true, "allowed-email-domains": "^(.*@gmail.com)|(.*@appbricks.io)$", "allowed-email-example": "only @gmail.com and @appbricks.io domains accepted", "mobile-number-required": true, "mobile-dial-code-read-only": false, "mobile-number-country-code": "US", "agreements": {"eula": "https://letterassist.ai/pdf/EULA.pdf", "tandc": "https://letterassist.ai/pdf/T&Cs.pdf", "privacypolicy": "https://letterassist.ai/pdf/Privacy-Policy.pdf"}, "custom-profile-input": {"add-height": 136, "num-fields-per-row": 2, "input-groups": [{"heading": "Professional", "inputs": [{"id": "gp_associate_role", "label": "GP Associate Role", "type": "fixedList", "options": ["Doctor", "Nurse", "Pharmacist", "Pharmacist <PERSON><PERSON>ian", "Physiotherapist", "Administrator", "Allied Healthcare Professional", "Associate"], "options-semantic-label": "Click to select GP Associate Role"}, {"id": "nhs_registration", "type": "text", "label": "Registration N/A", "conditions": {"enable-if": {"condition": "gp_associate_role != '' && gp_associate_role != 'Associate' && gp_associate_role != 'Allied Healthcare Professional' && gp_associate_role != 'Administrator'"}, "required-if": {"condition": "gp_associate_role == 'Doctor' || gp_associate_role == 'Nurse' || gp_associate_role == 'Pharmacist' || gp_associate_role == 'Pharmacist Technician' || gp_associate_role == 'Physiotherapist'"}, "label-value-if": [{"condition": "gp_associate_role == 'Doctor'", "value": "GMC Registration"}, {"condition": "gp_associate_role == 'Nurse'", "value": "NMC Pin"}, {"condition": "gp_associate_role == 'Pharmacist'", "value": "GPhC Number"}, {"condition": "gp_associate_role == 'Pharmacist Technician'", "value": "GPhC Number"}, {"condition": "gp_associate_role == 'Physiotherapist'", "value": "PH Registration"}]}}]}]}}