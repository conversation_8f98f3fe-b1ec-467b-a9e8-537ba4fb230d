{"option-subtitle": "GP Practice", "membership-required": false, "creation-restricted": false, "show-default-org-in-status": true, "can-change-default-org": true, "quotas-to-show-in-status": ["bandwidth"], "allowed-email-domains": "^(.*@gmail.com)|(.*@appbricks.io)$", "allowed-email-example": "only @gmail.com and @appbricks.io domains accepted", "default-country-code": "GB", "address-input": {"show-type-selection": true, "show-description-field": false, "show-county-field": false, "show-country-field": false}, "contact-input": {"show-type-selection": true, "show-description-field": false, "show-country-for-phone-numbers": true, "enable-country-selection": false}, "billing": {"product-name": "org-test-product", "billing-key": "stripe.customerID", "subscription-key": "stripe.subscriptionID", "subscription-plan-key": "stripe.subscriptionPlanId", "subscription-status-key": "stripe.subscriptionStatus", "subscription-period-end-key": "stripe.subscriptionCancelAtPeriodEnd", "active-status-pattern": "^(active)|(trialing)$"}}