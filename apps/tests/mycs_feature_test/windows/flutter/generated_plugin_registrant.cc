//
//  Generated file. Do not edit.
//

// clang-format off

#include "generated_plugin_registrant.h"

#include <amplify_db_common/amplify_db_common_plugin.h>
#include <app_framework_component/app_framework_component_plugin_c_api.h>
#include <billing_feature/billing_feature_plugin_c_api.h>
#include <connectivity_plus/connectivity_plus_windows_plugin.h>
#include <flutter_secure_storage_windows/flutter_secure_storage_windows_plugin.h>
#include <nav_layouts_component/nav_layouts_component_plugin_c_api.h>
#include <organization_feature/organization_feature_plugin_c_api.h>
#include <platform_utilities_component/platform_utilities_component_plugin_c_api.h>
#include <screen_retriever_windows/screen_retriever_windows_plugin_c_api.h>
#include <ui_widgets_component/ui_widgets_component_plugin_c_api.h>
#include <url_launcher_windows/url_launcher_windows.h>
#include <user_identity_feature/user_identity_feature_plugin_c_api.h>
#include <window_manager/window_manager_plugin.h>

void RegisterPlugins(flutter::PluginRegistry* registry) {
  AmplifyDbCommonPluginRegisterWithRegistrar(
      registry->GetRegistrarForPlugin("AmplifyDbCommonPlugin"));
  AppFrameworkComponentPluginCApiRegisterWithRegistrar(
      registry->GetRegistrarForPlugin("AppFrameworkComponentPluginCApi"));
  BillingFeaturePluginCApiRegisterWithRegistrar(
      registry->GetRegistrarForPlugin("BillingFeaturePluginCApi"));
  ConnectivityPlusWindowsPluginRegisterWithRegistrar(
      registry->GetRegistrarForPlugin("ConnectivityPlusWindowsPlugin"));
  FlutterSecureStorageWindowsPluginRegisterWithRegistrar(
      registry->GetRegistrarForPlugin("FlutterSecureStorageWindowsPlugin"));
  NavLayoutsComponentPluginCApiRegisterWithRegistrar(
      registry->GetRegistrarForPlugin("NavLayoutsComponentPluginCApi"));
  OrganizationFeaturePluginCApiRegisterWithRegistrar(
      registry->GetRegistrarForPlugin("OrganizationFeaturePluginCApi"));
  PlatformUtilitiesComponentPluginCApiRegisterWithRegistrar(
      registry->GetRegistrarForPlugin("PlatformUtilitiesComponentPluginCApi"));
  ScreenRetrieverWindowsPluginCApiRegisterWithRegistrar(
      registry->GetRegistrarForPlugin("ScreenRetrieverWindowsPluginCApi"));
  UiWidgetsComponentPluginCApiRegisterWithRegistrar(
      registry->GetRegistrarForPlugin("UiWidgetsComponentPluginCApi"));
  UrlLauncherWindowsRegisterWithRegistrar(
      registry->GetRegistrarForPlugin("UrlLauncherWindows"));
  UserIdentityFeaturePluginCApiRegisterWithRegistrar(
      registry->GetRegistrarForPlugin("UserIdentityFeaturePluginCApi"));
  WindowManagerPluginRegisterWithRegistrar(
      registry->GetRegistrarForPlugin("WindowManagerPlugin"));
}
