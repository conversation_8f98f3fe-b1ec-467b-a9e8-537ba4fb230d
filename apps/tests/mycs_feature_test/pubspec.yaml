name: mycs_feature_test
description: "MyCS Feature Test Application"
publish_to: 'none'

environment:
  sdk: '>=3.4.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  intl: any

  cupertino_icons: ^1.0.2
  window_manager: ^0.4.3
  logging: ^1.2.0
  provider: ^6.1.1
  bloc: ^9.0.0
  go_router: ^14.1.2
  dio: ^5.8.0+1
  get_it: ^8.0.3
  amplify_flutter: ^2.5.0
  amplify_auth_cognito: ^2.5.0
  amplify_api: ^2.5.0
  hydrated_bloc: ^10.0.0

  accessibility_tools: ^2.4.1

  utilities_ab:
    path: ../../../libs/commons/dart/utilities
  platform_utilities_component:
    path: ../../../libs/component/flutter/platform_utilities
  ui_widgets_component:
    path: ../../../libs/component/flutter/ui_widgets
  app_framework_component:
    path: ../../../libs/component/flutter/app_framework
  nav_layouts_component:
    path: ../../../libs/component/flutter/nav_layouts

  identity_service:
    path: ../../../libs/shared/service/flutter/identity
  user_identity_feature:
    path: ../../../libs/shared/feature/flutter/user_identity

  user_space_model:
    path: ../../../libs/shared/service/flutter/user_space
  organization_service:
    path: ../../../libs/shared/service/flutter/organization
  organization_feature:
    path: ../../../libs/shared/feature/flutter/organization
  billing_service:
    path: ../../../libs/shared/service/flutter/billing
  billing_feature:
    path: ../../../libs/shared/feature/flutter/billing


dev_dependencies:
  integration_test:
    sdk: flutter
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.3
  build_runner: ">=2.4.8 <4.0.0"

flutter:
  generate: true
  uses-material-design: true

  assets:
    - assets/

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/test-app-icon.png"
  min_sdk_android: 21
  web:
    generate: true
  windows:
    generate: true
    icon_size: 48
  macos:
    generate: true
