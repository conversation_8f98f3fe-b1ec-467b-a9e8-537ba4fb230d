import 'package:flutter/material.dart';

class OptionSelectedAlert extends StatelessWidget {
  final String optionTitle;

  const OptionSelectedAlert({
    super.key,
    required this.optionTitle,
  });

  static show(BuildContext context, String optionTitle) {
    showDialog(
      context: context,
      builder: (context) {
        return OptionSelectedAlert(
          optionTitle: optionTitle,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Option selected'),
      content: Text('You selected the $optionTitle option.'),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: const Text('OK'),
        ),
      ],
    );
  }
}
