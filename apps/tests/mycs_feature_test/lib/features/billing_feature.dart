import 'package:flutter/material.dart';

import 'package:app_framework_component/app_framework.dart' as app;
// import 'package:nav_layouts_component/nav_layouts.dart';
import 'package:user_identity_feature/user_identity.dart';

import 'option_selected_alert.dart';

final class BillingFeature extends app.Feature {
  static String get featureName => 'billing';

  @override
  String get name => featureName;

  @override
  app.FeatureConfigDeserializer? get configDeserializer => null;

  @override
  List<LocalizationsDelegate> get localizationsDelegates => [];

  @override
  Future<void> initialize(
    app.FeatureConfig? config,
    Map<String, app.Feature> registeredFeatures,
  ) async {}

  @override
  List<app.FeatureAnchor> anchors(
    BuildContext? context,
  ) {
    return [
      // BootstrapTarget(
      //   hookName: RootNavLayout.postLoginHookName,
      //   order: 100,
      //   builder: (context, complete) {
      //     return LayoutBuilder(builder: (context, constraints) {
      //       print(
      //         'BillingFeature BootstrapTarget: starting... '
      //         'constraints(${constraints.maxWidth}x${constraints.maxHeight})',
      //       );

      //       final future = Future.delayed(const Duration(seconds: 10), () {
      //         print('BillingFeature BootstrapTarget: complete');
      //         complete(true);
      //       });

      //       return FutureBuilder(
      //         future: future,
      //         builder: (context, snapshot) {
      //           if (snapshot.hasData) {
      //             return const SizedBox();
      //           } else {
      //             return const CircularProgressIndicator();
      //           }
      //         },
      //       );
      //     });
      //   },
      // ),
      ProfileMenuOptionGroup(
        order: 1,
        options: [
          ProfileMenuOption(
            title: 'Billing',
            icon: Icons.credit_card,
            onSelected: (context) => OptionSelectedAlert.show(
              context,
              'Billing',
            ),
          ),
          ProfileMenuOption(
            title: 'History',
            icon: Icons.history,
            onSelected: (context) => OptionSelectedAlert.show(
              context,
              'History',
            ),
          ),
        ],
      ),
    ];
  }

  @override
  void dispose() {}

  // Feature helper methods

  static Future<void> register() async {
    final featureRegistry = app.FeatureRegistry.instance();
    await featureRegistry.registerFeature(BillingFeature._());
  }

  BillingFeature._();
}
