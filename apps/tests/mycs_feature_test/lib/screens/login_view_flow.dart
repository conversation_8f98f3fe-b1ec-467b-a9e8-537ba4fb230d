import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import 'package:user_identity_feature/user_identity.dart';

import 'home_layout.dart';
import 'applications.dart';

class LoginViewFlow extends UserAuthFlow {
  @override
  Widget buildFeatureContainer(
    BuildContext context,
    GoRouterState state,
    Widget feature,
  ) {
    return HomeLayout(
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16.0, 32.0, 16.0, 32.0),
        child: feature,
      ),
    );
  }

  LoginViewFlow()
      : super(
          initialLoggedInRoute: ApplicationsPage.name,
        );
}
