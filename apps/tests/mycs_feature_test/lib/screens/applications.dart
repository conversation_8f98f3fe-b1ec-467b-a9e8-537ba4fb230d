import 'dart:convert';
import 'package:flutter/material.dart';

import 'package:nav_layouts_component/nav_layouts.dart';
import 'package:organization_feature/organization_feature.dart';

class ApplicationsPage extends StatelessNavigableWidget {
  @override
  String get routeName => name;
  static const String name = 'applications';

  @override
  String get routePath => path;
  static const String path = '/applications';

  const ApplicationsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Applications Test Page',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.headlineLarge,
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              child: const Text('Test Organization Config'),
              onPressed: () {
                _testOrganizationConfig(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testOrganizationConfig(
    BuildContext context,
  ) async {
    final test1Config = OrganizationFeature.getDefaultOrganizationConfig(
      key: 'test1',
    );
    final test2Config = OrganizationFeature.getDefaultOrganizationConfig(
      key: 'test2',
    );

    print(
      'Organization Feature Config '
      '"test1": ${jsonEncode(test1Config)}',
    );
    print(
      'Organization Feature Config '
      '"test2": ${jsonEncode(test2Config)}',
    );

    test1Config['value1'] = '++1 - ${DateTime.now().toIso8601String()}';
    test2Config['value2'] = '++2 - ${DateTime.now().toIso8601String()}';

    await OrganizationFeature.updateDefaultOrganizationConfig(
      key: 'test1',
      config: test1Config,
    );
    await OrganizationFeature.updateDefaultOrganizationConfig(
      key: 'test2',
      config: test2Config,
    );

    print(
      'Organization Feature Config '
      '"test1" updated: ${jsonEncode(test1Config)}',
    );
    print(
      'Organization Feature Config '
      '"test2" updated: ${jsonEncode(test2Config)}',
    );
  }
}
