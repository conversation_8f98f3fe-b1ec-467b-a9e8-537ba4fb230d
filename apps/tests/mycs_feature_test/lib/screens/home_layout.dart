import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

import 'package:nav_layouts_component/nav_layouts.dart';

import '../config/app_config.dart';

class HomeLayout extends StatelessWidget {
  final Widget body;

  const HomeLayout({
    super.key,
    required this.body,
  });

  @override
  Widget build(BuildContext context) {
    return RootSplashLayout(
      body: body,
      background: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/matrix.jpeg'),
          fit: BoxFit.cover,
        ),
      ),
      bodyMaxWidth: 500,
      bodyMinWidth: 320,
      title: AppConfig.title,
    );
  }
}
