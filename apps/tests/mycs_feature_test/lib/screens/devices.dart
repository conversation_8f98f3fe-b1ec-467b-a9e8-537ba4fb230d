import 'package:flutter/material.dart';

import 'package:nav_layouts_component/nav_layouts.dart';

class DevicesPage extends StatelessNavigableWidget {
  @override
  String get routeName => name;
  static const String name = 'devices';

  @override
  String get routePath => path;
  static const String path = '/devices';

  const DevicesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Devices Test Page',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.headlineLarge,
            ),
          ],
        ),
      ),
    );
  }
}
