import 'package:flutter/material.dart';

import 'package:nav_layouts_component/nav_layouts.dart' as nav;

import 'package:user_identity_feature/user_identity.dart';

import '../config/app_config.dart';
import 'home_page.dart';
import 'applications.dart';
import 'devices.dart';

class AppNavLayout extends nav.AppNavLayout {
  @override
  List<nav.NavTarget> get navTargets => [
        nav.NavTarget(
          body: const ApplicationsPage(),
          destBuilder: (_) => nav.NavDest(
            iconData: Icons.apps,
            selectedIconData: Icons.apps_sharp,
            label: 'Applications',
          ),
        ),
        nav.NavTarget(
          body: const DevicesPage(),
          destBuilder: (_) => nav.NavDest(
            iconData: Icons.devices,
            selectedIconData: Icons.devices_sharp,
            label: 'Devices',
          ),
        ),
      ];

  @override
  Widget? buildTitleBarLeading(BuildContext context) {
    return IconButton(
      icon: Icon(
        Icons.arrow_back_ios_new,
        color: Theme.of(context).textTheme.titleLarge!.color,
        semanticLabel: 'Go Back',
      ),
      onPressed: () {
        nav.GlobalNavigator.goTo(HomePage.name);
      },
    );
  }

  @override
  List<Widget>? buildTitleBarActions(BuildContext context) {
    const defaultAvatarImage = AssetImage('assets/doctor.png');

    return [
      const ProfileMenu(
        defaultAvatarImage: defaultAvatarImage,
        showLogout: false,
      ),
    ];
  }

  AppNavLayout() : super(AppConfig.title);
}
