import 'dart:io';
import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:window_manager/window_manager.dart';
import 'package:logging/logging.dart';
import 'package:get_it/get_it.dart';
import 'package:dio/dio.dart';
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:amplify_auth_cognito/amplify_auth_cognito.dart';
import 'package:amplify_api/amplify_api.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';

// import 'package:accessibility_tools/accessibility_tools.dart' as at;

import 'package:utilities_ab/utilities.dart';
import 'package:platform_utilities_component/platform_utilities.dart';
import 'package:ui_widgets_component/ui_widgets.dart' as ui;
import 'package:nav_layouts_component/nav_layouts.dart' as nav;
import 'package:app_framework_component/app_framework.dart' as app;

import 'package:identity_service/identity_service.dart' as identity;
import 'package:user_identity_feature/user_identity.dart';

import 'package:organization_service/organization_service.dart' as organization;
import 'package:organization_feature/organization.dart';

import 'package:billing_service/billing_service.dart' as billing;
import 'package:billing_feature/billing.dart';

import 'aws_amplify_config.dart';

import 'config/app_config.dart';
import 'screens/home_page.dart';
import 'screens/app_nav_layout.dart';
import 'screens/login_view_flow.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  initLogging(
    Level.ALL,
    logToConsole: true,
  );

  // On Desktop platforms the minimum size
  // of the window is fixed at 240x400
  if (!AppPlatform.isWeb && !AppPlatform.isMobile) {
    await windowManager.ensureInitialized();

    WindowManager.instance.setMinimumSize(AppConfig.minWindowSize);
    WindowManager.instance.setTitle(AppConfig.title);
  }

  // Initialize AppPlatform
  await AppPlatform.init();

  // Initialize Amplify
  await _configureAmplify();

  // Initialize HydratedBloc storage
  HydratedBloc.storage = await HydratedStorage.build(
    storageDirectory: kIsWeb
        ? HydratedStorageDirectory.web
        : HydratedStorageDirectory('${AppPlatform.appDataPath}/state'),
  );
  // HydratedBloc.storage = await SecureHydratedStorage.build();

  // Create and initialize the
  // feature registry singleton
  app.FeatureRegistry.intialize(featureConfigLoader);

  runApp(
    nav.StatefulWrapper(
      onInit: (_) async {
        GetIt.instance.registerLazySingleton<identity.IdentityProvider>(
          () {
            return identity.AWSCognitoService();
          },
        );
        GetIt.instance.registerLazySingleton<app.AuthSession>(
          () {
            return identity.AWSCognitoService();
          },
        );
        GetIt.instance.registerLazySingleton<organization.OrgProvider>(
          () {
            return organization.UserSpaceOrgService();
          },
        );
        GetIt.instance.registerLazySingleton<billing.BillingProvider>(
          () {
            return billing.UserSpaceBillingService(
              billing.UserSpaceBillingApi(
                _createDioClient(
                  userSpaceApiEndpoint,
                ),
              ),
            );
          },
        );

        // register the user identity feature
        await UserIdentityFeature.register();
        // register the organization feature
        await OrganizationFeature.register();
        // register the billing feature
        await BillingFeature.register();
      },
      onDispose: () async {
        GetIt.instance.unregister<app.AuthSession>();
        GetIt.instance.unregister<identity.IdentityProvider>(
          disposingFunction: (service) async => await service.dispose(),
        );

        app.FeatureRegistry.instance().dispose();
      },
      child: MainApp(),
    ),
  );
}

class MainApp extends StatelessWidget {
  late final AuthRouter _authRouter;

  MainApp({super.key}) {
    _authRouter = AuthRouter(
      navigatorKey: nav.GlobalNavigator.key,
      initialLocation: '/',
      authRoute: LoginViewFlow(),
      publicRoutes: [
        const HomePage().route(),
      ],
      privateRoutes: [
        AppNavLayout().route(),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    AppPlatform.initOnBuild(context);

    if (AppPlatform.isDesktop && Platform.isMacOS) {
      // On macOS a system menu is a required part of every application
      final List<PlatformMenuItem> menus = <PlatformMenuItem>[
        PlatformMenu(
          label: '', // In macOS the application name is shown in the menu bar
          menus: <PlatformMenuItem>[
            PlatformMenuItemGroup(
              members: <PlatformMenuItem>[
                PlatformMenuItem(
                  label: 'About',
                  onSelected: () {
                    showAboutDialog(
                      context: context,
                      applicationName: AppConfig.title,
                      applicationVersion: AppConfig.version,
                    );
                  },
                ),
              ],
            ),
            if (PlatformProvidedMenuItem.hasMenu(
                PlatformProvidedMenuItemType.quit))
              const PlatformProvidedMenuItem(
                  type: PlatformProvidedMenuItemType.quit),
          ],
        ),
      ];
      WidgetsBinding.instance.platformMenuDelegate.setMenus(menus);
    }

    final featureRegistry = app.FeatureRegistry.instance();
    final localizationDelegates = [
      GlobalMaterialLocalizations.delegate,
      GlobalWidgetsLocalizations.delegate,
      GlobalCupertinoLocalizations.delegate,
      ui.UIWidgetLocalizations.delegate,
      ...featureRegistry.getLocalizationsDelegates(),
    ];

    return featureRegistry.scope(
      context,
      child: MaterialApp.router(
        debugShowCheckedModeBanner: false,
        title: AppConfig.title,
        localizationsDelegates: localizationDelegates,
        supportedLocales: const [
          Locale('en'), // English
        ],
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: Colors.indigo,
          ),
          useMaterial3: false,
        ),
        darkTheme: ThemeData(
          colorScheme: ColorScheme.fromSeed(
            brightness: Brightness.dark,
            seedColor: Colors.indigo,
          ),
          useMaterial3: false,
        ),
        routerConfig: _authRouter.config,
        // builder: (context, child) => at.AccessibilityTools(
        //   // Set to null to disable tap area checking
        //   minimumTapAreas: at.MinimumTapAreas.material,
        //   // Check for semantic labels
        //   checkSemanticLabels: true,
        //   // Check for flex overflows
        //   checkFontOverflows: true,
        //   // Check for image labels
        //   checkImageLabels: true,
        //   // Set how much info about issues is printed
        //   logLevel: at.LogLevel.verbose,
        //   // Set where the buttons are placed
        //   buttonsAlignment: at.ButtonsAlignment.bottomRight,
        //   // Enable or disable draging the buttons around
        //   enableButtonsDrag: false,
        //   // Customize testing tools configuration
        //   testingToolsConfiguration: at.TestingToolsConfiguration(
        //     enabled: true,
        //     minTextScale: 1.0,
        //     maxTextScale: 2,
        //   ),
        //   child: child,
        // ),
      ),
    );
  }
}

Future<void> _configureAmplify() async {
  // Add any Amplify plugins you want to use
  await Amplify.addPlugins([
    AmplifyAuthCognito(),
    AmplifyAPI(),
  ]);

  // You can use addPlugins if you are going to be adding multiple plugins
  // await Amplify.addPlugins([authPlugin, analyticsPlugin]);

  // Once Plugins are added, configure Amplify
  // Note: Amplify can only be configured once.
  try {
    await Amplify.configure(amplifyconfig);
  } on AmplifyAlreadyConfiguredException {
    safePrint(
        "Tried to reconfigure Amplify; this can occur when your app restarts on Android.");
  }
}

Dio _createDioClient(
  String endpoint, {
  Duration connectTimeout = const Duration(seconds: 30),
  Duration receiveTimeout = const Duration(seconds: 30),
}) {
  final authProvider = GetIt.instance.get<identity.IdentityProvider>();

  final dio = Dio(
    BaseOptions(
      baseUrl: endpoint,
      connectTimeout: connectTimeout,
      receiveTimeout: receiveTimeout,
    ),
  );
  dio.interceptors.add(authProvider.authInterceptor);

  return dio;
}

Future<Map<String, dynamic>> featureConfigLoader(
  String featureName,
) async {
  /// Load the feature configuration json from the assets
  return jsonDecode(
    await rootBundle.loadString('assets/${featureName}_feature.json'),
  );
}
