PODS:
  - amplify_auth_cognito (0.0.1):
    - Flutter
    - FlutterMacOS
  - amplify_secure_storage (0.0.1):
    - FlutterMacOS
  - app_framework_component (0.0.1):
    - FlutterMacOS
  - billing_feature (0.0.1):
    - FlutterMacOS
  - connectivity_plus (0.0.1):
    - FlutterMacOS
  - device_info_plus (0.0.1):
    - FlutterMacOS
  - flutter_secure_storage_macos (6.1.3):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - nav_layouts_component (0.0.1):
    - FlutterMacOS
  - organization_feature (0.0.1):
    - FlutterMacOS
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - platform_utilities_component (0.0.1):
    - FlutterMacOS
  - screen_retriever_macos (0.0.1):
    - FlutterMacOS
  - ui_widgets_component (0.0.1):
    - FlutterMacOS
  - url_launcher_macos (0.0.1):
    - FlutterMacOS
  - user_identity_feature (0.0.1):
    - FlutterMacOS
  - window_manager (0.2.0):
    - FlutterMacOS

DEPENDENCIES:
  - amplify_auth_cognito (from `Flutter/ephemeral/.symlinks/plugins/amplify_auth_cognito/darwin`)
  - amplify_secure_storage (from `Flutter/ephemeral/.symlinks/plugins/amplify_secure_storage/macos`)
  - app_framework_component (from `Flutter/ephemeral/.symlinks/plugins/app_framework_component/macos`)
  - billing_feature (from `Flutter/ephemeral/.symlinks/plugins/billing_feature/macos`)
  - connectivity_plus (from `Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos`)
  - device_info_plus (from `Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos`)
  - flutter_secure_storage_macos (from `Flutter/ephemeral/.symlinks/plugins/flutter_secure_storage_macos/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - nav_layouts_component (from `Flutter/ephemeral/.symlinks/plugins/nav_layouts_component/macos`)
  - organization_feature (from `Flutter/ephemeral/.symlinks/plugins/organization_feature/macos`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - platform_utilities_component (from `Flutter/ephemeral/.symlinks/plugins/platform_utilities_component/macos`)
  - screen_retriever_macos (from `Flutter/ephemeral/.symlinks/plugins/screen_retriever_macos/macos`)
  - ui_widgets_component (from `Flutter/ephemeral/.symlinks/plugins/ui_widgets_component/macos`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)
  - user_identity_feature (from `Flutter/ephemeral/.symlinks/plugins/user_identity_feature/macos`)
  - window_manager (from `Flutter/ephemeral/.symlinks/plugins/window_manager/macos`)

EXTERNAL SOURCES:
  amplify_auth_cognito:
    :path: Flutter/ephemeral/.symlinks/plugins/amplify_auth_cognito/darwin
  amplify_secure_storage:
    :path: Flutter/ephemeral/.symlinks/plugins/amplify_secure_storage/macos
  app_framework_component:
    :path: Flutter/ephemeral/.symlinks/plugins/app_framework_component/macos
  billing_feature:
    :path: Flutter/ephemeral/.symlinks/plugins/billing_feature/macos
  connectivity_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos
  device_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos
  flutter_secure_storage_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_secure_storage_macos/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  nav_layouts_component:
    :path: Flutter/ephemeral/.symlinks/plugins/nav_layouts_component/macos
  organization_feature:
    :path: Flutter/ephemeral/.symlinks/plugins/organization_feature/macos
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  platform_utilities_component:
    :path: Flutter/ephemeral/.symlinks/plugins/platform_utilities_component/macos
  screen_retriever_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/screen_retriever_macos/macos
  ui_widgets_component:
    :path: Flutter/ephemeral/.symlinks/plugins/ui_widgets_component/macos
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos
  user_identity_feature:
    :path: Flutter/ephemeral/.symlinks/plugins/user_identity_feature/macos
  window_manager:
    :path: Flutter/ephemeral/.symlinks/plugins/window_manager/macos

SPEC CHECKSUMS:
  amplify_auth_cognito: b76a0e367489da2b9a3c6e5a8ec74a72a0a7cc2f
  amplify_secure_storage: a0f7269fd7bca15e77c30abae739000a990e8fcd
  app_framework_component: 2609c1dc52844c7313b2d5eb6751735732fa0a99
  billing_feature: e51d7091afe98d4d998e137f35661f6553286019
  connectivity_plus: 0a976dfd033b59192912fa3c6c7b54aab5093802
  device_info_plus: 1b14eed9bf95428983aed283a8d51cce3d8c4215
  flutter_secure_storage_macos: c2754d3483d20bb207bb9e5a14f1b8e771abcdb9
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  nav_layouts_component: 43f424aa2aa3319e5db8371b10314ad4732cb7c5
  organization_feature: 85d8ef679b2c5afc3773e6c118a311d915b4f432
  package_info_plus: 12f1c5c2cfe8727ca46cbd0b26677728972d9a5b
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  platform_utilities_component: ce2e5110f9b66f0822d20cd97447eee489a752ab
  screen_retriever_macos: 776e0fa5d42c6163d2bf772d22478df4b302b161
  ui_widgets_component: 2a6413461141c044780a7b4827df7f2e3a6dad85
  url_launcher_macos: c82c93949963e55b228a30115bd219499a6fe404
  user_identity_feature: 74f33eb1e89592c38ad4812f1f09d978e09bf3a5
  window_manager: 3a1844359a6295ab1e47659b1a777e36773cd6e8

PODFILE CHECKSUM: 9ebaf0ce3d369aaa26a9ea0e159195ed94724cf3

COCOAPODS: 1.16.2
