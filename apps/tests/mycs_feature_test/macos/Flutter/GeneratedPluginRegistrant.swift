//
//  Generated file. Do not edit.
//

import FlutterMacOS
import Foundation

import amplify_auth_cognito
import amplify_secure_storage
import app_framework_component
import billing_feature
import connectivity_plus
import device_info_plus
import flutter_secure_storage_macos
import nav_layouts_component
import organization_feature
import package_info_plus
import path_provider_foundation
import platform_utilities_component
import screen_retriever_macos
import ui_widgets_component
import url_launcher_macos
import user_identity_feature
import window_manager

func RegisterGeneratedPlugins(registry: FlutterPluginRegistry) {
  AmplifyAuthCognitoPlugin.register(with: registry.registrar(forPlugin: "AmplifyAuthCognitoPlugin"))
  AmplifySecureStoragePlugin.register(with: registry.registrar(forPlugin: "AmplifySecureStoragePlugin"))
  AppFrameworkComponentPlugin.register(with: registry.registrar(forPlugin: "AppFrameworkComponentPlugin"))
  BillingFeaturePlugin.register(with: registry.registrar(forPlugin: "BillingFeaturePlugin"))
  ConnectivityPlusPlugin.register(with: registry.registrar(forPlugin: "ConnectivityPlusPlugin"))
  DeviceInfoPlusMacosPlugin.register(with: registry.registrar(forPlugin: "DeviceInfoPlusMacosPlugin"))
  FlutterSecureStoragePlugin.register(with: registry.registrar(forPlugin: "FlutterSecureStoragePlugin"))
  NavLayoutsComponentPlugin.register(with: registry.registrar(forPlugin: "NavLayoutsComponentPlugin"))
  OrganizationFeaturePlugin.register(with: registry.registrar(forPlugin: "OrganizationFeaturePlugin"))
  FPPPackageInfoPlusPlugin.register(with: registry.registrar(forPlugin: "FPPPackageInfoPlusPlugin"))
  PathProviderPlugin.register(with: registry.registrar(forPlugin: "PathProviderPlugin"))
  PlatformUtilitiesComponentPlugin.register(with: registry.registrar(forPlugin: "PlatformUtilitiesComponentPlugin"))
  ScreenRetrieverMacosPlugin.register(with: registry.registrar(forPlugin: "ScreenRetrieverMacosPlugin"))
  UiWidgetsComponentPlugin.register(with: registry.registrar(forPlugin: "UiWidgetsComponentPlugin"))
  UrlLauncherPlugin.register(with: registry.registrar(forPlugin: "UrlLauncherPlugin"))
  UserIdentityFeaturePlugin.register(with: registry.registrar(forPlugin: "UserIdentityFeaturePlugin"))
  WindowManagerPlugin.register(with: registry.registrar(forPlugin: "WindowManagerPlugin"))
}
