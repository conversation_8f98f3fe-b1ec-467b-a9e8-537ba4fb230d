{"name": "novassist", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "export": "next build && next export", "clean": "rm -rf .next out node_modules/.cache"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "next": "^13.5.0"}, "devDependencies": {"typescript": "^5.0.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "eslint": "^8.0.0", "eslint-config-next": "^13.5.0"}}